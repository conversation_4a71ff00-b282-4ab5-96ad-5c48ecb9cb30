{"Nav": {"students": "Students", "class subject management": "Class/ Subject management", "class management": "Class Management", "classes": "Classes", "library": "Library", "member management": "Member Management", "members": "Members", "book management": "Book Management", "books": "Books", "borrow/return books": "Borrow/Return Books", "book sources": "Book Sources", "book categories": "Book Categories", "book classifications": "Book Classifications", "book sub classifications": "Book Sub Classifications", "co-curricular": "Co-Curricular", "club/society management": "Club/Society Management", "club/society categories": "Club/Society Categories", "clubs/societies": "Clubs/Societies", "coach management": "Coach Management", "student affairs": "Student Affairs", "conduct management": "Conduct Management", "conduct deadline": "Conduct Deadline", "conduct grading scheme settings": "Conduct Grading Scheme Settings", "conduct assign settings": "Conduct Assign Settings", "conduct marks entry": "Conduct Marks Entry", "comprehensive quality assessment": "Comprehensive Quality Assessment", "comprehensive assessment categories": "Comprehensive Assessment Categories", "comprehensive assessment questions": "Comprehensive Assessment Questions", "comprehensive assessment result entry": "Comprehensive Assessment Result Entry", "class leadership list": "Class Leadership List", "class leadership position settings": "Class Leadership Position Settings", "class leaderships": "Class Leaderships", "reward/punishment": "Reward/ Punishment", "merit/demerit settings": "Merit/ Demerit Settings", "reward/punishment categories": "Reward/ Punishment Categories", "reward/punishment sub categories": "Reward/ Punishment Sub categories", "reward/punishment settings": "Reward/ Punishment Settings", "reward/punishment records": "Reward/ Punishment Records", "special competition/special activity": "Special Competition/ Special Activity", "awards": "Awards", "counselling": "Counselling", "case records": "Case Records", "product categories": "Product Categories", "reports": "Reports", "hostel": "Hostel", "accounting": "Accounting"}, "enrollment_summary": {"by_citizenship": "By Citizenship", "by_enrollment_status": "By Enrollment Status", "by_exam_requirement": "By Exam Requirement", "by_full_payment_status": "By Full Payment Status", "by_gender": "By Gender", "by_hostel": "By Hostel", "by_submission_date": "By Submission Date", "total_applications_submitted": "Total Applications Submitted", "total_confirmed_students": "Total Confirmed Students", "total_fee_collection": "Total Fee Collection"}, "common": {"rows_per_page": "Rows per page", "page_total": "{number} in total", "pagination": "Page {number} of {total}", "uom": "UOM", "adjust_wallet_note": "Please enter negative value to deduct funds from wallet, and positive value to top up wallet.", "selected_already_added": "The selected {item} has already been added", "one_or_more_selected_already_added": "One or more selected {item}s are already added", "mix_name": "name", "new_student": "NEW", "primary_class_type": "Primary", "primary_school_level": "Primary School", "secondary_school_level": "Secondary School", "quick search": "Quick Search"}}