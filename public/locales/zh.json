{"Nav": {"Pin Hwa High School": "滨华中学", "My Profile": "我的资料", "Log Out": "登出", "Welcome Back,": "欢迎回来,", "master data": "主数据", "announcement": "通告", "announcement groups": "通告组", "announcements": "通告", "information center": "资讯处", "smart cards": "智能卡名单", "withdrawal reasons": "支出原因", "payment methods": "付款方式", "employee sessions": "职员时段", "employee job titles": "职员职务", "semester settings": "学期设定", "configs": "系统设定", "grades": "年级", "disciplinary actions": "奖惩管理", "create disciplinary record": "奖惩输入", "disciplinary records": "奖惩记录", "arrival & departure": "出入校管理", "create departure record": "离宿记录", "arrival/departure": "返宿/离宿记录", "bulk mark arrival": "批量返宿", "hostel students": "住宿生", "person in charge": "宿舍执委", "import/export smart cards": "导入/导出智能卡", "wallet management": "钱包管理", "wallet transactions": "钱包记录", "class attendance taking": "班级点名", "enrollment": "注册", "enrollment users": "注册用户", "enrollment sessions": "注册时段", "newly registered students": "新注册学生", "import new students": "导入新学生", "bulk update new students": "批量更新新学生", "payment": "付款", "uom": "UOM", "internationalization": "国际化", "attendance management": "考勤管理", "students": "学生", "student management": "学生管理", "employee management": "职员管理", "employees": "职员", "configuration": "设定", "roles": "角色", "class/subject management": "班级/科目管理", "class management": "班级管理", "substitute management": "代课管理", "classes": "班级", "semester classes": "学期班级", "users": "用户", "banks": "银行", "currencies": "货币", "gl account": "总账账户", "terminals": "终端", "terminal keys": "终端键", "copy semester settings": "复制学期设定", "primary": "正课", "english": "英语", "society": "学会", "elective": "选修", "major": "专业", "co-curriculum": "联课活动", "ecommerce": "电子商务", "bookstore": "书店", "canteen": "食堂", "library": "图书馆", "member management": "会员管理", "members": "会员", "book management": "书籍管理", "books": "书籍", "borrow/return books": "租借/归还", "book sources": "书籍来源", "book categories": "书籍分类", "book classifications": "书籍类别", "book subclassifications": "书籍子类别", "co-curricular": "联课处", "club/society management": "学会管理", "club/society categories": "学会类别", "clubs/societies": "学会名称", "coach management": "教练管理", "executive committee positions": "执委职位", "student affairs": "训导处", "conduct management": "操行管理", "conduct deadline": "操行输入截止日期", "conduct grading scheme settings": "操行评分设定", "conduct assign settings": "操行指派设定", "conduct marks entry": "操行输入", "comprehensive quality assessment": "综合素质评量", "comprehensive assessment categories": "综合素质评量类别", "comprehensive assessment questions": "综合素质评量问题", "comprehensive assessment result entry": "综合素质评量评估输入", "class leadership list": "班委名单", "class leadership position settings": "班级委员设置", "class leaderships": "班委名单", "reward/punishment": "奖惩管理", "merit/demerit settings": "功过设定", "reward/punishment categories": "奖惩类别", "reward/punishment sub categories": "奖惩子类别", "reward/punishment settings": "奖惩设定", "reward/punishment records": "奖惩输入", "special competition/special activity": "校外学艺及特殊表现", "competition/special activity settings": "校外学艺及特殊表现设定", "awards": "奖项", "counselling": "辅导处", "case records": "个案记录", "create case record": "创建个案记录", "primary schools": "小学名称", "diseases": "疾病", "educations": "教育", "departments": "部门", "product categories": "产品类别", "report": "报表", "reports": "报表", "hostel": "住宿", "student hostel configuration": "学生住宿设定", "employee hostel configuration": "职员住宿设定", "saving account": "储蓄户口", "hostels": "住宿", "wallets": "钱包", "hostel employees": "住宿职员", "bed assignment": "床位分配", "seat settings": "座位编排", "timetable": "时间表", "subject management": "科目管理", "subjects": "科目", "class subjects": "班级科目", "student assessment management": "学生成绩管理", "countries": "国家", "courses": "课程", "races": "种族", "religions": "宗教", "schools": "学校", "school profile": "学校资料", "states": "州属", "health concerns": "健康状况", "leave reasons": "离校理由", "blocks": "楼栋", "rooms": "房间", "beds": "床位", "calendar": "日历", "by boarders name list": "宿舍生名单", "by boarders list information": "宿舍生详细名单", "by available bed": "房间供应情况", "by checkout record": "退宿记录", "by change room record": "换房记录", "by boarders contact number": "住宿生联络名单", "by boarders date of birth": "住宿生生日日期名单", "by arrival/departure record": "返家/外出", "by stay back student": "留宿名单", "by employee lodging": "职员住宿名单", "by borrowed report": "租借报表", "by top borrower": "最⾼租借者", "by top ten borrowed books": "租借书籍排行榜(前十)", "by school rate of borrow": "学校书籍租借率", "by book loan": "书籍租借记录(个别)", "by daily sales": "每日销售报告", "by block": "楼栋", "by room": "房间", "by student": "学生", "post to autocount": "输出Autocount", "accounting": "会计处", "executive committee position settings": "执委职位设定", "executive committee name list": "执委名单", "exam management": "考试管理", "exams": "考试", "Exam Grading Scheme Settings": "考试评分设定", "Exam Grading Frameworks": "考试评分框架", "Exam Subject Exemption": "考试科目豁免", "Exam Grading Framework Assignment": "考试评分框架分配", "Exam Mark Entry": "考试分数输入", "Exam Posting": "考试发布", "Exam Posting Pre-Checks": "考试发布预检查", "Exam Posting Status": "考试发布状态", "Exam Promotion Mark": "考试晋升分数", "By Classes": "按班级", "By Students": "按学生", "student outstanding balance": "欠款记录"}, "enrollment_session": {"Enrollment Sessions": "注册时段", "Enrollment Session": "注册时段", "Eligible Period": "符合资格的时段", "Summary": "摘要", "Fee Assignment": "费用分配", "Conditions": "条件", "Outcome": "结果", "Add New Condition Group": "添加新组", "Add Group Without Condition": "添加新组(无条件)", "NO CONDITION": "无条件", "Equals": "等于", "Not Equals": "不等于", "Staying in hostel": "住宿", "Is Foreigner": "外国学生", "Operator": "操作符"}, "enrollment_summary": {"by_citizenship": "按国籍", "by_enrollment_status": "按注册状态", "by_exam_requirement": "按考试要求", "by_full_payment_status": "按全额付款状态", "by_gender": "按性别", "by_hostel": "按宿舍", "by_submission_date": "按提交日期", "total_applications_submitted": "已提交申请总数", "total_confirmed_students": "已确认学生总数", "total_fee_collection": "总费用收集"}, "months": {"Jan": "一月", "Feb": "二月", "Mar": "三月", "Apr": "四月", "May": "五月", "Jun": "六月", "Jul": "七月", "Aug": "八月", "Sep": "九月", "Oct": "十月", "Nov": "十一月", "Dec": "十二月"}, "daysOfWeek": {"Sunday": "星期日", "Monday": "星期一", "Tuesday": "星期二", "Wednesday": "星期三", "Thursday": "星期四", "Friday": "星期五", "Saturday": "星期六"}, "common": {"English": "英语", "Chinese": "中文", "Malay": "马来语", "Name ( Malay )": "名字(马来语)", "Name ( Chinese )": "名字(中文)", "Name ( English )": "名字(英语)", "quick search": "快搜", "Create ": "创建", "Update ": "编辑", "Add ": "添加", "Add": "添加", "Delete": "删除", "Edit / View": "编辑 / 查看", "Edit": "编辑", "Edit ": "编辑", "View": "查看", "Filter": "筛选", "Filtered": "已筛选", "Refresh": "刷新", "Search": "搜索", "Clear": "清除", "Submit": "提交", "Enter": "输入", "Confirm": "确认", "Cancel": "取消", "Continue": "继续", "Reset": "重置", "Save": "保存", "Sort": "排序", "sort by": "排序方式", "Change Status": "更改状态", "Yes": "是", "No": "否", "active": "活跃", "inactive": "不活跃", "Active": "活跃", "Inactive": "不活跃", "ACTIVE": "活跃", "INACTIVE": "不活跃", "rows_per_page": "每页行数", "page_total": "共 {number} 行", "pagination": "第 {number} 页, 共 {total} 页", "students": "学生", "student": "学生", "STUDENT": "学生", "Student ": "学生", "Employee ": "职员", "EMPLOYEE": "职员", "LIBRARIAN": "图书管理员", "OTHER": "其他", "OTHERS": "其他", "FATHER": "父亲", "MOTHER": "母亲", "GUARDIAN": "监护人", "MERCHANT": "商户", "GUEST": "访客", "PAID": "已付款", "UNPAID": "未付款", "PARTIAL": "部分付款", "CONFIRMED": "已确认", "PENDING": "待定", "Pending": "待定", "VOIDED": "已作废", "DRAFT": "草稿", "REJECTED": "已拒绝", "APPROVED": "已批准", "SHORTLISTED": "已入围", "TRANSFERRED": "插班生", "COMPLETED": "已完成", "IN PROGRESS": "进行中", "ERROR": "错误", "CANCELLED": "已取消", "NEW": "新", "new_student": "新生", "NONE": "无", "None": "无", "VEGETARIAN": "素食者", "BOOKSHOP": "书店", "CANTEEN": "食堂", "OPERATOR_APP": "操作员应用", "SUCCESS": "成功", "FAILED": "失败", "NORMAL": "正常", "SICK": "生病", "PASSED_AWAY": "已去世", "Created successfully": "创建成功", "Updated successfully": "更新成功", "Deleted successfully": "删除成功", "Are you sure you want to delete this?": "您确定要删除此项吗？", "This field is required": "请填写此项", "Type to search": "输入以搜索", "Type to search student": "输入以搜索学生", "Type to add": "输入以添加", "No record available": "没有记录", "No data": "没有数据", "No data available": "没有数据", "No data found": "没有数据", "No errors to display": "没有错误可显示", "Submitted successfully. Request to copy data to semester is being processed": "提交成功, 正在处理复制数据到学期的请求", "The student number will be automatically generated using a sequential number": "学生学号将自动生成，使用顺序编号", "Are you sure you want to generate new student number?": "您确定要生成新的学生学号吗？", "Are you sure you want to proceed?": "您确定要继续吗？", "Auto-generated": "自动生成", "Generate New": "生成新学号", "File size should not exceed": "文件大小不应超过", "Name is required": "名字是必填项", "Select a guardian type and add guardian": "选择监护人类型并添加监护人", "Select Guardian Type": "选择监护人类型", "Pick a date and time": "选择日期和时间", "Pick a date": "选择日期", "Choose File": "选择文件", "Select Students": "选择学生", "Select Teacher": "选择教师", "name": "名字", "mix_name": "中/英文名字", "code": "代码", "course": "课程", "photo": "照片", "status": "状态", "Status": "状态", "created at": "创建时间", "created by": "创建者", "expires at": "过期于", "expired at": "过期于", "amount": "金额", "amount received": "已收金额", "amount with discount": "折扣后金额", "amount before tax": "税前金额", "amount before tax after less advance": "税前金额减去预付款后金额", "amount after tax": "税后金额", "receipt": "收据", "tax code": "税码", "tax rate": "税率", "tax description": "税务描述", "tax amount": "税务金额", "tax percentage": "税务百分比", "percentage": "百分比", "total": "总计", "period": "时段", "product": "产品", "admission year": "入学年份", "admission grade": "入学年级", "Subject": "科目", "subjects": "科目", "Subjects": "科目", "swift code": "Swift代码", "bank": "银行", "banks": "银行", "configs": "配置", "countries": "国家", "country": "国家", "courses": "课程", "currencies": "货币", "currency": "货币", "symbol": "符号", "departments": "部门", "department": "部门", "description": "描述", "internationalization": "国际化", "sequence": "顺序", "educations": "教育", "education": "教育", "employee job titles": "职员职务", "employee job title": "职员职务", "health concerns": "健康状况", "health concern": "健康状况", "employee sessions": "职员时段", "employee session": "职员时段", "determine attendance status": "确定出勤状态", "All same with Monday Setting": "所有设置与星期一相同", "gl account": "总账账户", "label": "标签", "external reference no": "外部参考编号", "grades": "年级", "grade": "年级", "leave reasons": "离校理由", "leave reason": "离校理由", "races": "种族", "race": "种族", "religions": "宗教", "religion": "宗教", "schools": "学校", "school": "学校", "level": "级别", "State is required when a country is selected": "选择国家时必须选择州属。", "School Profile": "学校资料", "Student Profile": "学生资料", "Employee Profile": "职员资料", "short name": "简称", "address": "地址", "address 2": "地址 2", "city": "城市", "postcode": "邮政编码", "phone number": "电话号码", "phone number 2": "电话号码 2", "fax number": "传真号码", "email": "电子邮件", "website url": "网站网址", "school logo": "校徽", "principal name": "校长名字", "Semester Settings": "学期设定", "semester setting": "学期设定", "semester year": "学年", "from": "从", "to": "到", "current semester": "当前学期", "Add Semester Year": "添加学年", "Add Semester Setting": "添加学期设定", "Semester Years List": "学年列表", "Please add semester year first": "请先添加学年。", "Please add course first": "请先添加课程。", "states": "州属", "state": "州属", "uom": "UOM", "withdrawal reasons": "支出原因", "withdrawal reason": "支出原因", "payment": "付款", "payments": "付款列表", "payment methods": "付款方式", "payment method": "付款方式", "student number": "学生学号", "semester": "学期", "classes": "班级", "Classes": "班级", "class": "班级", "class type": "班级类型", "current class": "当前班级", "admission type": "入学类型", "class stream": "课系", "stream": "课系", "guardian name": "监护人中/英文名字", "guardian email": "监护人电子邮件", "guardian phone number": "监护人电话号码", "student photo": "学生照片", "gender": "性别", "date": "日期", "date of birth": "出生日期", "birthplace": "出生地", "join date": "加入日期", "leave date": "离校日期", "semester class": "学期班级", "leave status": "离校状态", "Leave": "离校", "Rejoin": "重新加入", "personal information": "个人资料", "Guardian": "监护人", "Guardians": "监护人", "guardian": "监护人", "guardians": "监护人", "disciplinary record": "纪律记录", "comprehensive assessment record": "综合素质评量记录", "academic report": "学术成绩", "employment history": "就业历史", "fee record": "费用记录", "activity report": "活动报告", "society report": "学会报告", "cocurriculum": "联课活动", "attendance report": "出席记录", "hostel applicant": "宿舍申请人", "NRIC": "身份证号码", "passport number": "护照号码", "birth cert number": "出生证明号码", "dietary restriction": "饮食限制", "primary school": "小学名称", "primary_school_level": "小学", "secondary_school_level": "中学", "VOCATIONAL": "职业学校", "COLLEGE": "学院", "UNIVERSITY": "大学", "remarks": "备注", "postal code": "邮政编码", "grading framework": "评分框架", "Primary": "主要", "primary_class_type": "正课", "is primary": "是主要的", "with user account": "有用户账户", "has user account": "有用户账户", "live status": "实时状态", "is direct dependant": "是直接受抚养人", "link to existing user": "链接到现有用户", "marital status": "婚姻状况", "occupation": "职业", "occupation description": "职业描述", "nationality": "国籍", "merit/demerit": "功过", "Merit/ Demerit": "功过", "Reward/ Punishment": "奖惩", "average exam marks": "平均考试分数", "conduct marks": "操行分数", "conduct mark": "操行分数", "display in report card": "在成绩单中显示", "records": "记录", "year": "年份", "month": "月份", "fee": "费用", "Fees": "费用", "Invoices": "发票", "Billing Document Details": "账单文件详情", "purchase date": "购买日期", "payment date": "付款日期", "payment due date": "付款截止日期", "document date": "文件日期", "reference no": "参考编号", "reference number": "参考编号", "payment reference no": "付款参考编号", "payment reference no 2": "付款参考编号 2", "paid at": "付款时间", "type": "类型", "sub type": "子类型", "payment status": "付款状态", "category": "类别", "item": "项目", "line items": "行项目", "result": "结果", "legal entity name": "法人名称", "legal entity address": "法人地址", "bill to name": "账单名称", "bill to address": "账单地址", "bill to reference number": "账单参考编号", "remit to account name": "汇款账户名称", "remit to account number": "汇款账户号码", "remit to bank name": "汇款银行名称", "remit to bank address": "汇款银行地址", "remit to swift code": "汇款Swift代码", "UOM Code": "UOM代码", "is discount": "是否折扣", "unit price": "单价", "quantity": "数量", "terminals": "终端", "terminal": "终端", "merchant": "商户", "secret": "密钥", "terminal keys": "终端键", "terminal key": "终端键", "Regenerate Secret": "重新生成密钥", "Generate QR Code": "生成二维码", "QR Code": "二维码", "Failed to generate QR code": "生成二维码失败", "role": "角色", "roles": "角色", "permissions": "权限", "system default role": "系统默认角色", "Select Permission Category": "选择权限类别", "user name": "用户名字", "user type": "用户类型", "user": "用户", "users": "用户", "Access Students": "访问学生", "User Info": "用户资料", "New Password": "密码", "New Password Confirmation": "确认密码", "student/employee number": "学生/职员编号", "access students": "访问学生", "Copy Semester Settings": "复制学期设定", "from semester": "从学期", "to semester": "到学期", "wallet": "钱包", "wallets": "钱包", "user email": "用户电子邮件", "user phone number": "用户电话号码", "balance": "余额", "Wallet Transactions": "交易记录", "card number": "卡号", "transaction amount": "交易金额", "wallet balance before": "交易前钱包余额", "wallet balance after": "交易后钱包余额", "action": "操作", "View Transactions": "查看交易记录", "Withdraw": "支出", "Adjust": "调整", "Adjust Wallet": "调整钱包", "adjust_wallet_note": "请输入负值以从钱包中扣除资金，正值以充值钱包", "Download Report": "下载报告", "Refund": "退款", "user number": "用户编号", "report language": "报告语言", "Transaction Date Range": "交易日期范围", "DEPOSIT": "存款", "TRANSFER": "转账", "TRANSACTION": "交易", "REFUND": "退款", "employee": "职员", "employees": "职员", "employee number": "职员编号", "badge number": "胸卡编号", "is hostel": "是否住宿", "Teacher": "教师", "Teachers": "教师", "Staff": "职员", "WORKING": "在职", "RESIGNED": "已辞职", "Female": "女", "Male": "男", "Others": "其他", "job title": "职务", "Resign": "辞职", "Reinstate": "复职", "Update Job Title": "更新职务", "personal email": "个人电子邮件", "employment type": "雇佣类型", "employee category": "职员类别", "employment start date": "雇佣开始日期", "employment end date": "雇佣结束日期", "highest education": "最高学历", "highest education country": "最高学历国家", "marriage status": "婚姻状况", "epf number": "EPF号码", "MARRIED": "已婚", "SINGLE": "未婚", "DIVORCED": "离异", "SEPARATED": "分居", "WIDOWED": "丧偶", "UNKNOWN": "未知", "FULL_TIME": "全职", "PART_TIME": "兼职", "CONTRACT": "合同工", "TEMPORARY": "临时工", "FREELANCE": "自由职业者", "CONTRACTOR": "承包商", "employee work status": "职员工作状态", "effective date": "生效日期", "smart cards": "智能卡名单", "smart card": "智能卡", "Search by Card Number": "按卡号搜索", "Please enter card number": "请输入卡号", "Card number": "卡号", "Card number must be in 10 digits": "卡号必须为10位", "Select ": "选择", "Student": "学生", "Students": "学生", "Employee": "职员", "Coach": "教练", "Existing Cards": "现有卡", "Deactivate": "停用", "Deactivate ": "停用", "library ": "图书馆", "Search ": "搜索", "Next": "下一步", "Copy": "复制", "Engine": "引擎", "Results": "结果", "Not Applicable": "初中级", "N/A": "不适用", "Commerce": "商科", "Science": "理科", "student name": "学生名字", "Current Class": "当前班级", "employee name": "职员名字", "is teacher": "教师", "Select All": "全选", "Deselect All": "取消全选", "import": "导入", "export": "导出", "Download": "下载", "Download Template": "下载模板", "Upload File": "上传文件", "card type": "卡类型", "update library card number": "更新图书馆卡号", "error message": "错误信息", " number": "编号", " name": "名字", "Please revise the data to resolve all errors before submitting": "请修订数据以解决所有错误后再提交", "Assigned successfully": "分配成功", "export type": "导出格式", "No Record Found": "没有记录", "student ": "学生", "hostel ": "住宿", "block": "楼栋", "blocks": "楼栋", "room": "房间", "room ": "房间", "rooms": "房间", "capacity": "容量", "bed": "床位", "bed ": "床位", "beds": "床位", "AVAILABLE": "可用", "OCCUPIED": "已占用", "Both": "通用", "merit/demerit settings": "功过设定", "merit/demerit setting": "功过设定", "MERIT": "功", "DEMERIT": "过", "reward/punishment": "奖惩", "reward/punishment settings": "奖惩设定", "reward/punishment setting": "奖惩设定", "points": "分", "employee ": "职员", "bed availability": "床位可用性", "hostel students": "住宿生", "Bulk Mark ": "批量标记", "Departure": "离宿", "Arrival": "返宿", "Disciplinary": "违纪", "has bed": "有床位", "is checked out": "已退宿", "start date": "开始日期", "end date": "结束日期", "Reason": "原因", "Apply": "应用", "Apply All": "应用全部", "Apply All Due Date": "应用所有截止日期", "Apply All Class Enter Date": "应用所有班级进入日期", "Apply All Conduct Mark": "应用所有操行分数", "Apply All Net Average": "应用所有净平均分数", "remark": "备注", "check in date time": "返宿时间", "check out date time": "离宿时间", "signing guardian": "签署监护人", "person in charge": "宿舍执委", "hostel person in charge": "宿舍执委", "remarks for all": "所有人的备注", "mark (before)": "积分(前)", "mark (after)": "积分(后)", "mark before": "积分(前)", "mark after": "积分(后)", "enrollment users": "注册用户", "Newly Registered Students": "新注册学生", "Newly Registered Student": "新注册学生", "Newly Registered Student Payment": "新注册学生付款", "enrollment status": "注册状态", "registration date": "注册日期", "register date": "注册日期", "expiry date": "到期日期", "is foreigner": "是外国人", "Extend Expiry Date": "延长到期日期", "have siblings": "有兄弟姐妹", "Siblings (As Students)": "兄弟姐妹(作为学生)", "Select a guardian type and add guardians": "选择监护人类型并添加监护人", "exams": "考试", "exam": "考试", "exam slip number": "考试单号", "total average marks": "总平均分", "Subjects Marks": "科目分数", "Import New Students": "导入新学生", "Select Enrollment Session": "选择注册时段", "foreigner": "外国人", "hostel": "宿舍", "total average": "总平均分", "conduct": "操行", "Error Message": "错误信息", "Warning Message": "警告信息", "guardian type": "监护人类型", "Student Name (English)": "学生名字 (英语)", "Student Name (Malay)": "学生名字 (马来语)", "Student Name (Chinese)": "学生名字 (中文)", "Bulk Update New Students": "批量更新新学生", "Select Newly Registered Student": "选择新注册学生", "bed assignment": "床位分配", "assign beds": "分配床位", "bulk bed assignment": "批量分配床位", "unassign beds": "取消分配床位", "change beds": "更改床位", "Selected ": "已选", "Bed": "床位", "Select a ": "请选择一个", "and the bed to be assigned": "并选择要分配的床位", "assigned to": "分配给", "Assignment": "分配", "Assigned Bed": "已分配床位", "Unassigned successfully": "取消分配成功", "Unassignment": "取消分配", "to be unassigned": "要取消分配的", "Selected bed is the same as the selected student's bed": "所选床位与所选学生的床位相同", "Selected bed is occupied": "所选床位已被占用", "Selected bed has already added": "所选床位已添加", "Please make sure all required selection are made": "请确保已做出所有必填选择", "Selected student has already added": "所选学生已添加", "selected_already_added": "该{item}已被添加。", "one_or_more_selected_already_added": "所选{item}中已有重复项。", "Select the Bed To Change": "选择要更改的床位", "Invalid Date Format In Excel File": "Excel文件中的日期格式不正确", "block code": "楼栋代码", "bed assign to": "床位分配给", "Start Date (Y-m-d)": "开始日期 (年-月-日)", "Remarks (optional)": "备注 (选填)", "Student number does not exist in the database": "学生学号不存在于数据库中", "hostel employees": "住宿职员", "create disciplinary record": "奖惩输入", "disciplinary records": "奖惩记录", "hostel reward punishment": "宿舍奖惩", "checkout date time": "退宿时间", "checkin date time": "返宿时间", "HOME": "回家", "OUTING": "外出", "arrival records": "返宿记录", "Edit Remarks": "编辑备注", "checked out by": "办理退宿人", "checked in by": "办理返宿人", "departure date time": "离宿时间", "arrival date time": "返宿时间", "update record": "更新记录", "create departure record": "离宿记录", "arrival/departure records": "返宿/离宿记录", "departure records": "离宿记录", "departed/arrived": "离宿/返宿", "BOTH": "全部", "DEPARTED": "离宿", "ARRIVED": "返宿", "Add Departure": "添加离宿记录", "Arrival marked successfully": "返宿登记成功", "Submit Bulk Mark Arrival": "提交批量返宿", "Bulk Mark Arrival": "批量返宿", "Hostel Saving Account": "宿舍储蓄户口", "Transaction History": "交易记录", "Student information": "学生资料", "available balance": "可用余额", "Deposit": "存款", "Withdrawal": "支出", "Void Transaction": "作废交易", "document number": "文件编号", "transaction date": "交易日期", "Document Type": "文件类型", "Transaction Date": "交易日期", "Amount": "金额", "Bill To": "账单名称", "Bill To Address": "账单地址", "BANK": "银行", "CASH": "现金", "Are you sure you want to void this transaction?": "您确定要作废此交易吗？", "Voided successfully": "作废成功", "period from": "起始日期", "period to": "结束日期", "badge no": "胸卡编号", "member number": "会员编号", "student class": "学生班级", "unreturned book": "未归还书籍", "valid from": "有效期从", "valid to": "有效期至", "borrow limit": "借阅限制", "is_librarian": "是图书管理员", "is active": "是活跃的", "members": "会员", "member": "会员", "Create Other ": "创建其他", "book": "书籍", "books": "书籍", "title": "标题", "authors": "作者", "due date": "到期日期", "book category": "书籍分类", "book categories": "书籍分类", "book classification": "书籍类别", "book classifications": "书籍类别", "book subclassification": "书籍子类别", "book subclassifications": "书籍子类别", "subclassification": "子类别", "book source": "书籍来源", "book sources": "书籍来源", "language": "语言", "location": "位置", "publisher": "出版社", "publisher place": "出版社地点", "published date": "出版日期", "condition": "状态", "entry date": "输入日期", "binding": "装订", "book size": "书籍尺寸", "book page": "书籍页数", "words": "字数", "purchase value": "购买价值", "lost penalty value": "丢失罚款价值", "CD Rom": "光盘", "Loan Settings": "借阅设置", "loan period days": "借阅天数", "can borrow": "可以借阅", "Retrieve Information": "检索资料", "GOOD": "良好", "DAMAGE": "损坏", "MISSING": "丢失", "WRITE OFF": "注销", "HARD COVER": "精装书", "SOFT COVER": "平装书", "Recover Lost Book": "找回丢失的书籍", "Borrow/Return Books": "借阅/归还书籍", "Member/Card Number": "会员/卡号", "member type": "会员类型", "Borrow": "借阅", "borrowed books": "已借书籍", "total overdue amount": "总逾期金额", "book title": "书籍标题", "Unreturned Books": "未归还书籍", "Borrowed Books": "已借书籍", "borrow date": "借阅日期", "due aging": "逾期天数", "overdue amount": "逾期金额", "lost amount": "丢失金额", "BORROWED": "已借", "Extend": "延长", "Extend Borrow Period": "延长借阅期限", "Return Books": "归还书籍", "Return": "归还", " & Pay Penalty": "并支付罚款", "fine amount": "罚款金额", "fine payment status": "罚款付款状态", "borrow status": "借阅状态", "Total Payable Amount": "应付总额", "is lost": "是已丢失的", "penalty lost amount": "罚款丢失金额", "return date": "归还日期", "extended date": "延长日期", "MAJOR": "专业", "PRIMARY": "正课", "ENGLISH": "英语", "SOCIETY": "学会", "Society": "学会", "society": "学会", "ELECTIVE": "选修", "Elective": "选修", "Major": "专业", "COCURRICULUM": "联课活动", "Co-curriculum": "联课活动", "Society/Club": "学会/俱乐部", "Society/Club Category": "学会/俱乐部类别", "club": "俱乐部", "STARTER": "初级", "ELEMENTARY": "初等", "PRE-INTERMEDIATE": "预中级", "INTERMEDIATE": "中级", "UPPER INTERMEDIATE": "中高级", "ADVANCED 1": "高级 1", "ADVANCED 2": "高级 2", "ADVANCED 3": "高级 3", "english level": "英语水平", "Assign Class to Semester": "分配班级到学期", "Assign Class": "分配班级", "semester classes": "学期班级", "Semester Classes": "学期班级", "Semester Class Settings": "学期班级设置", "teacher guardian": "教师监护人", "Teacher Guardian": "教师监护人", "Settings": "设置", "Teacher guardian assigned successfully": "教师监护人分配成功", "Students assigned successfully": "学生分配成功", "Subjects assigned successfully": "科目分配成功", "in total": "总计", "Total of": "总计", "class enter date": "班级进入日期", "Please make sure all class enter dates are filled": "请确保所有班级进入日期已填写", "Class Subjects": "班级科目", "subject": "科目", "number of period per week": "每周课时数", "Class Subject Settings": "班级科目设置", "Set a primary teacher by ticking the checkbox": "通过勾选复选框设置正课教师", "Comprehensive Assessment Result Entry": "综合素质评量结果输入", "Students Assessment Results": "学生评估结果", "ACHIEVED": "已达成", "NEED IMPROVEMENT": "需要改进", "Seat Settings": "座位设置", "By Semester": "按学期", "By Class": "按班级", "ENGLISH NAME": "英语名字", "STUDENT NUMBER": "学生学号", "seat no": "座位号", "Fill In Empty Seats": "填写空座位", "Substitute Management": "替代管理", "Substitute Teacher": "替代教师", "Bulk Assign Substitute Teachers": "批量分配替代教师", "substitute date": "替代日期", "allowance": "津贴", "Requestor": "申请人", "Substitute Date Range": "替代日期范围", "exam start date": "考试开始日期", "exam end date": "考试结束日期", "Exam Date Range": "考试日期范围", "Result Key in Date (From)": "结果输入日期 (从)", "Result Key in Date (To)": "结果输入日期 (至)", "Exam Grading Scheme Settings": "考试评分设定", "Exam Grading Framework": "考试评分框架", "Exam Grading Frameworks": "考试评分框架", "Exam Subject Exemption": "考试科目豁免", "Exam Grading Framework Assignment": "考试评分框架分配", "Exam Mark Entry": "考试分数输入", "Exam Posting": "考试发布", "Exam Posting Pre-Checks": "考试发布预检查", "Exam Posting Status": "考试发布状态", "Exam Promotion Mark": "考试晋升分数", "Compulsory Output Component": "强制输出组件", "Result Source": "结果来源", "Output Settings": "输出设置", "Validate & Save": "验证并保存", "Do you want to replace the existing student grading framework?": "您是否要替换现有的学生评分框架？", "number of subjects": "科目数量", "number of components": "组件数量", "is final": "是最终的", "Subjects need to be added after result source is created": "创建结果来源后需要添加科目", "Components need to be added after result source subject is created": "创建结果来源科目后需要添加组件", "Components need to be added after output setting is created": "创建输出设置后需要添加组件", "Total formula need to be added after output component is created": "创建输出组件后需要添加总公式", "Label formula need to be added after output component is created": "创建输出组件后需要添加标签公式", "scoring type": "评分类型", "Result Source Subjects": "结果来源科目", "SCORE": "分数", "GRADE": "等级", "weightage": "权重", "weightage percentage": "权重百分比", "is exempted": "是豁免的", "Exempted": "豁免", "Component": "组件", "Components": "组件", "Output": "输出", "Result Source Subject Components": "结果来源科目组件", "Result Source Output Component": "结果来源输出组件", "Result Source Output Setting": "结果来源输出设置", "grading scheme": "评分方案", "service": "服务", "Please get this value from system provider": "请从系统提供商处获取此值", "Report Name": "报告名称", "grading scheme code": "评分方案代码", "weightage multiplier": "权重乘数", "priority": "优先级", "output type": "输出类型", "calculate rank": "计算排名", "Total Formula": "总公式", "Label Formula": "标签公式", "Edit Formula": "编辑公式", "Current Formula": "当前公式", "New Formula": "新公式", "Fixed Formulas": "固定公式", "Result Source Formulas": "结果来源公式", "Output Formulas": "输出公式", "From Date should be earlier than To Date": "开始日期应早于结束日期", "Student Grading Framework Assignment": "学生评分框架分配", "By Classes": "按班级", "By Students": "按学生", "academic year": "学年", "Date Range": "日期范围", "View Students": "查看学生", "students found": "找到的学生", "primary class": "正课班级", "grading type": "评分类型", "Exam Score": "考试分数", "Reopen Mark Entry": "重新打开分数输入", "Reopen successfully": "重新打开成功", "Are you sure you want to reopen mark entry for this student?": "您确定要重新打开此学生的分数输入吗?", "Loading": "加载中", "score": "分数", "publish date": "发布日期", "Post Result": "发布结果", "Error Logs": "错误日志", "batch no": "批次号", "report card output code": "成绩单输出代码", "posted at": "已发布于", "processing start": "处理开始于", "processing end": "处理结束于", "net average": "净平均分", "Export Excel": "导出Excel", "Download PDF": "下载PDF", "Post to Autocount": "输出Autocount", "Payment Date Range": "付款日期范围", "INVOICE": "发票", "ADVANCE INVOICE": "预付款发票", "CREDIT NOTE": "贷项通知单", "DEBIT NOTE": "借项通知单", "FEES": "费用", "WALLET": "钱包", "ECOMMERCE": "电子商务", "HOSTEL SAVINGS ACCOUNT": "住宿储蓄户口", "ENROLLMENT FEES": "注册费用", "POSTED": "已发布", "Type to search book title": "输入以搜索书籍标题", "View Report": "查看报告", "report type": "报告类型", "Student Contacts": "学生联络资料", "Records": "记录", "Grading Scheme List": "评分方案列表", "display as": "显示为", "extra marks": "额外分数"}}