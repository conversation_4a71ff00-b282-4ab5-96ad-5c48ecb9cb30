/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ["class"],
    content: [
        "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    ],
    prefix: "",
    theme: {
        container: {
            center: true,
            padding: "2rem",
            screens: {
                "2xl": "1400px",
            },
        },
        extend: {
            fontFamily: {
                pjs: '"Plus Jakarta Sans", sans-serif',
                noto: '"Noto Sans SC", sans-serif',
            },
            colors: {
                themeGray: "#9EA3AE",
                themeGray2: "#ebebeb",
                themeGray3: "#8F90A6",
                themeGray4: "#F2F2F5",
                themeGray5: "#C7C9D9",

                themeBlack: "#130F26",

                themeGreenDark: "#1F4838",
                themeGreen: "#44B656",
                themeGreen2: "#2F803D",
                themeGreen3: "#F2FCF3",
                themeGreen4: "#C7F2CD",
                themeGreen5: "#2B624C",

                themeBlue: "#4AA7FC",
                themeBlueLight: "#E8F3FD",
                themeOrange: "#FF7352",
                themeOrangeLight: "#FFF2EF",
                themeYellow: "#FFCB13",
                themeYellowLight: "#FFF8DF",
                themeLabel: "#919191",

                border: "hsl(var(--border))",
                input: "#cccccc",
                ring: "hsl(var(--ring))",
                background: "hsl(var(--background))",
                foreground: "hsl(var(--foreground))",
                primary: {
                    DEFAULT: "hsl(var(--primary))",
                    foreground: "hsl(var(--primary-foreground))",
                },
                secondary: {
                    DEFAULT: "hsl(var(--secondary))",
                    foreground: "hsl(var(--secondary-foreground))",
                },
                destructive: {
                    DEFAULT: "hsl(var(--destructive))",
                    foreground: "hsl(var(--destructive-foreground))",
                },
                muted: {
                    DEFAULT: "hsl(var(--muted))",
                    foreground: "hsl(var(--muted-foreground))",
                },
                accent: {
                    DEFAULT: "hsl(var(--accent))",
                    foreground: "hsl(var(--accent-foreground))",
                },
                popover: {
                    DEFAULT: "hsl(var(--popover))",
                    foreground: "hsl(var(--popover-foreground))",
                },
                card: {
                    DEFAULT: "hsl(var(--card))",
                    foreground: "hsl(var(--card-foreground))",
                },
            },
            borderRadius: {
                lg: "var(--radius)",
                md: "calc(var(--radius) - 2px)",
                sm: "calc(var(--radius) - 4px)",
            },
            keyframes: {
                "accordion-down": {
                    from: { height: "0" },
                    to: { height: "var(--radix-accordion-content-height)" },
                },
                "accordion-up": {
                    from: { height: "var(--radix-accordion-content-height)" },
                    to: { height: "0" },
                },
            },
            animation: {
                "accordion-down": "accordion-down 0.2s ease-out",
                "accordion-up": "accordion-up 0.2s ease-out",
            },
        },
    },
    plugins: [require("tailwindcss-animate")],
};
