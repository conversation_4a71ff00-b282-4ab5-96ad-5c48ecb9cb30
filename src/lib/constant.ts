import { Calculator, Calendar, MessageSquareText } from "lucide-react";
import { toYMD } from "./utils";

const IconStudents = "/icons/nav/icon-students.svg";
const IconAttendance = "/icons/nav/icon-attendance.svg";
const IconPayments = "/icons/nav/icon-payments.svg";
const IconMasterData = "/icons/nav/icon-master-data.svg";
const IconCards = "/icons/nav/icon-cards.svg";
const IconEmployee = "/icons/nav/icon-employee.svg";
const IconClass = "/icons/nav/icon-class.svg";
const IconHostel = "/icons/nav/icon-hostel.svg";
const IconLibrary = "/icons/nav/icon-library.svg";
const IconSubjects = "/icons/nav/icon-subjects.svg";
const IconExam = "/icons/nav/icon-exam.svg";
const IconTimetable = "/icons/nav/icon-timetable.svg";
const IconAcademic = "/icons/nav/icon-academic.svg";
const IconClub = "/icons/nav/icon-club.svg";
const IconStudentAffair = "/icons/nav/icon-student-affair.svg";
const IconCounselling = "/icons/nav/icon-counselling.svg";
const IconShopping = "/icons/nav/icon-shopping.svg";
const IconConfiguration = "/icons/nav/icon-configuration.svg";

export const nav = <Record<string, any>>{
    master_data: {
        title: "master data",
        icon: IconMasterData,
        permissions: ["master-data-menu-view"],
        items: {
            banks: { title: "banks", permissions: ["master-bank-view"] },
            configs: {
                title: "configs",
                permissions: ["master-config-update"],
            },
            countries: {
                title: "countries",
                permissions: ["master-country-view"],
            },
            courses: { title: "courses", permissions: ["master-course-view"] },
            currencies: {
                title: "currencies",
                permissions: ["master-currency-view"],
            },
            departments: {
                title: "departments",
                permissions: ["department-view"],
            },
            health_concerns: {
                title: "health concerns",
                permissions: ["master-health-concern-view"],
            },
            educations: {
                title: "educations",
                permissions: ["master-education-view"],
            },
            employee_job_titles: {
                title: "employee job titles",
                permissions: ["master-employee-job-title-view"],
            },
            employee_sessions: {
                title: "employee sessions",
                permissions: ["master-employee-session-view"],
            },
            gl_account: {
                title: "gl account",
                permissions: ["master-gl-account-view"],
            },
            grades: { title: "grades", permissions: ["master-grade-view"] },
            internationalization: {
                title: "internationalization",
                permissions: ["master-internationalization-view"],
            },
            leave_reasons: {
                title: "leave reasons",
                permissions: ["master-leave-reason-view"],
            },
            races: { title: "races", permissions: ["master-race-view"] },
            religions: {
                title: "religions",
                permissions: ["master-religion-view"],
            },
            schools: {
                title: "schools",
                permissions: ["master-school-view"],
            },
            school_profile: {
                title: "school profile",
                permissions: ["master-school-profile-view"],
            },
            semester_settings: {
                title: "semester settings",
                permissions: ["master-semester-setting-view"],
            },
            states: { title: "states", permissions: ["master-state-view"] },
            uom: { title: "uom", permissions: ["master-uom-view"] },
            withdrawal_reasons: {
                title: "withdrawal reasons",
                permissions: ["master-withdrawal-reason-view"],
            },
            payment_methods: {
                title: "payment methods",
                permissions: ["master-payment-method-view"],
            },
        },
    },
    configuration: {
        title: "configuration",
        icon: IconConfiguration,
        hideForMerchantRole: true,
        items: {
            terminals: {
                title: "terminals",
                permissions: ["terminal-view"],
            },
            terminal_keys: {
                title: "terminal keys",
                permissions: ["pos-terminal-key-view"],
            },
            roles: { title: "roles", permissions: ["role-view"] },
            users: { title: "users", permissions: ["user-update"] },
            copy_semester_settings: {
                title: "copy semester settings",
                permissions: ["master-semester-setting-copy"],
            },
        },
    },
    wallet_management: {
        title: "wallet management",
        icon: IconPayments,
        items: {
            wallets: {
                title: "wallets",
                permissions: ["wallet-admin-view"],
            },
            wallet_transactions: {
                title: "wallet transactions",
                permissions: ["wallet-admin-view"],
            },
        },
    },
    enrollment: {
        title: "enrollment",
        icon: IconAttendance,
        items: {
            enrollment_sessions: {
                title: "enrollment sessions",
                permissions: ["enrollment-session-view"],
            },
            enrollment_users: {
                title: "enrollment users",
                permissions: ["enrollment-user-view"],
            },
            newly_registered_students: {
                title: "newly registered students",
                permissions: ["enrollment-view"],
            },
            import_new_students: {
                title: "import new students",
                permissions: ["enrollment-create"],
            },
            bulk_update_new_students: {
                title: "bulk update new students",
                permissions: ["enrollment-create"],
            },
            payment: {
                title: "payment",
                permissions: ["enrollment-make-payment"],
            },
        },
    },
    student_management: {
        title: "student management",
        icon: IconStudents,
        hideForMerchantRole: true,
        items: {
            students: { title: "students", permissions: ["student-view"] },
        },
    },
    employee_management: {
        title: "employee management",
        icon: IconEmployee,
        items: {
            employees: { title: "employees", permissions: ["employee-view"] },
        },
    },
    information_center: {
        title: "information center",
        icon: IconCards,
        items: {
            smart_cards: { title: "smart cards", permissions: ["card-view"] },
            import_export_smart_cards: {
                title: "import/export smart cards",
                permissions: ["card-import", "card-report"],
            },
        },
    },
    hostels: {
        title: "hostels",
        icon: IconHostel,
        hideForMerchantRole: true,
        items: {
            student_hostel_configuration: {
                title: "student hostel configuration",
                permissions: [
                    "hostel-block-view",
                    "hostel-room-view",
                    "hostel-room-bed-view",
                    "hostel-merit-demerit-setting-view",
                    "hostel-reward-punishment-setting-view",
                ],
                items: {
                    blocks: {
                        title: "blocks",
                        permissions: ["hostel-student", "hostel-block-view"],
                        containAllPermissions: true,
                    },
                    rooms: {
                        title: "rooms",
                        permissions: ["hostel-student", "hostel-room-view"],
                        containAllPermissions: true,
                    },
                    beds: {
                        title: "beds",
                        permissions: ["hostel-student", "hostel-room-bed-view"],
                        containAllPermissions: true,
                    },
                    merit_demerit_settings: {
                        title: "merit/demerit settings",
                        permissions: ["hostel-merit-demerit-setting-view"],
                    },
                    reward_punishment_settings: {
                        title: "reward/punishment settings",
                        permissions: ["hostel-reward-punishment-setting-view"],
                    },
                },
            },
            employee_hostel_configuration: {
                title: "employee hostel configuration",
                permissions: [
                    "hostel-block-view",
                    "hostel-room-view",
                    "hostel-room-bed-view",
                ],
                items: {
                    blocks: {
                        title: "blocks",
                        permissions: ["hostel-employee", "hostel-block-view"],
                        containAllPermissions: true,
                    },
                    rooms: {
                        title: "rooms",
                        permissions: ["hostel-employee", "hostel-room-view"],
                        containAllPermissions: true,
                    },
                    beds: {
                        title: "beds",
                        permissions: [
                            "hostel-employee",
                            "hostel-room-bed-view",
                        ],
                        containAllPermissions: true,
                    },
                },
            },
            student_management: {
                title: "student management",
                permissions: ["hostel-student", "hostel-bed-assignment-assign"],
                items: {
                    hostel_students: {
                        title: "hostel students",
                        permissions: ["hostel-student"],
                    },
                    bed_assignment: {
                        title: "bed assignment",
                        permissions: [
                            "hostel-student",
                            "hostel-bed-assignment-assign",
                        ],
                        containAllPermissions: true,
                    },
                },
            },
            employee_management: {
                title: "employee management",
                permissions: [
                    "hostel-employee",
                    "hostel-bed-assignment-assign",
                ],
                items: {
                    hostel_employees: {
                        title: "hostel employees",
                        permissions: ["hostel-employee"],
                    },
                    bed_assignment: {
                        title: "bed assignment",
                        permissions: [
                            "hostel-employee",
                            "hostel-bed-assignment-assign",
                        ],
                        containAllPermissions: true,
                    },
                },
            },
            disciplinary_actions: {
                title: "disciplinary actions",
                items: {
                    create_disciplinary_record: {
                        title: "create disciplinary record",
                        permissions: ["hostel-reward-punishment-record-create"],
                    },
                    disciplinary_records: {
                        title: "disciplinary records",
                        permissions: ["hostel-reward-punishment-record-view"],
                    },
                },
            },
            arrival_and_departure: {
                title: "arrival & departure",
                items: {
                    create_departure_record: {
                        title: "create departure record",
                        permissions: ["hostel-in-out-record-view"],
                    },
                    arrival_departure: {
                        title: "arrival/departure",
                        permissions: ["hostel-in-out-record-view"],
                    },
                    bulk_mark_arrival: {
                        title: "bulk mark arrival",
                        permissions: ["hostel-in-out-record-view"],
                    },
                },
            },
            saving_account: {
                title: "saving account",
                permissions: ["hostel-savings-account-view"],
            },
            person_in_charge: {
                title: "person in charge",
                permissions: ["hostel-person-in-charge-admin-view"],
            },
        },
    },
    library: {
        title: "library",
        icon: IconLibrary,
        items: {
            member_management: {
                title: "member management",
                items: {
                    members: {
                        title: "members",
                        permissions: ["library-member-view"],
                    },
                },
            },
            book_management: {
                title: "book management",
                items: {
                    books: {
                        title: "books",
                        permissions: ["book-view"],
                    },
                    book_categories: {
                        title: "book categories",
                        permissions: ["master-book-category-view"],
                    },
                    book_sources: {
                        title: "book sources",
                        permissions: ["master-book-source-view"],
                    },
                    book_classifications: {
                        title: "book classifications",
                        permissions: ["master-book-classification-view"],
                    },
                    book_sub_classifications: {
                        title: "book subclassifications",
                        permissions: ["master-book-sub-classification-view"],
                    },
                },
            },
            borrow_return_books: {
                title: "borrow/return books",
                permissions: ["book-loan-view"],
            },
        },
    },
    // academic: {
    //     title: "academic",icon: IconAcademic,
    //     items: {
    //         school_holiday_settings: {title:"school holiday settings",permissions:[]},
    //     },
    // },
    class_subject_management: {
        title: "class/subject management",
        icon: IconSubjects,
        hideForMerchantRole: true,
        items: {
            class_management: {
                title: "class management",
                items: {
                    primary: {
                        title: "primary",
                        permissions: [
                            "primary-class-view",
                            "primary-semester-class-view",
                            "primary-class-subject-view",
                            "class-seat-assignment-view",
                        ],
                        items: {
                            classes: {
                                title: "classes",
                                permissions: ["primary-class-view"],
                            },
                            semester_classes: {
                                title: "semester classes",
                                permissions: [
                                    "semester-class-view",
                                    "primary-semester-class-view",
                                ],
                                containAllPermissions: true,
                            },
                            class_subjects: {
                                title: "class subjects",
                                permissions: ["primary-class-subject-view"],
                            },
                            seat_settings: {
                                title: "seat settings",
                                permissions: ["class-seat-assignment-view"],
                            },
                        },
                    },
                    english: {
                        title: "english",
                        permissions: [
                            "english-class-view",
                            "english-semester-class-view",
                            "english-class-subject-view",
                        ],
                        items: {
                            classes: {
                                title: "classes",
                                permissions: ["english-class-view"],
                            },
                            semester_classes: {
                                title: "semester classes",
                                permissions: [
                                    "semester-class-view",
                                    "english-semester-class-view",
                                ],
                                containAllPermissions: true,
                            },
                            class_subjects: {
                                title: "class subjects",
                                permissions: ["english-class-subject-view"],
                            },
                        },
                    },
                    society: {
                        title: "society",
                        permissions: [
                            "society-class-view",
                            "society-semester-class-view",
                            "society-class-subject-view",
                        ],
                        items: {
                            classes: {
                                title: "classes",
                                permissions: ["society-class-view"],
                            },
                            semester_classes: {
                                title: "semester classes",
                                permissions: [
                                    "semester-class-view",
                                    "society-semester-class-view",
                                ],
                                containAllPermissions: true,
                            },
                            class_subjects: {
                                title: "class subjects",
                                permissions: ["society-class-subject-view"],
                            },
                        },
                    },
                    elective: {
                        title: "elective",
                        permissions: [
                            "elective-class-view",
                            "elective-semester-class-view",
                            "elective-class-subject-view",
                        ],
                        items: {
                            classes: {
                                title: "classes",
                                permissions: ["elective-class-view"],
                            },
                            semester_classes: {
                                title: "semester classes",
                                permissions: [
                                    "semester-class-view",
                                    "elective-semester-class-view",
                                ],
                                containAllPermissions: true,
                            },
                            class_subjects: {
                                title: "class subjects",
                                permissions: ["elective-class-subject-view"],
                            },
                        },
                    },
                },
            },
            subject_management: {
                title: "subject management",
                items: {
                    major: {
                        title: "major",
                        items: {
                            subjects: {
                                title: "subjects",
                                permissions: ["subject-view"],
                            },
                        },
                    },
                    elective: {
                        title: "elective",
                        items: {
                            subjects: {
                                title: "subjects",
                                permissions: ["subject-view"],
                            },
                        },
                    },
                    co_curriculum: {
                        title: "co-curriculum",
                        items: {
                            subjects: {
                                title: "subjects",
                                permissions: ["subject-view"],
                            },
                        },
                    },
                    // subject_categories:{title: "subject categories",permissions:[]},
                },
            },
            substitute_management: {
                title: "substitute management",
                permissions: ["substitute-record-view"],
            },
        },
    },
    exam_management: {
        title: "exam management",
        icon: IconExam,
        items: {
            exams: {
                title: "exams",
                permissions: ["exam-view"],
            },
            // exam_semester_settings: {
            //     title: "Exam Semester Settings",
            //     permissions: ["exam-semester-setting-view"],
            // },
            exam_grading: {
                title: "Exam Grading Scheme Settings",
                permissions: ["grading-scheme-view"],
            },
            exam_grading_framework: {
                title: "Exam Grading Frameworks",
                permissions: ["grading-framework-view"],
            },
            exam_subject_exemption: {
                title: "Exam Subject Exemption",
                permissions: ["exam-exemption-save"],
            },
            exam_grading_framework_assignment: {
                title: "Exam Grading Framework Assignment",
                items: {
                    by_classes: {
                        title: "By Classes",
                        permissions: ["grading-framework-apply"],
                    },
                    by_students: {
                        title: "By Students",
                        permissions: ["grading-framework-apply"],
                    },
                },
            },
            exam_mark_entry: {
                title: "Exam Mark Entry",
                permissions: ["exam-results-data-entry-view"],
            },
            exam_posting: {
                title: "Exam Posting",
                items: {
                    exam_posting_prechecks: {
                        title: "Exam Posting Pre-Checks",
                        permissions: ["results-posting-header-view"],
                    },
                    exam_posting: {
                        title: "Exam Posting",
                        permissions: [
                            "exam-results-data-entry-create-posting-session",
                        ],
                    },
                    exam_posting_status: {
                        title: "Exam Posting Status",
                        permissions: ["results-posting-header-view"],
                    },
                },
            },
            exam_promotion_mark: {
                title: "Exam Promotion Mark",
                permissions: ["promotion-mark-view"],
            },
        },
    },
    timetable: {
        title: "timetable",
        icon: IconTimetable,
        items: {
            period_groups: {
                title: "period groups",
                permissions: ["period-group-view"],
            },
            periods: { title: "periods", permissions: ["period-view"] },
            class_timetables: {
                title: "class timetables",
                permissions: ["timetable-view"],
            },
        },
    },
    co_curricular: {
        title: "co-curricular",
        icon: IconClub,
        items: {
            club_society_management: {
                title: "club/society management",
                items: {
                    club_society_categories: {
                        title: "club/society categories",
                        permissions: ["club-category-view"],
                    },
                    clubs_societies: {
                        title: "clubs/societies",
                        permissions: ["club-view"],
                    },
                    executive_committee_position_settings: {
                        title: "executive committee position settings",
                        permissions: ["master-society-position-view"],
                    },
                    executive_committee_name_list: {
                        title: "executive committee name list",
                        permissions: ["student-society-position-update"],
                    },
                },
            },
            coach_management: {
                title: "coach management",
                permissions: ["contractor-view"],
            },
        },
    },
    student_affairs: {
        title: "student affairs",
        icon: IconStudentAffair,
        items: {
            conduct_management: {
                title: "conduct management",
                items: {
                    conduct_deadline: {
                        title: "conduct deadline",
                        permissions: ["conduct-deadline-view"],
                    },
                    conduct_grading: {
                        title: "conduct grading scheme settings",
                        permissions: ["grading-scheme-view"],
                    },
                    conduct_assign_settings: {
                        title: "conduct assign settings",
                        permissions: ["conduct-setting-view"],
                    },
                    conduct_marks_entry: {
                        title: "conduct marks entry",
                        permissions: ["conduct-record-view"],
                    },
                },
            },
            comprehensive_quality_assessment: {
                title: "comprehensive quality assessment",
                items: {
                    comprehensive_assessment_categories: {
                        title: "comprehensive assessment categories",
                        permissions: ["comprehensive-assessment-category-view"],
                    },
                    comprehensive_assessment_questions: {
                        title: "comprehensive assessment questions",
                        permissions: ["comprehensive-assessment-question-view"],
                    },
                    comprehensive_assessment_result_entry: {
                        title: "comprehensive assessment result entry",
                        permissions: ["comprehensive-assessment-record-view"],
                    },
                },
            },
            class_leadership_list: {
                title: "class leadership list",
                items: {
                    class_leadership_position_settings: {
                        title: "class leadership position settings",
                        permissions: ["master-leadership-position-view"],
                    },
                    class_leaderships: {
                        title: "class leaderships",
                        permissions: ["leadership-position-record-view"],
                    },
                },
            },
            reward_punishment: {
                title: "reward/punishment",
                items: {
                    merit_demerit_settings: {
                        title: "merit/demerit settings",
                        permissions: ["merit-demerit-setting-view"],
                    },
                    reward_punishment_categories: {
                        title: "reward/punishment categories",
                        permissions: ["master-reward-punishment-category-view"],
                    },
                    reward_punishment_sub_categories: {
                        title: "reward/punishment sub categories",
                        permissions: [
                            "master-reward-punishment-sub-category-view",
                        ],
                    },
                    reward_punishment_settings: {
                        title: "reward/punishment settings",
                        permissions: ["reward-punishment-view"],
                    },
                    reward_punishment_records: {
                        title: "reward/punishment records",
                        permissions: ["reward-punishment-record-view"],
                    },
                    create_reward_punishment_record: {
                        title: "create reward/punishment record",
                        permissions: ["reward-punishment-record-create"],
                    },
                },
            },
            special_competition_special_activity: {
                title: "special competition/special activity",
                items: {
                    awards: {
                        title: "awards",
                        permissions: ["master-award-view"],
                    },
                    competition_or_special_activity_settings: {
                        title: "competition/special activity settings",
                        permissions: ["competition-view"],
                    },
                },
            },
        },
    },
    counselling: {
        title: "counselling",
        icon: IconCounselling,
        items: {
            create_case_record: {
                title: "create case record",
                permissions: ["counselling-case-record-create"],
            },
            case_records: {
                title: "case records",
                permissions: ["counselling-case-record-view"],
            },
        },
    },
    ecommerce: {
        title: "ecommerce",
        icon: IconShopping,
        hideForMerchantRole: true,
        items: {
            bookstore: {
                title: "bookstore",
                permissions: ["bookstore-view"],
                items: {
                    merchants: {
                        title: "merchants",
                        permissions: ["merchant-view"],
                    },
                    product_categories: {
                        title: "product categories",
                        permissions: ["product-category-admin-view"],
                    },
                    product_subcategories: {
                        title: "product subcategories",
                        permissions: ["product-category-admin-view"],
                    },
                    product_groups: {
                        title: "product groups",
                        permissions: ["product-group-view"],
                    },
                    product_tags: {
                        title: "product tags",
                        permissions: ["product-tag-view"],
                    },
                    products: {
                        title: "products",
                        permissions: ["product-view"],
                    },
                    assign_products: {
                        title: "assign products",
                        permissions: ["product-available-date-bulk-update"],
                    },
                    orders: {
                        title: "orders",
                        permissions: ["order-admin-view"],
                    },
                },
            },
            canteen: {
                title: "canteen",
                permissions: ["canteen-view"],
                items: {
                    merchants: {
                        title: "merchants",
                        permissions: ["merchant-view"],
                    },
                    product_categories: {
                        title: "product categories",
                        permissions: ["product-category-admin-view"],
                    },
                    product_subcategories: {
                        title: "product subcategories",
                        permissions: ["product-category-admin-view"],
                    },
                    product_groups: {
                        title: "product groups",
                        permissions: ["product-group-view"],
                    },
                    product_tags: {
                        title: "product tags",
                        permissions: ["product-tag-view"],
                    },
                    products: {
                        title: "products",
                        permissions: ["product-view"],
                    },
                    assign_products: {
                        title: "assign products",
                        permissions: ["product-delivery-date-bulk-update"],
                    },
                    orders: {
                        title: "orders",
                        permissions: ["order-admin-view"],
                    },
                },
            },
        },
    },
    reports: {
        title: "reports",
        icon: IconExam,
        items: {
            academic: {
                title: "academic",
                items: {
                    student_analysis_by_semester: {
                        title: "student analysis by semester",
                        permissions: ["academy-student-analysis-report"],
                    },
                    transferred_student_list: {
                        title: "transferred student list",
                        permissions: ["academy-transferred-student-list"],
                    },
                    student_report_card: {
                        title: "student report card",
                        permissions: ["student-report-card-view"],
                    },
                    student_report_card_by_group: {
                        title: "student report card (by group)",
                        permissions: ["report-card-by-morph-view"],
                    },
                    examination_result_by_class: {
                        title: "examination result by class",
                        permissions: [
                            "academy-examination-result-by-semester-class-report",
                        ],
                    },
                    examination_result_by_exam: {
                        title: "examination result by exam",
                        permissions: ["examination-result-by-exam-report"],
                    },
                    examination_result_by_student: {
                        title: "examination result by student",
                        permissions: ["examination-result-by-student-report"],
                    },
                    net_average_passing_rate: {
                        title: "net average passing rate",
                        permissions: ["net-average-passing-rate-report"],
                    },
                    subject_passing_rate: {
                        title: "subject passing rate",
                        permissions: ["subject-passing-rate-report"],
                    },
                    subject_passing_rate_average_mark: {
                        title: "subject passing rate/ average mark",
                        permissions: ["subject-average-mark-report"],
                    },
                    subject_score_analysis: {
                        title: "subject score analysis",
                        permissions: ["subject-analysis-data-report"],
                    },
                    student_merit_exceptional_performance: {
                        title: "student merit and exceptional performance",
                        permissions: ["academy-student-merit-exceptional-performance-report"],
                    },
                },
            },
            accounting: {
                title: "accounting",
                items: {
                    billing_documents_by_daily_collection: {
                        title: "billing documents - by daily collection",
                        permissions: [
                            "billing-document-by-daily-collection-report",
                        ],
                    },
                    student_outstanding_balance: {
                        title: "student outstanding balance",
                        permissions: [
                            "accounting-student-outstanding-balance-report",
                        ],
                    },
                    post_to_autocount: {
                        title: "post to autocount",
                        permissions: ["post-billing-documents-to-autocount"],
                    },
                },
            },
            attendance: {
                title: "Attendance",
                items: {
                    by_attendance_summary: {
                        title: "by attendance summary",
                        permissions: ["attendance-by-summary-list-report"],
                    },
                    class_attendance: {
                        title: "class attendance",
                        permissions: ["class-attendance-report"],
                    },
                    teacher_attendance: {
                        title: "teacher attendance",
                        permissions: ["class-attendance-taking-status-report"],
                    },
                    student_attendance: {
                        title: "student attendance",
                        permissions: ["student-attendance-report"],
                    },
                    student_absent: {
                        title: "student absent",
                        permissions: ["student-absent-report"],
                    },
                    student_mark_deduction: {
                        title: "student attendance mark deduction",
                        permissions: [
                            "attendance-by-student-attendance-mark-deduction-report",
                        ],
                    },
                    coach_daily_attendance: {
                        title: "coach daily attendance",
                        permissions: ["contractor-daily-attendance-report"],
                    },
                },
            },
            bookstore: {
                title: "bookstore",
                items: {
                    by_class: {
                        title: "by class",
                        permissions: ["ecommerce-bookshops-classes-report"],
                    },
                    by_students: {
                        title: "by students",
                        permissions: ["ecommerce-bookshops-students-report"],
                    },
                    by_order: {
                        title: "by order",
                        permissions: ["ecommerce-bookshops-orders-report"],
                    },
                    by_order_item: {
                        title: "by order item",
                        permissions: ["ecommerce-order-items-bookshops-report"],
                    },
                },
            },
            canteen: {
                title: "canteen",
                items: {
                    by_classes: {
                        title: "by classes",
                        permissions: ["ecommerce-canteens-classes-report"],
                    },
                    by_weekly_classes: {
                        title: "by classes (weekly)",
                        permissions: [
                            "ecommerce-canteens-classes-weekly-report",
                        ],
                    },
                    by_classes_within_date_range: {
                        title: "by classes (date range)",
                        permissions: [
                            "ecommerce-canteens-classes-date-range-report",
                        ],
                    },
                    by_student: {
                        title: "by student",
                        permissions: ["ecommerce-canteens-by-student-report"],
                    },
                    by_merchant: {
                        title: "by merchant",
                        permissions: ["ecommerce-canteens-merchants-report"],
                    },
                    by_daily_collection: {
                        title: "by daily collection",
                        permissions: [
                            "ecommerce-canteens-by-daily-collection-report",
                        ],
                    },
                    by_merchant_daily_sales: {
                        title: "by daily sales (merchant)",
                        permissions: [
                            "ecommerce-canteens-by-merchant-daily-sales-report",
                        ],
                    },
                    by_all_merchants_daily_sales: {
                        title: "by daily sales (all merchants)",
                        permissions: [
                            "ecommerce-canteens-by-daily-sales-group-by-merchant-report",
                        ],
                    },
                },
            },
            co_curricular: {
                title: "Co-Curricular",
                items: {
                    student_statistic: {
                        title: "student statistic",
                        permissions: [
                            "cocurriculum-student-statistic-by-semester-report",
                        ],
                    },
                },
            },
            hostel: {
                title: "hostel",
                items: {
                    reward_punishment: {
                        title: "reward/punishment",
                        items: {
                            by_block: {
                                title: "by block",
                                permissions: [
                                    "hostel-report-reward-punishment-by-block",
                                ],
                            },
                            by_room: {
                                title: "by room",
                                permissions: [
                                    "hostel-report-reward-punishment-by-room",
                                ],
                            },
                            by_student: {
                                title: "by student",
                                permissions: [
                                    "hostel-report-reward-punishment-by-student",
                                ],
                            },
                        },
                    },
                    by_boarders_name_list: {
                        title: "by boarders name list",
                        permissions: ["hostel-by-boarders-name-list-report"],
                    },
                    by_available_bed: {
                        title: "by available bed",
                        permissions: ["hostel-by-available-bed-report"],
                    },
                    by_checkout_record: {
                        title: "by checkout record",
                        permissions: ["hostel-by-checkout-record-report"],
                    },
                    by_change_room_record: {
                        title: "by change room record",
                        permissions: ["hostel-by-change-room-record-report"],
                    },
                    by_boarders_list_information: {
                        title: "by boarders list information",
                        permissions: ["hostel-by-boarders-list-info-report"],
                    },
                    by_boarders_contact_number: {
                        title: "by boarders contact number",
                        permissions: ["hostel-by-boarders-contact-info-report"],
                    },
                    by_boarders_date_of_birth: {
                        title: "by boarders date of birth",
                        permissions: [
                            "hostel-by-boarders-date-of-birth-report",
                        ],
                    },
                    by_arrival_departure_record: {
                        title: "by arrival/departure record",
                        permissions: [
                            "hostel-by-boarders-go-home-or-out-report",
                        ],
                    },
                    by_stay_back_student: {
                        title: "by stay back student",
                        permissions: ["hostel-by-boarders-stayback-report"],
                    },
                    by_employee_lodging: {
                        title: "by employee lodging",
                        permissions: ["hostel-by-employee-lodging-report"],
                    },
                },
            },
            library: {
                title: "library",
                items: {
                    by_borrowed_report: {
                        title: "by borrowed report",
                        permissions: ["library-book-borrow-records-report"],
                    },
                    by_top_borrower: {
                        title: "by top borrower",
                        permissions: ["library-top-borrowers-report"],
                    },
                    by_top_ten_borrowed_books: {
                        title: "by top ten borrowed books",
                        permissions: ["library-top-borrowed-books-report"],
                    },
                    by_school_rate_of_borrow: {
                        title: "by school rate of borrow",
                        permissions: [
                            "library-school-rate-borrow-books-report",
                        ],
                    },
                    by_book_loan_individual: {
                        title: "by book loan",
                        permissions: ["library-book-loans-report"],
                    },
                },
            },
            semester_class: {
                title: "semester class",
                items: {
                    student_contacts: {
                        title: "student contacts",
                        permissions: ["semester-class-student-contacts-report"],
                    },
                    student_details: {
                        title: "student details",
                        permissions: ["semester-class-student-details-report"],
                    },
                    teacher_guardian: {
                        title: "teacher guardians",
                        permissions: [
                            "semester-class-homeroom-teachers-report",
                        ],
                    },
                    by_students_in_primary_class: {
                        title: "by students in primary class",
                        permissions: [
                            "semester-class-by-students-in-class-report",
                        ],
                    },
                    by_students_in_non_primary_classes: {
                        title: "by students in non primary classes",
                        permissions: [
                            "non-primary-semester-class-by-students-in-class-report",
                        ],
                    },
                },
            },
            student_affair: {
                title: "Student Affair",
                items: {
                    student_conduct: {
                        title: "student conduct",
                        permissions: ["student-conduct-report"],
                    },
                },
            },
            pos_terminal: {
                title: "POS Terminal",
                items: {
                    by_daily_sales: {
                        title: "by daily sales",
                        permissions: ["wallet-report"],
                    },
                },
            },
            enrollment: {
                title: "enrollment",
                items: {
                    newly_registered_students: {
                        title: "newly registered students",
                        permissions: ["student-registration-report"],
                    },
                    registration_fee_daily_collection: {
                        title: "registration fee daily collection",
                        permissions: ["enrollment-by-daily-collection-report"],
                    },
                    enrollment_autocount: {
                        title: "post to autocount",
                        permissions: ["enrollment-post-to-autocount"],
                    },
                },
            },
        },
    },
    announcement: {
        title: "announcement",
        lucideIcon: MessageSquareText,
        items: {
            announcement_groups: {
                title: "announcement groups",
                permissions: ["announcement-group-view"],
            },
            announcements: {
                title: "announcements",
                permissions: ["announcement-view"],
            },
        },
    },
    accounting: {
        title: "accounting",
        lucideIcon: Calculator,
        items: {
            products: {
                title: "products",
                permissions: ["master-product-view"],
            },
            scholarship: {
                title: "scholarship",
                permissions: ["scholarship-admin-view"],
            },
            discount: {
                title: "discount",
                permissions: ["discount-view"],
            },
            create_fee: {
                title: "create fee",
                permissions: ["fees-create-and-assign"],
            },
            fee_management: {
                title: "fee management",
                permissions: ["fees-unpaid-item-admin-view"],
            },
            payment: {
                title: "payment",
                permissions: ["fees-unpaid-item-admin-view"],
            },
            billing_documents: {
                title: "billing documents",
                permissions: ["billing-document-admin-view"],
            },
        },
    },
    attendance_management: {
        title: "attendance management",
        icon: IconAttendance,
        items: {
            attendances: {
                title: "school attendances",
                permissions: ["attendance-view"],
            },
            student_attendance_inputs: {
                title: "school attendance inputs (Student)",
                permissions: ["student-attendance-input-view"],
            },
            employee_attendance_inputs: {
                title: "school attendance inputs (Employee)",
                permissions: ["employee-attendance-input-view"],
            },
            coach_attendance_inputs: {
                title: "school attendance inputs (Coach)",
                permissions: ["contractor-attendance-input-view"],
            },
            attendance_posting: {
                title: "school attendance posting",
                permissions: ["trigger-attendance-posting"],
            },
            individual_attendance_period_override: {
                title: "individual attendance period override",
                permissions: ["attendance-period-override-view"],
            },
            school_attendance_period_override: {
                title: "school attendance period override",
                permissions: ["school-attendance-period-override-view"],
            },
            class_attendance_taking: {
                title: "class attendance taking",
                permissions: ["class-attendance-taking-view"],
            },
            student_timeslot_override: {
                title: "student timeslot override",
                permissions: ["timeslot-override-view"],
            },
            leave_applications: {
                title: "leave applications",
                items: {
                    leave_application_types: {
                        title: "leave application types",
                        permissions: ["leave-application-type-view"],
                    },
                    leave_application_individual: {
                        title: "leave application (individual)",
                        permissions: ["leave-application-view"],
                    },
                    leave_application_group: {
                        title: "leave application (group)",
                        permissions: ["leave-application-view"],
                    },
                    leave_applications: {
                        title: "leave applications",
                        permissions: ["leave-application-view"],
                    },
                },
            },
        },
    },
    calendar: {
        lucideIcon: Calendar,
        title: "calendar",
        permissions: ["calendar-view"],
    },
};

export const attendanceDashboard = {
    students: "students",
    teachers: "teachers",
    parents: "parents",
};

export const locales = {
    zh: "中文",
    en: "English",
};

export const schoolAppName = process.env.NEXT_PUBLIC_SCHOOL_APP_NAME ?? "";
export const appCurrencySymbol = process.env.NEXT_PUBLIC_CURRENCY_SYMBOL ?? "";
export const appCurrencyCode = process.env.NEXT_PUBLIC_CURRENCY_CODE ?? "";

export const TOKEN = "skribble_learn_token";

export const requestOtpAPI = "/login/request-otp";
export const loginAPI = "/login";
export const profileAPI = "/users/get-profile";
export const userAPI = "/users";
export const changePasswordAPI = "/users/change-password";
export const requestResetPasswordOtpAPI = "/login/reset-password/request-otp";
export const resetPasswordAPI = "/login/reset-password/";

export const mediaUploadAPI = "/media/upload";

export const internationalizationAPI = "/master-data/internationalization";
export const raceAPI = "/master-data/races";
export const bankAPI = "/master-data/banks";
export const religionAPI = "/master-data/religions";

export const countryAPI = "/master-data/countries";
export const stateAPI = "/master-data/states";
export const gradeAPI = "/master-data/grades";
export const schoolProfileAPI = "/master-data/school-profile";
export const configAPI = "admin/master-data/configs";
export const semesterSettingAPI = "/master-data/semester-settings";
export const copySemesterSettingsAPI = "/master-data/semester-settings/copy";
export const semesterYearsAPI = "/master-data/semester-year-settings";
export const employeeJobTitlesAPI = "/master-data/employee-job-titles";
export const employeeSessionsAPI = "/master-data/employee-session";
export const employeeCategoriesAPI = "/master-data/employee-categories";

export const enrollmentAPI = "/admin/enrollments";
export const enrollmentUsersAPI = "/admin/enrollment-users";
export const enrollmentBillingDocAPI = "/admin/enrollments/billing-documents";
export const enrollmentSessionsAPI = "/admin/enrollment-sessions";
export const enrollmentConditionAPI =
    "/admin/enrollment-sessions/get-fee-setting-conditions";
export const enrollmentPrePaymentTemplateAPI =
    "/admin/enrollments/pre-payment-template";
export const enrollmentPostPaymentTemplateAPI =
    "/admin/enrollments/post-payment-template";
export const enrollmentPrePayImportAPI = "/admin/enrollments/import";
export const enrollmentPostPayImportAPI =
    "/admin/enrollments/post-payment-import";
export const enrollmentPrePayInsertAPI = "/admin/enrollments/import-insert";
export const enrollmentPostPayInsertAPI =
    "/admin/enrollments/post-payment-import-insert";
export const registrationFeeDailyCollectionReportAPI =
    "/reports/enrollment/by-daily-collection";
export const newRegisteredStudentReportAPI =
    "/reports/enrollment/student-registration-report";
export const enrollmentReportPostToAutocountAPI =
    "admin/enrollments/autocount-posting";

export const cardAPI = "/users/cards";
export const cardUserableListAPI = "/users/cards/get-userable-list";
export const cardTemplateAPI = "/cards/template";
export const cardImportAPI = "/cards/import";
export const cardExportAPI = "/reports/cards/by-cards-list";
export const cardBulkAssignAPI = "/cards/bulk-assignment";

export const studentAPI = "/students";
export const guardianAPI = "/guardians";
export const employeeAPI = "/employees";
export const courseAPI = "/master-data/courses";
export const authorAPI = "/master-data/authors";
export const bookLanguagesAPI = "/master-data/book-languages";
export const schoolAPI = "/master-data/schools";
export const educationAPI = "/master-data/educations";
export const leaveReasonAPI = "/master-data/leave-reasons";
export const departmentAPI = "/master-data/departments";
export const healthConcernAPI = "/master-data/health-concerns";
export const withdrawalReasonsAPI = "/master-data/withdrawal-reasons";
export const paymentMethodsAPI = "/master-data/payment-methods";

export const terminalAPI = "/terminals";
export const terminalKeyAPI = "/pos-terminal-keys";

export const roleAPI = "/roles";
export const permissionsAPI = "/permissions";

export const timetableAPI = "/timetables";
export const employeeTimetableAPI = "/employee-timetables";

export const bookCategoryAPI = "/master-data/book-categories";
export const bookSourceAPI = "/master-data/book-sources";
export const bookClassificationAPI = "/master-data/book-classifications";
export const bookSubClassficationAPI = "/master-data/book-sub-classifications";

export const libraryMembersAPI = "/libraries/members";
export const libraryBooksAPI = "/libraries/books";
export const libraryBooksInfoAPI = "/libraries/books/book-no";
export const libraryBorrowBookAPI = "/libraries/book-loans";
export const libraryReturnBookAPI = "/libraries/book-loans/return";
export const librayExtendBorrowAPI = "/libraries/book-loans/extend";
export const isbnAPI = "/isbn";

export const hostelBlocksAPI = "/hostel-blocks";
export const hostelRoomsAPI = "/hostel-rooms";
export const hostelBedsAPI = "/hostel-room-beds";

export const hostelBedAssignmentAPI = "/hostel-bed-assignments/assign";
export const hostelBedUnassignmentAPI = "/hostel-bed-assignments/unassign";
export const hostelBedChangeAPI = "/hostel-bed-assignments/change";
export const hostelBulkBedAssignmentTemplateAPI =
    "/hostel-bed-assignments/template";
export const hostelBulkBedAssignmentImportAPI =
    "/hostel-bed-assignments/import";
export const hostelBulkBedAssignmentAPI =
    "/hostel-bed-assignments/bulk-assignment";

export const hostelMeritDemeritSettingsAPI = "/hostel-merit-demerit-settings";
export const hostelArrivalDepartureAPI = "/hostel-in-out-records";

export const hostelRewardPunishmentSettingsAPI =
    "/hostel-reward-punishment-settings";
export const hostelDisciplinaryRecordsAPI = "/hostel-reward-punishment-records";
export const hostelPersonInChargeAPI = "/admin/hostels/person-in-charge";

export const classesAPI = "/classes";
export const semesterClassesAPI = "/semester-classes";
export const assignSemesterClassesAPI = "/classes/assign-classes-to-semester";
export const assignClassStudentsAPI = "/classes/assign-student";
export const assignClassSubjectsAPI =
    "/class-subjects/assign-subjects-to-semester-class";
export const assignSubjectClassesAPI =
    "/class-subjects/assign-semester-classes-to-subject";
export const seatSettingsAPI = "/classes/seat-assignment";

export const subjectAPI = "/subjects";
export const subjectCategoryAPI = ""; //TODO: pending for API
export const classSubjectAPI = "/class-subjects";
export const substituteRecordAPI = "/substitute-record";

export const societyAPI = "/clubs";
export const societyCategoryAPI = "/club-categories";
export const societyPositionAPI = "/master-data/society-positions";
export const studentSocietyPositionAPI = "/student-society-positions";
export const trainerAPI = "/contractors";

export const examAPI = "/exams";
export const gradingSchemeAPI = "/grading-schemes";
export const examGradingFrameworkAPI = "/grading-frameworks";
export const examGradingFrameworkApplyAPI = "/grading-frameworks/apply";
export const examGradingFrameworkBulkApplyAPI = "/grading-frameworks/bulkApply";
export const examFixedFormulaAPI = (id) =>
    `/grading-frameworks/${id}/fixed-formula`;
export const examResultSourceFormulaAPI = (id) =>
    `/grading-frameworks/${id}/result-source-formula`;
export const examOutputFormulaAPI = (id) =>
    `/grading-frameworks/${id}/output-formula`;
export const examMarkEntryGetExamAPI =
    "/exam-results-data-entry/eligible-exams";
export const examMarkEntryGetSubjectAPI =
    "/exam-results-data-entry/eligible-subjects";
export const examMarkEntryGetClassesAPI =
    "/exam-results-data-entry/eligible-classes";
export const examMarkEntryGetStudentsAPI =
    "/exam-results-data-entry/eligible-students-and-data";
export const examMarkEntrySaveAPI = "/exam-results-data-entry/save";
export const examMarkEntryReopenAPI = "/exam-results-data-entry/reopen";
export const examPostingAPI = "/exam-results-posting/create-posting-session";
export const examPostingGradingFrameworkAPI =
    "/exam-results-posting/eligible-grading-frameworks";
export const examPostingOutputCodesAPI =
    "/exam-results-posting/eligible-output-codes";
export const examPostingPrechecksDashboardAPI =
    "/exam-results-posting/pre-checks";
export const examPostingPrechecksManualRefreshAPI =
    "/exam-results-posting/refresh-pre-checks";
export const examSubjectExemptionStatusAPI =
    "/exam-subject-exemption/student_exemption_status";
export const examSubjectExemptionSubjectsAPI =
    "/exam-subject-exemption/eligible_subjects";
export const examSubjectExemptionClassesAPI =
    "/exam-subject-exemption/eligible_classes";
export const examSubjectExemptionSetAPI =
    "/exam-subject-exemption/set_exemption";
export const examPromotionMarkAPI = "/promotion-marks";
export const examReportCardAPI = "/reports/academics/student-report-card";
export const examReportCardByGroupAPI =
    "/reports/academics/report-card-by-morph";
export const examResultByClassAPI =
    "/reports/academics/examination-result-by-semester-class";
export const examResultByStudentAPI =
    "/reports/academics/examination-result-by-student-report";
export const examResultByExamAPI =
    "/reports/academics/examination-result-by-exam-report";
export const examSemesterSettingAPI = "/exam-semester-settings";
export const examSemesterSettingCreateUpdateAPI =
    "/exam-semester-settings/bulk-create-or-update";
export const netAveragePassingRateReportAPI =
    "/reports/academics/net-average-passing-rate";
export const subjectPassingRateReportAPI =
    "/reports/academics/subject-passing-rate";
export const subjectAverageMarkReportAPI =
    "/reports/academics/subject-average-mark";
export const subjectScoreAnalysisReportAPI =
    "/reports/academics/subject-analysis-data";

export const conductDeadlineAPI = "/deadlines/conduct";
export const conductSettingAPI = "/conduct-settings";
export const conductRecordsAPI = "/conduct-records";
export const conductSettingSemesterClassTeachersAPI =
    "/conduct-settings/get-teachers-by-semester-class";
export const conductSettingTeachersAPI =
    "/conduct-settings/get-conduct-teacher-list";
export const conductSettingTeachersSemesterClassAPI =
    "/conduct-settings/get-semester-classes-by-teacher";

export const comprehensiveAssessmentCategoryAPI =
    "/comprehensive-assessment/categories";
export const comprehensiveAssessmentQuestionAPI =
    "/comprehensive-assessment/questions";
export const comprehensiveAssessmentResultEntryAPI =
    "/comprehensive-assessment/records";
export const studentComprehensiveAssessmentRecordAPI =
    "/comprehensive-assessment/records/by-student";

export const leadershipPositionSettingsAPI =
    "/master-data/leadership-positions";
export const leadershipPositionRecordsAPI = "/leadership-position-records";
export const leadershipPositionBulkCreateRecordsAPI =
    "/leadership-position-records/bulk-create";

export const meritDemeritSettingsAPI = "/merit-demerit-settings";
export const rewardPunishmentCategoriesAPI =
    "/master-data/reward-punishment-categories";
export const rewardPunishmentSubCategoriesAPI =
    "/master-data/reward-punishment-sub-categories";
export const rewardPunishmentSettingsAPI = "/reward-punishments";
export const rewardPunishmentRecordsAPI = "/reward-punishment-records";
export const rewardPunishmentRecordsBulkChangeStatusAPI =
    "/reward-punishment-records/bulk-update-status";

export const awardAPI = "/master-data/awards";
export const specialCompetitionAPI = "/competitions";

export const counsellingCaseRecordAPI = "/counselling-case-records";

export const merchantAPI = "/merchants";
export const productCategoryAPI = "/product-categories";
export const productSubCategoryAPI = "/product-sub-categories";
export const productAPI = "/products";
export const productGroupAPI = "/product-groups";
export const productTagAPI = "/product-tags";

export const walletAPI = "admin/wallets";
export const walletTransactionsAPI = "admin/wallets/transactions";
export const walletTransactionsReportAPI =
    "reports/wallet-transactions/by-all-wallet-transactions";

export const orderAPI = "/admin/ecommerce/orders";

export const bookstoreReportsByClassAPI =
    "/reports/ecommerce/bookshops/classes";
export const bookstoreReportsByStudentAPI =
    "/reports/ecommerce/bookshops/students";
export const bookstoreReportsByOrderItemAPI =
    "/reports/ecommerce/bookshops/order-items";
export const bookstoreReportsByOrderAPI = "/reports/ecommerce/bookshops/orders";

export const canteenReportsByClassesAPI = "/reports/ecommerce/canteens/classes";
export const canteenReportsByWeeklyClassesAPI =
    "/reports/ecommerce/canteens/classes/weekly";
export const canteenReportsByDateRangeClassesAPI =
    "/reports/ecommerce/canteens/classes/date-range";
export const canteenReportsByDailySalesAPI =
    "/reports/ecommerce/canteens/by-merchant-daily-sales";
export const canteenReportsByDailySalesGroupAPI =
    "/reports/ecommerce/canteens/by-daily-sales-group-by-merchant";
export const canteenReportsByDailyCollectionAPI =
    "/reports/ecommerce/canteens/by-daily-collection";
export const canteenReportsByMerchantsAPI =
    "/reports/ecommerce/canteens/merchants";
export const canteenReportsByStudentAPI =
    "/reports/ecommerce/canteens/by-student";

export const semesterClassReportsStudentContactAPI =
    "/reports/semester-classes/student-contacts";
export const semesterClassReportsStudentDetailAPI =
    "/reports/semester-classes/student-details";
export const semesterClassReportsHomeroomTeacherAPI =
    "/reports/semester-classes/homeroom-teachers";
export const semesterClassReportsByStudentsInClassAPI =
    "/reports/semester-classes/by-students-in-class";
export const semesterClassReportByStudentInNonPrimaryClassAPI =
    "/reports/semester-classes/by-non-primary-students-in-class";

export const accountingReportsStudentOutstandingBalanceAPI =
    "/reports/accounting/student-outstanding-balance-report";
export const accountingReportBillingDocumentByDailyCollectionAPI =
    "/reports/billing-documents/by-daily-collection";
export const accountingReportsPostToAutocountAPI = "admin/accounting/posting";

export const announcementAPI = "/announcements";
export const announcementGroupAPI = "/announcements/groups";
export const inboxAPI = "/users/inbox";

export const hostelReportsByBoardersNameListAPI =
    "/reports/hostels/by-boarders-name-list";
export const hostelReportsByAvailableBedAPI =
    "/reports/hostels/by-available-bed";
export const hostelReportsByCheckoutRecordAPI =
    "/reports/hostels/by-checkout-record";
export const hostelReportsByChangeRoomRecordAPI =
    "/reports/hostels/by-change-room-record";
export const hostelReportsByBoardersListInfoAPI =
    "/reports/hostels/by-boarders-list-info";
export const hostelReportsByBoardersDateOfBirthAPI =
    "/reports/hostels/by-boarders-date-of-birth";
export const hostelReportsByBoardersContactInfoAPI =
    "/reports/hostels/by-boarders-contact-info";
export const hostelReportByBoardersStaybackAPI =
    "/reports/hostels/by-boarders-stayback";
export const hostelReportByEmployeeLodgingAPI =
    "reports/hostels/by-employee-lodging";
export const hostelReportByBoardersGoHomeOrOutAPI =
    "/reports/hostels/by-boarders-go-home-or-out";
export const hostelReportRewardPunishmentByBlockAPI =
    "/reports/hostels/by-reward-punishment-block";
export const hostelReportRewardPunishmentByRoomAPI =
    "/reports/hostels/by-reward-punishment-room";
export const hostelReportRewardPunishmentByStudentAPI =
    "/reports/hostels/by-reward-punishment-student";

export const libraryReportByBorrowReportAPI = "/reports/libraries/book-loans";
export const libraryReportByTopBorrowerAPI = "/reports/libraries/top-borrowers";
export const libraryReportByTopTenBorrowedBooksAPI =
    "/reports/libraries/top-borrowed-books";
export const libraryReportBySchoolRateOfBorrowAPI =
    "/reports/libraries/school-rate-borrow-books";
export const libraryReportByBookLoanIndividualAPI =
    "/reports/libraries/book-borrow-records";

export const cocurriculumTrainerDetailReportAPI =
    "/reports/cocurriculum/trainer-detail";
export const cocurriculumStudentStatisticReportAPI =
    "/reports/cocurriculum/student-statistic-report-by-semester";

export const academyTransferredStudentListReportAPI =
    "/reports/academics/transferred-student-list";
export const studentAnalysisBySemesterReportAPI =
    "/reports/academics/student-analysis";
export const coachDailyAttendanceReportAPI =
    "/reports/contractor-attendances/daily-attendance-report";
export const attendanceReportByAttendanceSummaryAPI =
    "/reports/attendances/by-summary-list";
export const teacherAttendanceReportAPI =
    "/reports/attendances/class-attendance-taking-status-report";

export const posTerminalReportByDailySalesAPI =
    "/reports/wallets/by-daily-wallet-transactions";
export const studentLeaveAPI = "/students/leave";
export const studentReturnAPI = "/students/return";

export const studentAttendanceReportAPI =
    "/reports/attendances/student-attendance";
export const classAttendanceReportAPI =
    "/reports/attendances/class-attendance-report";
export const studentAttendanceMarkDeductionReportAPI =
    "/reports/attendances/by-student-attendance-mark-deduction";
export const coachAttendanceDailyReportAPI =
    "/reports/contractor-attendances/daily-attendance-report";
export const studentAbsentReportAPI = "/reports/attendances/student-absent";

export const employeeResignAPI = "/employees/resign";
export const employeeReinstateAPI = "employees/reinstate";
export const employeeTransferAPI = "/employees/transfer";

export const hostelSavingAccountGetBalanceAPI =
    "/hostel-savings-account/balance";
export const hostelSavingAccountTransactionsAPI =
    "/hostel-savings-account/transactions";
export const hostelSavingAccountDepositAPI = "/hostel-savings-account/deposit";
export const hostelSavingAccountWithdawAPI = "/hostel-savings-account/withdraw";
export const hostelSavingAccountBillingDocumentAPI =
    "/hostel-savings-account/billing-document";

export const uomAPI = "/master-data/uoms";
export const glAccountAPI = "/master-data/gl-accounts";
export const currencyAPI = "/master-data/currencies";
export const accountingProductAPI = "/master-data/products";
export const accountingFeeAPI = "/admin/unpaid-item-assignments";
export const accountingFeeCreateAPI =
    "/admin/accounting/fees/create-and-assign";
export const accountingUnpaidFeeAPI = "/admin/accounting/fees/unpaid-items";
export const scholarshipAPI = "/admin/scholarships";
export const discountAPI = "/admin/discounts";
export const discountBulkCreateAPI = "/admin/discounts/bulk-create";
export const discountBulkUpdateAPI = "/admin/discounts/bulk-update";
export const discountConfirmAPI = "/admin/discounts/confirm";
export const billingDocumentAPI = "/admin/billing-document";
export const billingDocumentChangeStatusAPI = "/admin/billing-document/status";
export const billingDocumentCreateAPI =
    "/admin/accounting/fees/unpaid-items/create-billing-document";

export const periodGroupAPI = "/timetables/period-groups";
export const periodGroupByStudentAPI =
    "/timetables/period-groups/get-period-group-label-by-student-ids";
export const periodAPI = "/timetables/periods";
export const leaveApplicationTypeAPI = "/leave-application-types";
export const leaveApplicationsAPI = "/leave-applications";
export const leaveApplicationHistoryAPI =
    "/leave-applications/leave-application-history";

export const attendanceAPI = "/attendances";
export const attendanceInputAPI = "/attendance-input";
export const studentAttendanceInputAPI = "/attendance-input/students";
export const employeeAttendanceInputAPI = "/attendance-input/employees";
export const contractorAttendanceInputAPI = "/attendance-input/contractors";
export const attendancePostingAPI = "/attendances/trigger-attendance-posting";
export const individualAttendancePeriodOverrideAPI =
    "/attendance-period-override";
export const schoolAttendancePeriodOverrideAPI =
    "/school-attendance-period-override";
export const timeslotOverrideAPI = "/timeslot-override";
export const getAllPeriodsAPI =
    "/timetables/period-groups/get-all-grouped-periods";

export const resultsPostingHeaderAPI = "/results-posting-header";

export const classAttendanceGetPeriodsByDateAPI =
    "class-attendance-taking/get-period-by-timeslot-teacher";
export const classAttendanceGetStudentsAPI =
    "class-attendance-taking/get-class-attendance-by-timeslot";
export const classAttendanceBulkUpdateAPI =
    "class-attendance-taking/bulk-update-class-attendance";

export const calendarAPI = "/calendars";
export const calendarTargetAPI = "/calendar-targets";

export const studentConductReportAPI =
    "/reports/student-conduct/student-conduct-report";

export const defaultErrorMessage =
    "Something went wrong, please try again later.";

export const duplicateEntryErrorMessage = "Duplicate entry detected: ";

export const DEFAULT_FILTER_PARAMS = {
    page: 1,
    per_page: 10,
    order_by: {
        updated_at: "desc",
    },
};

export const CHUNKED_FILTER_PARAMS = {
    page: 1,
    per_page: 10,
};
export const GET_ALL_PARAMS = {
    page: 1,
    per_page: -1,
};

export const DATE_FORMAT = {
    YMD: "yyyy-MM-dd",
    YMD_HMS: "yyyy-MM-dd HH:mm:ss",
    DMY: "d MMM yyyy",
    MY: "MMM yyyy",
    EDMY: "EEE (d MMM yyyy)",
    forDisplay: "d LLL yyyy h:mmaaa",
    timeDisplay: "h:mmaaa",
    time: "HH:mm",
    timeHms: "HH:mm:ss",
    PPP: "PPP",
};

export const monthPickerLang = {
    months: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
    ],
};

export const monthOptions = [
    { id: 1, name: "January" },
    { id: 2, name: "February" },
    { id: 3, name: "March" },
    { id: 4, name: "April" },
    { id: 5, name: "May" },
    { id: 6, name: "June" },
    { id: 7, name: "July" },
    { id: 8, name: "August" },
    { id: 9, name: "September" },
    { id: 10, name: "October" },
    { id: 11, name: "November" },
    { id: 12, name: "December" },
];

export const daysOfWeek = [
    { label: "Monday", value: "MONDAY" },
    { label: "Tuesday", value: "TUESDAY" },
    { label: "Wednesday", value: "WEDNESDAY" },
    { label: "Thursday", value: "THURSDAY" },
    { label: "Friday", value: "FRIDAY" },
    { label: "Saturday", value: "SATURDAY" },
    { label: "Sunday", value: "SUNDAY" },
];

type YearOption = {
    id: number;
    name: number;
};

export const getYearOptions = (): YearOption[] => {
    const currentYear = new Date().getFullYear();
    const yearOptions: YearOption[] = [];

    for (let i = 0; i < 10; i++) {
        yearOptions.push({
            id: currentYear - i,
            name: currentYear - i,
        });
    }

    return yearOptions;
};

export const generateMonthOptionsWithYMDDateValue = () => {
    const currentYear = new Date().getFullYear();
    return monthOptions.map((month, index) => {
        const date = new Date(currentYear, index, 1);
        return {
            name: month.name,
            id: toYMD(date),
        };
    });
};

export const currentMonth = new Date().getMonth() + 1;
export const currentYear = new Date().getFullYear();

export const firstDayOfCurrentMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    1
);

export const lastDayOfCurrentMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth() + 1,
    0
);

export const getCurrentWeekStartDate = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const currentWeekStartDate = new Date(today);
    currentWeekStartDate.setDate(today.getDate() - dayOfWeek);
    currentWeekStartDate.setHours(0, 0, 0, 0);
    return currentWeekStartDate;
};

export const getLastDayOfCurrentWeek = () => {
    const currentWeekStartDate = getCurrentWeekStartDate();
    const lastDayOfCurrentWeek = new Date(currentWeekStartDate);
    lastDayOfCurrentWeek.setDate(currentWeekStartDate.getDate() + 6);
    return lastDayOfCurrentWeek;
};

const getNextWeekStartDate = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const daysUntilNextMonday = (8 - dayOfWeek) % 7 || 7;
    const nextWeekStartDate = new Date(today);
    nextWeekStartDate.setDate(today.getDate() + daysUntilNextMonday);
    nextWeekStartDate.setHours(0, 0, 0, 0);
    return nextWeekStartDate;
};

export const getFirstDayOfNextWeek = () => {
    return toYMD(getNextWeekStartDate());
};

export const getLastDayOfNextWeek = () => {
    const nextWeekStartDate = getNextWeekStartDate();
    const lastDayOfNextWeek = new Date(nextWeekStartDate);
    lastDayOfNextWeek.setDate(nextWeekStartDate.getDate() + 6);
    return toYMD(lastDayOfNextWeek);
};

export const ASC = "asc";
export const DESC = "desc";

export const FATHER = "FATHER" as const;
export const MOTHER = "MOTHER" as const;
export const GUARDIAN = "GUARDIAN" as const;
export const MERCHANT = "MERCHANT" as const;
export const GUEST = "GUEST" as const;

export const STUDENT = "STUDENT" as const;
export const LIBRARIAN = "LIBRARIAN" as const;
export const EMPLOYEE = "EMPLOYEE" as const;
export const CONTRACTOR = "CONTRACTOR" as const;
export const OTHERS = "OTHERS" as const;
export const STAFF = "staff" as const;
export const BED = "bed" as const;
export const MULTI = "MULTI" as const;

export const MALE = "MALE";
export const FEMALE = "FEMALE";
export const UNKNOWN = "UNKNOWN";
export const BOTH = "BOTH";

export const ACTIVE = "ACTIVE";
export const INACTIVE = "INACTIVE";

export const DRAFT = "DRAFT";
export const PENDING_PAYMENT = "PENDING_PAYMENT";
export const PAYMENT_FAILED = "PAYMENT_FAILED";

export const MERIT = "MERIT";
export const DEMERIT = "DEMERIT";

export const OCCUPIED = "OCCUPIED";

export const POSTED = "POSTED";
export const CONFIRMED = "CONFIRMED";
export const VOIDED = "VOIDED";

export const CONDUCT = "CONDUCT";
export const EXAM = "EXAM";

export const MAJOR = "MAJOR";
export const ELECTIVE = "ELECTIVE";
export const COCURRICULUM = "COCURRICULUM";

export const PRIMARY = "PRIMARY";
export const SECONDARY = "SECONDARY";
export const ENGLISH = "ENGLISH";
export const SOCIETY = "SOCIETY";

export type ClassTypes =
    | typeof PRIMARY
    | typeof ENGLISH
    | typeof SOCIETY
    | typeof ELECTIVE;

export const NOT_APPLICABLE = "NOT_APPLICABLE";
export const SCIENCE = "SCIENCE";
export const ART = "ART";
export const COMMERCE = "COMMERCE";
export const NA = "NA";

export const VOCATIONAL = "VOCATIONAL";
export const COLLEGE = "COLLEGE";
export const UNIVERSITY = "UNIVERSITY";

export const CLUB = "CLUB";
export const ACHIEVED = "ACHIEVED";
export const NEED_IMPROVEMENT = "NEED_IMPROVEMENT";

export const STARTER = "STARTER";
export const ELEMENTARY = "ELEMENTARY";
export const PRE_INTERMEDIATE = "PRE-INTERMEDIATE";
export const INTERMEDIATE = "INTERMEDIATE";
export const UPPER_INTERMEDIATE = "UPPER_INTERMEDIATE";
export const ADVANCED_1 = "ADVANCED_1";
export const ADVANCED_2 = "ADVANCED_2";
export const ADVANCED_3 = "ADVANCED_3";

export const BOOKSHOP = "BOOKSHOP";
export const CANTEEN = "CANTEEN";
export const OPERATOR_APP = "OPERATOR_APP";
export const OTHER = "OTHER";

export const ALL = "ALL";
export const SPORTS = "SPORTS";

export const PENDING = "PENDING";
export const SUCCESS = "SUCCESS";
export const FAILED = "FAILED";
export const APPROVED = "APPROVED";
export const REJECTED = "REJECTED";
export const SHORTLISTED = "SHORTLISTED";

export const HOME = "HOME";
export const OUTING = "OUTING";

export const LEAVE_SCHOOL = "LEAVE_SCHOOL";
export const RETURNED_HOSTEL = "RETURNED_HOSTEL";
export const YET_RETURN = "YET_RETURN";

export const LOAN_DATE = "LOAN_DATE";
export const MEMBER_NUMBER = "MEMBER_NUMBER";
export const CLASS_NAME = "CLASS_NAME";
export const NAME = "NAME";

export const BORROWED = "BORROWED";
export const RETURNED = "RETURNED";

export const DEPOSIT = "DEPOSIT";
export const WITHDRAWAL = "WITHDRAWAL";
export const TRANSACTION = "TRANSACTION";
export const REFUND = "REFUND";

export const SUBJECT = "SUBJECT";

export const WORKING = "WORKING";
export const RESIGNED = "RESIGNED";
export const RESIGN = "RESIGN";
export const TRANSFER = "TRANSFER";
export const REINSTATE = "REINSTATE";

export const LEAVE = "LEAVE";
export const RETURN = "RETURN";

export const FULL = "FULL";
export const SIMPLE = "SIMPLE";

export const ONE_TIME = "ONE_TIME";
export const MONTHLY = "MONTHLY";

export const NEW = "NEW";
export const APPLYING = "APPLYING";
export const COMPLETED = "COMPLETED";
export const IN_PROGRESS = "IN_PROGRESS";
export const CANCELLED = "CANCELLED";
export const ERROR = "ERROR";

export const FIXED = "FIXED";
export const PERCENT = "PERCENT";

export const UNPAID = "UNPAID";
export const PAID = "PAID";
export const PARTIAL = "PARTIAL";

export const CASH = "CASH";
export const CHEQUE = "CHEQUE";
export const BANK_TRANSFER = "BANK_TRANSFER";

export const SCHOLARSHIP = "SCHOLARSHIP";

export const ABSENT = "ABSENT";
export const LATE = "LATE";
export const PRESENT = "PRESENT";

export const INVOICE = "INVOICE";
export const ADVANCE_INVOICE = "ADVANCE_INVOICE";
export const CREDIT_NOTE = "CREDIT_NOTE";
export const DEBIT_NOTE = "DEBIT_NOTE";

export const FEES = "FEES";
export const WALLET = "WALLET";
export const ECOMMERCE = "ECOMMERCE";
export const HOSTEL_SAVINGS_ACCOUNT = "HOSTEL_SAVINGS_ACCOUNT";
export const ENROLLMENT_EXAM_FEES = "ENROLLMENT_EXAM_FEES";
export const ENROLLMENT_FEES = "ENROLLMENT_FEES";

export const AVAILABLE = "AVAILABLE";
export const LOST = "LOST";

export const hostelStudentAPIFilter = {
    is_hostel: 1,
    includes: ["activeHostelBedAssignments.bed.hostelRoom.hostelBlock"],
    fields: ["hostel_reward_punishment_points"],
};

export const hostelEmployeeAPIFilter = {
    is_hostel: 1,
    includes: ["activeHostelBedAssignments.bed.hostelRoom.hostelBlock"],
};

export const teacherDropdownFilter = {
    status: WORKING,
    is_active: 1,
    is_teacher: 1,
};

export const semesterClassAPIFilter = {
    includes: ["semesterSetting", "classModel", "homeroomTeacher"],
};

export const semesterClassOrderBy = (locale) => ({
    order_by: {
        class: {
            name: {
                en: "asc",
            },
        },
    },
});

export const semesterClassDropdownFilter = (locale) => {
    console.log("here");
    return {
        includes: ["classModel"],
        ...semesterClassOrderBy(locale),
    };
};

export const semesterClassPrimaryDropdownFilter = (locale) => ({
    ...semesterClassDropdownFilter(locale),
    class_type: PRIMARY,
});

export const classSubjectsAPIFilter = {
    includes: ["semesterClass.semesterSetting", "semesterClass.classModel"],
};

export const selectStyles = {
    option: (provided, state) => ({
        ...provided,
        color: "#130F26",
        backgroundColor: state.isFocused
            ? "#efefef"
            : state.isSelected
                ? "#d7d7d7"
                : provided.backgroundColor,
        padding: "6px 12px",
        top: -6,
        fontSize: 15,
    }),
    control: (provided, state) => ({
        ...provided,
        padding: "2px 0 2px 4px",
        fontSize: "15px",
        boxShadow: "none",
        borderColor: state.isFocused
            ? "#2F803D"
            : state.isDisabled
                ? "#ebebeb"
                : provided.borderColor,
        "&:hover": {
            borderColor: "#2F803D",
        },
        backgroundColor: state.isDisabled ? "white" : provided.backgroundColor,
    }),
    singleValue: (provided, state) => ({
        ...provided,
        color: state.isDisabled ? "gray" : provided.color,
    }),
    dropdownIndicator: (provided) => ({
        ...provided,
        color: "gray",
    }),
    menu: (provided) => ({
        ...provided,
        marginTop: "2px",
    }),
    multiValue: (provided) => ({
        ...provided,
        backgroundColor: "#f5f5f5",
        padding: "2px 4px",
        fontWeight: 500,
    }),
};

export type TableColumnType = {
    key: any;
    displayAs?: string;
    hasSort?: boolean;
    modify?: (value, cell) => any;
    modifyHeader?: (value) => any;
    rowSpan?: any;
};

export type GuardianType = typeof FATHER | typeof MOTHER | typeof GUARDIAN;

export type LoanSettingType =
    | typeof STUDENT
    | typeof LIBRARIAN
    | typeof EMPLOYEE
    | typeof OTHERS;

export type CommonFormProps = {
    id?: number | null;
    isCreate?: boolean;
    refresh: () => void;
    close: () => void;
    isViewOnly?: boolean;
    hostelType?: string;
    gradingType?: string;
    currentTableData?: Record<string, any>;
};

export type CommonFilterProps = {
    filter: Record<string, any>;
    filterForReset?: Record<string, any>;
    setFilter: React.Dispatch<React.SetStateAction<Record<string, any>>>;
    close: () => void;
    type?: string;
};

export type CommonSearchEngineProps = {
    isMultiSelect?: boolean;
    isHostel?: boolean;
    hasActiveBed?: boolean;
    hasButtons?: boolean;
    selection?: any;
    setSelection: (val: any) => void;
    reset?: () => void;
    close?: () => void;
    onConfirm?: () => void;
    styleClass?: string;
    hostelType?: string;
    otherFilterParams?: Record<string, any>;
    extraDescription?: string;
};

export type SearchEngineFormProps = {
    onSubmit: (data: Record<string, any>) => void;
    onReset: () => void;
    otherFilterParams?: Record<string, any>;
    isSelectAll: boolean;
    setIsSelectAll: (val: boolean) => void;
};

export const EXCEL = "EXCEL";
export const PDF = "PDF";

export const maritalStatusOptions = [
    "MARRIED",
    "SINGLE",
    "DIVORCED",
    "SEPARATED",
    "WIDOWED",
    "UNKNOWN",
];

export const employeeStatusOptions = [WORKING, RESIGNED];

export const password = "password";

export const view_all_merchant = "view-all-merchant";

export const malaysia = "malaysia";

export const genderOptions = [
    { id: MALE, name: "Male" },
    { id: FEMALE, name: "Female" },
    { id: UNKNOWN, name: "Others" },
];

export const IconProfilePhotoPlaceholder =
    "/icons/icon-profile-photo-placeholder.svg";

export const classStreamOptions = [
    { id: NA, name: "Not Applicable" },
    { id: SCIENCE, name: "Science" },
    { id: COMMERCE, name: "Commerce" },
];

export const nonDirectGuardianUpdatePermit = "hostel-student-update";

export const studentProfileParams = {
    includes: [
        "guardians",
        "currentSemesterPrimaryClass.semesterSetting",
        "currentSemesterPrimaryClass.semesterClass.classModel",
        "admissionGrade",
        "nationality",
        "race",
        "religion",
        "state",
        "country",
        "guardians.country",
        "guardians.race",
        "guardians.religion",
        "guardians.education",
        "leadershipPositionRecord.leadershipPosition",
        "healthConcern",
        "primarySchool",
        "classAndGradeBySemesterSetting",
        "attendances",
        "reportCards.semesterSetting",
        "activeGradingFramework.gradingFramework",
    ],
    response: FULL,
};
