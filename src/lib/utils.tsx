import { clsx } from "clsx";
import { eachYearOfInterval, format, isValid, parse, parseISO } from "date-fns";
import { format as formatToUTC, toZonedTime } from "date-fns-tz";
import {
    capitalize,
    chunk,
    flatMap,
    get,
    groupBy,
    isEmpty,
    orderBy,
    sortBy,
    startCase,
    truncate,
} from "lodash";
import { NextRouter } from "next/router";
import { destroyCookie, parseCookies, setCookie } from "nookies";
import {
    formatPhoneNumber,
    isValidPhoneNumber,
} from "react-phone-number-input";
import { twMerge } from "tailwind-merge";
import { axiosInstance } from "./api";
import {
    CHUNKED_FILTER_PARAMS,
    COCURRICULUM,
    CONTRACTOR,
    DATE_FORMAT,
    ELECTIVE,
    EMPLOYEE,
    ENGLISH,
    FATHER,
    GET_ALL_PARAMS,
    GUARDIAN,
    MAJOR,
    monthOptions,
    MOTHER,
    nav,
    PRIMARY,
    schoolAppName,
    SOCIETY,
    STUDENT,
    TOKEN,
} from "./constant";
import { enUS, zhCN } from "date-fns/locale";

export function cn(...inputs) {
    return twMerge(clsx(inputs));
}

export const getAxiosErrorMessage = (axiosError) => {
    return (
        get(axiosError, "response.data.message") ||
        getAxiosValidationFlatMapError(axiosError).join(" ") ||
        "Something went wrong, please try again later."
    );
};

export const getAxiosValidationFlatMapError = (axiosError) => {
    const errors = get(axiosError, "response.data.error", []);
    if (Array.isArray(errors) || typeof errors === "object") {
        return flatMap(errors);
    } else if (typeof errors === "string") {
        return [errors];
    }
    return [];
};

export const isUnauthenticated = (error) => {
    const msg = getAxiosErrorMessage(error);
    return (
        error?.response?.status === 403 &&
        typeof msg === "string" &&
        msg.includes("Unauthenticated")
    );
};

export const logout = (router: NextRouter, clearUserProfile: () => void) => {
    setCookie(null, "lastViewed", window.location.href, {
        path: "/",
        sameSite: "none",
        secure: true,
    });
    destroyCookie(null, TOKEN, { path: "/", sameSite: "none", secure: true });
    router.replace("/login");
    clearUserProfile();
};

export const isObjectType = (data: unknown) =>
    typeof data === "object" && !Array.isArray(data) && data !== null;

export const showBackendFormError = (form, error, appendedKey?: string) => {
    if (error && error?.response?.status === 422) {
        const errorData = error?.response?.data?.error;
        if (isObjectType(errorData)) {
            Object.entries(errorData).forEach(
                ([key, list]: [string, Array<any>]) => {
                    form?.setError(
                        appendedKey ? `${appendedKey}.${key}` : key,
                        {
                            type: "custom",
                            message: list?.join(" "),
                        },
                        { shouldFocus: true }
                    );
                }
            );
        }
    }
};

export const getAxiosObjectError = (error) => {
    const errorData: any = error?.response?.data?.error;
    let errorMsg = "";
    if (isObjectType(errorData)) {
        errorMsg = Object.values(errorData).join("");
    }
    return errorMsg;
};

export const getInputErrorMessage = (errorData) => {
    const error = errorData?.message ?? errorData;
    return typeof error === "string" &&
        error.includes("required") &&
        !error.includes("when")
        ? "This field is required"
        : error;
};

export const formatForSelect = (data: unknown) => {
    if (!Array.isArray(data)) return;
    const selection =
        data
            .map((item: Record<string, any> | null) => ({
                label: item?.name ?? item?.year ?? item,
                value: item?.id ?? item,
            }))
            .filter((item) => item?.label?.toString()?.length > 0) || [];

    return selection;
};

export const formatStringsForSelect = (
    array: Array<string>,
    t,
    isStartCase = true
) => {
    const selection =
        array
            .map((text) => ({
                label: isStartCase
                    ? strStartCase(t(replaceAll(text, "_", " ")))
                    : text,
                value: text,
            }))
            .filter((text) => text?.label?.length > 0) || [];

    return selection;
};

export const formatKeyForLabel = (key: unknown) => {
    if (typeof key === "string") {
        return startCase(replaceAll(key, "_", " ").toLowerCase());
    }
    return `${key}`;
};

export const formatUnderscores = (value) => {
    if (typeof value === "string") {
        return replaceAll(value, "_", " ");
    }
    return value;
};

export const getStreamName = (stream) => {
    if (!stream) return "-";
    if (stream === "NA") return "Not Applicable";
    return strStartCase(stream.replaceAll("_", " "));
};

export const getYearsRange = (startYear: number, endYear: number | null) => {
    const startDate = new Date(startYear, 0, 1);
    const endDate = endYear ? new Date(endYear, 0, 1) : new Date();

    const years = eachYearOfInterval({ start: startDate, end: endDate });
    return years.reverse().map((year) => format(year, "yyyy"));
};

export const configForGetAll = (locale: string) => {
    return {
        params: GET_ALL_PARAMS,
        headers: { "Accept-Language": locale },
    };
};

export function replaceAll(
    target: string | number,
    search: string,
    replacement: string
) {
    if (!target) return "";
    const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const regex = new RegExp(escapedSearch, "g");
    return target.toString().replace(regex, replacement);
}

export function formatStudentData(data) {
    return {
        photo: data?.photo,
        student_number: data?.student_number,
        name: combinedNames(data?.translations?.name),
        gender: capitalize(data?.gender ?? "-"),
        email: data?.email ?? "-",
        address: data?.address ?? "-",
        phone: formatPhoneNumber(data?.phone_number ?? "-") ?? "-",
    };
}

export function formatEmployeeData(data) {
    return {
        photo: data?.photo,
        employee_number: data?.employee_number,
        name: combinedNames(data?.translations?.name),
        gender: capitalize(data?.gender ?? "-"),
        email: data?.email ?? "-",
        address: data?.address ?? "-",
        phone: formatPhoneNumber(data?.phone_number ?? "-") ?? "-",
    };
}

export function guardiansData(guardians: any[], languages: any[]) {
    const _guardian = {};
    const roles = [FATHER, MOTHER, GUARDIAN];

    roles.forEach((role) => {
        const data = guardians?.[role.toLowerCase()];

        _guardian[role.toLowerCase()] = {
            name: languages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: data?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            nric_no: data?.nric_no ?? "",
            passport_no: data?.passport_no ?? "",
            email: data?.email ?? "",
            phone: data?.phone ?? "",
            nationality_id: data?.nationality?.id ?? "",
            race_id: data?.race?.id ?? "",
            religion_id: data?.religion?.id ?? "",
            type: role,
        };
    });

    return _guardian;
}

export function studentInfoDisplay(data, includeBed?: boolean) {
    const newData: Record<string, any> = {
        name: combinedNames(data?.translations?.name),
        student_number: data?.student_number,
        gender: capitalize(data?.gender),
    };
    if (includeBed) {
        newData.bed = data?.active_hostel_bed_assignment?.bed?.name;
        newData.room =
            data?.active_hostel_bed_assignment?.bed?.hostel_room?.name;
        newData.block =
            data?.active_hostel_bed_assignment?.bed?.hostel_room?.hostel_block?.name;
    }
    return newData;
}

export function employeeInfoDisplay(data, includeBed?: boolean) {
    const newData: Record<string, any> = {
        name: data?.name,
        employee_number: data?.employee_number,
        gender: capitalize(data?.gender),
    };
    if (includeBed) {
        newData.bed = data?.active_hostel_bed_assignment?.bed?.name;
        newData.room =
            data?.active_hostel_bed_assignment?.bed?.hostel_room?.name;
        newData.block =
            data?.active_hostel_bed_assignment?.bed?.hostel_room?.hostel_block?.name;
    }
    return newData;
}

export function bedInfoDisplay(data) {
    return {
        block: data?.hostel_room?.hostel_block?.name,
        room: data?.hostel_room?.name,
        bed: data?.name,
        status: data.status,
    };
}

export function toMyZonedTime(dateTime) {
    if (!dateTime) return null;
    // Convert UTC time to Malaysia Time (UTC+8)
    const utcDate = parse(dateTime + "Z", "yyyy-MM-dd HH:mm:ssX", new Date());
    const zonedDate = toZonedTime(utcDate, "Asia/Kuala_Lumpur");
    return zonedDate;
}

export function convertDateTime(
    dateTime,
    convertFormat = DATE_FORMAT.forDisplay
) {
    if (!dateTime) return "--";
    try {
        const zonedDateTime = toMyZonedTime(dateTime);
        if (!zonedDateTime) return "--";

        return format(zonedDateTime, convertFormat);
    } catch (error) {
        return "--";
    }
}

export function convertDateToYearMonth(
    date: Date | { year: number; month: number } | string
): { year: number; month: number } {
    if (typeof date === "string") {
        const [year, month] = date.split("-").map(Number);
        return { year, month };
    }

    if (date instanceof Date) {
        return { year: date.getFullYear(), month: date.getMonth() + 1 };
    }

    if (typeof date === "object" && "year" in date && "month" in date) {
        return date;
    }

    throw new Error("Invalid date format");
}

export function convertYearMonthToDate(
    yearMonth: { year: number; month: number } | null,
    outputFormat: string,
    isEndOfMonth: boolean = false
): string {
    if (!yearMonth || !yearMonth.year || !yearMonth.month) return "-";
    try {
        const { year, month } = yearMonth;
        let date = new Date(year, month - 1, 1); // Subtract 1 because JavaScript months are 0-based
        if (isEndOfMonth) {
            date = new Date(year, month, 0); // Get the last day of the month
        }
        return format(date, outputFormat);
    } catch (error) {
        return "--";
    }
}

export function displayDateTime(dateTime, dateTimeFormat, locale = "en") {
    try {
        return dateTime
            ? format(
                  dateTime,
                  locale === "zh" ? DATE_FORMAT.PPP : dateTimeFormat,
                  {
                      locale: locale === "zh" ? zhCN : enUS,
                  }
              )
            : "-";
    } catch (e) {
        return "-";
    }
}

export function displayTime(time: string) {
    if (!time) return "-";
    try {
        const parsedTime = parse(time, "HH:mm:ss", new Date());
        return format(parsedTime, DATE_FORMAT.timeDisplay);
    } catch (error) {
        return "--";
    }
}

export function toYMD(date) {
    if (!date) return null;
    try {
        return format(date, DATE_FORMAT.YMD);
    } catch (error) {
        return "--";
    }
}

export function toUTC(dateTime: Date, dateFormat?: string) {
    if (!dateTime) return null;
    try {
        return formatToUTC(
            toZonedTime(dateTime, "UTC"),
            dateFormat ?? DATE_FORMAT.YMD_HMS,
            {
                timeZone: "UTC",
            }
        );
    } catch (error) {
        return "";
    }
}

export function formatStringToDate(dateString?: string) {
    if (!dateString) return undefined;
    try {
        return dateString
            ? parse(dateString, "yyyy-MM-dd", new Date())
            : undefined;
    } catch (error) {
        return undefined;
    }
}

export function formatTimeToDate(timeString?: string) {
    if (!timeString) return undefined;
    try {
        return timeString
            ? parse(timeString, "HH:mm:ss", new Date())
            : undefined;
    } catch (error) {
        return undefined;
    }
}

export function removeDuplicates(list: Record<string, any>[]) {
    return Array.from(new Map(list.map((item) => [item.id, item])).values());
}

export function getTableSelection(ids: any[], navigatedResults: any[]) {
    const _selection = navigatedResults.filter((result) =>
        ids.includes(result?.id?.toString())
    );
    return removeDuplicates(_selection);
}

export function getDecimalValues(value) {
    let sanitizedValue = value.replace(/[^0-9.-]/g, "");

    if (sanitizedValue.includes("-")) {
        sanitizedValue = sanitizedValue.replace(/-/g, "");
        sanitizedValue = "-" + sanitizedValue;
    }

    const parts = sanitizedValue.split(".");
    if (parts.length > 1) {
        sanitizedValue = parts[0] + "." + parts.slice(1).join("");
    }

    if (parts[1] && parts[1].length > 2) {
        sanitizedValue = `${parts[0]}.${parts[1].slice(0, 2)}`;
    }

    return sanitizedValue;
}

export function getPositiveDecimalValues(value) {
    let sanitizedValue = value.replace(/[^0-9.]/g, "");
    const parts = sanitizedValue.split(".");
    if (parts.length > 1) {
        sanitizedValue = parts[0] + "." + parts.slice(1).join("");
    }
    if (parts[1] && parts[1].length > 2) {
        sanitizedValue = `${parts[0]}.${parts[1].slice(0, 2)}`;
    }
    return sanitizedValue;
}

export function isValueTrue(value) {
    return value === true || value == 1;
}

export function isProperPhoneNumber(form, name, phoneNumber) {
    if (isEmpty(phoneNumber)) return true;
    if (!isValidPhoneNumber(phoneNumber)) {
        form.setError(
            name,
            {
                type: "manual",
                message: "Invalid phone number",
            },
            { shouldFocus: true }
        );
    }
    return isValidPhoneNumber(phoneNumber);
}

export function getNameColumns(activeLanguages, hasSort = true) {
    return (
        activeLanguages?.map((lang) => ({
            key: lang.code,
            displayAs: `Name ( ${lang?.name} )`,
            hasSort: hasSort,
            modify: (value) => (
                <span className="capitalize">{value ?? "-"}</span>
            ),
        })) ?? []
    );
}

export function combinedNames(names) {
    if (!isObjectType(names)) return "-";

    const entries = Object.entries(names);
    const sortedEntries = sortBy(entries, ([key]) => key);
    const sortedNames = Object.fromEntries(sortedEntries);
    const nameList: any[] = [
        ...new Set(Object.values(sortedNames).filter((name) => name)),
    ];
    const namesCombined =
        nameList.length === 0
            ? "-"
            : nameList?.length === 1
              ? nameList[0]
              : nameList?.join(" - ");
    return isEmpty(namesCombined) ? "-" : namesCombined;
}

export function combinedNamesCell(names) {
    const nameList: any[] = names?.filter((name) => name);

    return (
        <span className="whitespace-nowrap text-[14px]">
            {nameList?.length === 1 ? nameList[0] : nameList?.join(" - ")}
        </span>
    );
}

export function optionUserLabel(number, names) {
    if (!isObjectType(names)) return "-";
    return `${number ? number + " - " : ""}${combinedNames(names)}`;
}

export const strStartCase = (str: string) => {
    return str
        .toLowerCase()
        .split("/")
        .map((part) =>
            part
                .split(" ")
                .map((word) =>
                    word.replace(/([a-zA-Z])/, (match) => match.toUpperCase())
                )
                .join(" ")
        )
        .join("/");
};

export const firstStartCase = (str: string) => {
    return str
        .split("/")
        .map((part) =>
            part
                .split(" ")
                .map((word) =>
                    word.replace(/([a-zA-Z])/, (match) => match.toUpperCase())
                )
                .join(" ")
        )
        .join("/");
};

export const selectStyles = (isSmaller = false) => ({
    option: (provided, state) => ({
        ...provided,
        color: "#130F26",
        backgroundColor: state.isFocused
            ? "#efefef"
            : state.isSelected
              ? "#d7d7d7"
              : provided.backgroundColor,
        padding: "6px 12px",
        top: -6,
        fontSize: isSmaller ? "13px" : "15px",
    }),
    control: (provided, state) => ({
        ...provided,
        padding: isSmaller ? 0 : "2px 0 2px 4px",
        fontSize: isSmaller ? "13px" : "15px",
        boxShadow: "none",
        borderColor: state.isFocused
            ? "#2F803D"
            : state.isDisabled
              ? "#ebebeb"
              : provided.borderColor,
        "&:hover": {
            borderColor: "#2F803D",
        },
        backgroundColor: state.isDisabled ? "white" : provided.backgroundColor,
    }),
    singleValue: (provided, state) => ({
        ...provided,
        color: state.isDisabled ? "gray" : provided.color,
    }),
    dropdownIndicator: (provided) => ({
        ...provided,
        color: "gray",
        padding: isSmaller ? "0 4px" : provided.padding,
    }),
    menu: (provided) => ({
        ...provided,
        marginTop: "2px",
    }),
    placeholder: (provided) => ({
        ...provided,
        lineHeight: "1",
    }),
    multiValue: (provided) => ({
        ...provided,
        backgroundColor: "#f5f5f5",
        padding: "2px 4px",
        fontWeight: 500,
    }),
});

export function removeLastWordWith(str: string, char: string): string {
    if (str[str.length - 1] === char) {
        return str.slice(0, -1);
    }
    return str;
}

export function formatNumberForRead(value) {
    if (typeof value !== "number") return value ?? "-";
    return new Intl.NumberFormat("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(value);
}

export function getPageList(path: string): string[] {
    const pageNames = replaceAll(path, "-", "_").split("/");
    const pages: string[] = [];

    addTtile(nav, pageNames[0], 1);

    function addTtile(data: Record<string, any>, name: string, depth: number) {
        if (!data[name]) return;

        pages.push(data[name]?.title ?? "");

        if (data[name]?.items) {
            addTtile(data[name]?.items, pageNames[depth], depth + 1);
        }
    }
    return pages.reverse();
}

export function getPageTitle(path: string, translate) {
    const appName = translate(schoolAppName ?? "");
    const pages: string[] = getPageList(path).map((page) => {
        return strStartCase(translate(page) ?? page);
    });

    pages.push(appName ?? "");
    return pages.join(" | ");
}

export function downloadFile(url) {
    console.log("url", url);
    // Function to convert an ArrayBuffer to a Base64 string
    function arrayBufferToBase64(buffer) {
        let binary = "";
        const bytes = new Uint8Array(buffer);
        const len = bytes.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    fetch(url)
        .then((response) => response.arrayBuffer())
        .then((buffer) => {
            const base64String = arrayBufferToBase64(buffer);
            const a = document.createElement("a");
            // a.style = "display: none";
            a.href = "data:application/pdf;base64," + base64String;
            a.download = replaceAll(
                url,
                "https://skribble-learn-test.s3.ap-southeast-1.amazonaws.com",
                ""
            );
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        })
        .catch((err) =>
            console.error("Error fetching or converting PDF:", err)
        );
}

export async function downloadBlobFile(url, fileName) {
    try {
        const response = await axiosInstance.get(url, {
            responseType: "blob",
        });

        const blobUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = fileName;
        link.click();

        window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
        console.error("Error downloading file:", error);
    }
}

export const getSemesterClassName = (semesterClass: Record<string, any>) => {
    let _class = semesterClass?.class_model?.name ?? "";
    if (typeof _class === "object") {
        _class = combinedNames(_class);
    }
    const _semester = semesterClass?.semester_setting?.name ?? "";

    return `${_class} ( ${_semester} )`;
};

export const getNameWithCurrency = (name: string, currency: string) => {
    return name ?? "" + ` (${currency ?? ""})`;
};

export const getValueWithCurrency = (currency: string, value: string) => {
    return `${currency} ${value}`;
};

export function isTypeStudent(type) {
    return type?.toLowerCase() === STUDENT.toLowerCase();
}

export function isTypeEmployee(type) {
    return type?.toLowerCase() === EMPLOYEE.toLowerCase();
}

export function isTypeContractor(type) {
    return type?.toLowerCase() === CONTRACTOR.toLowerCase();
}

export const getYesterday = () => {
    const today = new Date();
    today.setDate(today.getDate() - 1);
    return today;
};

export const getDateFromTimeString = (timeString: string): Date => {
    const [hours, minutes] = timeString.split(":").map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
};

export const getDayName = (dateString: string): string => {
    if (!dateString) return "-";
    const date = parseISO(dateString);
    return format(date, "EEEE");
};

export const isChineseCode = (code: string) => {
    if (!code) return false;
    return code === "zh" || code === "zh-CN" || code === "zh-TW";
};

export const getChunkedUserableData = (data: any[]) => {
    return (
        chunk(
            data?.map((target) => {
                const type = target?.user_type_description.toUpperCase();
                return {
                    id: type + target?.userable_id,
                    target_id: target?.userable_id,
                    type: capitalize(type),
                    number:
                        target?.number ??
                        target?.student_number ??
                        target?.employee_number,
                    name: combinedNames(target?.translations?.name),
                };
            }),
            CHUNKED_FILTER_PARAMS.per_page
        ) ?? []
    );
};

export const getChunkedStudentData = (
    data: any[],
    hasPrimaryClass?: boolean
) => {
    const formattedData = data?.map((target) => ({
        id: target?.id,
        student_number: target?.student_number,
        name: combinedNames(target?.translations?.name),
        ...(hasPrimaryClass
            ? {
                  primary_class: combinedNames(
                      target?.current_primary_class?.semester_class?.class_model
                          ?.translations?.name ?? target?.primary_class?.class
                  ),
              }
            : {}),
    }));
    return (
        chunk(
            orderBy(
                formattedData,
                hasPrimaryClass ? ["primary_class", "name"] : ["name"],
                hasPrimaryClass ? ["asc", "asc"] : ["asc"]
            ),
            CHUNKED_FILTER_PARAMS.per_page
        ) ?? []
    );
};

export const getUserableType = (type: string) =>
    "App\\Models\\" + capitalize(type);

export const getTypeFromUserModel = (user: string) =>
    user?.replace("App\\Models\\", "") ?? "-";

export const combineSemesterClass = (semesterClass: Record<string, any>) => {
    if (!semesterClass) return "-";
    const _class = semesterClass?.semester_class?.class_model?.name ?? "";
    const _semester = semesterClass?.semester_setting?.name ?? "-";
    return `${_class} (${_semester})`;
};

export function groupedSemesterClassOptions(semesterClasses) {
    const labelOrder = [PRIMARY, ENGLISH, SOCIETY, ELECTIVE];

    function sortedSocietyOptions(options) {
        return sortBy(
            options.map((option) => ({
                value: option.id,
                label:
                    (option?.class_model?.type === SOCIETY
                        ? `${option?.class_model?.code}-`
                        : "") +
                    combinedNames(option?.class_model?.translations?.name),
            })),
            "label"
        );
    }
    const groupedOptions =
        Object.entries(groupBy(semesterClasses, "class_model.type")).map(
            ([type, options]) => ({
                label: type,
                options:
                    type === SOCIETY
                        ? sortedSocietyOptions(options)
                        : options.map((option) => ({
                              value: option.id,
                              label: combinedNames(
                                  option?.class_model?.translations?.name
                              ),
                          })),
            })
        ) ?? [];

    return sortBy(groupedOptions, (group) => labelOrder.indexOf(group.label));
}

export const arrayContainsAny = (array1: any[], array2: any[]): boolean => {
    return array1?.some((item) => array2?.includes(item));
};

export const arrayContainsAll = (array1: any[], array2: any[]): boolean => {
    return array2?.every((item) => array1?.includes(item));
};

export const goToLastViewed = (router: NextRouter) => {
    const cookies = parseCookies();
    const lastViewed = cookies.lastViewed;

    if (lastViewed?.includes("login")) {
        destroyCookie(null, "lastViewed", {
            path: "/",
            sameSite: "none",
            secure: true,
        });
        router.replace("/");
        return;
    }
    router.replace(lastViewed ?? "/");
};

export const getDateRangeDisplay = (
    startDate: Date,
    endDate: Date,
    locale = "en"
) => {
    if (!startDate || !endDate) return "-";
    const selectedLocaleDateFormat =
        locale === "zh" ? DATE_FORMAT.PPP : DATE_FORMAT.DMY;
    const selectedLocale = locale === "zh" ? zhCN : enUS;

    try {
        if (startDate.toISOString() === endDate.toISOString())
            return format(startDate, selectedLocaleDateFormat, {
                locale: selectedLocale,
            });
        return `${format(startDate, selectedLocaleDateFormat, {
            locale: selectedLocale,
        })} - ${format(endDate, selectedLocaleDateFormat, {
            locale: selectedLocale,
        })}`;
    } catch (e) {
        console.log(e);
        return "Invalid Date";
    }
};

// TODO: fix
export const getFilteredMonthOptions = (from, to) => {
    const startMonth = new Date(from).getMonth() + 1;
    const endMonth = new Date(to).getMonth() + 1;

    return monthOptions.filter(
        (month) => month.id >= startMonth && month.id <= endMonth
    );
};
export const getFiles = (filesObj) =>
    isObjectType(filesObj)
        ? Object.values(filesObj).filter((file) => file instanceof File)
        : [];

export const formatDate = (
    date: Date | string,
    formatString: string = "dd-MM-yyyy"
): string => {
    if (!date) return "-";

    const parsedDate = typeof date === "string" ? parseISO(date) : date;
    if (!isValid(parsedDate)) return "-";
    return format(parsedDate, formatString);
};

export const isMY = (name: string) => {
    const MYCountryNames = ["malaysia", "马来西亚"];
    return MYCountryNames.includes(name.toLowerCase());
};

export const refreshForUpdate = (filter, setFilter) => {
    setFilter({
        page: 1,
        per_page: filter?.per_page,
        order_by: {
            updated_at: "desc",
        },
    });
};

export const displayCellValue = (value) => {
    return isEmpty(value) ? "-" : `${value ?? "-"}`;
};

export const getSubjectTypesForClassType = (classType) => {
    if (classType === PRIMARY || classType === ENGLISH)
        return [MAJOR, ELECTIVE];
    if (classType === SOCIETY) return [COCURRICULUM];
    return [];
};

export const truncateText = (text, length = 60) => {
    if (typeof text !== "string") return "";
    return truncate(text, {
        length,
        omission: "...",
        separator: /[\s,]+/, //truncates at a space or comma
    });
};

export function shortenDay(day) {
    return day?.slice(0, 3);
}

export function showInteger(value) {
    let number: any = value;

    if (typeof value === "string") {
        number = parseFloat(value);
    }

    if (Number.isInteger(number)) {
        number = parseInt(value);
    }
    return number;
}

export const YES = "Yes";
export const NO = "No";

export function getNumBooleanValue(value) {
    return value === 1 ? YES : value === 0 ? NO : undefined;
}

export function formatEnumToString(value) {
    if (!value || value == "") return "-";
    if (typeof value === "string") {
        return strStartCase(value.replaceAll("_", " "));
    }
    return `${value}`;
}

export function formatWithBrackets(value) {
    if (!value || value == "") return "";
    return `(${value})`;
}
