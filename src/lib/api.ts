import axios from "axios";
import { destroyCookie, parseCookies, setCookie } from "nookies";
import { TOKEN } from "./constant";
import { isUnauthenticated } from "./utils";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const axiosPublic = axios.create({
    baseURL: baseURL,
    headers: {
        Accept: "application/json",
        "X-SKLearn-Platform": "WEB",
    },
});

export const axiosInstance = axios.create();

axiosInstance.interceptors.request.use(
    (config) => {
        const token = parseCookies()[TOKEN];
        const auth = token ? `Bearer ${token}` : "";

        config.baseURL = baseURL;
        config.headers["Authorization"] = auth;
        config.headers["Accept"] = "application/json";
        config.headers["X-SKLearn-Platform"] = "WEB";
        config.headers["ngrok-skip-browser-warning"] = "69420";
        return config;
    },
    (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (isUnauthenticated(error)) {
            setCookie(null, "lastViewed", window.location.href, {
                path: "/",
                sameSite: "none",
                secure: true,
            });
            destroyCookie(null, TOKEN, {
                path: "/",
                sameSite: "none",
                secure: true,
            });
            window.location.href = "/login";
        }
        return Promise.reject(error);
    }
);
