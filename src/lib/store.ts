import { create } from "zustand";
import { persist } from "zustand/middleware";

type LoadingState = {
    isLoading: boolean;
    setLoading: (toStart: boolean) => void;
};

type LanguageState = {
    languages: any[] | null;
    activeLanguages: any[] | null;
    setLanguages: (list: any[]) => void;
};

type UserProfileState = {
    userProfile: Record<string, any> | null;
    setUserProfile: (data: Record<string, any> | null) => void;
    hasPermit: (permit: string | undefined) => boolean;
    hasMerchantType: () => boolean;
};

export const useNav = create(
    persist<any>(
        (set) => ({
            isMinimized: false,
            setIsMinimized: (isMinimized: boolean) => set({ isMinimized }),
        }),
        {
            name: "nav-settings",
        }
    )
);

export const useFilter = create(
    persist<any>(
        (set) => ({
            isExpanded: true,
            setIsExpanded: (isExpanded: boolean) => set({ isExpanded }),
        }),
        {
            name: "filter-settings",
        }
    )
);

export const useLoading = create<LoadingState>((set) => ({
    isLoading: false,
    setLoading: (toLoad: boolean) => set({ isLoading: toLoad }),
}));

export const useLanguages = create<LanguageState>((set) => ({
    languages: null,
    activeLanguages: null,
    setLanguages: (list: any[]) =>
        set({
            languages: list,
            activeLanguages: list?.filter((lang) => lang.is_active),
        }),
}));

export const useUserProfile = create<UserProfileState>((set, get) => ({
    userProfile: null,
    setUserProfile: (data: Record<string, any>) =>
        set({
            userProfile: data,
        }),
    hasPermit: (permit: string | undefined) =>
        get()
            .userProfile?.permissions.map((item) => item.name)
            .includes(permit),
    hasMerchantType: () =>
        get().userProfile?.userables?.some((userable) =>
            userable?.userable_type?.toLowerCase()?.includes("merchant")
        ) ?? false,
}));
