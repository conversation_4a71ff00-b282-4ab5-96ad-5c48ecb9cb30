import { useLocale, useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { AxiosResponse } from "axios";
import { capitalize, chunk, flatten, isEmpty, isEqual, sortBy } from "lodash";
import { useRouter } from "next/router";
import toast from "react-hot-toast";
import { axiosInstance, axiosPublic } from "./api";
import {
    CHUNKED_FILTER_PARAMS,
    CONTRACTOR,
    DESC,
    EMPLOYEE,
    merchantAPI,
    MULTI,
    STUDENT,
    view_all_merchant,
} from "./constant";
import { useLoading, useUserProfile } from "./store";
import {
    combinedNames,
    getAxiosErrorMessage,
    isUnauthenticated,
} from "./utils";

type UseAxiosParams = {
    api?: string;
    onSuccess?: (result: any) => void;
    onError?: (error: any, payload: any) => void;
    toastMsg?: string;
    noToast?: boolean;
    locale?: string;
    noLoading?: boolean;
};

type AxiosQueryParams = {
    params?: Record<string, any>;
    id?: string | number;
};

export const useAxios = ({
    api,
    onSuccess = () => {},
    onError = (error, payload) => {},
    toastMsg,
    noToast,
    locale = "en",
    noLoading = false,
}: UseAxiosParams) => {
    const t = useTranslations("common");

    const [data, setData] = useState<any>(null);
    const [error, setError] = useState<any>(null);

    const isLoading = useLoading((state) => state.isLoading);
    const setLoading = useLoading((state) => state.setLoading);

    function handleError(
        error: any,
        payload?: any,
        { showToastForForm = false } = {}
    ) {
        console.log(error);
        if (isUnauthenticated(error)) {
            return;
        }
        onError(error, payload);
        setError(error);

        if (
            !showToastForForm &&
            error &&
            error.config?.method !== "get" &&
            error.response?.status === 422
        ) {
            return;
        }
        toast.error(t(getAxiosErrorMessage(error)));
    }

    function axiosQuery({ params, id }: AxiosQueryParams = {}) {
        if (!noLoading) {
            setLoading(true);
        }
        axiosInstance
            .get(api + (id ? `/${id}` : ""), {
                params,
                headers: { "Accept-Language": locale },
            })
            .then((res) => {
                setData(res.data.data);
                onSuccess(res.data);
            })
            .catch((error) => {
                handleError(error, null);
            })
            .finally(() => {
                if (!noLoading) setLoading(false);
            });
    }

    function axiosPost(
        newData: Record<string, any>,
        { showErrorInToast = false, returnPayloadOnError = false } = {}
    ) {
        if (!api) return;
        if (!noLoading) {
            setLoading(true);
        }
        axiosInstance
            .post(api, newData)
            .then((res) => {
                onSuccess(res.data);
                if (!noToast) {
                    toast(t(toastMsg || `Created successfully`));
                }
            })
            .catch((error) => {
                handleError(error, returnPayloadOnError ? newData : null, {
                    showToastForForm: showErrorInToast,
                });
            })
            .finally(() => {
                if (!noLoading) setLoading(false);
            });
    }

    function axiosPublicPost(newData: Record<string, any>) {
        if (!api) return;
        setLoading(true);
        axiosPublic
            .post(api, newData)
            .then((res) => {
                onSuccess(res.data);
                if (!noToast) {
                    toast(toastMsg || `Created successfully`);
                }
            })
            .catch((error) => {
                handleError(error, null);
            })
            .finally(() => setLoading(false));
    }

    function axiosPut({
        id,
        data,
        showErrorInToast = false,
        returnPayloadOnError = false,
    }: {
        id?: number | string;
        data: Record<string, any>;
        showErrorInToast?: boolean;
        returnPayloadOnError?: boolean;
    }) {
        if (!api) return;
        setLoading(true);
        axiosInstance
            .put(api + (id ? `/${id}` : ""), data)
            .then((res) => {
                onSuccess(res.data);
                if (!noToast) {
                    toast(toastMsg || `Updated successfully`);
                }
            })
            .catch((error) => {
                handleError(
                    error,
                    returnPayloadOnError ? { id, ...data } : null,
                    { showToastForForm: showErrorInToast }
                );
            })
            .finally(() => setLoading(false));
    }

    function axiosPutInBulk(data: Record<string, any>) {
        if (!api) return;
        setLoading(true);
        axiosInstance
            .put(api, data)
            .then((res) => {
                onSuccess(res.data);
                if (!noToast) {
                    toast(toastMsg || `Updated successfully`);
                }
            })
            .catch((error) => {
                handleError(error, null);
            })
            .finally(() => setLoading(false));
    }

    function axiosPatch(data: Record<string, any>) {
        if (!api) return;
        setLoading(true);
        axiosInstance
            .patch(api, data)
            .then((res) => {
                onSuccess(res.data);
                if (!noToast) {
                    toast(toastMsg || `Updated successfully`);
                }
            })
            .catch((error) => {
                handleError(error, null);
            })
            .finally(() => setLoading(false));
    }

    const multipartConfig = {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    };

    function axiosMultipartPost(
        newData: Record<string, any>,
        showToast: boolean = true
    ) {
        if (!api) return;
        setLoading(true);
        axiosInstance
            .post(api, newData, multipartConfig)
            .then((res) => {
                onSuccess(res.data);
                if (showToast) toast(toastMsg || `Created successfully`);
            })
            .catch((error) => {
                handleError(error, null);
            })
            .finally(() => setLoading(false));
    }

    function axiosMultipartPut({
        id,
        data,
    }: {
        id: number | string;
        data: Record<string, any>;
    }) {
        if (id) {
            setLoading(true);
            axiosInstance
                .post(
                    `${api}/${id}`,
                    { ...data, _method: "PUT" },
                    multipartConfig
                )
                .then((res) => {
                    onSuccess(res.data);
                    toast(toastMsg || `Updated successfully`);
                })
                .catch((error) => {
                    handleError(error, null);
                })
                .finally(() => setLoading(false));
        }
    }

    function axiosDelete({ id }: { id: number | string }) {
        if (id) {
            setLoading(true);
            axiosInstance
                .delete(`${api}/${id}`)
                .then((res) => {
                    onSuccess(res.data);
                    toast(toastMsg || `Deleted successfully`);
                })
                .catch((error) => {
                    handleError(error, null);
                })
                .finally(() => setLoading(false));
        }
    }

    function axiosMultipleQueries(
        queries: Array<null | Promise<AxiosResponse<any, any>>>
    ) {
        if (!queries) return;
        setLoading(true);
        Promise.all(queries)
            .then((res) => {
                setData(res);
                onSuccess(res);
            })
            .catch((error) => {
                handleError(error, null);
            })
            .finally(() => setLoading(false));
    }

    return {
        data,
        setData,
        axiosQuery,
        axiosMultipleQueries,
        axiosPost,
        axiosPublicPost,
        axiosPut,
        axiosPutInBulk,
        axiosPatch,
        axiosMultipartPost,
        axiosMultipartPut,
        axiosDelete,
        handleError,
        error,
        isLoading,
    };
};

export const useSubmit = (showLoader = true) => {
    const submitRef = useRef<any>();
    const setLoading = useLoading((state) => state.setLoading);

    // fix submit slight delay in showing loading screen for bigger form
    function initLoader(submit: () => void) {
        if (showLoader) {
            setLoading(true);
        }
        submitRef.current = setTimeout(() => {
            if (showLoader) {
                setLoading(false);
            }
            submit();
        }, 100);
    }

    useEffect(() => {
        return () => {
            if (submitRef.current) {
                clearTimeout(submitRef.current);
            }
        };
    }, []);

    return { initLoader };
};

export const usePaginatedTable = ({
    targetsChunk,
    setTargetsChunk,
    targetType,
    formattedData,
    showPrimaryClass,
    perPage,
}: {
    targetsChunk: any[];
    setTargetsChunk: any;
    targetType:
        | typeof MULTI
        | typeof STUDENT
        | typeof EMPLOYEE
        | typeof CONTRACTOR;
    formattedData?: (data: any) => any[];
    showPrimaryClass?: boolean;
    perPage?: number;
}) => {
    const [filter, setFilter] = useState<Record<string, any>>({
        ...CHUNKED_FILTER_PARAMS,
        per_page: perPage ?? CHUNKED_FILTER_PARAMS.per_page,
    });
    const [pagination, setPagination] = useState<any>();

    const [clonedFilteredChunk, setClonedFilteredChunk] = useState<
        any[] | null
    >(null);
    // used to compare if the filter has changed
    const [prevFilter, setPrevFilter] = useState<Record<string, any>>();

    function onAdd(
        selections: any[],
        multiType?: typeof STUDENT | typeof EMPLOYEE | typeof CONTRACTOR
    ) {
        resetFilter();
        const currentTargets = flatten(targetsChunk);
        const currentTargetIds = new Set(
            currentTargets.map((target: any) => target.id)
        );

        let newSelections: any[] = [];
        switch (targetType) {
            case MULTI:
                newSelections = selections.map((target) => ({
                    id: multiType + target?.id,
                    target_id: target?.id,
                    type: capitalize(multiType),
                    name: combinedNames(target?.translations?.name),
                    number:
                        target?.student_number ??
                        target?.employee_number ??
                        target?.contractor_number,
                }));
                break;
            case STUDENT:
                newSelections = selections.map((target) => ({
                    id: target?.id,
                    name: combinedNames(target?.translations?.name),
                    student_number: target?.student_number,
                    ...(showPrimaryClass
                        ? {
                              primary_class: combinedNames(
                                  target?.current_primary_class?.semester_class
                                      ?.class_model?.translations?.name
                              ),
                          }
                        : {}),
                }));
                break;
            case EMPLOYEE:
                newSelections = selections.map((target) => ({
                    id: target?.id,
                    name: combinedNames(target?.translations?.name),
                    employee_number: target?.employee_number,
                }));
                break;
            case CONTRACTOR:
                newSelections = selections.map((target) => ({
                    id: target?.id,
                    name: combinedNames(target?.translations?.name),
                    contractor_number: target?.contractor_number,
                }));
                break;
            default:
                newSelections = formattedData ? formattedData(selections) : [];
                break;
        }
        const newTargets = newSelections.filter(
            (target) => !currentTargetIds.has(target.id)
        );

        const updatedTargets = [...currentTargets, ...newTargets];
        const newChunkedTargets = chunk(updatedTargets, filter.per_page);

        setTargetsChunk(newChunkedTargets);
        onSetPagination(updatedTargets.length);
    }

    function onRemove(id) {
        resetFilter();
        const currentTargets = flatten(targetsChunk);
        const updatedTargets = currentTargets.filter(
            (target: any) => target.id !== id
        );

        const newChunkedTargets = chunk(updatedTargets, filter.per_page);
        setTargetsChunk(newChunkedTargets);
        onSetPagination(updatedTargets.length, pagination.current_page);
    }

    function onSetPagination(totalItems: number, currentPage?: number) {
        const itemsPerPage = filter.per_page;
        const lastPage = Math.ceil(totalItems / itemsPerPage);
        setPagination({
            current_page: currentPage
                ? currentPage > lastPage
                    ? lastPage
                    : currentPage
                : lastPage,
            last_page: lastPage,
            per_page: itemsPerPage,
            total: totalItems,
        });
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });

        let total = 0;

        if (clonedFilteredChunk) {
            let clonedSortedTargets = sortBy(flatten(clonedFilteredChunk), [
                name,
            ]);
            if (direction === DESC) {
                clonedSortedTargets = clonedSortedTargets.reverse();
            }
            setClonedFilteredChunk(chunk(clonedSortedTargets, filter.per_page));
            total = clonedSortedTargets.length;
        } else {
            const flattenTargets = flatten(targetsChunk);
            let sortedTargets = sortBy(flattenTargets, [name]);

            if (direction === DESC) {
                sortedTargets = sortedTargets.reverse();
            }
            setTargetsChunk(chunk(sortedTargets, filter.per_page));
            total = flattenTargets.length;
        }
        onSetPagination(total, 1);
    }

    function resetFilter() {
        const hasOtherFilter = prevFilter?.name || prevFilter?.number;
        if (!hasOtherFilter) return;

        const newFilter: any = { ...filter, page: 1 };
        delete newFilter.name;
        delete newFilter.number;
        delete newFilter.order_by;
        setFilter(newFilter);

        setPrevFilter(undefined);
        setClonedFilteredChunk(null);
    }

    function resetClonedFilter() {
        resetFilter();
        onSetPagination(flatten(targetsChunk).length, 1);
    }

    function hasOtherNewFilter() {
        let allTargets = flatten(targetsChunk);
        const hasNewFilter =
            prevFilter?.name !== filter.name ||
            prevFilter?.number !== filter.number;
        const hasFilter = filter.name || filter.number;

        if (hasNewFilter && hasFilter) {
            if (filter.number) {
                allTargets = allTargets.filter((target) => {
                    const targetNumber =
                        targetType === STUDENT
                            ? target?.student_number
                            : targetType === EMPLOYEE
                              ? target?.employee_number
                              : target?.number;
                    return targetNumber?.includes(filter.number);
                });
            }
            if (filter.name) {
                allTargets = allTargets.filter((target) => {
                    return target?.name
                        ?.toLowerCase()
                        .includes(filter.name.toLowerCase());
                });
            }

            const newChunkedTargets = chunk(allTargets, filter.per_page);
            setClonedFilteredChunk(newChunkedTargets);

            onSetPagination(allTargets.length, hasNewFilter ? 1 : filter.page);
            setPrevFilter(filter);
            return true;
        } else if (hasFilter) {
            return false;
        } else if (hasNewFilter) {
            resetClonedFilter();
            return false;
        }
        return false;
    }

    function hasNewSort() {
        const isNew = prevFilter?.order_by !== filter.order_by;
        setPrevFilter(filter);
        return isNew;
    }

    useEffect(() => {
        if (hasOtherNewFilter()) return;
        if (hasNewSort()) return;
        // when start page
        if (!pagination && targetsChunk?.length > 0) {
            onSetPagination(flatten(targetsChunk).length, 1);
            return;
        }
        // for change page
        setPagination({
            ...pagination,
            current_page: filter.page,
        });
    }, [filter]);

    return {
        filter,
        setFilter,
        pagination,
        setPagination,
        onAdd,
        onRemove,
        clonedFilteredChunk,
        resetClonedFilter,
        onSort,
        onSetPagination,
    };
};

export const useCheckViewPermit = (viewPermit) => {
    const router = useRouter();

    const userProfile = useUserProfile((state) => state.userProfile);
    const hasPermit = useUserProfile((state) => state.hasPermit);

    useEffect(() => {
        if (userProfile) {
            const permit = hasPermit(viewPermit);
            if (!permit) {
                router.replace("/");
            }
        }
    }, [userProfile]);
};

export const useMerchants = ({ params }) => {
    const { userProfile, hasPermit, hasMerchantType } = useUserProfile(
        (state) => state
    );

    const locale = useLocale();
    const [merchantOptions, setMerchantOptions] = useState<any[]>([]);
    const lastParamsRef = useRef(null);

    const { axiosQuery: getMerchants, isLoading: isLoadingMerchants } =
        useAxios({
            api: merchantAPI,
            locale,
            onSuccess: (res) => {
                if (hasMerchantType() && !hasPermit(view_all_merchant)) {
                    const filteredMerchants = res.data.filter((merchant) =>
                        userProfile?.userables?.some(
                            (userable) => userable.userable_id === merchant.id
                        )
                    );
                    setMerchantOptions(filteredMerchants);
                } else if (hasPermit(view_all_merchant)) {
                    setMerchantOptions(res.data);
                }
            },
        });

    useEffect(() => {
        if (userProfile && !isEmpty(params)) {
            if (!isEqual(params, lastParamsRef.current)) {
                lastParamsRef.current = params;
                getMerchants({ params });
            }
        }
    }, [userProfile, params]);

    return { merchantOptions, isLoadingMerchants };
};
