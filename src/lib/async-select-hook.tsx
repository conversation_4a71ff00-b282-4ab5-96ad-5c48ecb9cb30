import { useLocale } from "next-intl";
import { useCallback, useState } from "react";
import { useAxios } from "./hook";
import { axiosInstance } from "./api";
import { optionUserLabel } from "./utils";
import { debounce } from "lodash";

export const useAsyncSelect = ({
    api,
    labelFormatter,
    optionFormatter,
    params,
    useCommonSearch,
    useTitle,
    isStudentNumber,
    showGuardians,
}: {
    api: string;
    labelFormatter?: (option) => void;
    optionFormatter?: (option) => void;
    params?: Record<string, any>;
    useCommonSearch?: boolean;
    useTitle?: boolean;
    isStudentNumber?: boolean;
    showGuardians?: boolean;
}) => {
    const locale = useLocale();
    const [asyncOptions, setAsyncOptions] = useState<any[]>([]);

    const { handleError } = useAxios({ api });

    const loadAsyncOptions = (inputValue, callback) => {
        const defaultParams = {
            per_page: 50,
        };
        if (useCommonSearch) {
            defaultParams["common_search"] = inputValue;
        } else if (useTitle) {
            defaultParams["title"] = inputValue;
        } else {
            defaultParams["name"] = inputValue;
        }
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(api, {
                    params: {
                        ...defaultParams,
                        ...(params ?? {}),
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const result = res.data.data;
                    const options = result.map((option) => {
                        if (optionFormatter) {
                            return optionFormatter(option);
                        }
                        return {
                            label: labelFormatter
                                ? labelFormatter(option)
                                : optionUserLabel(
                                      option?.number ??
                                          option?.student_number ??
                                          option?.employee_number,
                                      option?.translations?.name
                                  ),
                            value: isStudentNumber
                                ? option?.student_number
                                : option?.id,
                            photo: option?.photo,
                            name: option.name,
                            guardians: showGuardians ? option?.guardians : null,
                            activeHostelBedAssignments:
                                option?.active_hostel_bed_assignment,
                        };
                    });
                    callback(options);
                })
                .catch((error) => {
                    handleError(error);
                });
        } else {
            callback([]);
        }
    };

    const debouncedFetchOptions = useCallback(
        debounce(loadAsyncOptions, 300),
        []
    );

    return {
        asyncOptions,
        setAsyncOptions,
        loadAsyncOptions: debouncedFetchOptions,
    };
};
