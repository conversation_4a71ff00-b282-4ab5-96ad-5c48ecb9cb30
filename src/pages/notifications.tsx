import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterNotificationForm from "@/components/forms/FilterNotificationForm";
import Layout from "@/components/Layout";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import { inboxAPI, TableColumnType, DATE_FORMAT } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useEffect, useState } from "react";
import DOMPurify from "isomorphic-dompurify";
import Link from "next/link";
import { Paperclip } from "lucide-react";
import { displayDateTime } from "@/lib/utils";

const Notifications = ({ locale }) => {
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
        order_by: {
            created_at: "desc",
        },
    });
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);

    const [targetNotice, setTargetNotice] = useState<Record<
        string,
        any
    > | null>(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data: inboxAnnouncements, axiosQuery: getInboxAnnouncements } =
        useAxios({
            api: inboxAPI,
            locale,
            onSuccess: (result) => setPagination(result?.pagination),
        });

    function fetchInboxAnnouncements() {
        getInboxAnnouncements({
            params: filter,
        });
    }

    useEffect(() => {
        fetchInboxAnnouncements();
    }, []);

    function definedData() {
        return Array.isArray(inboxAnnouncements)
            ? inboxAnnouncements.map((item) => ({
                  id: item?.id,
                  title: item?.title,
                  created_at: displayDateTime(
                      item?.created_at,
                      DATE_FORMAT.forDisplay
                  ),
                  is_read: item?.read_at ? "Yes" : "No",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    useEffect(() => {
        if (inboxAnnouncements) {
            const _columns: TableColumnType[] = [
                {
                    key: "title",
                    hasSort: true,
                },
                {
                    key: "created_at",
                    displayAs: "Date",
                    hasSort: true,
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "is_read",
                    modify: (value) => (
                        <div className="min-w-[54px]">{value}</div>
                    ),
                },
            ];
            setColumns(_columns);
        }
    }, [inboxAnnouncements]);

    useEffect(() => {
        fetchInboxAnnouncements();
    }, [filter, locale]);

    return (
        <Layout path="notifications" locale={locale}>
            <Card styleClass="table-card">
                <div className="table-page-top">
                    <h2 className="mr-auto capitalize">Notifications</h2>

                    <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                    />
                </div>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => {
                        return (
                            <>
                                <ActionDropdown>
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() => {
                                            const notice =
                                                inboxAnnouncements.find(
                                                    (item) =>
                                                        item.id ==
                                                        cell.row.original.id
                                                );
                                            setTargetNotice(notice);
                                        }}
                                    >
                                        View
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        Delete
                                    </DropdownMenuItem>
                                </ActionDropdown>
                            </>
                        );
                    }}
                />
            </Card>

            {/* view */}
            <Modal
                open={targetNotice}
                onOpenChange={setTargetNotice}
                size="medium"
            >
                <div className="w-full py-6">
                    <h3 className="mb-1 font-semibold text-themeBlack">
                        {targetNotice?.title}
                    </h3>
                    <div className="mb-3 text-[14px] text-gray-500">
                        {new Date(targetNotice?.created_at).toDateString()}
                    </div>
                    <div
                        className="border-t pt-2"
                        dangerouslySetInnerHTML={{
                            __html: DOMPurify.sanitize(targetNotice?.message),
                        }}
                    />
                    {targetNotice?.attachments?.length > 0 && (
                        <div className="mb-1 mt-3 border-t pt-2 text-[14px] font-medium text-themeLabel">
                            Attachments
                        </div>
                    )}
                    {targetNotice?.attachments?.map((url, index) => (
                        <Link
                            key={`${url} + ${index}`}
                            href={url}
                            className="mb-1.5 flex w-fit items-start gap-x-1.5"
                            target="_blank"
                        >
                            <Paperclip size={16} className="text-gray-500" />
                            <div className="text-sm leading-none underline">
                                File {index + 1}
                            </div>
                        </Link>
                    ))}
                </div>
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={inboxAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchInboxAnnouncements}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterNotificationForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default Notifications;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
