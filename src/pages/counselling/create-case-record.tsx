import React from "react";
import Layout from "@/components/Layout";
import CreateCaseRecordForm from "@/components/forms/counselling/CreateCaseRecordForm";
import { useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";

const CreateCaseRecord = ({ locale }) => {
    useCheckViewPermit("counselling-case-record-create");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    return (
        <Layout locale={locale} path="counselling/create-case-record">
            {hasPermit("counselling-case-record-create") && (
                <CreateCaseRecordForm />
            )}
        </Layout>
    );
};

export default CreateCaseRecord;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
