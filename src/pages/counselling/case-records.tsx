import { useEffect, useState } from "react";
import { capitalize, isArray } from "lodash";
import { useRouter } from "next/router";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import CaseRecordForm from "@/components/forms/counselling/CaseRecordForm";
import FilterCaseRecordForm from "@/components/forms/counselling/FilterCaseRecordForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    TableColumnType,
    DATE_FORMAT,
    counsellingCaseRecordAPI,
    DEFAULT_FILTER_PARAMS,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNamesCell,
    convertDateTime,
    displayDateTime,
    optionUserLabel,
    refreshForUpdate,
} from "@/lib/utils";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import FilterChevronButton from "@/components/ui/FilterChevronButton";

const CaseRecords = ({ locale }) => {
    useCheckViewPermit("counselling-case-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const router = useRouter();
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getCaseRecords } = useAxios({
        api: counsellingCaseRecordAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "visit_datetime",
                modify: (value) => {
                    return <span className="text-sm">{value}</span>;
                },
                hasSort: true,
            },
            {
                key: "student_number",
            },
            {
                key: "student_name",
                modify(value) {
                    return combinedNamesCell(value);
                },
            },
            {
                key: "note",
                modify: (value) => (
                    <div className="min-w-[200px] text-sm leading-tight">
                        {value}
                    </div>
                ),
            },
            {
                key: "created_by",
                modify: (value) => (
                    <div className="min-w-[120px] text-sm leading-tight">
                        {value}
                    </div>
                ),
            },
            {
                key: "status",
                hasSort: true,
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  visit_datetime: item.visit_datetime
                      ? convertDateTime(item.visit_datetime)
                      : "-",
                  student_number: item?.student?.student_number,
                  student_name: Object.values(
                      item?.student?.translations?.name ?? {}
                  ),
                  note: item?.note,
                  created_by: optionUserLabel(
                      item?.created_by?.employee_number,
                      item?.created_by?.translations?.name
                  ),
                  status: capitalize(item?.status),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setTargetId(null);
    }

    function fetchCaseRecords() {
        getCaseRecords({
            params: {
                includes: [
                    "student",
                    "createdBy",
                    "student.currentSemesterPrimaryClass.semesterClass.classModel",
                ],
                order_by: {
                    visit_datetime: "desc",
                    created_at: "desc",
                },
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchCaseRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <>
            <Layout locale={locale} path="counselling/case-records">
                <Card styleClass="table-card">
                    <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                        <h2>Case Records</h2>
                        <div className="flex items-center gap-x-3">
                            {hasPermit("counselling-case-record-create") && (
                                <Button
                                    onClick={() =>
                                        router.push(
                                            "/counselling/create-case-record"
                                        )
                                    }
                                >
                                    Add Case Record
                                </Button>
                            )}
                            {/* <TableFilterBtn
                                filter={filter}
                                onClick={() => setOpenFilter(true)}
                            /> */}
                            <FilterChevronButton />
                        </div>
                    </div>

                    <FilterFormWrapper>
                        <FilterCaseRecordForm
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                        />
                    </FilterFormWrapper>

                    <DataTable
                        columns={columns}
                        data={definedData(data)}
                        pagination={pagination}
                        setPagination={setPagination}
                        changePage={(arg) => setFilter({ ...filter, ...arg })}
                        sorted={filter?.order_by}
                        sort={onSort}
                        actionMenu={({ cell }) => (
                            <ActionDropdown>
                                {hasPermit(
                                    "counselling-case-record-update"
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        Edit / View
                                    </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                {hasPermit(
                                    "counselling-case-record-delete"
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        Delete
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        )}
                    />
                </Card>

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                    <CaseRecordForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={counsellingCaseRecordAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={fetchCaseRecords}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterCaseRecordForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </Layout>
        </>
    );
};

export default CaseRecords;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
