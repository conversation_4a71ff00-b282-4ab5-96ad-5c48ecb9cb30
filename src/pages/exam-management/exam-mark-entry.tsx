import { useEffect, useRef, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import FreeInput from "@/components/ui/FreeInput";
import FreeInputInterger from "@/components/ui/FreeInputInterger";
import {
    examMarkEntryGetClassesAPI,
    examMarkEntryGetExamAPI,
    examMarkEntryGetStudentsAPI,
    examMarkEntryGetSubjectAPI,
    examMarkEntryReopenAPI,
    examMarkEntrySaveAPI,
    GET_ALL_PARAMS,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    combinedNames,
    optionUserLabel,
    showBackendFormError,
} from "@/lib/utils";
import Modal from "@/components/ui/Modal";
import { DialogFooter } from "@/components/base-ui/dialog";
import { Check, Loader2, X } from "lucide-react";
import toast from "react-hot-toast";
import FreeTable from "@/components/ui/FreeTable";
import { useTranslations } from "next-intl";

const ExamMarkEntry = ({ locale }) => {
    useCheckViewPermit("exam-results-data-entry-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const inputHighlightRef = useRef<any>({});
    const timerRef = useRef({});

    const [students, setStudents] = useState<any[]>([]);
    const [targetReopen, setTargetReopen] = useState<any | null>(null);
    const [hasSearched, setHasSearched] = useState(false);
    const [tableTitle, setTableTitle] = useState("");
    const [loadingList, setLoadingList] = useState<any[]>([]);
    const [successList, setSuccessList] = useState<any[]>([]);
    const [errorList, setErrorList] = useState<any[]>([]);

    const { data: examOptions, axiosQuery: getEligibleExamOptions } = useAxios({
        api: examMarkEntryGetExamAPI,
        locale,
    });

    const { data: subjectOptions, axiosQuery: getEligibleSubjectOptions } =
        useAxios({
            api: examMarkEntryGetSubjectAPI,
            locale,
            onError: () => close(),
        });

    const { data: classOptions, axiosQuery: getEligibleClassOptions } =
        useAxios({
            api: examMarkEntryGetClassesAPI,
            locale,
            onError: () => close(),
        });

    function focusAndHighlightInput(fieldName) {
        form.setFocus(fieldName);
        inputHighlightRef.current[fieldName]?.select();
    }

    function definedColumns(activeLanguages) {
        const nameColumns = activeLanguages?.map(
            (lang): TableColumnType => ({
                key: lang.code,
                displayAs: `Student Name (${lang?.name})`,
            })
        );

        const allComponents: any = students.length
            ? Array.from(
                  new Map(
                      students.flatMap((student) =>
                          student.components.map((c) => [c.component_code, c])
                      )
                  ).values()
              )
            : [];

        const componentColumns =
            students[0].grading_type === "SCORE"
                ? allComponents.map(({ component_code, component_name }) => ({
                      key: `${component_code}`,
                      displayAs: combinedNames(component_name),

                      modify: (_, index) => {
                          const studentIndex = index;
                          const studentComponents =
                              students[studentIndex]?.components;

                          const componentIndex = studentComponents?.findIndex(
                              (c) => c.component_code === component_code
                          );

                          if (componentIndex === -1) return null;

                          const student = students[studentIndex];
                          const studentIdenfier = getStudentIdentifier(
                              student?.result_source_subject_id,
                              student?.student_id
                          );

                          return (
                              <div className="relative min-w-[100px]">
                                  <FreeInputInterger
                                      hasLabel={false}
                                      control={form.control}
                                      min={0}
                                      name={`students[${studentIndex}].components[${componentIndex}].score`}
                                      disabled={
                                          students[studentIndex]
                                              ?.data_entry_status ===
                                              "POSTED" ||
                                          students[studentIndex]
                                              ?.is_exempted === true
                                      }
                                      error={
                                          form.formState.errors?.students?.[
                                              studentIndex
                                          ]?.components?.[componentIndex]?.score
                                      }
                                      onKeyDown={(e) => {
                                          if (e.key === "f") {
                                              e.preventDefault();
                                              form.setValue(
                                                  `students[${studentIndex}].components[${componentIndex}].score`,
                                                  100
                                              );
                                              onSubmit(studentIndex);
                                              const fieldName = `students[${studentIndex + 1}].components[${componentIndex}].score`;
                                              focusAndHighlightInput(fieldName);
                                          }
                                          if (e.key === "Tab") {
                                              e.preventDefault();
                                              onSubmit(studentIndex);
                                              const fieldName = `students[${studentIndex + 1}].components[${componentIndex}].score`;
                                              focusAndHighlightInput(fieldName);
                                          }
                                      }}
                                      onMouseDown={(e) => {
                                          e.preventDefault();
                                          const fieldName = `students[${studentIndex}].components[${componentIndex}].score`;
                                          focusAndHighlightInput(fieldName);
                                      }}
                                      onBlur={(e) => {
                                          e.preventDefault();
                                          onSubmit(studentIndex);
                                      }}
                                      refs={inputHighlightRef}
                                      onChange={(val) => {
                                          if (val === "") {
                                              return;
                                          }

                                          val = Number(val);

                                          if (val.toString().includes(".")) {
                                              val = Math.round(val);
                                          }

                                          if (val > 100) {
                                              val = 100;
                                          }

                                          form.setValue(
                                              `students[${studentIndex}].components[${componentIndex}].score`,
                                              val
                                          );
                                          if (val.toString().length >= 2) {
                                              onSubmit(studentIndex);
                                              const fieldName = `students[${studentIndex + 1}].components[${componentIndex}].score`;
                                              focusAndHighlightInput(fieldName);
                                          }
                                      }}
                                  />
                                  {loadingList.includes(studentIdenfier) ? (
                                      <Loader2
                                          size={24}
                                          className="pointer-events-none absolute right-2 top-[9px] animate-spin text-gray-400"
                                      />
                                  ) : successList.includes(studentIdenfier) ? (
                                      <Check
                                          size={24}
                                          className="pointer-events-none absolute right-2 top-[9px] text-themeGreen"
                                      />
                                  ) : errorList.includes(studentIdenfier) ? (
                                      <X
                                          size={24}
                                          className="pointer-events-none absolute right-2 top-[9px] text-red-500"
                                      />
                                  ) : null}
                              </div>
                          );
                      },
                  }))
                : [
                      {
                          key: "grade",
                          displayAs: "value",
                          modify: (_, cell) => {
                              const rowIndex = cell?.row?.index;
                              return (
                                  <FreeInput
                                      hasLabel={false}
                                      control={form.control}
                                      name={`students[${rowIndex}].grade`}
                                      disabled={
                                          students[rowIndex]
                                              ?.data_entry_status ===
                                              "POSTED" ||
                                          students[rowIndex]?.is_exempted ===
                                              true
                                      }
                                      error={
                                          form.formState.errors?.students?.[
                                              rowIndex
                                          ]?.grade
                                      }
                                  />
                              );
                          },
                      },
                  ];

        const selectedClass = classOptions?.find(
            (option) => option.id === form.watch("class_id")
        );
        const hideSeatNo = ["SOCIETY", "ELECTIVE"].includes(
            selectedClass?.type
        );

        const columns: TableColumnType[] = [
            ...(!hideSeatNo
                ? [
                      {
                          key: "seat_no",
                          modify: (value) => value ?? "-",
                      },
                  ]
                : []),
            {
                key: "student_number",
            },
            ...nameColumns,
            {
                key: "primary_class",
                modify: (value) => value ?? "-",
            },
            {
                key: "grading_type",
            },
            ...componentColumns,
            {
                key: "_",
                displayAs: "",
                modify: (value, index) => {
                    const student = students[index];
                    return (
                        <>
                            {student?.data_entry_status == "POSTED" &&
                                hasPermit("exam-results-data-entry-reopen") && (
                                    <ActionDropdown>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetReopen(student);
                                            }}
                                        >
                                            {t("Reopen Mark Entry")}
                                        </DropdownMenuItem>
                                    </ActionDropdown>
                                )}
                        </>
                    );
                },
            },
        ];

        return columns;
    }

    function definedData(students) {
        return students.map((student, index) => ({
            seat_no: student?.seat_no,
            student_number: student.student_number,
            primary_class: student?.primary_class,
            grading_type: student.grading_type,
            ...student?.student_name,
            ...form.watch(`students[${index}]`), // Syncs with form state
        }));
    }

    const { axiosQuery: getEligibleStudents } = useAxios({
        api: examMarkEntryGetStudentsAPI,
        locale,
        onSuccess(result) {
            if (!result.data) {
                setStudents([]);
                return;
            }

            form.setValue(
                "students",
                result.data.map((student) => ({
                    student_id: student.student_id,
                    grading_type: student.grading_type,
                    result_source_subject_id: student.result_source_subject_id,
                    data_entry_status: student.data_entry_status,
                    is_exempted: student.is_exempted,
                    ...(student.grading_type === "GRADE"
                        ? { grade: student.actual_score_grade ?? "" }
                        : {
                              components:
                                  student.components?.map((component) => ({
                                      result_source_subject_component_id:
                                          component.component_id,
                                      score:
                                          component.component_actual_score ??
                                          "",
                                  })) ?? [],
                          }),
                }))
            );

            const selectedExam = examOptions?.find(
                (opt) => opt.id === form.watch("exam_id")
            )?.name;
            const selectedSubject = subjectOptions?.find(
                (opt) => opt.id === form.watch("subject_id")
            )?.name;
            const selectedClass = classOptions?.find(
                (opt) => opt.id === form.watch("class_id")
            )?.name;

            setStudents(result.data);
            setHasSearched(true);
            setTableTitle(
                `${selectedExam} - ${selectedSubject} (${selectedClass})`
            );
        },
    });

    const { axiosPost: saveExamMarkEntry } = useAxios({
        api: examMarkEntrySaveAPI,
        noLoading: true,
        noToast: true,
        onSuccess: (result) => {
            const studentIdentifier = getStudentIdentifier(
                result.data?.result_source_subject_id,
                result.data?.student_id
            );

            setLoadingList(
                loadingList.filter((id) => id !== studentIdentifier)
            );

            if (!studentIdentifier) return;

            setSuccessList((prev) => [...prev, studentIdentifier]);

            if (timerRef.current[studentIdentifier]) {
                clearTimeout(timerRef.current[studentIdentifier]);
            }

            timerRef.current[studentIdentifier] = setTimeout(() => {
                setSuccessList((prev) =>
                    prev.filter((id) => id !== studentIdentifier)
                );
            }, 5000);
        },
        onError: (_, payload) => {
            const studentIdentifier = getStudentIdentifier(
                payload?.result_source_subject_id,
                payload?.student_id
            );

            setLoadingList(
                loadingList.filter((id) => id !== studentIdentifier)
            );

            if (!studentIdentifier) {
                toast.error("An error occurred while saving the mark entry.");
                return;
            }

            setErrorList((prev) => [...prev, studentIdentifier]);

            if (timerRef.current[studentIdentifier]) {
                clearTimeout(timerRef.current[studentIdentifier]);
            }

            timerRef.current[studentIdentifier] = setTimeout(() => {
                setErrorList((prev) =>
                    prev.filter((id) => id !== studentIdentifier)
                );
            }, 5000);
        },
    });

    const { axiosPost: reopenExamMarkEntry, error: postReopenError } = useAxios(
        {
            api: examMarkEntryReopenAPI,
            toastMsg: "Reopen successfully",
            noLoading: true,
            onSuccess() {
                setTargetReopen(null);
                handleViewStudents();
            },
        }
    );

    function handleViewStudents() {
        getEligibleStudents({
            params: {
                subject_id: form.watch("subject_id"),
                class_id: form.watch("class_id"),
                exam_id: form.watch("exam_id"),
            },
        });
    }

    function getStudentIdentifier(studentResultSourceSubjectId, studentId) {
        if (!studentResultSourceSubjectId || !studentId) {
            return null;
        }
        const studentIdentifier = `${studentResultSourceSubjectId ?? ""}-${studentId ?? ""}`;
        return studentIdentifier === "-" ? null : studentIdentifier;
    }

    function onSubmit(studentIndex) {
        const students = form.getValues("students");
        const student = students[studentIndex];
        if (student.grading_type === "GRADE") {
            const payload = {
                student_id: student.student_id,
                result_source_subject_id: student.result_source_subject_id,
                grade: student.grade,
            };
            saveExamMarkEntry(payload);
        }

        if (student.grading_type === "SCORE") {
            for (const component of student.components) {
                const payload = {
                    student_id: student.student_id,
                    result_source_subject_id: student.result_source_subject_id,
                    ...component,
                };
                saveExamMarkEntry(payload, { returnPayloadOnError: true });
            }
        }
        const studentIdentifier = getStudentIdentifier(
            student.result_source_subject_id,
            student.student_id
        );
        if (studentIdentifier && !loadingList.includes(studentIdentifier)) {
            setLoadingList([...loadingList, studentIdentifier]);
        }
    }

    const form = useForm<any>({
        defaultValues: {
            exam_id: "",
            subject_id: "",
            class_id: "",
            students: [],
        },
    });

    useEffect(() => {
        showBackendFormError(form, postReopenError);
    }, [postReopenError]);

    useEffect(() => {
        getEligibleExamOptions({ params: { ...GET_ALL_PARAMS } });
        getEligibleSubjectOptions({ params: { ...GET_ALL_PARAMS } });
    }, []);

    useEffect(() => {
        if (form.watch("subject_id")) {
            getEligibleClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    subject_id: form.watch("subject_id"),
                },
            });
        }
    }, [form.watch("subject_id")]);

    useEffect(() => {
        const subscription = form.watch((value, { name }) => {
            if (name && ["subject_id", "class_id", "exam_id"].includes(name)) {
                setHasSearched(false);
            }
        });

        return () => subscription.unsubscribe();
    }, [form]);

    const t = useTranslations("common");

    return (
        <Layout path="exam-management/exam-mark-entry" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">{t("Exam Mark Entry")}</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="exam_id"
                            label={t("exam") + "*"}
                            options={examOptions}
                        />

                        <FormSelect
                            control={form.control}
                            name="subject_id"
                            label={t("subject") + "*"}
                            options={
                                subjectOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.code} - ${option?.name}`,
                                })) ?? []
                            }
                            onChange={() => {
                                form.setValue("class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="class_id"
                            label={t("class") + "*"}
                            options={
                                classOptions?.map((option) => ({
                                    id: option?.id,
                                    name:
                                        option?.type === "SOCIETY"
                                            ? `${option?.code} - ${option?.name}`
                                            : combinedNames(
                                                  option?.translations?.name
                                              ),
                                })) ?? []
                            }
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button
                                onClick={handleViewStudents}
                                variant={"outline"}
                            >
                                {t("View Students")}
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(students) ? (
                        <p className="h-20 text-center text-themeLabel">
                            {t("No Record Found")}
                        </p>
                    ) : (
                        <div className="overflow-auto">
                            <div className="report-table-title">
                                {tableTitle}
                            </div>
                            <FreeTable
                                columns={definedColumns(activeLanguages)}
                                data={definedData(students)}
                            />

                            <div className="mb-10 mt-2">
                                <div className="text-[14px] text-gray-500">
                                    <span className="font-medium">
                                        {students?.length}
                                    </span>{" "}
                                    {t("students found")}
                                </div>
                            </div>

                            {/* reopen mark entry */}
                            <Modal
                                open={targetReopen}
                                onOpenChange={setTargetReopen}
                            >
                                <p className="mt-3 font-medium leading-relaxed">
                                    {t(
                                        "Are you sure you want to reopen mark entry for this student?"
                                    )}
                                    <br />{" "}
                                    {optionUserLabel(
                                        targetReopen?.student_number,
                                        targetReopen?.student_name
                                    )}
                                </p>
                                <DialogFooter className={"mt-2"}>
                                    <Button
                                        variant="outline"
                                        onClick={() => setTargetReopen(null)}
                                    >
                                        {t("Cancel")}
                                    </Button>
                                    <Button
                                        onClick={() => {
                                            if (
                                                targetReopen?.result_source_subject_id
                                            ) {
                                                reopenExamMarkEntry({
                                                    result_source_subject_id:
                                                        targetReopen.result_source_subject_id,
                                                });
                                            }
                                        }}
                                    >
                                        {t("Confirm")}
                                    </Button>
                                </DialogFooter>
                            </Modal>
                        </div>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default ExamMarkEntry;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
