import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { isEmpty } from "lodash";
import { ChevronDown } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import FreeSelect from "@/components/ui/FreeSelect";
import {
    examPromotionMarkAPI,
    GET_ALL_PARAMS,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { arrayContainsAny, showBackendFormError } from "@/lib/utils";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import FreeInputDecimal from "@/components/ui/FreeInputDecimal";

const _promotion_mark_create = "promotion-mark-create";

const ExamPromotionMark = ({ locale }) => {
    useCheckViewPermit("promotion-mark-view");

    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [groupedClasses, setGroupedClasses] = useState({});
    const [collapsedGrades, setCollapsedGrades] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
            classes: [],
        },
    });

    function onSearchSemester() {
        form.clearErrors("classes");
        setIsLoading(true);
        setGroupedClasses({});
        getSemesterClasses({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassPrimaryDropdownFilter(locale),
                semester_setting_id: form.getValues().semester_setting_id,
                is_active: 1,
            },
        });
    }

    const { data: semesterSettings, axiosQuery: getSemesterOptions } = useAxios(
        {
            api: semesterSettingAPI,
            locale,
            onSuccess: (response) => {
                const currentSemester = response.data.find(
                    (option) => option.is_current_semester === true
                );
                if (currentSemester) {
                    form.setValue("semester_setting_id", currentSemester.id);
                }
            },
        }
    );

    const { axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
        onSuccess: (res) => {
            const grouped = {};

            res.data.forEach((item) => {
                const gradeName =
                    item?.class_model?.grade?.translations?.name?.[locale] ??
                    item?.class_model?.grade?.name;
                if (!grouped[gradeName]) {
                    grouped[gradeName] = {
                        sequence: item?.class_model?.grade?.sequence,
                        classes: [],
                    };
                }
                grouped[gradeName].classes.push({
                    id: item?.id,
                    name:
                        item?.class_model?.translations?.name?.[locale] ??
                        item?.class_model?.name,
                });
            });

            const sortedGrouped = Object.keys(grouped)
                .sort((a, b) => grouped[b].sequence - grouped[a].sequence)
                .reduce((acc, key) => {
                    acc[key] = grouped[key];
                    return acc;
                }, {});

            let index = 0;
            for (const gradeKey in sortedGrouped) {
                form.register(`_${gradeKey}_net_average_for_promotion`);
                form.register(`_${gradeKey}_conduct_mark_for_promotion`);

                form.setValue(`_${gradeKey}_net_average_for_promotion`, "");
                form.setValue(`_${gradeKey}_conduct_mark_for_promotion`, "");

                sortedGrouped[gradeKey].classes.forEach((classObj) => {
                    classObj.index = index++;
                    form.setValue(`classes[${classObj.index}]`, {
                        semester_class_id: classObj.id,
                        net_average_for_promotion: "",
                        conduct_mark_for_promotion: "",
                    });
                });
            }

            setGroupedClasses(sortedGrouped);
            setIsLoading(false);
        },
        onError: () => setIsLoading(false),
    });

    const { axiosQuery: getExamPromotionMark } = useAxios({
        api: examPromotionMarkAPI,
        onSuccess: (result) => {
            const classList = Object.values(groupedClasses).flatMap(
                (grade: any) => grade.classes
            );

            result.data.forEach((item) => {
                const matchedClass = classList.find(
                    (classObj) => classObj.id == item.semester_class_id
                );
                if (matchedClass) {
                    form.setValue(
                        `classes[${matchedClass.index}].net_average_for_promotion`,
                        item.net_average_for_promotion
                    );
                    form.setValue(
                        `classes[${matchedClass.index}].conduct_mark_for_promotion`,
                        item.conduct_mark_for_promotion
                    );
                }
            });
        },
    });

    useEffect(() => {
        if (!isEmpty(groupedClasses)) {
            getExamPromotionMark({
                params: {
                    ...GET_ALL_PARAMS,
                    semester_setting_id: form.getValues("semester_setting_id"),
                },
            });
        }
    }, [groupedClasses]);

    const { axiosPut: updateExamPromotionMark, error: putError } = useAxios({
        api: examPromotionMarkAPI + "/bulk-create-or-update",
    });

    const toggleCollapse = (grade: string) => {
        setCollapsedGrades((prev) =>
            prev.includes(grade)
                ? prev.filter((item) => item !== grade)
                : [...prev, grade]
        );
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors("classes");
        initLoader(
            form.handleSubmit((data: any) => {
                if (data.semester_year_setting_id) {
                    delete data.semester_year_setting_id;
                }

                console.log("data.classes", data.classes);
                updateExamPromotionMark({ data: data.classes });
            })
        );
    }

    function expandTabWithError() {
        const _errorIndexes = Array.isArray(form.formState.errors?.classes)
            ? form.formState.errors.classes
                  .map((classError, index) =>
                      isEmpty(classError) ? null : index
                  )
                  .filter((classIndex) => classIndex !== null)
            : [];

        Object.keys(groupedClasses).forEach((grade) => {
            const classIndexes = [
                ...groupedClasses[grade].classes.map(
                    (classObj) => classObj.index
                ),
            ];
            const hasError = arrayContainsAny(_errorIndexes, classIndexes);

            if (hasError) {
                setCollapsedGrades((prev) =>
                    prev.filter((item) => item !== grade)
                );
            }
        });
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        showBackendFormError(form, putError, "classes");
        expandTabWithError();
    }, [putError]);

    const t = useTranslations("common");

    return (
        <Layout path="exam-management/exam-promotion-mark" locale={locale}>
            <Card styleClass="table-card">
                <h2 className="mb-5">{t("Exam Promotion Mark")}</h2>
                <div
                    className={clsx(
                        "grid max-w-[800px]",
                        !isEmpty(groupedClasses) && "overflow-auto"
                    )}
                >
                    <div className="mb-4 flex items-end gap-3">
                        <div className="lg:min-w-[300px]">
                            <FreeSelect
                                control={form.control}
                                name="semester_setting_id"
                                label="semester"
                                options={semesterSettings}
                                isClearable={false}
                            />
                        </div>
                        <Button onClick={onSearchSemester} variant={"outline"}>
                            {t("Search")}
                        </Button>
                    </div>

                    {isLoading ? (
                        <div className="ml-2 text-[14px] text-themeLabel">
                            {t("Loading")}...
                        </div>
                    ) : (
                        !isEmpty(groupedClasses) && (
                            <div>
                                {Object.keys(groupedClasses).map((grade) => {
                                    const isCollapsed =
                                        collapsedGrades.includes(grade);
                                    return (
                                        <div key={grade} className="mb-2">
                                            {/* Header */}
                                            <div className="flex min-h-[46px] items-center justify-between gap-2 border border-themeGreen4 bg-themeGreen3 px-4 py-1 text-xl">
                                                <div
                                                    className="c-text-size cursor-pointer font-semibold text-themeGreenDark"
                                                    onClick={() =>
                                                        toggleCollapse(grade)
                                                    }
                                                >
                                                    {grade}
                                                </div>
                                                {hasPermit(
                                                    _promotion_mark_create
                                                ) &&
                                                    !isCollapsed && (
                                                        <>
                                                            <div className="ml-auto rounded-sm border border-themeGreen">
                                                                <FreeInputDecimal
                                                                    control={
                                                                        form.control
                                                                    }
                                                                    name={`_${grade}_net_average_for_promotion`}
                                                                    hasLabel={
                                                                        false
                                                                    }
                                                                    placeholder="Apply All Net Average"
                                                                    onChange={(
                                                                        value
                                                                    ) => {
                                                                        Object.values(
                                                                            groupedClasses[
                                                                                grade
                                                                            ]
                                                                                .classes
                                                                        ).forEach(
                                                                            (
                                                                                classModel: any
                                                                            ) => {
                                                                                form.setValue(
                                                                                    `classes[${classModel.index}].net_average_for_promotion`,
                                                                                    value
                                                                                );
                                                                            }
                                                                        );
                                                                    }}
                                                                />
                                                            </div>
                                                            <div className="rounded-sm border border-themeGreen">
                                                                <FreeInputDecimal
                                                                    control={
                                                                        form.control
                                                                    }
                                                                    name={`_${grade}_conduct_mark_for_promotion`}
                                                                    hasLabel={
                                                                        false
                                                                    }
                                                                    placeholder="Apply All Conduct Mark"
                                                                    onChange={(
                                                                        value
                                                                    ) => {
                                                                        Object.values(
                                                                            groupedClasses[
                                                                                grade
                                                                            ]
                                                                                .classes
                                                                        ).forEach(
                                                                            (
                                                                                classModel: any
                                                                            ) => {
                                                                                form.setValue(
                                                                                    `classes[${classModel.index}].conduct_mark_for_promotion`,
                                                                                    value
                                                                                );
                                                                            }
                                                                        );
                                                                    }}
                                                                />
                                                            </div>
                                                        </>
                                                    )}
                                                <ChevronDown
                                                    size={20}
                                                    className={clsx(
                                                        "cursor-pointer text-themeGreen5",
                                                        !isCollapsed &&
                                                            "rotate-180"
                                                    )}
                                                    onClick={() =>
                                                        toggleCollapse(grade)
                                                    }
                                                />
                                            </div>

                                            {/* Collapsible Content */}
                                            <div
                                                className={clsx(
                                                    "overflow-hidden border-x",
                                                    isCollapsed
                                                        ? "max-h-0"
                                                        : "max-h-none"
                                                )}
                                            >
                                                {groupedClasses[
                                                    grade
                                                ].classes.map((classModel) => (
                                                    <div
                                                        className={clsx(
                                                            "flex flex-wrap items-center justify-between gap-2 px-4 py-1 transition hover:bg-gray-50",
                                                            "border-b"
                                                        )}
                                                        key={classModel.id}
                                                    >
                                                        <p>{classModel.name}</p>
                                                        <div className="ml-auto flex items-start gap-2">
                                                            <FreeInputDecimal
                                                                control={
                                                                    form.control
                                                                }
                                                                name={`classes[${classModel.index}].net_average_for_promotion`}
                                                                hasLabel={false}
                                                                placeholder="net average"
                                                                error={
                                                                    form
                                                                        .formState
                                                                        .errors
                                                                        ?.classes?.[
                                                                        classModel
                                                                            .index
                                                                    ]
                                                                        ?.net_average_for_promotion
                                                                }
                                                                disabled={
                                                                    !hasPermit(
                                                                        _promotion_mark_create
                                                                    )
                                                                }
                                                            />
                                                            <FreeInputDecimal
                                                                control={
                                                                    form.control
                                                                }
                                                                name={`classes[${classModel.index}].conduct_mark_for_promotion`}
                                                                hasLabel={false}
                                                                placeholder="conduct mark"
                                                                error={
                                                                    form
                                                                        .formState
                                                                        .errors
                                                                        ?.classes?.[
                                                                        classModel
                                                                            .index
                                                                    ]
                                                                        ?.conduct_mark_for_promotion
                                                                }
                                                                disabled={
                                                                    !hasPermit(
                                                                        _promotion_mark_create
                                                                    )
                                                                }
                                                            />
                                                        </div>

                                                        <div className="mr-5"></div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        )
                    )}

                    {hasPermit(_promotion_mark_create) &&
                        !isLoading &&
                        !isEmpty(groupedClasses) && (
                            <Button
                                onClick={onSubmit}
                                className="ml-auto mr-10 mt-2"
                            >
                                {t("Save")}
                            </Button>
                        )}
                </div>
            </Card>
        </Layout>
    );
};

export default ExamPromotionMark;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
