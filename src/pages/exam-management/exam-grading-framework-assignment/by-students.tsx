import React, { useEffect, useState } from "react";
import Layout from "@/components/Layout";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import Card from "@/components/ui/Card";
import { Form } from "@/components/base-ui/form";
import { useFieldArray, useForm } from "react-hook-form";
import FormSelect from "@/components/ui/FormSelect";
import { Button } from "@/components/base-ui/button";
import {
    examGradingFrameworkAPI,
    examGradingFrameworkApplyAPI,
    GET_ALL_PARAMS,
    semesterSettingAPI,
    studentAPI,
} from "@/lib/constant";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { showBackendFormError, toYMD } from "@/lib/utils";
import { useAsyncSelect } from "@/lib/async-select-hook";
import { X } from "lucide-react";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useTranslations } from "next-intl";

const ExamGradingFrameworkAssignmentByStudents = ({ locale }) => {
    useCheckViewPermit("grading-framework-apply");
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [yearOptions, setYearOptions] = useState<any[]>([]);
    const [dateRange, setDateRange] = useState<any>(null);

    const form = useForm<any>({
        defaultValues: {
            academic_year: "",
            _select_student: null,
            grading_framework_id: null,
            effective_from: null,
            effective_to: null,
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { loadAsyncOptions: loadStudentAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
    });

    function onAppendStudent(target) {
        const existingStudents = form.getValues("students");

        if (
            existingStudents.find(
                (student: any) => student?.id == target?.value
            )
        )
            return;

        if (target?.value) {
            appendStudent({
                id: target.value,
                name: target?.label ?? target?.name,
            });
        }
    }

    const { append: appendStudent, remove: removeStudent } = useFieldArray<any>(
        {
            control: form.control,
            name: "students",
        }
    );

    const { data: gradingFrameworkOptions, axiosQuery: getGradingFrameworks } =
        useAxios({
            api: examGradingFrameworkAPI,
            locale,
        });

    const { axiosPost: assignGradingFramework, error: postError } = useAxios({
        api: examGradingFrameworkApplyAPI,
        onSuccess: () => {
            close();
        },
    });

    const { initLoader } = useSubmit();
    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                data.student_ids = data.students.map((student) => student.id);
                if (dateRange) {
                    data.effective_from = toYMD(dateRange.startDate);
                    data.effective_to = toYMD(dateRange.endDate);
                }

                assignGradingFramework(data);
            })
        );
    }

    useEffect(() => {
        const semesterSettingId = form.watch("semester_setting_id");
        if (!semesterSettingId) {
            setYearOptions([]);
            return;
        }

        const selectedSemester = semesterOptions.find(
            (item) => item.id === semesterSettingId
        );

        if (!selectedSemester) return;

        const fromYear = new Date(selectedSemester.from).getFullYear();
        const toYear = new Date(selectedSemester.to).getFullYear();

        const yearRange: {
            id: string;
            name: string;
        }[] = [];

        for (let year = fromYear; year <= toYear; year++) {
            yearRange.push({
                id: year.toString(),
                name: year.toString(),
            });
        }

        setYearOptions(yearRange);

        const currentYear = new Date().getFullYear().toString();
        const foundCurrentYear = yearRange.find((y) => y.id === currentYear);

        if (foundCurrentYear) {
            form.setValue("academic_year", currentYear);
        } else if (yearRange.length > 0) {
            form.setValue("academic_year", yearRange[0].id);
        } else {
            form.setValue("academic_year", undefined);
        }
    }, [form.watch("semester_setting_id"), semesterOptions]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });

        getGradingFrameworks({ params: GET_ALL_PARAMS });
    }, []);

    const t = useTranslations("common");

    return (
        <Layout
            locale={locale}
            path="exam-management/exam-grading-framework-assignment/by-students"
        >
            <Card styleClass="max-w-4xl mx-auto">
                <div className="lg:px-2">
                    <h2 className="mb-5 pt-2">
                        {t("Exam Grading Framework Assignment")} -{" "}
                        {t("By Students")}
                    </h2>
                    <Form {...form}>
                        <form onSubmit={onSubmit} className="grid-form">
                            <FormSelect
                                control={form.control}
                                name={"semester_setting_id"}
                                label="semester"
                                options={semesterOptions}
                                isSortByName={false}
                                isClearable={false}
                                onChange={() => {
                                    form.setValue("student_ids", []);
                                }}
                            />

                            <FormSelect
                                control={form.control}
                                name={"academic_year"}
                                label={t("academic year") + "*"}
                                options={yearOptions}
                                isClearable={false}
                                isDisabled={yearOptions.length === 0}
                            />

                            <FormSelect
                                control={form.control}
                                name={"grading_framework_id"}
                                label={t("grading framework") + "*"}
                                options={gradingFrameworkOptions}
                            />

                            <div className="lg:col-span-1">
                                <DateRangePicker
                                    label={t("Date Range") + "*"}
                                    range={dateRange}
                                    setRange={setDateRange}
                                    error={
                                        form.formState.errors?.effective_from
                                            ?.message ||
                                        form.formState.errors?.effective_to
                                            ?.message
                                    }
                                />
                            </div>

                            <div>
                                <FreeSelectAsync
                                    control={form.control}
                                    name="_select_student"
                                    label={t("Students") + "*"}
                                    placeholder="Select Students"
                                    minWidth={300}
                                    loadOptions={loadStudentAsyncOptions}
                                    value={null}
                                    onChange={(option) => {
                                        onAppendStudent(option);
                                    }}
                                />

                                {form.watch("students")?.length > 0 && (
                                    <div className="mt-2">
                                        {form
                                            .watch("students")
                                            .map((student: any, index) => (
                                                <div
                                                    className="mt-1.5 flex gap-x-3"
                                                    key={student?.id}
                                                >
                                                    <X
                                                        size={20}
                                                        className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                                        onClick={() =>
                                                            removeStudent(index)
                                                        }
                                                    />
                                                    <div className="c-text-size">
                                                        {student?.name}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                )}
                            </div>

                            <div className="lg:col-span-2">
                                <Button
                                    type="submit"
                                    className="ml-auto mt-2 block w-32"
                                >
                                    {t("Save")}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </Card>
        </Layout>
    );
};

export default ExamGradingFrameworkAssignmentByStudents;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
