import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray } from "lodash";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    COMPLETED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    ERROR,
    IN_PROGRESS,
    resultsPostingHeaderAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { displayDateTime, replaceAll } from "@/lib/utils";
import clsx from "clsx";
import Modal from "@/components/ui/Modal";
import FilterExamPostingStatusForm from "@/components/forms/exam-management/FilterExamPostingStatusForm";
import { AlertCircle } from "lucide-react";
import ErrorLogsDisplay from "@/components/ui/ErrorLogsDisplay";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import { useTranslations } from "next-intl";

const ExamPostingStatus = ({ locale }) => {
    useCheckViewPermit("results-posting-header-view");
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);
    const [openErrorLogs, setOpenErrorLogs] = useState(false);
    const [selectedErrors, setSelectedErrors] = useState<any[]>([]);

    const { data, axiosQuery: getExamPostingStatus } = useAxios({
        api: resultsPostingHeaderAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchExamPostingStatus() {
        getExamPostingStatus({
            params: {
                includes: ["grade", "semesterSetting"],
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchExamPostingStatus();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            const _columns: TableColumnType[] = [
                {
                    key: "id",
                    displayAs: "batch no",
                    hasSort: true,
                },
                {
                    key: "semester_setting",
                    displayAs: "semester",
                },
                {
                    key: "grade",
                },
                {
                    key: "report_card_output_code",
                },
                {
                    key: "posted_at",
                    hasSort: true,
                },
                {
                    key: "processing_start",
                    hasSort: true,
                },
                {
                    key: "processing_end",
                    hasSort: true,
                },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value, cell) => {
                        const errors = cell.row.original?.errors
                            ? cell.row.original.errors.flat()
                            : [];
                        return (
                            <div className="flex items-center gap-1">
                                <span
                                    className={clsx(
                                        value === COMPLETED && "text-green-600",
                                        value === IN_PROGRESS &&
                                            "text-yellow-500",
                                        value === ERROR && "text-red-500"
                                    )}
                                >
                                    {t(replaceAll(value, "_", " "))}
                                </span>
                                {value === ERROR && errors.length > 0 && (
                                    <AlertCircle
                                        className="h-4 w-4 cursor-pointer text-red-500"
                                        onClick={() => {
                                            setSelectedErrors(errors);
                                            setOpenErrorLogs(true);
                                        }}
                                    />
                                )}
                            </div>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [data]);

    function definedData() {
        return isArray(data)
            ? data.map((data) => ({
                  id: data?.id,
                  report_card_output_code: data?.report_card_output_code,
                  semester_setting: data?.semester_setting?.name,
                  grade: data?.grade?.name,
                  status: data?.status,
                  posted_at: displayDateTime(
                      data?.posted_at,
                      DATE_FORMAT.forDisplay
                  ),
                  processing_start: displayDateTime(
                      data?.processing_start,
                      DATE_FORMAT.forDisplay
                  ),
                  processing_end: displayDateTime(
                      data?.processing_end,
                      DATE_FORMAT.forDisplay
                  ),
                  errors: data?.errors,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    const t = useTranslations("common");

    return (
        <Layout
            locale={locale}
            path="exam-management/exam-posting/exam-posting-status"
        >
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">{t("Exam Posting Status")}</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["includes"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterExamPostingStatusForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            </Card>

            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterExamPostingStatusForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            <Modal
                open={openErrorLogs}
                onOpenChange={setOpenErrorLogs}
                size="medium"
            >
                <ErrorLogsDisplay
                    errors={selectedErrors}
                    close={() => setOpenErrorLogs(false)}
                    refresh={fetchExamPostingStatus}
                />
            </Modal>
        </Layout>
    );
};

export default ExamPostingStatus;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
