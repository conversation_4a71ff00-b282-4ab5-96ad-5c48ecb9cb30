import React, { useEffect, useState } from "react";
import Layout from "@/components/Layout";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import Card from "@/components/ui/Card";
import { Form } from "@/components/base-ui/form";
import { useForm } from "react-hook-form";
import FormSelect from "@/components/ui/FormSelect";
import { Button } from "@/components/base-ui/button";
import {
    examPostingAPI,
    examPostingGradingFrameworkAPI,
    examPostingOutputCodesAPI,
    GET_ALL_PARAMS,
    gradeAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { showBackendFormError, toYMD } from "@/lib/utils";
import { DatePicker } from "@/components/ui/DatePicker";
import { useTranslations } from "next-intl";

const ExamPosting = ({ locale }) => {
    useCheckViewPermit("exam-results-data-entry-create-posting-session");
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [outputCodeOptions, setOutputCodeOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: null,
            grade_id: null,
            grading_framework_id: null,
            code: null,
            publish_date: null,
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: gradeOptions, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: gradingFrameworkOptions, axiosQuery: getGradingFrameworks } =
        useAxios({
            api: examPostingGradingFrameworkAPI,
            locale,
        });

    const { axiosQuery: getOutputCodes } = useAxios({
        api: examPostingOutputCodesAPI,
        locale,
        onSuccess: (res) => {
            if (res.data) {
                const codes = res.data.map((item) => ({
                    id: item.code,
                    name: item.name,
                }));

                setOutputCodeOptions(codes);
            }
        },
    });

    const { axiosPost: createPostingSession, error: postError } = useAxios({
        api: examPostingAPI,
        onSuccess: () => {
            close();
        },
    });

    const { initLoader } = useSubmit();
    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                data.publish_date = toYMD(data.publish_date);
                createPostingSession(data);
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGrades({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    name: {
                        [locale]: "asc",
                    },
                },
            },
        });
    }, []);

    const t = useTranslations("common");

    return (
        <Layout
            locale={locale}
            path="exam-management/exam-posting/exam-posting"
        >
            <Card styleClass="max-w-4xl mx-auto">
                <div className="lg:px-2">
                    <h2 className="mb-5 pt-2">{t("Exam Posting")}</h2>
                    <Form {...form}>
                        <form onSubmit={onSubmit} className="grid-form">
                            <FormSelect
                                control={form.control}
                                name={"semester_setting_id"}
                                label="semester"
                                options={semesterOptions}
                                isSortByName={false}
                                isClearable={false}
                            />

                            <FormSelect
                                control={form.control}
                                name="grade_id"
                                label={t("grade") + "*"}
                                options={gradeOptions}
                                isSortByName={false}
                                onChange={(gradeId) => {
                                    form.setValue("grading_framework_id", "");
                                    getGradingFrameworks({
                                        params: {
                                            ...GET_ALL_PARAMS,
                                            grade_id: gradeId,
                                            semester_setting_id: form.watch(
                                                "semester_setting_id"
                                            ),
                                        },
                                    });
                                }}
                            />

                            <FormSelect
                                control={form.control}
                                name={"grading_framework_id"}
                                label={t("grading framework") + "*"}
                                options={gradingFrameworkOptions}
                                onChange={(gradingFrameworkId) => {
                                    form.setValue("code", "");
                                    getOutputCodes({
                                        params: {
                                            ...GET_ALL_PARAMS,
                                            grading_framework_id:
                                                gradingFrameworkId,
                                        },
                                    });
                                }}
                            />

                            <FormSelect
                                control={form.control}
                                name={"code"}
                                label={t("code") + "*"}
                                options={outputCodeOptions}
                                isDisabled={!form.watch("grading_framework_id")}
                            />

                            <DatePicker
                                control={form.control}
                                name={"publish_date"}
                                label={t("publish date") + "*"}
                                disableDateBeforeToday={true}
                            />

                            <div className="lg:col-span-2">
                                <Button
                                    type="submit"
                                    className="ml-auto mt-2 block w-32"
                                >
                                    {t("Post Result")}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </Card>
        </Layout>
    );
};

export default ExamPosting;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
