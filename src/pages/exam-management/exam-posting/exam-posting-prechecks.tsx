import ExamPostingPrechecksForm from "@/components/forms/exam-management/ExamPostingPrechecksForm";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import { useCheckViewPermit } from "@/lib/hook";

const ExamPostingPrechecks = ({ locale }) => {
    useCheckViewPermit("results-posting-header-view");

    return (
        <Layout
            path="exam-management/exam-posting/exam-posting-prechecks"
            locale={locale}
        >
            <Card styleClass="table-card">
                <ExamPostingPrechecksForm />
            </Card>
        </Layout>
    );
};

export default ExamPostingPrechecks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
