import { isArray, lowerCase } from "lodash";
import FilterGradingSchemeForm from "@/components/forms/FilterGradingSchemeForm";
import GradingSchemeForm from "@/components/forms/GradingSchemeForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    EXAM,
    gradingSchemeAPI,
    INACTIVE,
    TableColumnType,
} from "@/lib/constant";

const ExamGrading = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="exam-management/exam-grading"
        api={gradingSchemeAPI}
        otherFilterParams={{ type: EXAM }}
        definedColumn={(): TableColumnType[] => {
            return [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "grading_scheme_items",
                    displayAs: "grading scheme",
                    hasSort: false,
                    modify: (value, cell) => {
                        return isArray(value) ? (
                            value.map((item) => (
                                <div key={item.id} className="text-[14px]">
                                    <span className="mr-1 font-medium">
                                        {item.display_as_name} :
                                    </span>
                                    <span className="">
                                        {item.from} - {item.to}
                                    </span>
                                </div>
                            ))
                        ) : (
                            <></>
                        );
                    },
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      name: item?.name,
                      code: item?.code,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                      grading_scheme_items: item?.grading_scheme_items,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <GradingSchemeForm gradingType={EXAM} {...params} />}
        filterForm={(params) => <FilterGradingSchemeForm {...params} />}
        formSize="medium"
        viewPermit="grading-scheme-view"
        createPermit="grading-scheme-create"
        updatePermit="grading-scheme-update"
        deletePermit="grading-scheme-delete"
    />
);

export default ExamGrading;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
