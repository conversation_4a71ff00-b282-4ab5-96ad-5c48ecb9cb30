import Layout from "@/components/Layout";
import ExamSemesterSettingForm from "@/components/forms/exam-management/ExamSemesterSettingForm";
import Card from "@/components/ui/Card";
import { useCheckViewPermit } from "@/lib/hook";

const ExamSemesterSettings = ({ locale }) => {
    useCheckViewPermit("exam-semester-setting-view");

    return (
        <Layout path="exam-management/exam-semester-settings" locale={locale}>
            <Card styleClass="table-card">
                <ExamSemesterSettingForm />
            </Card>
        </Layout>
    );
};

export default ExamSemesterSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
