import { useEffect, useState } from "react";
import React from "react";
import { lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import ExamGradingFrameworkCreateForm from "@/components/forms/exam-management/ExamGradingFrameworkCreateForm";
import ExamGradingFrameworkUpdateForm from "@/components/forms/exam-management/ExamGradingFrameworkUpdateForm";
import FilterSemesterSettingForm from "@/components/forms/semester-setting/FilterSemesterSettingForm";
import SemesterSettingForm from "@/components/forms/semester-setting/SemesterSettingForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DEFAULT_FILTER_PARAMS,
    examGradingFrameworkAPI,
    INACTIVE,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { getNameColumns, refreshForUpdate } from "@/lib/utils";
import { useTranslations } from "next-intl";

const ExamGradingFramework = ({ locale }) => {
    useCheckViewPermit("grading-framework-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);

    const { data, axiosQuery: getExamGradingFrameworks } = useAxios({
        api: examGradingFrameworkAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchExamGradingFrameworks() {
        getExamGradingFrameworks({ params: filter });
    }

    useEffect(() => {
        fetchExamGradingFrameworks();
    }, [filter, locale]);

    useEffect(() => {
        if (data && activeLanguages) {
            defineColumn();
        }
    }, [data, activeLanguages]);

    function defineColumn() {
        setColumns([
            ...getNameColumns(activeLanguages),
            {
                key: "is_active",
                displayAs: "Status",
                hasSort: true,
                modify: (value) => (
                    <div className={`cell-status ${value}`}>{t(value)}</div>
                ),
            },
        ]);
    }

    function definedData(data) {
        return Array.isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  ...item?.translations?.name,
                  is_active: lowerCase(item.is_active ? ACTIVE : INACTIVE),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    function onAfterCreate(id) {
        setOpenCreate(false);
        refreshForUpdate(filter, setFilter);
        setTargetId(id);
    }

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path="exam-management/exam-grading-framework">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>{t("Exam Grading Frameworks")}</h2>
                    <div className="flex flex-wrap gap-3">
                        {hasPermit("grading-framework-create") && (
                            <Button
                                size="smallerOnMobile"
                                className="lg:ml-auto"
                                onClick={() => setOpenCreate(true)}
                            >
                                {t("Add ")}
                                {t("Exam Grading Frameworks")}
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                    </div>
                </div>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                {hasPermit("grading-framework-update") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        {t("Edit / View")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <ExamGradingFrameworkCreateForm onNext={onAfterCreate} />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <ExamGradingFrameworkUpdateForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* filter */}
            {/* <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterSemesterSettingForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal> */}
        </Layout>
    );
};

export default ExamGradingFramework;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
