import React, { useEffect, useState } from "react";
import Layout from "@/components/Layout";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import Card from "@/components/ui/Card";
import { Form } from "@/components/base-ui/form";
import { useForm } from "react-hook-form";
import FormSelect from "@/components/ui/FormSelect";
import { Button } from "@/components/base-ui/button";
import {
    examGradingFrameworkBulkApplyAPI,
    examGradingFrameworkAPI,
    GET_ALL_PARAMS,
    gradeAPI,
    PRIMARY,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { showBackendFormError, toYMD } from "@/lib/utils";
import { useTranslations } from "next-intl";

const StudentGradingFrameworkAssignment = ({ locale }) => {
    useCheckViewPermit("grading-framework-apply");
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [yearOptions, setYearOptions] = useState<any[]>([]);
    const [dateRange, setDateRange] = useState<any>(null);

    const form = useForm<any>({
        defaultValues: {
            academic_year: "",
            semester_class_ids: [],
            grading_framework_id: null,
            effective_from: null,
            effective_to: null,
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: gradeOptions, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClasses } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradingFrameworkOptions, axiosQuery: getGradingFrameworks } =
        useAxios({
            api: examGradingFrameworkAPI,
            locale,
        });

    const { axiosPost: assignGradingFramework, error: postError } = useAxios({
        api: examGradingFrameworkBulkApplyAPI,
        onSuccess: () => {
            close();
        },
    });

    function fetchSemesterClasses() {
        const gradeId = form.watch("grade_id");
        if (!gradeId) return;
        getSemesterClasses({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: gradeId,
                class_type: PRIMARY,
            },
        });
    }

    const { initLoader } = useSubmit();
    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                if (dateRange) {
                    data.effective_from = toYMD(dateRange.startDate);
                    data.effective_to = toYMD(dateRange.endDate);
                }

                assignGradingFramework(data);
            })
        );
    }

    useEffect(() => {
        const semesterSettingId = form.watch("semester_setting_id");
        if (!semesterSettingId) {
            setYearOptions([]);
            return;
        }

        const selectedSemester = semesterOptions.find(
            (item) => item.id === semesterSettingId
        );

        if (!selectedSemester) return;

        const fromYear = new Date(selectedSemester.from).getFullYear();
        const toYear = new Date(selectedSemester.to).getFullYear();

        const yearRange: {
            id: string;
            name: string;
        }[] = [];

        for (let year = fromYear; year <= toYear; year++) {
            yearRange.push({
                id: year.toString(),
                name: year.toString(),
            });
        }

        setYearOptions(yearRange);
    }, [form.watch("semester_setting_id"), semesterOptions]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });

        getGrades({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    name: {
                        [locale]: "asc",
                    },
                },
            },
        });

        getGradingFrameworks({ params: GET_ALL_PARAMS });
    }, []);

    const t = useTranslations("common");

    return (
        <Layout
            locale={locale}
            path="exam-management/student-grading-framework-assignment"
        >
            <Card styleClass="max-w-4xl mx-auto">
                <div className="lg:px-2">
                    <h2 className="mb-5 pt-2">
                        {t("Student Grading Framework Assignment")}
                    </h2>
                    <Form {...form}>
                        <form onSubmit={onSubmit} className="grid-form">
                            <FormSelect
                                control={form.control}
                                name={"semester_setting_id"}
                                label="semester"
                                options={semesterOptions}
                                isSortByName={false}
                                isClearable={false}
                                onChange={() => {
                                    form.setValue("semester_class_ids", []);
                                    fetchSemesterClasses();
                                }}
                            />

                            <FormSelect
                                control={form.control}
                                name={"academic_year"}
                                label={t("academic year") + "*"}
                                options={yearOptions}
                                isClearable={false}
                                isDisabled={yearOptions.length === 0}
                            />

                            <FormSelect
                                control={form.control}
                                name="grade_id"
                                label="grade"
                                options={gradeOptions}
                                isSortByName={false}
                                onChange={(gradeId) => {
                                    form.setValue("semester_class_id", "");
                                    fetchSemesterClasses();
                                }}
                            />

                            <FormSelect
                                isMulti={true}
                                control={form.control}
                                name={"semester_class_ids"}
                                label={t("classes") + "*"}
                                isDisabled={!form.watch("grade_id")}
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                            />

                            <FormSelect
                                control={form.control}
                                name={"grading_framework_id"}
                                label={t("grading framework") + "*"}
                                options={gradingFrameworkOptions}
                            />

                            <div className="lg:col-span-1">
                                <DateRangePicker
                                    label={t("Date Range") + "*"}
                                    range={dateRange}
                                    setRange={setDateRange}
                                    error={
                                        form.formState.errors?.effective_from
                                            ?.message ||
                                        form.formState.errors?.effective_to
                                            ?.message
                                    }
                                />
                            </div>

                            <div className="lg:col-span-2">
                                <Button
                                    type="submit"
                                    className="ml-auto mt-2 block w-32"
                                >
                                    {t("Save")}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </Card>
        </Layout>
    );
};

export default StudentGradingFrameworkAssignment;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
