import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import FreeCheckbox from "@/components/ui/FreeCheckbox";
import {
    examAPI,
    examSubjectExemptionClassesAPI,
    examSubjectExemptionSetAPI,
    examSubjectExemptionStatusAPI,
    examSubjectExemptionSubjectsAPI,
    GET_ALL_PARAMS,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { combinedNames, isValueTrue } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm, useWatch } from "react-hook-form";

const ExamSubjectExemption = ({ locale }) => {
    useCheckViewPermit("exam-exemption-save");

    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            subject_id: null,
            class_id: null,
            exam_id: null,
            records: [],
        },
    });

    const [exemptionRecords, setExemptionRecords] = useState<any[] | null>(
        null
    );
    const [isAllSelected, setIsAllSelected] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "student_number",
        },
        {
            key: "student_name",
        },
        {
            key: "is_exempted",
            modifyHeader: () => (
                <span className="whitespace-nowrap">{t("is exempted")}</span>
            ),
            modify: (value, cell) => {
                const index = cell.row.index;
                return (
                    <div className="flex items-center justify-center">
                        <FreeCheckbox
                            control={form.control}
                            name={`records.${index}.is_exempted`}
                            hasLabel={false}
                            error={
                                form.formState.errors.records?.[index]
                                    ?.is_exempted
                            }
                        />
                    </div>
                );
            },
        },
    ];

    const { data: examOptions, axiosQuery: getExams } = useAxios({
        api: examAPI,
        locale,
    });

    const { data: eligibleSubjects, axiosQuery: getEligibleSubjects } =
        useAxios({
            api: examSubjectExemptionSubjectsAPI,
            locale,
        });

    const { data: eligibleClasses, axiosQuery: getEligibleClasses } = useAxios({
        api: examSubjectExemptionClassesAPI,
        locale,
    });

    const { axiosQuery: getExamSubjectExemptions } = useAxios({
        api: examSubjectExemptionStatusAPI,
        locale,
        onSuccess: (result) => {
            if (!result.data) return;

            const data = result.data.map((item) => ({
                student_id: item.student_id,
                is_exempted: isValueTrue(item.is_exempted),
            }));
            form.setValue("records", data);
            setExemptionRecords(result.data);
        },
        onError: resetRecords,
    });

    const { axiosPost: updateExemption } = useAxios({
        api: examSubjectExemptionSetAPI,
        toastMsg: "Updated successfully",
        locale,
        onSuccess: fetchExamSubjectExemption,
    });

    function resetRecords() {
        form.setValue("records", []);
        setExemptionRecords(null);
    }

    function definedData() {
        if (!exemptionRecords) return [];

        return exemptionRecords.map((item) => {
            return {
                student_number: item.student_number ?? "-",
                student_name: combinedNames(item.student_name),
                is_exempted: isValueTrue(item.is_exempted),
            };
        });
    }

    function fetchExamSubjectExemption() {
        getExamSubjectExemptions({
            params: {
                subject_id: form.watch("subject_id"),
                class_id: form.watch("class_id"),
                exam_id: form.watch("exam_id"),
            },
        });
    }

    function handleSelectAllToggle() {
        const currentRecords = form.getValues("records");

        const updatedRecords = currentRecords.map((record) => ({
            ...record,
            is_exempted: !isAllSelected,
        }));

        form.setValue("records", updatedRecords);
        setIsAllSelected(!isAllSelected);
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                updateExemption(data);
            })
        );
    }

    const watchedRecords = useWatch({
        control: form.control,
        name: "records",
    });

    useEffect(() => {
        if (!watchedRecords || watchedRecords.length === 0) return;

        const allChecked = watchedRecords.every((r) => r.is_exempted === true);
        setIsAllSelected(allChecked);
    }, [watchedRecords]);

    useEffect(() => {
        getExams({ params: GET_ALL_PARAMS });
        getEligibleSubjects({ params: GET_ALL_PARAMS });
    }, []);

    return (
        <Layout path="exam-management/exam-subject-exemption" locale={locale}>
            <Card styleClass="max-w-screen-2xl mx-auto">
                <h2>{t("Exam Subject Exemption")}</h2>

                <Form {...form}>
                    <form
                        className="mt-5 grid gap-3 lg:grid-cols-3"
                        onSubmit={onSubmit}
                    >
                        <FormSelect
                            control={form.control}
                            name="exam_id"
                            label="exam"
                            options={examOptions}
                            onChange={(examId) => {
                                if (examId && form.getValues("class_id")) {
                                    fetchExamSubjectExemption();
                                } else {
                                    resetRecords();
                                }
                            }}
                        />
                        <FormSelect
                            control={form.control}
                            name="subject_id"
                            label="subject"
                            options={eligibleSubjects}
                            onChange={(subjectId) => {
                                resetRecords();
                                if (subjectId) {
                                    getEligibleClasses({
                                        params: { subject_id: subjectId },
                                    });
                                } else {
                                    form.setValue("class_id", null);
                                }
                            }}
                        />
                        <FormSelect
                            control={form.control}
                            name="class_id"
                            label="class"
                            options={eligibleClasses}
                            isDisabled={!form.watch("subject_id")}
                            onChange={(classId) => {
                                if (classId && form.getValues("exam_id")) {
                                    fetchExamSubjectExemption();
                                } else {
                                    resetRecords();
                                }
                            }}
                        />

                        {exemptionRecords && (
                            <div className="mt-2 w-fit lg:min-w-[800px]">
                                <div className="mb-2 mt-3 flex justify-between gap-4 lg:col-span-1">
                                    <p className="content-center font-medium capitalize text-themeLabel">
                                        {t("records")}
                                    </p>
                                    <Button
                                        variant="outline"
                                        size="smaller"
                                        onClick={handleSelectAllToggle}
                                    >
                                        {t(
                                            isAllSelected
                                                ? "Deselect All"
                                                : "Select All"
                                        )}
                                    </Button>
                                </div>
                                <DataTable
                                    columns={columns}
                                    data={definedData()}
                                />
                                <Button type="submit" className="ml-auto mt-4">
                                    {t("Save")}
                                </Button>
                            </div>
                        )}
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default ExamSubjectExemption;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
