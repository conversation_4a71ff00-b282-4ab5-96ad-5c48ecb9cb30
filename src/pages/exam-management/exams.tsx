import { isArray } from "lodash";
import ExamForm from "@/components/forms/exam-management/ExamForm";
import FilterExamForm from "@/components/forms/exam-management/FilterExamForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { examAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Exams = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="exam-management/exams"
        api={examAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "start_date",
                    displayAs: "exam start date",
                    hasSort: true,
                },
                {
                    key: "end_date",
                    displayAs: "exam end date",
                    hasSort: true,
                },
                {
                    key: "description",
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item?.code,
                      ...item?.translations?.name,
                      description: item?.description ?? "-",
                      start_date: item?.start_date ?? "-",
                      end_date: item?.end_date ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => ({
            [name]: direction,
        })}
        form={(params) => <ExamForm {...params} />}
        filterForm={(params) => <FilterExamForm {...params} />}
        formSize="medium"
        viewPermit={"exam-view"}
        createPermit={"exam-create"}
        updatePermit={"exam-update"}
        deletePermit={"exam-delete"}
    />
);

export default Exams;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
