import { useEffect, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import Layout from "@/components/Layout";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import AdjustWalletForm from "@/components/forms/wallet/AdjustWalletForm";
import FilterWalletForm from "@/components/forms/wallet/FilterWalletForm";
import WalletTransactionsUser from "@/components/forms/wallet/WalletTransactionsUser";
import WalletWithdrawForm from "@/components/forms/wallet/WalletWithdrawForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    appCurrencySymbol,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
    walletAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    formatNumberForRead,
    optionUserLabel,
    refreshForUpdate,
} from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const Wallets = ({ locale }) => {
    useCheckViewPermit("wallet-admin-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);

    const [viewId, setViewId] = useState(null);
    const [viewUser, setViewUser] = useState(null);
    const [withdrawId, setWithdrawId] = useState(null);
    const [adjustId, setAdjustId] = useState(null);

    const { data, axiosQuery: getWallets } = useAxios({
        api: walletAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchWallets() {
        getWallets({
            params: {
                includes: ["userables", "user"],
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchWallets();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            const _columns: TableColumnType[] = [
                {
                    key: "email",
                    hasSort: true,
                    modify: (value) => (value?.length === 0 ? "-" : value),
                },
                {
                    key: "phone_number",
                    hasSort: true,
                    modify: (value) => (value?.length === 0 ? "-" : value),
                },
                {
                    key: "user",
                    modify(list) {
                        return (
                            <ul>
                                {list.length > 0
                                    ? list.map((user, index) => (
                                          <li
                                              key={index}
                                              className="table-item-li"
                                          >
                                              <span>{user}</span>
                                          </li>
                                      ))
                                    : "-"}
                            </ul>
                        );
                    },
                },
                {
                    key: "balance",
                    hasSort: true,
                    modify: (value) =>
                        `${appCurrencySymbol} ${formatNumberForRead(value)}`,
                },
            ];
            setColumns(_columns);
        }
    }, [data]);

    function definedData() {
        return isArray(data)
            ? data.map((wallet) => ({
                  id: wallet?.id,
                  email: wallet?.user?.email ?? "-",
                  phone_number: wallet?.user?.phone_number ?? "-",
                  balance: wallet?.balance ?? 0,
                  user: isArray(wallet?.userables)
                      ? wallet?.userables.map(
                            (userable) =>
                                optionUserLabel(
                                    userable?.number,
                                    userable?.translations?.name
                                ) + ` (${userable?.user_type_description})`
                        )
                      : [],
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by:
                name === "balance"
                    ? { balance: direction }
                    : { userable: { [name]: direction } },
        });
    }

    return (
        <Layout locale={locale} path="wallet-management/wallets">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">{t("wallets")}</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["includes"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterWalletForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                {hasPermit("transaction-admin-view") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() => {
                                            setViewUser(
                                                cell.row.original.user[0]
                                            );
                                            setViewId(cell.row.original.id);
                                        }}
                                    >
                                        {t("View Transactions")}
                                    </DropdownMenuItem>
                                )}
                                {hasPermit("wallet-admin-withdraw") &&
                                    cell.row.original.balance > 0 && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setWithdrawId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {t("Withdraw")}
                                        </DropdownMenuItem>
                                    )}
                                {hasPermit("wallet-admin-adjustment") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setAdjustId(cell.row.original.id)
                                        }
                                    >
                                        {t("Adjust")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* view */}
            <Modal open={viewId} onOpenChange={setViewId} size="large">
                <WalletTransactionsUser user={viewUser} id={viewId} />
            </Modal>

            {/* withdraw */}
            <Modal open={withdrawId} onOpenChange={setWithdrawId}>
                <WalletWithdrawForm
                    currentData={data?.find((item) => item?.id === withdrawId)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setWithdrawId(null)}
                />
            </Modal>

            {/* adjust balance */}
            <Modal open={adjustId} onOpenChange={setAdjustId}>
                <AdjustWalletForm
                    currentData={data?.find((item) => item.id === adjustId)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setAdjustId(null)}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterWalletForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default Wallets;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
