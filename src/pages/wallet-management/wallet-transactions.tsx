import { useEffect, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    appCurrencySymbol,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    FAILED,
    PENDING,
    SUCCESS,
    TableColumnType,
    walletTransactionsAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    displayDateTime,
    formatNumberForRead,
    getTypeFromUserModel,
    isValueTrue,
} from "@/lib/utils";
import clsx from "clsx";
import { Button } from "@/components/base-ui/button";
import WalletTransactionDownloadReportForm from "@/components/forms/wallet/WalletTransactionDownloadReportForm";
import WalletRefundForm from "@/components/forms/wallet/WalletRefundForm";
import FilterWalletTransactionForm from "@/components/forms/wallet/FilterWalletTransactionForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const WalletTransactions = ({ locale }) => {
    useCheckViewPermit("transaction-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);
    const [openDownloadReport, setOpenDownloadReport] = useState(false);
    const [targetRefundId, setTargetRefundId] = useState(null);

    const { data, axiosQuery: getWalletTransactions } = useAxios({
        api: walletTransactionsAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchWalletTransactions(isRefresh = false) {
        getWalletTransactions({
            params: {
                includes: ["userable"],
                order_by: { created_at: "desc" },
                ...filter,
                ...(isRefresh ? { page: 1 } : {}),
            },
        });
    }

    useEffect(() => {
        fetchWalletTransactions();
    }, [filter, locale]);

    const columns: TableColumnType[] = [
        {
            key: "reference_no",
            modify: (value) => <span className="text-[14px]">{value}</span>,
            hasSort: true,
        },
        {
            key: "user_type",
            modify: (value) => <div className="capitalize">{value}</div>,
        },
        {
            key: "userable",
            displayAs: "user name",
            modify: (value) => <div className="min-w-[100px]">{value}</div>,
        },
        {
            key: "user_no",
            displayAs: "user number",
        },
        {
            key: "card",
            displayAs: "card number",
            hasSort: true,
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "type",
            hasSort: true,
        },
        {
            key: "status",
            hasSort: true,
            modify: (status) => (
                <span
                    className={clsx(
                        "capitalize",
                        status?.value === SUCCESS && "text-green-600",
                        status?.value === PENDING && "text-yellow-500",
                        status?.value === FAILED && "text-red-500"
                    )}
                >
                    {t(status?.value)}
                </span>
            ),
        },
        {
            key: "total_amount",
            displayAs: `${t("amount")} (${appCurrencySymbol})`,
            hasSort: true,
        },
        {
            key: "balance_before",
            displayAs: `${t("wallet balance before")} (${appCurrencySymbol})`,
            hasSort: true,
            modify: (value) => <div className="min-w-[130px]">{value}</div>,
        },
        {
            key: "balance_after",
            displayAs: `${t("wallet balance after")} (${appCurrencySymbol})`,
            hasSort: true,
            modify: (value) => <div className="min-w-[130px]">{value}</div>,
        },
        {
            key: "description",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "remarks",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "created_at",
            hasSort: true,
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
    ];

    const refundActionColumn = {
        key: "can_refund",
        displayAs: "action",
        modify: (canRefund, cell) =>
            canRefund ? (
                <span
                    className="cursor-pointer rounded-md border border-gray-300 px-2 py-1.5 text-[12px] shadow-sm transition hover:border-gray-600"
                    onClick={() => setTargetRefundId(cell.row.original.id)}
                >
                    {t("Refund")}
                </span>
            ) : (
                <span className="text-gray-400">-</span>
            ),
    };

    function definedData() {
        return isArray(data)
            ? data.map((wallet) => ({
                  id: wallet?.id,
                  reference_no: wallet?.reference_no,
                  user_type: t(
                      (
                          getTypeFromUserModel(
                              wallet?.userable?.userable_type
                          ) ?? "-"
                      ).toUpperCase()
                  ),
                  userable: wallet?.userable ? wallet?.userable?.name : "-",
                  user_no: wallet?.userable?.number ?? "-",
                  card: wallet?.card ? wallet?.card?.name : "-",
                  type: t(wallet?.type?.label),
                  status: wallet?.status,
                  total_amount: formatNumberForRead(wallet?.total_amount),
                  balance_before: formatNumberForRead(wallet?.balance_before),
                  balance_after: formatNumberForRead(wallet?.balance_after),
                  description: wallet?.description ?? "-",
                  remarks: wallet?.remark ?? "-",
                  created_at: wallet?.created_at
                      ? displayDateTime(
                            wallet?.created_at,
                            DATE_FORMAT.forDisplay
                        )
                      : "-",
                  can_refund: isValueTrue(wallet?.can_refund),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: {
                ...(name === "userable" && { userable: { name: direction } }),
                ...(name === "card" && { card: { name: direction } }),
                ...(!["userable", "card"].includes(name) && {
                    [name]: direction,
                }),
            },
        });
    }

    return (
        <Layout locale={locale} path="wallet-management/wallet-transactions">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">{t("Wallet Transactions")}</h2>
                    <div className="flex flex-wrap items-center gap-3">
                        <Button
                            onClick={() => setOpenDownloadReport(true)}
                            variant={"outline"}
                        >
                            {t("Download Report")}
                        </Button>
                        {/* <TableFilterBtn
                            filter={filter}
                            excludeFields={["includes"]}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterWalletTransactionForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={
                        hasPermit("transaction-admin-refund")
                            ? [...columns, refundActionColumn]
                            : columns
                    }
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            </Card>

            {/* adjust balance */}
            <Modal open={targetRefundId} onOpenChange={setTargetRefundId}>
                <WalletRefundForm
                    data={data?.find((item) => item.id === targetRefundId)}
                    refresh={() => fetchWalletTransactions(true)}
                    close={() => setTargetRefundId(null)}
                />
            </Modal>

            {/* report */}
            <Modal
                open={openDownloadReport}
                onOpenChange={setOpenDownloadReport}
            >
                <WalletTransactionDownloadReportForm />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter} size="medium">
                <FilterWalletTransactionForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default WalletTransactions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
