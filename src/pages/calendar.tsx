import { But<PERSON> } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import CalenderEditor from "@/components/forms/calendar/CalendarEditor";
import CalendarForm from "@/components/forms/calendar/CalendarForm";
import CalendarTargetForm from "@/components/forms/calendar/CalendarTargetForm";
import FilterCalendarForm from "@/components/forms/calendar/FilterCalendarForm";
import Layout from "@/components/Layout";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    calendarAPI,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    getNameColumns,
    isValueTrue,
    refreshForUpdate,
    strStartCase,
} from "@/lib/utils";
import { useEffect, useState } from "react";

const Calendar = ({ locale }) => {
    useCheckViewPermit("calendar-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetSettingId, setTargetSettingId] = useState(null);
    const [targetStudentsId, setTargetStudentsId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data: calendarData, axiosQuery: getCalendar } = useAxios({
        api: calendarAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchCalendar() {
        getCalendar({
            params: {
                ...filter,
            },
        });
    }

    function definedData() {
        return Array.isArray(calendarData)
            ? calendarData.map((item) => ({
                  id: item?.id,
                  ...item?.translations?.name,
                  year: item?.year ?? "-",
                  priority: item?.priority ?? "-",
                  targets_count: item?.targets_count ?? 0,
                  is_default: strStartCase(`${isValueTrue(item?.is_default)}`),
                  is_active: strStartCase(`${isValueTrue(item?.is_active)}`),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        const list = ["year", "priority", "targets", "is_default", "is_active"];
        setFilter({
            ...filter,
            order_by: list.includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } },
        });
    }

    useEffect(() => {
        if (calendarData && activeLanguages) {
            const _columns: TableColumnType[] = [
                {
                    key: "year",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "priority",
                    hasSort: true,
                },
                {
                    key: "targets_count",
                    displayAs: "No. of Assigned Students",
                    hasSort: true,
                },
                {
                    key: "is_default",
                    modifyHeader: () => (
                        <span className="whitespace-nowrap">Is Default</span>
                    ),
                },
                {
                    key: "is_active",
                    displayAs: "Is Active",
                    hasSort: true,
                },
            ];
            setColumns(_columns);
        }
    }, [calendarData, activeLanguages]);

    useEffect(() => {
        fetchCalendar();
    }, [filter, locale]);

    return (
        <Layout path="calendar" locale={locale}>
            <Card styleClass="table-card">
                <div className="table-page-top">
                    <h2 className="capitalize">Calendars</h2>
                    {hasPermit("calendar-create") && (
                        <Button
                            size="smallerOnMobile"
                            className="ml-auto capitalize"
                            onClick={() => setOpenCreate(true)}
                        >
                            Add Calendar
                        </Button>
                    )}
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterCalendarForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => {
                        return (
                            <>
                                <ActionDropdown>
                                    {hasPermit("calendar-update") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Edit
                                        </DropdownMenuItem>
                                    )}
                                    {hasPermit("calendar-setting-view") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetSettingId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Edit Settings
                                        </DropdownMenuItem>
                                    )}
                                    {hasPermit("calendar-target-view") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetStudentsId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Edit Targets
                                        </DropdownMenuItem>
                                    )}

                                    {hasPermit("calendar-delete") && (
                                        <>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                Delete
                                            </DropdownMenuItem>
                                        </>
                                    )}
                                </ActionDropdown>
                            </>
                        );
                    }}
                />
            </Card>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <CalendarForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setOpenCreate(false)}
                />
            </Modal>

            {/* edit */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <CalendarForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setTargetId(null)}
                />
            </Modal>

            {/* edit settings */}
            <Modal
                open={targetSettingId}
                onOpenChange={setTargetSettingId}
                noCloseButton={true}
                size="fit"
            >
                <CalenderEditor
                    id={targetSettingId}
                    close={() => setTargetSettingId(null)}
                />
            </Modal>

            {/* edit targets */}
            <Modal
                open={targetStudentsId}
                onOpenChange={setTargetStudentsId}
                size="medium"
            >
                <CalendarTargetForm
                    calendarName={
                        calendarData?.find(
                            (item) => item?.id == targetStudentsId
                        )?.name
                    }
                    id={targetStudentsId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setTargetStudentsId(null)}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={calendarAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchCalendar}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterCalendarForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default Calendar;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
