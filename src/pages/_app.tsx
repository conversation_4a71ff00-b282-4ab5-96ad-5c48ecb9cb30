import "@/styles/globals.css";
import { NextIntlClientProvider } from "next-intl";
import React from "react";
import { X } from "lucide-react";
import { useRouter } from "next/router";
import toast, { ToastBar, Toaster } from "react-hot-toast";

export default function App({ Component, pageProps }) {
    const router = useRouter();
    function onError(error) {
        // console.log(error);
    }

    function getMessageFallback({ namespace, key, error }) {
        return key;
    }

    return (
        <>
            <NextIntlClientProvider
                locale={router.locale}
                messages={pageProps.messages}
                onError={onError}
                getMessageFallback={getMessageFallback}
            >
                <Component {...pageProps} />
            </NextIntlClientProvider>
            <PropagationStopper>
                <Toaster
                    toastOptions={{ duration: 5000 }}
                    containerClassName="m-2"
                    position="bottom-left"
                >
                    {(t) => (
                        <ToastBar
                            position="bottom-left"
                            toast={t}
                            style={{
                                ...t.style,
                                fontWeight: 500,
                                maxWidth: 500,
                            }}
                        >
                            {({ icon, message }) => (
                                <>
                                    {icon}
                                    <div className="c-text-size w-full">
                                        {message}
                                    </div>
                                    <X
                                        className="mb-auto ml-5 mt-1 h-5 w-5 cursor-pointer rounded-sm transition hover:bg-gray-100"
                                        onClick={() => toast.dismiss(t.id)}
                                    />
                                </>
                            )}
                        </ToastBar>
                    )}
                </Toaster>
            </PropagationStopper>
        </>
    );
}

const preventDefaultPropagtion = (e) => {
    e.nativeEvent.stopImmediatePropagation();
    e.nativeEvent.preventDefault();
    e.preventDefault();
    e.stopPropagation();
};

const PropagationStopper = ({ children, ...props }) => {
    return (
        <div
            {...props}
            onClick={(e) => {
                preventDefaultPropagtion(e);
            }}
        >
            {children}
        </div>
    );
};
