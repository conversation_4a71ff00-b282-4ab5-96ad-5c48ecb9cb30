import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { add, differenceInDays, isBefore, parse } from "date-fns";
import { capitalize, isEmpty } from "lodash";
import { useFieldArray, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import { Form } from "@/components/base-ui/form";
import ExtendBorrowForm from "@/components/forms/library/ExtendBorrowForm";
import ReturnBookForm from "@/components/forms/library/ReturnBookForm";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormInput from "@/components/ui/FormInput";
import InfoCard from "@/components/ui/InfoCard";
import Modal from "@/components/ui/Modal";
import Tabs from "@/components/ui/Tabs";
import {
    appCurrencySymbol,
    BORROWED,
    DATE_FORMAT,
    libraryBooksAPI,
    libraryBorrowBookAPI,
    libraryMembersAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    getTableSelection,
    strStartCase,
    toYMD,
} from "@/lib/utils";
import clsx from "clsx";
import OutlinedX from "@/components/ui/OutlinedX";
import { DatePicker } from "@/components/ui/DatePicker";

const IconBarcodeScanner = "/icons/icon-barcode-scanner.svg";

const BorrowReturnBooks = ({ locale }) => {
    useCheckViewPermit("book-loan-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [member, setMember] = useState<Record<string, any> | null>(null);
    const [booksColumns, setBooksColumns] = useState<any[]>([]);
    const [promptConfirmBorrow, setPromptConfirmBorrow] = useState(false);

    // scan inputs
    const form = useForm<any>({
        defaultValues: {
            member_or_card_number: "",
            book_no: "",
            books: [],
            _apply_date: null,
        },
    });

    const { append, remove } = useFieldArray({
        control: form.control,
        name: "books",
    });

    const { axiosQuery: getMembers } = useAxios({
        api: libraryMembersAPI,
        locale,
        onSuccess(result) {
            form.setValue("member_or_card_number", "");

            if (result.data?.length === 0) {
                toast("Member not found.");
                return;
            }
            const _member = result.data?.[0];
            setMember(_member);

            const currentBooks = form.getValues("books");
            form.setValue(
                "books",
                currentBooks.map((book) => {
                    const newBook = {
                        ...book,
                        due_date:
                            book?.due_date ?? getDueDate(book, _member?.type),
                    };
                    return newBook;
                })
            );
        },
    });

    const { axiosQuery: getBooks } = useAxios({
        api: libraryBooksAPI,
        locale,
        onSuccess(result) {
            form.setValue("book_no", "");

            if (result.data?.length === 0) {
                toast("Book not found.");
                return;
            }
            const resultBook = result.data?.[0];
            const existingbooks = form.getValues("books");
            const isAdded = existingbooks?.find((b) => b.id == resultBook.id);

            if (isAdded) {
                toast.error("This book already added.");
            } else {
                setBooksColumns(_booksColumns);
                append(formatBook(resultBook));
            }

            if (member === null && resultBook.active_book_loan) {
                const memberNumber =
                    resultBook?.active_book_loan?.member?.member_number;
                form.setValue("member_or_card_number", memberNumber);
                onScanMember();
            }
        },
    });

    const { axiosPost: borrowBooks } = useAxios({
        api: libraryBorrowBookAPI,
        toastMsg: "Books borrowed successfully",
        onSuccess() {
            form.setValue("books", []);
            onRefreshMember();
        },
    });

    function onScanMember() {
        const memberNumber = form.watch("member_or_card_number");
        if (isEmpty(memberNumber.trim())) return;
        getMembers({
            params: { member_or_card_number: memberNumber },
        });
    }

    function onScanBook() {
        const bookNumber = form.watch("book_no");
        if (isEmpty(bookNumber.trim())) return;

        if (form.getValues("books").length === member?.borrow_limit) {
            toast.error("Borrow limit reached");
            return;
        }
        getBooks({
            params: { book_no: bookNumber },
        });
    }

    function onRefreshMember() {
        getMembers({
            params: { member_number: member?.member_number },
        });
    }

    function formattedMember(member) {
        return {
            name: combinedNames(member?.translations?.name),
            member_number: member?.member_number,
            card_number: member?.card_number,
            member_type: capitalize(t(member?.type ?? "-")),
            ...(member?.userable?.current_primary_class && {
                class: combinedNames(
                    member?.userable?.current_primary_class?.class
                ),
            }),
            borrowed_books: `${member?.active_book_loans?.length} / ${member?.borrow_limit}`,
            total_overdue_amount: member.penalty_total_overdue_amount ?? "-",
        };
    }

    function getDueDate(book, memberType = null) {
        const type = memberType ?? member?.type;
        if (!type) return null;
        const loanDays = book?.loan_settings?.find(
            (item) => item?.type === type
        )?.loan_period_day;

        return loanDays ? add(new Date(), { days: loanDays }) : null;
    }

    function formatBook(book) {
        return {
            id: book?.id,
            "Barcode Number (条码)": book?.book_no,
            "Book Number (书号)": book?.call_no,
            book_title: book?.title,
            authors: book?.authors?.map((author) => author?.name).join(", "),
            subclassification: book?.book_sub_classification?.name ?? "-",
            status: book?.status,
            due_date: getDueDate(book),
            loan_settings: book?.loan_settings,
        };
    }

    function onSubmit() {
        setPromptConfirmBorrow(false);
        const books = form.getValues("books");
        borrowBooks({
            member_id: member?.id,
            books: books?.map((book) => ({
                id: book.id,
                due_date: toYMD(book.due_date),
            })),
        });
    }

    const handleKeyDown = (
        e: React.KeyboardEvent<HTMLInputElement>,
        callback: () => void
    ) => {
        if (e.key === "Enter") {
            e.preventDefault();
            callback();
        }
    };

    const _booksColumns = [
        {
            key: "id",
            displayAs: "_",
            modify: (_, cell) => (
                <OutlinedX onClick={() => remove(cell?.row?.index)} />
            ),
        },
        {
            key: "Barcode Number (条码)",
            displayAs: "Barcode Number (条码)",
        },
        {
            key: "Book Number (书号)",
            displayAs: "Book Number (书号)",
            modify: (value) => <div className="text-[14px]">{value}</div>,
        },
        {
            key: "book_title",
            modify: (value) => <div className="min-w-[100px]">{value}</div>,
        },
        {
            key: "authors",
        },
        {
            key: "subclassification",
        },
        {
            key: "status",
            modify: (value) => (
                <div
                    className={clsx(
                        "text-[13px] font-medium",
                        value === BORROWED && "text-orange-400"
                    )}
                >
                    {t(value)}
                </div>
            ),
        },
        {
            key: "due_date",
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <DatePicker
                        control={form.control}
                        name={`books.${index}.due_date`}
                        hasLabel={false}
                    />
                );
            },
        },
    ];

    function onApplyAllDueDates(date) {
        const newBooks = form.getValues("books").map((book) => {
            return {
                ...book,
                due_date: date,
            };
        });
        form.setValue("books", newBooks);
        form.setValue("_apply_date", null);
    }

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path="library/borrow-return-books">
            <Card styleClass="max-w-screen-2xl mx-auto">
                <div className="pb-7 lg:px-2">
                    <h2 className="mb-5 pt-2">{t("Borrow/Return Books")}</h2>
                    <Form {...form}>
                        <form className="grid gap-y-5">
                            <div className="grid max-w-3xl gap-5 lg:grid-cols-2">
                                <div className="flex items-center gap-x-3">
                                    <div className="w-full">
                                        <p className="mb-2.5 ml-0.5 font-medium text-gray-500">
                                            {t("Member/Card Number")}
                                        </p>
                                        <FormInput
                                            control={form.control}
                                            name={"member_or_card_number"}
                                            hasLabel={false}
                                            suffixIcon={IconBarcodeScanner}
                                            onKeyDown={(e) =>
                                                handleKeyDown(e, onScanMember)
                                            }
                                        />
                                    </div>

                                    <Button
                                        type="button"
                                        variant={"outline"}
                                        onClick={onScanMember}
                                        className="max-h-0 self-end"
                                    >
                                        {t("Enter")}
                                    </Button>
                                </div>

                                <div className="flex items-center gap-x-3">
                                    <div className="w-full">
                                        <p className="mb-2.5 ml-0.5 font-medium text-gray-500">
                                            Barcode Number (条码)
                                        </p>
                                        <FormInput
                                            control={form.control}
                                            name={"book_no"}
                                            hasLabel={false}
                                            suffixIcon={IconBarcodeScanner}
                                            onKeyDown={(e) =>
                                                handleKeyDown(e, onScanBook)
                                            }
                                        />
                                    </div>

                                    <Button
                                        type="button"
                                        variant={"outline"}
                                        onClick={onScanBook}
                                        className="max-h-0 self-end"
                                    >
                                        {t("Enter")}
                                    </Button>
                                </div>
                            </div>
                            {(member || form.watch("books").length > 0) && (
                                <div className="mb-4 border-y border-dashed bg-themeGreen3 bg-opacity-70">
                                    <div className="grid gap-5 pt-3">
                                        {member && (
                                            <InfoCard
                                                title={capitalize(t("member"))}
                                                data={formattedMember(member)}
                                                cardStyleClass="w-full max-w-[500px] bg-white bg-opacity-50"
                                            />
                                        )}

                                        {form.watch("books").length > 0 && (
                                            <div className="max-w-full overflow-auto">
                                                {
                                                    <div className="flex flex-wrap items-end justify-between gap-3">
                                                        <h3 className="mb-3 capitalize">
                                                            {t("books")}
                                                        </h3>
                                                        <div className="mb-3 max-w-[300px]">
                                                            <DatePicker
                                                                control={
                                                                    form.control
                                                                }
                                                                name="_apply_date"
                                                                hasLabel={false}
                                                                placeholder="Apply All Due Date"
                                                                onChange={
                                                                    onApplyAllDueDates
                                                                }
                                                            />
                                                        </div>
                                                    </div>
                                                }
                                                <DataTable
                                                    isSmaller={true}
                                                    styleClass="border-themeGreen bg-white bg-opacity-50"
                                                    columns={booksColumns}
                                                    data={form.getValues(
                                                        "books"
                                                    )}
                                                />
                                            </div>
                                        )}
                                    </div>

                                    <div className="mt-5 flex justify-end gap-x-3 pb-4">
                                        <Button
                                            type="button"
                                            variant={"outlineGray"}
                                            onClick={() => {
                                                form.reset();
                                                setMember(null);
                                                form.setValue("books", []);
                                            }}
                                        >
                                            {t("Reset")}
                                        </Button>
                                        {hasPermit("book-loan-create") &&
                                            member &&
                                            form.watch("books").length > 0 && (
                                                <Button
                                                    onClick={() =>
                                                        setPromptConfirmBorrow(
                                                            true
                                                        )
                                                    }
                                                >
                                                    {t("Borrow")}
                                                </Button>
                                            )}
                                    </div>
                                </div>
                            )}
                        </form>
                    </Form>
                    {member && (
                        <MemberBooksTabs
                            memberId={member.id}
                            bookLoan={member.active_book_loans}
                            refresh={onRefreshMember}
                        />
                    )}
                </div>
            </Card>
            <Modal
                open={promptConfirmBorrow}
                onOpenChange={setPromptConfirmBorrow}
            >
                <p className="mt-3 font-medium">
                    {t("Are you sure you want to proceed?")}
                </p>
                <DialogFooter className={"mt-2"}>
                    <Button
                        variant="outline"
                        onClick={() => setPromptConfirmBorrow(false)}
                    >
                        {t("Cancel")}
                    </Button>
                    <Button onClick={onSubmit}>{t("Confirm")}</Button>
                </DialogFooter>
            </Modal>
        </Layout>
    );
};

const MemberBooksTabs = ({ memberId, bookLoan, refresh }) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const _unreturned = "Unreturned Books";
    const _borrowed = "Borrowed Books";

    const unreturnedColumns = [
        {
            key: "book_number",
            displayAs: "Barcode Number (条码)",
            modify: (value) => <div className="min-w-[102px]">{value}</div>,
        },
        {
            key: "book_name",
            displayAs: "book title",
            modify: (value) => (
                <div className="min-w-[140px] text-[14px]">{value}</div>
            ),
        },
        {
            key: "borrow_date",
            modify: (value) => (
                <div className="whitespace-nowrap text-[14px]">{value}</div>
            ),
        },
        {
            key: "current_due_date",
            displayAs: "due date",
            modify: (value) => (
                <div className="whitespace-nowrap text-[14px]">{value}</div>
            ),
        },
        {
            key: "due_aging",
            modify: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: "overdue_amount",
            displayAs: `${t("overdue amount")} (${appCurrencySymbol})`,
            modify: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: "lost_amount",
            displayAs: `${t("lost amount")} (${appCurrencySymbol})`,
            modify: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: "status",
            modify: (value) => (
                <div
                    className={clsx(
                        "text-[13px] font-medium",
                        value === BORROWED && "text-orange-400"
                    )}
                >
                    {t(value)}
                </div>
            ),
        },
        {
            key: "action",
            modify: (value, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="flex items-start gap-x-2">
                        <Button
                            variant={"outline"}
                            size={"smaller"}
                            onClick={() => {
                                setTargetBook(unreturnedBooks[index]);
                                setOpenExtend(true);
                            }}
                        >
                            {t("Extend")}
                        </Button>
                        <Button
                            variant={"outline"}
                            size={"smaller"}
                            onClick={() => {
                                setTargetBook(unreturnedBooks[index]);
                                setOpenBorrow(true);
                            }}
                        >
                            {t("Return")}
                        </Button>
                    </div>
                );
            },
        },
    ];

    const borrowedColumns = [
        {
            key: "book_number",
            displayAs: "Barcode Number (条码)",
            modify: (value) => <div className="min-w-[102px]">{value}</div>,
        },
        {
            key: "book_name",
            displayAs: "book title",
            modify: (value) => (
                <div className="min-w-[140px] text-[14px]">{value}</div>
            ),
        },
        {
            key: "borrow_date",
            modify: (value) => (
                <div className="whitespace-nowrap text-[14px]">{value}</div>
            ),
        },
        {
            key: "due_date",
            modify: (value) => (
                <div className="whitespace-nowrap text-[14px]">{value}</div>
            ),
            hasSort: true,
        },
        {
            key: "return_date",
            modify: (value) => (
                <div className="whitespace-nowrap text-[14px]">{value}</div>
            ),
            hasSort: true,
        },
        {
            key: "penalty_total_fine_amount",
            displayAs: `${t("fine amount")} (${appCurrencySymbol})`,
            modify: (value) => <div className="text-[14px]">{value}</div>,
            hasSort: true,
        },
        {
            key: "penalty_payment_status",
            displayAs: "fine payment status",
            modify: (value) => (
                <div className="max-w-[60px] text-[14px]">{value}</div>
            ),
            hasSort: true,
        },
        {
            key: "status",
            displayAs: "borrow status",
            modify: (value) => (
                <div
                    className={clsx(
                        "text-[13px] font-medium",
                        value === BORROWED && "text-orange-400"
                    )}
                >
                    {t(value)}
                </div>
            ),
        },
    ];

    function getDueAging(date) {
        const targetDate = parse(date, DATE_FORMAT.YMD, new Date());
        if (isBefore(targetDate, new Date())) {
            return differenceInDays(targetDate, new Date());
        }
        return "-";
    }

    const unreturnedBooks = bookLoan.map((item) => ({
        id: item?.id,
        book_number: item?.book?.book_no,
        book_name: item?.book?.title,
        borrow_date: item?.loan_date,
        current_due_date: item?.due_date,
        due_aging: getDueAging(item?.due_date),
        status: item?.status ?? "-",
        overdue_amount: item?.overdue_amount ?? 0,
        lost_amount: item?.lost_amount ?? 0,
        total_fine_amount: item?.total_fine_amount ?? 0,
        book_id: item?.book?.id,
    }));

    const [tab, setTab] = useState(_unreturned);
    const [selection, setSelection] = useState<any[]>([]);
    const [targetBook, setTargetBook] = useState<any>();
    const [openBorrow, setOpenBorrow] = useState(false);
    const [openExtend, setOpenExtend] = useState(false);
    const [openBulkBorrow, setOpenBulkBorrow] = useState(false);
    const [openBulkExtend, setOpenBulkExtend] = useState(false);

    const _defaultBorrowedFilter = {
        page: 1,
        per_page: 10,
        order_by: { return_date: "desc" },
    };
    const [historyfilter, setHistoryFilter] = useState<Record<string, any>>({
        _defaultBorrowedFilter,
    });
    const [historyPagination, setHistoryPagination] = useState();

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, unreturnedBooks);
        setSelection(_selection);
    }

    const locale = useLocale();

    const { data: borrowHistory, axiosQuery: getBorrowHistory } = useAxios({
        api: libraryBorrowBookAPI,
        locale,
        onSuccess: (result) => setHistoryPagination(result?.pagination),
    });

    useEffect(() => {
        if (memberId && tab === _borrowed) {
            getBorrowHistory({
                params: { ...historyfilter, member_id: memberId },
            });
        }
    }, [historyfilter, memberId]);

    return (
        <>
            <div className="mb-5 mt-3 lg:mt-0">
                <div className="flex flex-wrap items-end justify-between gap-x-2">
                    <div className="flex-grow">
                        <Tabs
                            list={[_unreturned, _borrowed]}
                            selected={tab}
                            setSelected={setTab}
                            onChangeTab={(selected) => {
                                if (selected === _borrowed) {
                                    getBorrowHistory({
                                        params: {
                                            ..._defaultBorrowedFilter,
                                            member_id: memberId,
                                        },
                                    });
                                }
                            }}
                        />
                    </div>
                </div>
                {tab === _unreturned && unreturnedBooks.length > 0 && (
                    <div className="flex flex-wrap gap-x-2">
                        {hasPermit("book-loan-extend") && (
                            <Button
                                onClick={() => setOpenBulkExtend(true)}
                                className="mb-4"
                                disabled={selection.length === 0}
                            >
                                {t("Extend Borrow Period")}
                            </Button>
                        )}
                        {hasPermit("book-loan-return") && (
                            <Button
                                onClick={() => setOpenBulkBorrow(true)}
                                className="mb-4"
                                disabled={selection.length === 0}
                            >
                                {t("Return Books")}
                            </Button>
                        )}
                    </div>
                )}
                {tab === _unreturned ? (
                    <div className="overflow-auto pb-5 lg:pb-0">
                        <div className="w-fit lg:w-full">
                            <DataTable
                                columns={unreturnedColumns}
                                data={unreturnedBooks}
                                onMultiSelect={onMultiSelect}
                            />
                        </div>
                    </div>
                ) : (
                    <div className="overflow-auto pb-5 lg:pb-0">
                        <div className="w-fit lg:w-full">
                            <DataTable
                                columns={borrowedColumns}
                                data={
                                    borrowHistory?.map((item) => {
                                        return {
                                            id: item?.id,
                                            book_number: item?.book?.book_no,
                                            book_name: item?.book?.title,
                                            borrow_date: item?.loan_date,
                                            due_date: item?.due_date,
                                            return_date:
                                                item?.return_date ?? "-",
                                            penalty_total_fine_amount:
                                                item?.penalty_total_fine_amount,
                                            penalty_payment_status:
                                                Number(
                                                    item?.penalty_total_fine_amount
                                                ) > 0
                                                    ? strStartCase(
                                                          item?.penalty_payment_status
                                                      )
                                                    : "-",
                                            status: item?.loan_status,
                                        };
                                    }) ?? []
                                }
                                pagination={historyPagination}
                                setPagination={setHistoryPagination}
                                changePage={(arg) =>
                                    setHistoryFilter({
                                        ...historyfilter,
                                        ...arg,
                                    })
                                }
                                sorted={historyfilter?.order_by}
                                sort={(name: string, direction: string) => {
                                    setHistoryFilter({
                                        ...historyfilter,
                                        order_by: { [name]: direction },
                                    });
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>
            <Modal open={openBorrow} onOpenChange={setOpenBorrow} size="large">
                <ReturnBookForm
                    books={[targetBook]}
                    refresh={refresh}
                    close={() => setOpenBorrow(false)}
                />
            </Modal>
            <Modal open={openExtend} onOpenChange={setOpenExtend} size="large">
                <ExtendBorrowForm
                    books={[targetBook]}
                    refresh={refresh}
                    close={() => setOpenExtend(false)}
                />
            </Modal>
            <Modal
                open={openBulkBorrow}
                onOpenChange={setOpenBulkBorrow}
                size="large"
            >
                <ReturnBookForm
                    books={selection}
                    refresh={refresh}
                    close={() => setOpenBulkBorrow(false)}
                />
            </Modal>
            <Modal
                open={openBulkExtend}
                onOpenChange={setOpenBulkExtend}
                size="large"
            >
                <ExtendBorrowForm
                    books={selection}
                    refresh={refresh}
                    close={() => setOpenBulkExtend(false)}
                />
            </Modal>
        </>
    );
};

export default BorrowReturnBooks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
