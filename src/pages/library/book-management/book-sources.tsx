import BookSourceForm from "@/components/forms/library/BookSourceForm";
import FilterBookSourceForm from "@/components/forms/library/FilterBookSourceForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { bookSourceAPI, TableColumnType } from "@/lib/constant";

const BookSources = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="library/book-management/book-sources"
        api={bookSourceAPI}
        definedColumn={(activeLanguages) => {
            return activeLanguages
                .filter((lang) => lang?.is_active)
                .map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `Name ( ${lang?.name} )`,
                        hasSort: true,
                    })
                );
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <BookSourceForm {...params} />}
        filterForm={(params) => <FilterBookSourceForm {...params} />}
        viewPermit="master-book-source-view"
        createPermit="master-book-source-create"
        updatePermit="master-book-source-update"
        deletePermit="master-book-source-delete"
    />
);

export default BookSources;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
