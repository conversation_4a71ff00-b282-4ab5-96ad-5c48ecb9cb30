import { capitalize, isArray } from "lodash";
import FilterBookForm from "@/components/forms/library/FilterBookForm";
import LibraryBookForm from "@/components/forms/library/LibraryBookForm";
import {
    AVAILABLE,
    BORROWED,
    DEFAULT_FILTER_PARAMS,
    libraryBooksAPI,
    LOST,
    TableColumnType,
} from "@/lib/constant";
import clsx from "clsx";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import Modal from "@/components/ui/Modal";
import Card from "@/components/ui/Card";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import Layout from "@/components/Layout";
import { useEffect, useState } from "react";
import DataTable from "@/components/ui/DataTable";
import { refreshForUpdate } from "@/lib/utils";
import DeletePrompt from "@/components/ui/DeletePrompt";
import ActionDropdown from "@/components/ui/ActionDropdown";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Button } from "@/components/base-ui/button";
import RecoverLostBookPrompt from "@/components/forms/library/RecoverLostBookPrompt";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const LibraryBooks = ({ locale }) => {
    useCheckViewPermit("book-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);
    const [openRecoverLostBook, setOpenRecoverLostBook] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [targetLostBookId, setTargetLostBookId] = useState(null);

    const { data, axiosQuery: getLibraryBooks } = useAxios({
        api: libraryBooksAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchLibraryBooks() {
        getLibraryBooks({
            params: {
                ...filter,
            },
        });
    }

    function definedData() {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  isbn: item?.isbn === "" ? "-" : item?.isbn ?? "-",
                  book_no: item?.book_no,
                  call_no: item?.call_no,
                  title: item?.title,
                  authors: item?.authors
                      ?.map((author) => author?.name)
                      .join(", "),
                  book_sub_classification:
                      item?.book_sub_classification?.name ?? "-",
                  status: item?.status ?? "-",
                  due_date: item?.active_book_loan?.due_date ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
        setOpenRecoverLostBook(false);
    }

    useEffect(() => {
        fetchLibraryBooks();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            const _columns: TableColumnType[] = [
                {
                    key: "isbn",
                    displayAs: "ISBN",
                    hasSort: true,
                    modify(value, cell) {
                        return <span className="text-[14px]">{value}</span>;
                    },
                },
                {
                    key: "book_no",
                    displayAs: "Barcode Number (条码)",
                    hasSort: true,
                },
                {
                    key: "call_no",
                    displayAs: "Book Number (书号)",
                    hasSort: true,
                },
                {
                    key: "title",
                    hasSort: true,
                    modify(value) {
                        return <div className="min-w-[110px]">{value}</div>;
                    },
                },
                {
                    key: "authors",
                    modify(value) {
                        return <span className="text-[14px]">{value}</span>;
                    },
                },
                {
                    key: "book_sub_classification",
                    displayAs: "book subclassification",
                },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value) => (
                        <span
                            className={clsx(
                                value === AVAILABLE && "text-green-600",
                                value === BORROWED && "text-yellow-500",
                                value === LOST && "text-red-500"
                            )}
                        >
                            {capitalize(t(value))}
                        </span>
                    ),
                },
                {
                    key: "due_date",
                    modify(value) {
                        return (
                            <div className="whitespace-nowrap px-1 text-[14px]">
                                {value}
                            </div>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [data]);

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path="library/book-management/books">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-end justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <div className="mr-auto grid gap-2">
                        <h2 className="capitalize">{t("books")}</h2>
                    </div>
                    <div className="flex items-center gap-x-3">
                        {hasPermit("book-create") && (
                            <Button
                                size="smallerOnMobile"
                                onClick={() => setOpenCreate(true)}
                            >
                                {t("Add ")}
                                {capitalize(t("book"))}
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterBookForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => {
                        const id = cell.row.original.id;
                        const status = cell.row.original.status;
                        return (
                            <>
                                <ActionDropdown>
                                    {hasPermit("book-update") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => setTargetId(id)}
                                        >
                                            {t("Edit / View")}
                                        </DropdownMenuItem>
                                    )}

                                    {hasPermit("book-update") &&
                                        status == LOST && (
                                            <>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() => {
                                                        setTargetLostBookId(id);
                                                        setOpenRecoverLostBook(
                                                            true
                                                        );
                                                    }}
                                                >
                                                    {t("Recover Lost Book")}
                                                </DropdownMenuItem>
                                            </>
                                        )}

                                    {hasPermit("book-delete") && (
                                        <>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(id)
                                                }
                                            >
                                                {t("Delete")}
                                            </DropdownMenuItem>
                                        </>
                                    )}
                                </ActionDropdown>
                            </>
                        );
                    }}
                />
            </Card>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <LibraryBookForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* edit / view */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <LibraryBookForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* change status */}
            <Modal
                open={openRecoverLostBook}
                onOpenChange={setOpenRecoverLostBook}
            >
                <RecoverLostBookPrompt
                    id={targetLostBookId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => {
                        closeForm();
                    }}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={libraryBooksAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchLibraryBooks}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterBookForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default LibraryBooks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
