import BookClassificationForm from "@/components/forms/library/BookClassificationForm";
import FilterBookClassificationForm from "@/components/forms/library/FilterBookClassificationForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { bookClassificationAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const BookClassifications = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="library/book-management/book-classifications"
        api={bookClassificationAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item?.code,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <BookClassificationForm {...params} />}
        filterForm={(params) => <FilterBookClassificationForm {...params} />}
        viewPermit="master-book-classification-view"
        createPermit="master-book-classification-create"
        updatePermit="master-book-classification-update"
        deletePermit="master-book-classification-delete"
    />
);

export default BookClassifications;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
