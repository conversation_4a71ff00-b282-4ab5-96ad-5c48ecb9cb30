import BookSubClassificationForm from "@/components/forms/library/BookSubClassificationForm";
import FilterBookSubClassificationForm from "@/components/forms/library/FilterBookSubCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { bookSubClassficationAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const BookSubClassifications = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="library/book-management/book-sub-classifications"
        api={bookSubClassficationAPI}
        otherFilterParams={{
            includes: ["bookClassification"],
        }}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
                {
                    key: "book_classification",
                    hasSort: true,
                },
            ];

            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      book_classification: item?.book_classification?.name,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <BookSubClassificationForm {...params} />}
        filterForm={(params) => <FilterBookSubClassificationForm {...params} />}
        viewPermit="master-book-sub-classification-view"
        createPermit="master-book-sub-classification-create"
        updatePermit="master-book-sub-classification-update"
        deletePermit="master-book-sub-classification-delete"
    />
);

export default BookSubClassifications;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
