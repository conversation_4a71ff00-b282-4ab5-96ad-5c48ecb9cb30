import BookCategoryForm from "@/components/forms/library/BookCategoryForm";
import FilterBookCategoryForm from "@/components/forms/library/FilterBookCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { bookCategoryAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const BookCategories = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="library/book-management/book-categories"
        api={bookCategoryAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item?.code,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <BookCategoryForm {...params} />}
        filterForm={(params) => <FilterBookCategoryForm {...params} />}
        viewPermit="master-book-category-view"
        createPermit="master-book-category-create"
        updatePermit="master-book-category-update"
        deletePermit="master-book-category-delete"
    />
);

export default BookCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
