import { isArray } from "lodash";
import FilterLibraryMemberForm from "@/components/forms/library/FilterLibraryMemberForm";
import LibraryMemberForm from "@/components/forms/library/LibraryMemberForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { nav, libraryMembersAPI, TableColumnType } from "@/lib/constant";
import { combinedNames, getNameColumns } from "@/lib/utils";

const LibraryMembers = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="library/member-management/members"
        api={libraryMembersAPI}
        definedColumn={(activeLanguages): TableColumnType[] => {
            return [
                {
                    key: "card_number",
                    hasSort: true,
                },
                {
                    key: "member_number",
                    hasSort: true,
                },
                {
                    key: "type",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "current_primary_class",
                    displayAs: "student class",
                },
                {
                    key: "total_unreturned_book",
                    displayAs: "unreturned book",
                    modify(value, cell) {
                        return (
                            <div className="w-full text-center">{value}</div>
                        );
                    },
                },
            ];
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((member) => ({
                      id: member?.id,
                      member_number: member?.member_number ?? "-",
                      card_number: member?.card_number ?? "-",
                      ...member?.translations?.name,
                      current_primary_class: combinedNames(
                          member?.userable?.current_primary_class?.class
                      ),
                      type: member?.type,
                      total_unreturned_book: member?.total_unreturned_book,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return ["member_number", "card_number", "type"].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <LibraryMemberForm {...params} />}
        filterForm={(params) => <FilterLibraryMemberForm {...params} />}
        formSize="medium"
        viewPermit="library-member-view"
        createPermit="library-member-create"
        updatePermit="library-member-update"
        deletePermit="library-member-delete"
    />
);

export default LibraryMembers;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
