import React, { useEffect, useRef, useState } from "react";
import Logo from "/public/icons/logo.png";
import clsx from "clsx";
import Head from "next/head";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import { parseCookies, setCookie } from "nookies";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FreeInput from "@/components/ui/FreeInput";
import LoaderOverlay from "@/components/ui/LoaderOverlay";
import { loginAPI, requestOtpAPI, schoolAppName, TOKEN } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { isProperPhoneNumber, showBackendFormError } from "@/lib/utils";

const _countdownSeconds = 90;

function displayCountdown(remainingSeconds: number) {
    if (remainingSeconds <= 0) return null;

    const minutes = Math.floor(remainingSeconds / 60);
    const seconds = remainingSeconds % 60;

    return `${minutes}:${String(seconds).padStart(2, "0")}`;
}

const Login = () => {
    const router = useRouter();
    const title = `Login | ${schoolAppName.toString()}`;

    const [type, setType] = useState<"phone" | "email" | null>(null);

    function onLoginSuccess({
        isPassword,
        resultData,
    }: {
        isPassword: boolean;
        resultData: any;
    }) {
        setCookie(null, TOKEN, resultData?.token, {
            path: "/",
            sameSite: "none",
            secure: true,
        });
        if (isPassword && resultData?.is_password_reset_required) {
            router.push("/change-password");
        } else {
            router.replace("/");
        }
    }
    return (
        <>
            <Head>
                <title>{title}</title>
            </Head>
            <div className="mx-auto flex h-screen flex-col items-center justify-center px-10 py-20">
                <Image
                    src={Logo}
                    alt={schoolAppName}
                    className="mb-5 w-[400px]"
                />
                {!type && (
                    <>
                        <Button
                            className="w-full max-w-[360px]"
                            variant="outline"
                            onClick={() => setType("email")}
                        >
                            Login with Email
                        </Button>
                        <Button
                            className="mt-3 w-full max-w-[360px]"
                            variant="outline"
                            onClick={() => setType("phone")}
                        >
                            Login with Phone
                        </Button>
                    </>
                )}

                {type === "email" ? (
                    <EmailLogin onLoginSuccess={onLoginSuccess} />
                ) : type === "phone" ? (
                    <PhoneLogin onLoginSuccess={onLoginSuccess} />
                ) : null}

                {type && (
                    <Button
                        className="mt-3 w-full max-w-[360px]"
                        variant="outline"
                        onClick={() => setType(null)}
                    >
                        Back
                    </Button>
                )}

                <div className="mt-5 text-center text-xs text-themeGray">
                    By entering you are agreeing to our Terms of use and Privacy
                    Policy
                </div>
            </div>
        </>
    );
};

const EmailLogin = ({ onLoginSuccess }) => {
    const router = useRouter();

    const form = useForm({
        defaultValues: {
            email: "",
            password: "",
            otp: "",
        },
    });

    const [loginStep, setLoginStep] = useState<"email" | "password" | "otp">(
        "email"
    );

    const [loginType, setLoginType] = useState<"password" | "otp">();
    const [email, setEmail] = useState<string>();

    const [otpMessage, setOtpMessage] = useState<string>();
    const [remainingSeconds, setRemainingSeconds] = useState<number>(0);

    const countdownRef = useRef<any>(null);

    function startCountdown() {
        setRemainingSeconds(_countdownSeconds);
        countdownRef.current = setInterval(() => {
            setRemainingSeconds((prevSeconds) => {
                const newSeconds = prevSeconds - 1;
                if (newSeconds <= 0) {
                    clearInterval(countdownRef.current);
                }
                return newSeconds;
            });
        }, 1000);
    }

    const {
        axiosPublicPost: requestOTP,
        error: requestError,
        isLoading: isRequesting,
    } = useAxios({
        api: requestOtpAPI,
        noToast: true,
        onSuccess(result) {
            startCountdown();
            setOtpMessage(result?.message);
            onContinue("otp");
        },
    });

    const {
        axiosPublicPost: resendOTP,
        error: resendError,
        isLoading: isResending,
    } = useAxios({
        api: requestOtpAPI,
        toastMsg: "OTP resent successfully",
        onSuccess(result) {
            startCountdown();
            setOtpMessage(result?.message);
            onContinue("otp");
        },
    });

    const {
        axiosPublicPost: login,
        error: loginError,
        isLoading,
    } = useAxios({
        api: loginAPI,
        noToast: true,
        onSuccess(result) {
            onLoginSuccess({
                isPassword: loginType === "password",
                resultData: result.data,
            });
        },
    });

    function onContinue(type: "password" | "otp") {
        setLoginType(type);
        setLoginStep(type);
        if (form.watch("email")) {
            setEmail(form.watch("email"));
        }
        if (type === "otp") {
            form.reset();
        }
    }

    function onRequestOTP() {
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            requestOTP({ email: data.email });
        })();
    }

    function onLogin(e) {
        e.preventDefault();
        if (loginStep === "email") return;
        form.clearErrors();
        form.handleSubmit((data) => {
            const isOTP = loginType === "otp";
            login({
                email: isOTP ? email : form.watch("email"),
                [loginType!]: data[loginType!],
                use_otp: isOTP,
            });
        })();
    }

    useEffect(() => {
        return () => clearInterval(countdownRef.current);
    }, []);

    useEffect(() => {
        const cookies = parseCookies();
        const userToken = cookies[TOKEN];

        if (userToken) {
            router.replace("/");
        }
    }, [router]);

    useEffect(() => {
        showBackendFormError(form, requestError || loginError);
    }, [requestError, resendError, loginError]);

    return (
        <Form {...form}>
            <form
                className="flex w-full flex-col items-center justify-center"
                onSubmit={onLogin}
            >
                <p className="mx-auto mb-3 mt-1.5 max-w-[420px] text-center text-themeGray">
                    {loginStep === "email" && "Enter your email address"}
                    {loginStep === "password" &&
                        "Enter your email and password"}
                    {loginStep === "otp" && otpMessage}
                </p>

                <div className="w-full max-w-[360px]">
                    {loginStep === "email" || loginStep === "password" ? (
                        <div className="grid gap-y-2">
                            <FreeInput
                                control={form.control}
                                name="email"
                                hasLabel={false}
                                isSearch={true}
                                placeholder="Email address"
                                error={form.formState.errors?.email}
                            />

                            {loginStep === "password" && (
                                <>
                                    <FreeInput
                                        control={form.control}
                                        name="password"
                                        hasLabel={false}
                                        isSearch={true}
                                        placeholder="Enter password"
                                        error={form.formState.errors?.password}
                                        type={"password"}
                                    />
                                    <Link
                                        href={"/reset-password"}
                                        className="ml-auto text-[14px] text-gray-500 underline"
                                    >
                                        Reset Password
                                    </Link>
                                </>
                            )}
                        </div>
                    ) : loginStep === "otp" ? (
                        <FreeInput
                            control={form.control}
                            name="otp"
                            hasLabel={false}
                            isSearch={true}
                            placeholder="Enter OTP"
                            error={form.formState.errors?.otp}
                        />
                    ) : (
                        <></>
                    )}

                    {loginStep === "otp" && (
                        <div className="mt-2 flex items-center justify-end gap-x-2 px-1">
                            <div
                                className={clsx(
                                    "w-fit cursor-pointer text-[14px] underline",
                                    remainingSeconds > 0
                                        ? "text-themeGray"
                                        : "text-themeGreen"
                                )}
                                onClick={() =>
                                    remainingSeconds > 0
                                        ? null
                                        : resendOTP({ email })
                                }
                            >
                                Resend OTP
                            </div>
                            <div className="text-[13px] text-gray-500">
                                {remainingSeconds > 0 && (
                                    <span>
                                        ( wait{" "}
                                        {displayCountdown(remainingSeconds)} )
                                    </span>
                                )}
                            </div>
                        </div>
                    )}
                    {loginStep === "email" ? (
                        <div className="mt-4 grid gap-y-2.5">
                            <Button className="w-full" onClick={onRequestOTP}>
                                Login with OTP
                            </Button>
                            <Button
                                className="w-full"
                                onClick={() => onContinue("password")}
                            >
                                Login with password
                            </Button>
                        </div>
                    ) : (
                        <Button type="submit" className="mt-4 w-full">
                            Login
                        </Button>
                    )}
                </div>
            </form>
            {(isRequesting || isResending || isLoading) && <LoaderOverlay />}
        </Form>
    );
};

const PhoneLogin = ({ onLoginSuccess }) => {
    const router = useRouter();

    const form = useForm({
        defaultValues: {
            phone_number: "",
            password: "",
            otp: "",
        },
    });

    const [loginStep, setLoginStep] = useState<"phone" | "password" | "otp">(
        "phone"
    );

    const [loginType, setLoginType] = useState<"password" | "otp">();
    const [phone, setPhone] = useState<string>();

    const [otpMessage, setOtpMessage] = useState<string>();
    const [remainingSeconds, setRemainingSeconds] = useState<number>(0);

    const countdownRef = useRef<any>(null);

    function startCountdown() {
        setRemainingSeconds(_countdownSeconds);
        countdownRef.current = setInterval(() => {
            setRemainingSeconds((prevSeconds) => {
                const newSeconds = prevSeconds - 1;
                if (newSeconds <= 0) {
                    clearInterval(countdownRef.current);
                }
                return newSeconds;
            });
        }, 1000);
    }

    const {
        axiosPublicPost: requestOTP,
        error: requestError,
        isLoading: isRequesting,
    } = useAxios({
        api: requestOtpAPI,
        noToast: true,
        onSuccess(result) {
            startCountdown();
            setOtpMessage(result?.message);
            onContinue("otp");
        },
    });

    const {
        axiosPublicPost: resendOTP,
        error: resendError,
        isLoading: isResending,
    } = useAxios({
        api: requestOtpAPI,
        toastMsg: "OTP resent successfully",
        onSuccess(result) {
            startCountdown();
            setOtpMessage(result?.message);
            onContinue("otp");
        },
    });

    const {
        axiosPublicPost: login,
        error: loginError,
        isLoading,
    } = useAxios({
        api: loginAPI,
        noToast: true,
        onSuccess(result) {
            onLoginSuccess({
                isPassword: loginType === "password",
                resultData: result.data,
            });
        },
    });

    function onContinue(type: "password" | "otp") {
        setLoginType(type);
        setLoginStep(type);
        if (form.watch("phone_number")) {
            setPhone(form.watch("phone_number"));
        }
        if (type === "otp") {
            form.reset();
        }
    }

    function onRequestOTP() {
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            if (!isProperPhoneNumber(form, "phone_number", data.phone_number)) {
                return;
            }
            requestOTP({ phone_number: data.phone_number });
        })();
    }

    function onLogin(e) {
        e.preventDefault();
        if (loginStep === "phone") return;
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            const isOTP = loginType === "otp";
            if (
                !isOTP &&
                !isProperPhoneNumber(form, "phone_number", data.phone_number)
            ) {
                return;
            }
            login({
                phone_number: isOTP ? phone : data.phone_number,
                [loginType!]: data[loginType!],
                use_otp: isOTP,
            });
        })();
    }

    useEffect(() => {
        return () => clearInterval(countdownRef.current);
    }, []);

    useEffect(() => {
        const cookies = parseCookies();
        const userToken = cookies[TOKEN];

        if (userToken) {
            router.replace("/");
        }
    }, [router]);

    useEffect(() => {
        showBackendFormError(form, requestError || loginError);
    }, [requestError, resendError, loginError]);

    return (
        <Form {...form}>
            <form
                className="flex w-full flex-col items-center justify-center"
                onSubmit={onLogin}
            >
                <p className="mx-auto mb-4 mt-1.5 max-w-[420px] text-center text-themeGray">
                    {loginStep === "phone" && "Enter your phone address"}
                    {loginStep === "password" &&
                        "Enter your phone and password"}
                    {loginStep === "otp" && otpMessage}
                </p>

                <div className="w-full max-w-[360px]">
                    {loginStep === "phone" || loginStep === "password" ? (
                        <div className="grid gap-y-2">
                            <FormPhoneInput
                                form={form}
                                name="phone_number"
                                hasLabel={false}
                                isGray={true}
                            />
                            {loginStep === "password" && (
                                <FreeInput
                                    control={form.control}
                                    name="password"
                                    hasLabel={false}
                                    isSearch={true}
                                    placeholder="Enter password"
                                    error={form.formState.errors?.password}
                                    type={"password"}
                                />
                            )}
                        </div>
                    ) : loginStep === "otp" ? (
                        <FreeInput
                            control={form.control}
                            name="otp"
                            hasLabel={false}
                            isSearch={true}
                            placeholder="Enter OTP"
                            error={form.formState.errors?.otp}
                        />
                    ) : (
                        <></>
                    )}

                    {loginStep === "otp" && (
                        <div className="mt-2 flex items-center justify-end gap-x-2 px-1">
                            <div
                                className={clsx(
                                    "w-fit cursor-pointer text-[14px] underline",
                                    remainingSeconds > 0
                                        ? "text-themeGray"
                                        : "text-themeGreen"
                                )}
                                onClick={() =>
                                    remainingSeconds > 0
                                        ? null
                                        : resendOTP({ phone_number: phone })
                                }
                            >
                                Resend OTP
                            </div>
                            <div className="text-[13px] text-gray-500">
                                {remainingSeconds > 0 && (
                                    <span>
                                        ( wait{" "}
                                        {displayCountdown(remainingSeconds)} )
                                    </span>
                                )}
                            </div>
                        </div>
                    )}
                    {loginStep === "phone" ? (
                        <div className="mt-4 grid gap-y-2.5">
                            <Button className="w-full" onClick={onRequestOTP}>
                                Login with OTP
                            </Button>
                            <Button
                                className="w-full"
                                onClick={() => onContinue("password")}
                            >
                                Login with password
                            </Button>
                        </div>
                    ) : (
                        <Button type="submit" className="mt-4 w-full">
                            Login
                        </Button>
                    )}
                </div>
            </form>
            {(isRequesting || isResending || isLoading) && <LoaderOverlay />}
        </Form>
    );
};

export default Login;
