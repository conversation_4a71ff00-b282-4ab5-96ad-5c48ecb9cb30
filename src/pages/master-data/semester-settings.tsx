import { useEffect, useState } from "react";
import React from "react";
import { format } from "date-fns";
import { isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import SemesterYearForm from "@/components/forms/SemesterYearForm";
import FilterSemesterSettingForm from "@/components/forms/semester-setting/FilterSemesterSettingForm";
import SemesterSettingForm from "@/components/forms/semester-setting/SemesterSettingForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    INACTIVE,
    semesterSetting<PERSON>I,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { refreshForUpdate } from "@/lib/utils";
import { useTranslations } from "next-intl";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const SemesterSettings = ({ locale }) => {
    const t = useTranslations("common");
    useCheckViewPermit("master-semester-setting-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openAddYear, setOpenAddYear] = useState(false);
    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);

    const { data, axiosQuery: getSemesterSettings } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchSemesterSettings() {
        getSemesterSettings({ params: filter });
    }

    useEffect(() => {
        fetchSemesterSettings();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    function defineColumn() {
        setColumns([
            {
                key: "course",
                hasSort: true,
            },
            {
                key: "semester_year",
                displayAs: "year",
                hasSort: true,
            },
            {
                key: "name",
                hasSort: true,
            },
            {
                key: "from",
                hasSort: true,
            },
            {
                key: "to",
                hasSort: true,
            },
            {
                key: "is_current_semester",
                displayAs: "current semester",
                hasSort: true,
                modify: (value) => (
                    <div className={`cell-status ${value}`}>{value}</div>
                ),
            },
        ]);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item.id,
                  course: item?.course?.name,
                  semester_year: item?.semester_year_setting?.year,
                  name: item?.name,
                  from: format(item?.from, DATE_FORMAT.DMY),
                  to: format(item?.to, DATE_FORMAT.DMY),
                  is_current_semester: lowerCase(
                      item.is_current_semester ? ACTIVE : INACTIVE
                  ),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    return (
        <Layout locale={locale} path="master-data/semester-settings">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>{t("Semester Settings")}</h2>
                    <div className="flex flex-wrap items-center gap-3">
                        {hasPermit("master-semester-year-setting-create") && (
                            <Button
                                variant="outline"
                                size="smallerOnMobile"
                                className="lg:ml-auto"
                                onClick={() => setOpenAddYear(true)}
                            >
                                {t("Add Semester Year")}
                            </Button>
                        )}
                        {hasPermit("master-semester-setting-create") && (
                            <Button
                                size="smallerOnMobile"
                                onClick={() => setOpenCreate(true)}
                            >
                                {t("Add Semester Setting")}
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterSemesterSettingForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                {hasPermit(
                                    "master-semester-setting-update"
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        {t("Edit / View")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* add year */}
            <Modal open={openAddYear} onOpenChange={setOpenAddYear}>
                <SemesterYearForm close={() => setOpenAddYear(false)} />
            </Modal>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <SemesterSettingForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <SemesterSettingForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterSemesterSettingForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default SemesterSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
