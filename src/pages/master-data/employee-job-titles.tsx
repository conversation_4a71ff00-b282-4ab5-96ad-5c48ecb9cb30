import EmployeeJobTitleForm from "@/components/forms/employee/EmployeeJobTitleForm";
import FilterEmployeeJobTitleForm from "@/components/forms/employee/FilterEmployeeJobTitleForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { employeeJobTitlesAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const EmployeeJobTitles = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/employee-job-titles"
        api={employeeJobTitlesAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <EmployeeJobTitleForm {...params} />}
        filterForm={(params) => <FilterEmployeeJobTitleForm {...params} />}
        viewPermit="master-employee-job-title-view"
        createPermit="master-employee-job-title-create"
        updatePermit="master-employee-job-title-update"
        deletePermit="master-employee-job-title-delete"
    />
);

export default EmployeeJobTitles;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
