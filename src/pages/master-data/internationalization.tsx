import { isArray, lowerCase } from "lodash";
import InternationalizationForm from "@/components/forms/master-data/InternationalizationForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    INACTIVE,
    internationalizationAPI,
    TableColumnType,
} from "@/lib/constant";

const Internationalization = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/internationalization"
        api={internationalizationAPI}
        definedColumn={(): TableColumnType[] => {
            return [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      name: item?.name,
                      code: item?.code,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "is_active"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <InternationalizationForm {...params} />}
        noDelete={true}
        viewPermit="master-internationalization-view"
        createPermit="master-internationalization-create"
        updatePermit="master-internationalization-update"
        deletePermit="master-internationalization-delete"
    />
);

export default Internationalization;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
