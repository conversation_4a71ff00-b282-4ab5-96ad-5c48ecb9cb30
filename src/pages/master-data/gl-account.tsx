import { isArray, lowerCase } from "lodash";
import FilterGlAccountForm from "@/components/forms/master-data/FilterGlAccountForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    glAccountAPI,
    INACTIVE,
    TableColumnType,
} from "@/lib/constant";

const GlAccount = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/gl-account"
        api={glAccountAPI}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "label",
                    hasSort: true,
                },
                {
                    key: "external_reference_no",
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item?.code,
                      name: item?.name,
                      label: item?.label,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                      external_reference_no: item?.external_reference_no ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return ["is_active"].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        useTableDataForForm={true}
        form={() => <></>}
        noDelete={true}
        filterForm={(params) => <FilterGlAccountForm {...params} />}
        viewPermit="master-gl-account-view"
    />
);

export default GlAccount;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
