import FilterSchoolForm from "@/components/forms/master-data/FilterSchoolForm";
import SchoolForm from "@/components/forms/master-data/SchoolForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { PRIMARY, schoolAPI, SECONDARY, TableColumnType } from "@/lib/constant";
import { useTranslations } from "next-intl";

const Schools = ({ locale }) => {
    const t = useTranslations("common");
    return (
        <CommonTablePageWrap
            locale={locale}
            path="master-data/schools"
            api={schoolAPI}
            definedColumn={(activeLanguages) => {
                const _nameColumns = activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `Name ( ${lang?.name} )`,
                        hasSort: true,
                        modify: (value) => value ?? "-",
                    })
                );

                const _columns: TableColumnType[] = [
                    ..._nameColumns,
                    {
                        key: "level",
                        hasSort: true,
                    },
                    {
                        key: "state",
                        hasSort: true,
                    },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          ...item?.translations?.name,
                          level: t(
                              item?.level === PRIMARY
                                  ? "primary_school_level"
                                  : item?.level === SECONDARY
                                    ? "secondary_school_level"
                                    : item?.level
                          ),
                          state: item?.state?.translations?.name[locale] ?? "-",
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                const sortByName = ["level", "state"].includes(name);
                return sortByName
                    ? { [name]: direction }
                    : { name: { [name]: direction } };
            }}
            form={(params) => <SchoolForm {...params} />}
            filterForm={(params) => <FilterSchoolForm {...params} />}
            viewPermit="master-school-view"
            createPermit="master-school-create"
            updatePermit="master-school-update"
            deletePermit="master-school-delete"
        />
    );
};

export default Schools;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
