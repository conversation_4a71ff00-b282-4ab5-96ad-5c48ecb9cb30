import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { format } from "date-fns";
import {
    capitalize,
    forIn,
    groupBy,
    initial,
    isArray,
    isObject,
    last,
} from "lodash";
import { Check, ChevronDown, Pen, X } from "lucide-react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import Card from "@/components/ui/Card";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import { TimePicker } from "@/components/ui/TimePicker";
import { configAPI, DATE_FORMAT, defaultErrorMessage } from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    formatKeyForLabel,
    getAxiosObjectError,
    getDateFromTimeString,
    isValueTrue,
    replaceAll,
} from "@/lib/utils";
import { useTranslations } from "next-intl";

const _string = "string";
const _array = "array";
const _object = "object";
const _integer = "integer";
const _decimal = "decimal";
const _boolean = "boolean";
const _time = "time";
const _select = "select";

const titleClass =
    "font-semibold text-themeGreen2 bg-gray-50 py-1 px-3 border-b border-themeGreen2 border-opacity-10 cursor-pointer";
const valueClass =
    "font-medium w-fit border px-4 py-1.5 rounded-sm leading-tight c-text-size";

function formatLabel(key, isLibrary) {
    if (!key) return key;
    return isLibrary
        ? replaceAll(initial(key.split("_"))?.join(" "), "LIBRARY", "")
        : key;
}

const Configs = ({ locale }) => {
    useCheckViewPermit("master-config-update");
    const t = useTranslations("common");

    const { data: configs, axiosQuery: getConfigs } = useAxios({
        api: configAPI,
        locale,
    });

    const [collapseList, setCollapseList] = useState<any[]>([]);

    useEffect(() => {
        getConfigs();
    }, [locale]);

    function itemInput(config, isLibrary = false) {
        if (config?.type === _string && config?.options.length > 0) {
            config.type = _select;
        }
        return [_string, _integer, _decimal].includes(config?.type) ? (
            <TypeSingleValueForm
                key={config.key}
                type={config?.type}
                config={config}
                refresh={getConfigs}
                isLibrary={isLibrary}
            />
        ) : config?.type === _array ? (
            <TypeArrayForm
                key={config.key}
                config={config}
                refresh={getConfigs}
                isLibrary={isLibrary}
            />
        ) : config?.type === _object ? (
            <TypeObjectForm
                key={config.key}
                config={config}
                refresh={getConfigs}
                isLibrary={isLibrary}
            />
        ) : config?.type === _boolean ? (
            <TypeBooleanForm
                key={config.key}
                config={config}
                refresh={getConfigs}
                isLibrary={isLibrary}
            />
        ) : config?.type === _time ? (
            <TypeTimeForm
                key={config.key}
                config={config}
                refresh={getConfigs}
            />
        ) : config?.type === _select ? (
            <TypeDropdownForm
                key={config.key}
                config={config}
                refresh={getConfigs}
            />
        ) : null;
    }

    function libraryGroup(list) {
        const newList = list.map((config) => ({
            ...config,
            subCategory: last(config.key.split("_")),
        }));

        return Object.entries(groupBy(newList, "subCategory")).map(
            ([subCategory, subList], index) => {
                return (
                    <div key={index}>
                        <div
                            className="grid gap-y-3 lg:grid-cols-9"
                            key={subCategory}
                        >
                            <div
                                className={clsx(
                                    "col-span-1 text-[14px] font-semibold text-gray-500 lg:mb-2 xl:text-[16px]",
                                    index > 0 &&
                                        "border-t pt-3 lg:border-t-0 lg:pt-0"
                                )}
                            >
                                {capitalize(t(subCategory))}
                            </div>
                            {subList.map((config, index) => (
                                <div
                                    key={index}
                                    className={clsx(
                                        "lg:col-span-2",
                                        index > 0 &&
                                            "lg:border-l lg:border-dashed lg:pl-5 lg:pr-3"
                                    )}
                                >
                                    {itemInput(config, true)}
                                </div>
                            ))}
                        </div>
                    </div>
                );
            }
        );
    }

    return (
        <Layout locale={locale} path="master-data/configs">
            {configs && (
                <Card styleClass="max-w-7xl mx-auto">
                    <div className="pb-6 lg:px-2">
                        <h2 className="mb-5 pt-1 capitalize">{t("configs")}</h2>
                        {Object.entries(groupBy(configs, "category")).map(
                            ([category, list], index) => {
                                return (
                                    <div
                                        key={category}
                                        className={clsx(
                                            "grid gap-y-4",
                                            index > 0 && "pt-7"
                                        )}
                                    >
                                        <div
                                            className={clsx(
                                                titleClass,
                                                "flex items-center justify-between gap-x-3"
                                            )}
                                            onClick={() => {
                                                if (
                                                    collapseList.includes(
                                                        category
                                                    )
                                                ) {
                                                    setCollapseList(
                                                        collapseList.filter(
                                                            (item) =>
                                                                item !==
                                                                category
                                                        )
                                                    );
                                                } else {
                                                    setCollapseList([
                                                        ...collapseList,
                                                        category,
                                                    ]);
                                                }
                                            }}
                                        >
                                            {capitalize(category)}
                                            <ChevronDown
                                                size={20}
                                                className={clsx(
                                                    !collapseList.includes(
                                                        category
                                                    ) && "rotate-180"
                                                )}
                                            />
                                        </div>
                                        <div
                                            className={clsx(
                                                "px-3",
                                                collapseList.includes(
                                                    category
                                                ) && "h-0 overflow-hidden"
                                            )}
                                        >
                                            {category === "LIBRARY" ? (
                                                <div className="grid gap-y-3">
                                                    {libraryGroup(list)}
                                                </div>
                                            ) : (
                                                <div className="grid gap-7 lg:grid-cols-2">
                                                    {list.map((config) =>
                                                        itemInput(config)
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                );
                            }
                        )}
                    </div>
                </Card>
            )}
        </Layout>
    );
};

type LabelWithEditableProps = {
    label: string;
    isEdit: boolean;
    setIsEdit: (value: boolean) => void;
    onSubmit: () => void;
    reset: () => void;
};

const LabelWithEditable = ({
    label,
    isEdit,
    setIsEdit,
    onSubmit,
    reset,
}: LabelWithEditableProps) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    return (
        <div className="mb-2 flex items-start gap-x-3">
            <Label
                className={clsx(
                    isEdit
                        ? "w-[calc(100%-48px)] max-w-fit"
                        : "w-[calc(100%-15px)] max-w-fit"
                )}
            >
                {formatKeyForLabel(t(label))}
            </Label>
            {isEdit && hasPermit("master-config-update") ? (
                <div className="flex items-center gap-x-2">
                    <X
                        size={20}
                        className="cursor-pointer text-gray-500"
                        onClick={() => {
                            setIsEdit(false);
                            reset();
                        }}
                    />
                    <Check
                        size={20}
                        className="cursor-pointer text-themeGreen"
                        onClick={onSubmit}
                    />
                </div>
            ) : (
                <Pen
                    size={16}
                    className="mt-0.5 cursor-pointer text-themeGreen"
                    onClick={() => setIsEdit(true)}
                />
            )}
        </div>
    );
};

const TypeSingleValueForm = ({ type, config, refresh, isLibrary }) => {
    const form = useForm({
        defaultValues: {
            [config?.key]: config.value,
        },
    });
    const [isEdit, setIsEdit] = useState(false);
    const { axiosPost: updateConfig, error: postError } = useAxios({
        api: configAPI,
        onSuccess() {
            setIsEdit(false);
            refresh();
        },
        toastMsg: "Updated successfully",
    });

    function reset() {
        form.reset({ [config?.key]: config.value });
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            updateConfig({
                category: config.category,
                key: config.key,
                value: data[config.key],
            });
        })();
    }

    useEffect(() => {
        if (postError) {
            toast(getAxiosObjectError(postError) || defaultErrorMessage);
        }
    }, [postError]);

    return (
        <Form {...form}>
            <form>
                <LabelWithEditable
                    label={formatLabel(config.key, isLibrary)}
                    isEdit={isEdit}
                    setIsEdit={setIsEdit}
                    onSubmit={onSubmit}
                    reset={reset}
                />

                {isEdit ? (
                    type === _string ? (
                        <FormInput
                            control={form.control}
                            name={config.key}
                            label={formatKeyForLabel(config.key)}
                            placeholder={config.value}
                            hasLabel={false}
                        />
                    ) : type === _integer ? (
                        <FormInputInterger
                            control={form.control}
                            name={config.key}
                            label={formatKeyForLabel(config.key)}
                            placeholder={config.value}
                            hasLabel={false}
                        />
                    ) : type === _decimal ? (
                        <FormInputDecimal
                            control={form.control}
                            name={config.key}
                            label={formatKeyForLabel(config.key)}
                            placeholder={config.value}
                            hasLabel={false}
                        />
                    ) : null
                ) : (
                    <p className={valueClass}>{config.value ?? "-"}</p>
                )}
            </form>
        </Form>
    );
};

const TypeArrayForm = ({ config, refresh, isLibrary }) => {
    function getDefaultValues() {
        const defaultValues = {};
        config.value.forEach((value) => (defaultValues[value] = true));
        return defaultValues;
    }

    const form = useForm({ defaultValues: getDefaultValues() });

    const [isEdit, setIsEdit] = useState(false);
    const { axiosPost: updateConfig, error: postError } = useAxios({
        api: configAPI,
        onSuccess() {
            setIsEdit(false);
            refresh();
        },
        toastMsg: "Updated successfully",
    });

    function reset() {
        form.reset(getDefaultValues());
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            data = Object.keys(data).filter((key) => !!data[key]);
            updateConfig({
                category: config.category,
                key: config.key,
                value: data,
            });
        })();
    }

    useEffect(() => {
        if (postError) {
            toast(getAxiosObjectError(postError) || defaultErrorMessage);
        }
    }, [postError]);

    function getSelectedLabelsFromOptions() {
        return config.options
            .filter((option) => config.value.includes(option?.value ?? option))
            .map((option) => option?.label ?? option);
    }

    return (
        <Form {...form}>
            <form>
                <LabelWithEditable
                    label={formatLabel(config.key, isLibrary)}
                    isEdit={isEdit}
                    setIsEdit={setIsEdit}
                    onSubmit={onSubmit}
                    reset={reset}
                />

                {isEdit ? (
                    isArray(config.options) &&
                    config.options.map((option) => (
                        <FormCheckbox
                            key={option?.value ?? option}
                            name={option?.value ?? option}
                            label={option?.label ?? option}
                            control={form.control}
                        />
                    ))
                ) : config?.value?.length > 0 ? (
                    <p className={valueClass}>
                        {getSelectedLabelsFromOptions().join(", ")}
                    </p>
                ) : (
                    <p>-</p>
                )}
            </form>
        </Form>
    );
};

const TypeObjectForm = ({ config, refresh, isLibrary }) => {
    function getDefaultValues() {
        const defaultValues =
            typeof config.value === "object" ? { ...config.value } : {};

        forIn(defaultValues, (value: any[], key) => {
            if (isArray(value)) {
                defaultValues[key] = {};
                value.forEach((val) => (defaultValues[key][val] = true));
            }
        });
        return defaultValues;
    }

    const form = useForm({ defaultValues: getDefaultValues() });

    const [isEdit, setIsEdit] = useState(false);
    const { axiosPost: updateConfig, error: postError } = useAxios({
        api: configAPI,
        onSuccess() {
            setIsEdit(false);
            refresh();
        },
        toastMsg: "Updated successfully",
    });

    function getSelectedLabelsFromOptions(subType, values) {
        const options = config?.options?.find(
            (optionType) => optionType.value === subType
        )?.sub_value;
        return options
            .filter((option) => values.includes(option.value))
            .map((option) => option.label);
    }

    function reset() {
        form.reset(getDefaultValues());
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            forIn(data, (value, key) => {
                if (isObject(value)) {
                    data[key] = Object.keys(value).filter(
                        (key) => !!value[key]
                    );
                }
            });
            console.log("data", data);
            updateConfig({
                category: config.category,
                key: config.key,
                value: data,
            });
        })();
    }

    useEffect(() => {
        if (postError) {
            toast(getAxiosObjectError(postError) || defaultErrorMessage);
        }
    }, [postError]);

    return (
        <Form {...form}>
            <form>
                <LabelWithEditable
                    label={formatLabel(config.key, isLibrary)}
                    isEdit={isEdit}
                    setIsEdit={setIsEdit}
                    onSubmit={onSubmit}
                    reset={reset}
                />
                <ul className="ml-4 flex list-disc flex-wrap gap-x-12 gap-y-5">
                    {config?.options?.map((subType) => {
                        const subTypeLabel = subType?.label;
                        const subTypeValue = subType?.value;
                        const subTypeOptions = isArray(subType?.sub_value)
                            ? subType?.sub_value
                            : [];
                        return (
                            <li className="text-xs" key={subTypeValue}>
                                <p className="mb-3 mt-1 text-themeLabel">
                                    {subTypeLabel}
                                </p>
                                <div>
                                    {isEdit ? (
                                        subTypeOptions.map((option) => (
                                            <FormCheckbox
                                                key={
                                                    subTypeLabel +
                                                    subTypeValue?.option?.value
                                                }
                                                name={`${subTypeValue}.${option?.value}`}
                                                label={option?.label}
                                                control={form.control}
                                            />
                                        ))
                                    ) : config?.value?.[subTypeValue]?.length >
                                      0 ? (
                                        <p
                                            className={clsx(
                                                valueClass,
                                                "-mt-1"
                                            )}
                                        >
                                            {getSelectedLabelsFromOptions(
                                                subTypeValue,
                                                config.value[subTypeValue]
                                            ).join(", ")}
                                        </p>
                                    ) : (
                                        <p>-</p>
                                    )}
                                </div>
                            </li>
                        );
                    })}
                </ul>
            </form>
        </Form>
    );
};

const TypeBooleanForm = ({ config, refresh, isLibrary }) => {
    const form = useForm({
        defaultValues: {
            [config?.key]: isValueTrue(config.value),
        },
    });
    const [isEdit, setIsEdit] = useState(false);
    const { axiosPost: updateConfig, error: postError } = useAxios({
        api: configAPI,
        onSuccess() {
            setIsEdit(false);
            refresh();
        },
        toastMsg: "Updated successfully",
    });

    function reset() {
        form.reset({ [config?.key]: isValueTrue(config.value) });
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            updateConfig({
                category: config.category,
                key: config.key,
                value: isValueTrue(data[config.key]) ? 1 : 0,
            });
        })();
    }

    useEffect(() => {
        if (postError) {
            toast(getAxiosObjectError(postError) || defaultErrorMessage);
        }
    }, [postError]);

    return (
        <Form {...form}>
            <form>
                <LabelWithEditable
                    label={formatLabel(config.key, isLibrary)}
                    isEdit={isEdit}
                    setIsEdit={setIsEdit}
                    onSubmit={onSubmit}
                    reset={reset}
                />

                {isEdit ? (
                    <div className="mt-3">
                        <FormCheckbox
                            name={config.key}
                            label={config.key}
                            control={form.control}
                            hasLabel={false}
                        />
                    </div>
                ) : (
                    <p className={valueClass}>
                        {isValueTrue(config.value) ? "Yes" : "No"}
                    </p>
                )}
            </form>
        </Form>
    );
};

const TypeTimeForm = ({ config, refresh }) => {
    const _defaultValue = config?.value
        ? getDateFromTimeString(config?.value)
        : new Date();

    const form = useForm<any>({
        defaultValues: {
            [config?.key]: _defaultValue,
        },
    });
    const [isEdit, setIsEdit] = useState(false);
    const { axiosPost: updateConfig, error: postError } = useAxios({
        api: configAPI,
        onSuccess() {
            setIsEdit(false);
            refresh();
        },
        toastMsg: "Updated successfully",
    });

    function reset() {
        form.reset({ [config?.key]: _defaultValue });
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            const time = format(data[config.key], DATE_FORMAT.time);
            updateConfig({
                category: config.category,
                key: config.key,
                value: time,
            });
        })();
    }

    useEffect(() => {
        if (postError) {
            toast(getAxiosObjectError(postError) || defaultErrorMessage);
        }
    }, [postError]);

    return (
        <Form {...form}>
            <form>
                <LabelWithEditable
                    label={formatLabel(config.key, false)}
                    isEdit={isEdit}
                    setIsEdit={setIsEdit}
                    onSubmit={onSubmit}
                    reset={reset}
                />

                {isEdit ? (
                    <TimePicker
                        date={form.watch(config.key)}
                        setDate={(date) => {
                            form.setValue(config.key, date);
                        }}
                    />
                ) : (
                    <p className={valueClass}>
                        {config.value
                            ? format(
                                  getDateFromTimeString(config.value),
                                  DATE_FORMAT.timeDisplay
                              )
                            : "-"}
                    </p>
                )}
            </form>
        </Form>
    );
};

const TypeDropdownForm = ({ config, refresh }) => {
    const form = useForm({
        defaultValues: {
            [config?.key]: config.value,
        },
    });
    const [isEdit, setIsEdit] = useState(false);
    const { axiosPost: updateConfig, error: postError } = useAxios({
        api: configAPI,
        onSuccess() {
            setIsEdit(false);
            refresh();
        },
        toastMsg: "Updated successfully",
    });

    function reset() {
        form.reset({ [config?.key]: config.value });
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            updateConfig({
                category: config.category,
                key: config.key,
                value: data[config.key],
            });
        })();
    }

    useEffect(() => {
        if (postError) {
            toast(getAxiosObjectError(postError) || defaultErrorMessage);
        }
    }, [postError]);

    return (
        <Form {...form}>
            <form>
                <LabelWithEditable
                    label={formatLabel(config.key, false)}
                    isEdit={isEdit}
                    setIsEdit={setIsEdit}
                    onSubmit={onSubmit}
                    reset={reset}
                />

                {isEdit ? (
                    <FormSelect
                        control={form.control}
                        name={config.key}
                        label={formatKeyForLabel(config.key)}
                        placeholder={config.value}
                        hasLabel={false}
                        isSortByName={false}
                        options={
                            config.options.map((option) => ({
                                id: option.value,
                                name: option.label,
                            })) ?? []
                        }
                    />
                ) : (
                    <p className={valueClass}>{config.value ?? "-"}</p>
                )}
            </form>
        </Form>
    );
};

export default Configs;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
