import React from "react";
import Layout from "@/components/Layout";
import SchoolProfileForm from "@/components/forms/SchoolProfileForm";
import { useCheckViewPermit } from "@/lib/hook";

const SchoolProfile = ({ locale }) => {
    useCheckViewPermit("master-school-profile-view");
    return (
        <Layout locale={locale} path="master-data/school-profile">
            <SchoolProfileForm />
        </Layout>
    );
};

export default SchoolProfile;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
