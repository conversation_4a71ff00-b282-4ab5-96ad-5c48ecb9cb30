import { lowerCase } from "lodash";
import DepartmentForm from "@/components/forms/master-data/DepartmentForm";
import FilterDepartmentForm from "@/components/forms/master-data/FilterDepartmentForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    departmentAPI,
    INACTIVE,
    TableColumnType,
} from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Departments = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/departments"
        api={departmentAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                {
                    key: "code",
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "is_active",
                    displayAs: "Status",
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      code: item.code,
                      ...item?.translations?.name,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <DepartmentForm {...params} />}
        filterForm={(params) => <FilterDepartmentForm {...params} />}
        viewPermit="department-view"
        createPermit="department-create"
        updatePermit="department-update"
        deletePermit="department-delete"
    />
);

export default Departments;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
