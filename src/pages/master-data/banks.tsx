import { isArray } from "lodash";
import BankForm from "@/components/forms/master-data/BankForm";
import FilterBankForm from "@/components/forms/master-data/FilterBankForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { bankAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Banks = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/banks"
        api={bankAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "swift_code",
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item.code,
                      swift_code: item.swift_code,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <BankForm {...params} />}
        filterForm={(params) => <FilterBankForm {...params} />}
        viewPermit="master-bank-view"
        createPermit="master-bank-create"
        updatePermit="master-bank-update"
        deletePermit="master-bank-delete"
    />
);

export default Banks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
