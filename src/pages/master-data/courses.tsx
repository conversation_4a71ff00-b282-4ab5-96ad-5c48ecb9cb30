import { lowerCase } from "lodash";
import CourseForm from "@/components/forms/master-data/CourseForm";
import FilterCourseForm from "@/components/forms/master-data/FilterCourseForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { courseAPI, TableColumnType, ACTIVE, INACTIVE } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Courses = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/courses"
        api={courseAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];

            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      is_active: lowerCase(item.is_active ? ACTIVE : INACTIVE),
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <CourseForm {...params} />}
        filterForm={(params) => <FilterCourseForm {...params} />}
        noDelete={true}
        viewPermit="master-course-view"
        createPermit="master-course-create"
        updatePermit="master-course-update"
        deletePermit="master-course-delete"
    />
);

export default Courses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
