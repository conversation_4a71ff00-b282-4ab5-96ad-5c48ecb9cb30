import FilterEmployeeJobTitleForm from "@/components/forms/employee/FilterEmployeeJobTitleForm";
import EmployeeSessionForm from "@/components/forms/employee-session/EmployeeSessionForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { employeeSessionsAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const EmployeeSessions = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/employee-sessions"
        api={employeeSessionsAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <EmployeeSessionForm {...params} />}
        filterForm={(params) => <FilterEmployeeJobTitleForm {...params} />}
        formSize="medium"
        viewPermit="master-employee-session-view"
        createPermit="master-employee-session-create"
        updatePermit="master-employee-session-update"
        deletePermit="master-employee-session-delete"
    />
);

export default EmployeeSessions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
