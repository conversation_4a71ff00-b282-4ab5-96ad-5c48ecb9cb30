import EducationForm from "@/components/forms/master-data/EducationForm";
import FilterEducationForm from "@/components/forms/master-data/FilterEducationForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { educationAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Educations = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/educations"
        api={educationAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <EducationForm {...params} />}
        filterForm={(params) => <FilterEducationForm {...params} />}
        viewPermit="master-education-view"
        createPermit="master-education-create"
        updatePermit="master-education-update"
        deletePermit="master-education-delete"
    />
);

export default Educations;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
