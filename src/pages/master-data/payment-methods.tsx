import { isArray, lowerCase } from "lodash";
import FilterPaymentMethodForm from "@/components/forms/master-data/FilterPaymentMethodForm";
import PaymentMethodForm from "@/components/forms/master-data/PaymentMethodForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    INACTIVE,
    TableColumnType,
    paymentMethodsAPI,
} from "@/lib/constant";

const PaymentMethods = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/payment-methods"
        api={paymentMethodsAPI}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item.code,
                      name: item.name,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        useTableDataForForm={true}
        form={(params) => <PaymentMethodForm {...params} />}
        filterForm={(params) => <FilterPaymentMethodForm {...params} />}
        viewPermit="master-payment-method-view"
        createPermit="master-payment-method-create"
        updatePermit="master-payment-method-update"
        deletePermit="master-payment-method-delete"
    />
);

export default PaymentMethods;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
