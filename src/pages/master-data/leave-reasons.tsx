import FilterLeaveReasonForm from "@/components/forms/master-data/FilterLeaveReasonForm";
import LeaveReasonForm from "@/components/forms/master-data/LeaveReasonForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { leaveReasonAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const LeaveReasons = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/leave-reasons"
        api={leaveReasonAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <LeaveReasonForm {...params} />}
        filterForm={(params) => <FilterLeaveReasonForm {...params} />}
        viewPermit="master-leave-reason-view"
        createPermit="master-leave-reason-create"
        updatePermit="master-leave-reason-update"
        deletePermit="master-leave-reason-delete"
    />
);

export default LeaveReasons;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
