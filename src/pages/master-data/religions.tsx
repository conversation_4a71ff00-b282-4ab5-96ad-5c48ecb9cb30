import FilterReligionForm from "@/components/forms/master-data/FilterReligionForm";
import ReligionForm from "@/components/forms/master-data/ReligionForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { religionAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Religions = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/religions"
        api={religionAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <ReligionForm {...params} />}
        filterForm={(params) => <FilterReligionForm {...params} />}
        viewPermit="master-religion-view"
        createPermit="master-religion-create"
        updatePermit="master-religion-update"
        deletePermit="master-religion-delete"
    />
);

export default Religions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
