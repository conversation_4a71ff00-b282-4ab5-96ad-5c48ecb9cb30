import FilterStateForm from "@/components/forms/master-data/FilterStateForm";
import StateForm from "@/components/forms/master-data/StateForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { stateAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const States = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/states"
        api={stateAPI}
        definedColumn={(activeLanguages) => {
            return [
                {
                    key: "country",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      country: item?.country?.name,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "country"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <StateForm {...params} />}
        filterForm={(params) => <FilterStateForm {...params} />}
        viewPermit="master-state-view"
        createPermit="master-state-create"
        updatePermit="master-state-update"
        deletePermit="master-state-delete"
    />
);

export default States;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
