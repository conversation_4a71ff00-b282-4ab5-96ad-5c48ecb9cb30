import FilterGradeForm from "@/components/forms/master-data/FilterGradeForm";
import GradeForm from "@/components/forms/master-data/GradeForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { gradeAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Grades = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/grades"
        api={gradeAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <GradeForm {...params} />}
        filterForm={(params) => <FilterGradeForm {...params} />}
        viewPermit="master-grade-view"
        createPermit="master-grade-create"
        updatePermit="master-grade-update"
        deletePermit="master-grade-delete"
    />
);

export default Grades;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
