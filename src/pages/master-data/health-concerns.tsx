import FilterHealthConcernForm from "@/components/forms/master-data/FilterHealthConcernForm";
import HealthConcernForm from "@/components/forms/master-data/HealthConcernForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { healthConcernAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const HealthConcerns = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/health-concerns"
        api={healthConcernAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <HealthConcernForm {...params} />}
        filterForm={(params) => <FilterHealthConcernForm {...params} />}
        viewPermit="master-health-concern-view"
        createPermit="master-health-concern-create"
        updatePermit="master-health-concern-update"
        deletePermit="master-health-concern-delete"
    />
);

export default HealthConcerns;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
