import { isArray, lowerCase } from "lodash";
import FilterCurrencyForm from "@/components/forms/master-data/FilterCurrencyForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { ACTIVE, currencyAPI, INACTIVE, TableColumnType } from "@/lib/constant";

const Currencies = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/currencies"
        api={currencyAPI}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "symbol",
                    hasSort: true,
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item?.code,
                      name: item?.name,
                      symbol: item?.symbol,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return ["is_active"].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        useTableDataForForm={true}
        form={() => <></>}
        noDelete={true}
        filterForm={(params) => <FilterCurrencyForm {...params} />}
        viewPermit="master-currency-view"
    />
);

export default Currencies;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
