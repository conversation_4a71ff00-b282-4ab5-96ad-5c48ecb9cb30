import CountryForm from "@/components/forms/master-data/CountryForm";
import FilterCountryForm from "@/components/forms/master-data/FilterCountryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { countryAPI } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Countries = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/countries"
        api={countryAPI}
        definedColumn={(activeLanguages) => {
            return getNameColumns(activeLanguages);
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <CountryForm {...params} />}
        filterForm={(params) => <FilterCountryForm {...params} />}
        viewPermit="master-country-view"
        createPermit="master-country-create"
        updatePermit="master-country-update"
        deletePermit="master-country-delete"
    />
);

export default Countries;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
