import { isArray } from "lodash";
import FilterRaceForm from "@/components/forms/master-data/FilterRaceForm";
import RaceForm from "@/components/forms/master-data/RaceForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { raceAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Races = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/races"
        api={raceAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <RaceForm {...params} />}
        filterForm={(params) => <FilterRaceForm {...params} />}
        viewPermit="master-race-view"
        createPermit="master-race-create"
        updatePermit="master-race-update"
        deletePermit="master-race-delete"
    />
);

export default Races;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
