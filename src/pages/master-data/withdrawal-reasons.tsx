import { isArray, lowerCase } from "lodash";
import FilterWithdrawalReasonForm from "@/components/forms/master-data/FilterWithdrawalReasonForm";
import WithdrawalReasonForm from "@/components/forms/master-data/WithdrawalReasonForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    INACTIVE,
    TableColumnType,
    withdrawalReasonsAPI,
} from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const WithdrawalReasons = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/withdrawal-reasons"
        api={withdrawalReasonsAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      ...item?.translations?.name,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return ["sequence", "is_active"].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        useTableDataForForm={true}
        form={(params) => <WithdrawalReasonForm {...params} />}
        filterForm={(params) => <FilterWithdrawalReasonForm {...params} />}
        viewPermit="master-withdrawal-reason-view"
        createPermit="master-withdrawal-reason-create"
        updatePermit="master-withdrawal-reason-update"
        deletePermit="master-withdrawal-reason-delete"
    />
);

export default WithdrawalReasons;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
