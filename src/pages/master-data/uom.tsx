import { isArray, lowerCase } from "lodash";
import FilterUomForm from "@/components/forms/master-data/FilterUomForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { ACTIVE, INACTIVE, TableColumnType, uomAPI } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Uom = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="master-data/uom"
        api={uomAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item?.code,
                      ...item?.translations?.name,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return ["is_active"].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={() => <></>}
        noDelete={true}
        filterForm={(params) => <FilterUomForm {...params} />}
        viewPermit="master-uom-view"
    />
);

export default Uom;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
