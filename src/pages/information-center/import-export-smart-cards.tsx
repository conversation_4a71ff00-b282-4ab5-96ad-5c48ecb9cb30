import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { capitalize, isArray, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FileInputExcel from "@/components/ui/FileInputExcel";
import FormRadioGroup from "@/components/ui/FormRadioGroup";
import FormSelect from "@/components/ui/FormSelect";
import Tabs from "@/components/ui/Tabs";
import {
    cardBulkAssignAPI,
    cardExportAPI,
    cardImportAPI,
    cardTemplateAPI,
    CONTRACTOR,
    EMPLOYEE,
    EXCEL,
    PDF,
    STUDENT,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const _import = "import";
const _export = "export";

const ImportExportSmartCards = ({ locale }) => {
    const userProfile = useUserProfile((state) => state.userProfile);
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [type, setType] = useState(_import);

    const tabList = [
        ...(hasPermit("card-import") ? [_import] : []),
        ...(hasPermit("card-report") ? [_export] : []),
    ];

    const displayTabs = tabList.map((key) => t(key));

    return (
        <Layout
            locale={locale}
            path={"information-center/import-export-smart-cards"}
        >
            {userProfile && (
                <Card styleClass="table-card">
                    <h2 className="mb-4 capitalize">
                        {`${t("import")} / ${t("export")} ${t("smart cards")}`}
                    </h2>
                    <Tabs
                        list={displayTabs}
                        selected={t(type)}
                        setSelected={(label) => {
                            const selectedKey = tabList.find(
                                (key) => t(key) === label
                            );
                            if (selectedKey) setType(selectedKey);
                        }}
                    />
                    {type === _import && <ImportSmartCards locale={locale} />}
                    {type === _export && <ExportSmartCards />}
                </Card>
            )}
        </Layout>
    );
};

const ImportSmartCards = ({ locale }) => {
    useCheckViewPermit("card-import");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            user_type: STUDENT,
        },
    });

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [fileCleared, setFileCleared] = useState(0);
    const [showTable, setShowTable] = useState(false);
    const [data, setData] = useState<any[]>([]);

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "number",
                displayAs: `${capitalize(t(form.watch("user_type"))) + t(" number")}`,
            },
            {
                key: "name",
                displayAs: `${capitalize(t(form.watch("user_type"))) + t(" name")}`,
                modify: (value: string | null | undefined) => (
                    <div className="min-w-[100px]">{value ?? "-"}</div>
                ),
            },
            {
                key: "card_number",
            },
            {
                key: "card_number2",
                displayAs: `${t("Card number")} 2`,
            },
            {
                key: "card_number3",
                displayAs: `${t("Card number")} 3`,
            },
            {
                key: "card_type",
                displayAs: t("card type"),
            },
            {
                key: "update_library_card",
                displayAs: `${t("Update ")}${t("library ")}${t("card number")}`,
            },
            {
                key: "error",
                displayAs: t("error message"),
                modify: (list) => (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li
                                      key={index}
                                      className="table-item-li text-[13px] text-red-600 marker:text-red-300"
                                  >
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                ),
            },
            {
                key: "_",
                hasSort: false,
                modify: (_, cell) => {
                    const index = cell.row.index;
                    return (
                        <div className="flex h-full items-center">
                            <div
                                className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                                onClick={() => handleRemoveRow(index)}
                            >
                                <X size={14} className="text-themeGreenDark" />
                            </div>
                        </div>
                    );
                },
            },
        ];
        setColumns(_columns);
    }

    function definedData() {
        const userType = form.watch("user_type")?.toLowerCase();
        return isArray(data)
            ? data.map((item) => ({
                  number: item?.number ?? "-",
                  name: item?.[`${userType}_name`]?.[locale],
                  card_number: item?.card_number ?? "-",
                  card_number2: item?.card_number2 ?? "-",
                  card_number3: item?.card_number3 ?? "-",
                  card_type: item?.card_type ?? "-",
                  update_library_card: item?.update_library_card,
                  error: item?.errors,
              }))
            : [];
    }

    const { axiosQuery: getTemplate } = useAxios({
        api: cardTemplateAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosPost: assignCards } = useAxios({
        api: cardBulkAssignAPI,
        onSuccess: () => onClear(),
        toastMsg: t("Assigned successfully"),
    });

    function onDownloadTemplate() {
        getTemplate({ params: { type: form.getValues("user_type") } });
    }

    const handleImportData = (response) => {
        const successData = response.success ?? [];
        const errorData = response.errors ?? [];
        const combinedData = [...successData, ...errorData];

        if (combinedData.length > 0) {
            setData(combinedData);
            setShowTable(true);
        } else {
            setData([]);
            setShowTable(false);
        }
    };

    const handleRemoveRow = (index) => {
        const updatedData = data.filter((_, i) => i !== index);
        setData(updatedData);

        if (updatedData.length === 0) {
            setShowTable(false);
        }
    };

    function onClear() {
        setData([]);
        setShowTable(false);
        setFileCleared(fileCleared + 1);
    }

    function onSubmit() {
        form.clearErrors();
        console.log("submit data:", data);

        const hasErrors = data.some((row) => row.errors?.length > 0);

        if (hasErrors) {
            toast.error(
                t(
                    "Please revise the data to resolve all errors before submitting"
                )
            );
            return;
        }
        assignCards({
            type: form.getValues("user_type"),
            profiles: data,
        });
    }

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <>
            <Form {...form}>
                <form className="grid gap-2">
                    <FormRadioGroup
                        control={form.control}
                        name="user_type"
                        label={t("type") + "*"}
                        isHorizontal={true}
                        options={[
                            { id: STUDENT, name: t("Student") },
                            { id: EMPLOYEE, name: t("Employee") },
                            { id: CONTRACTOR, name: t("Coach") },
                        ]}
                        onChange={onClear}
                    />

                    {form.getValues("user_type") && (
                        <div className="flex items-start gap-3">
                            {hasPermit("card-download-template") && (
                                <Button
                                    variant={"outline"}
                                    onClick={onDownloadTemplate}
                                >
                                    {t("Download Template")}
                                </Button>
                            )}

                            {hasPermit("card-bulk-assignment") && (
                                <FileInputExcel
                                    api={cardImportAPI}
                                    extraImportParams={{
                                        type: form.watch("user_type"),
                                    }}
                                    handleImportData={handleImportData}
                                    clearTrigger={fileCleared}
                                />
                            )}
                        </div>
                    )}
                </form>
            </Form>
            {showTable && (
                <>
                    <div className="overflow-x-auto pt-4">
                        <DataTable columns={columns} data={definedData()} />
                    </div>

                    <div className="mt-4 flex justify-end gap-2">
                        <Button
                            type="reset"
                            variant="outline"
                            className="ml-auto"
                            onClick={onClear}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button type="submit" onClick={onSubmit}>
                            {t("Submit")}
                        </Button>
                    </div>
                </>
            )}
        </>
    );
};

const ExportSmartCards = () => {
    useCheckViewPermit("card-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();
    const t = useTranslations("common");

    const [reportData, setReportData] = useState<any>();

    const form = useForm<any>({
        defaultValues: {
            type: STUDENT,
            report_language: locale,
            export_type: "",
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "number",
            displayAs: `${capitalize(t(form.watch("user_type"))) + t(" number")}`,
            modify: (value) => value ?? "-",
        },
        {
            key: "name",
            displayAs: `${capitalize(t(form.watch("user_type"))) + t(" name")}`,
            modify: (value) => value ?? "-",
        },
        ...(form.watch("type") === STUDENT
            ? [
                  {
                      key: "class_name",
                  },
              ]
            : []),
        {
            key: "card_number",
        },
        {
            key: "card_number2",
            displayAs: `${t("Card number")} 2`,
        },
        {
            key: "card_number3",
            displayAs: `${t("Card number")} 3`,
        },
    ];

    const { axiosQuery: getReportsUrl } = useAxios({
        api: cardExportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportData } = useAxios({
        api: cardExportAPI,
        locale,
        onSuccess(result) {
            const userType = form.watch("type")?.toLowerCase();
            if (Array.isArray(result.data)) {
                setReportData(
                    result.data.map((item) => ({
                        number: item?.[`${userType}_number`],
                        name: item?.[`${userType}_name`]?.[locale],
                        class_name: item?.class_name?.[locale] ?? "-",
                        card_number: item?.card_number ?? "-",
                        card_number2: item?.card_number2 ?? "-",
                        card_number3: item?.card_number3 ?? "-",
                    }))
                );
            } else {
                setReportData(null);
            }
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        setReportData(null);
        form.handleSubmit((data) => {
            if (!data.export_type) {
                getReportData({
                    params: data,
                });
                return;
            }
            getReportsUrl({
                params: data,
            });
        })();
    }

    return (
        <>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="mb-5 grid max-w-[800px] gap-2 lg:grid-cols-2"
                >
                    <div className="lg:col-span-2">
                        <FormRadioGroup
                            control={form.control}
                            name="type"
                            label={t("type") + "*"}
                            isHorizontal={true}
                            options={[
                                { id: STUDENT, name: t("Student") },
                                { id: EMPLOYEE, name: t("Employee") },
                                { id: CONTRACTOR, name: t("Coach") },
                            ]}
                            onChange={() => setReportData(null)}
                        />
                    </div>

                    <FormSelect
                        control={form.control}
                        name="report_language"
                        label={t("report language") + "*"}
                        options={activeLanguages?.map((language) => ({
                            id: language?.code,
                            name: t(language?.name),
                        }))}
                        onChange={() => setReportData(null)}
                    />
                    <FormSelect
                        control={form.control}
                        name="export_type"
                        options={[
                            { id: PDF, name: "PDF" },
                            { id: EXCEL, name: "Excel" },
                        ]}
                    />
                    <Button type="submit" className="mt-2 capitalize">
                        {t("export")}
                    </Button>
                </form>
            </Form>
            {Array.isArray(reportData) &&
                (isEmpty(reportData) ? (
                    <div className="h-20 text-center text-themeLabel">
                        {t("No Record Found")}
                    </div>
                ) : (
                    <DataTable columns={columns} data={reportData} />
                ))}
        </>
    );
};

export default ImportExportSmartCards;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
