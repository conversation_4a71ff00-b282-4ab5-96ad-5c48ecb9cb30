import { useEffect, useState } from "react";
import { isArray } from "lodash";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import CardDeactivateForm from "@/components/forms/CardDeactivateForm";
import CardForm from "@/components/forms/CardForm";
import FilterCardForm from "@/components/forms/FilterCardForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import FreeInput from "@/components/ui/FreeInput";
import Modal from "@/components/ui/Modal";
import RaisedButton from "@/components/ui/RaisedButton";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import TrainerSearchEngine from "@/components/ui/search-engines/TrainerSearchEngine";
import {
    cardAPI,
    TableColumnType,
    STUDENT,
    EMPLOYEE,
    DEFAULT_FILTER_PARAMS,
    ACTIVE,
    CONTRACTOR,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    getUserableType,
    optionUserLabel,
    refreshForUpdate,
} from "@/lib/utils";
import { useTranslations } from "next-intl";

const SmartCards = ({ locale }) => {
    useCheckViewPermit("card-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [cards, setCards] = useState<any>(null);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState<any>(null);

    const [openFilter, setOpenFilter] = useState(false);
    const [openCreate, setOpenCreate] = useState(false);
    const [targetId, setTargetId] = useState(null);
    const [deactivateId, setDeactivateId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [selectedUser, setSelectedUser] = useState<any>();

    function definedColumns() {
        const _columns: TableColumnType[] = [
            {
                key: "card_number",
                hasSort: true,
                modify: (value) => value ?? "-",
            },
            {
                key: "card_number2",
                displayAs: `${t("Card number")} 2`,
                hasSort: true,
                modify: (value) => value ?? "-",
            },
            {
                key: "card_number3",
                displayAs: `${t("Card number")} 3`,
                hasSort: true,
                modify: (value) => value ?? "-",
            },
            {
                key: "status",
                hasSort: true,
                modify: (value) => (
                    <div className={`cell-status ${value.toLowerCase()}`}>
                        {t(value)}
                    </div>
                ),
            },
            {
                key: "remarks",
            },
        ];
        return _columns;
    }

    function definedData() {
        return isArray(cards)
            ? cards.map((item) => {
                  const { userable, ...rest } = item;
                  return {
                      ...rest,
                      remarks: item.remarks ?? "-",
                  };
              })
            : [];
    }

    const filterAllCardsForm = useForm<any>({
        defaultValues: {
            card_number: "",
        },
    });

    const { axiosQuery: getCards } = useAxios({
        api: cardAPI,
        locale,
        onSuccess(result) {
            setCards(result.data);
            setPagination(result.pagination);
        },
        onError() {
            setCards(null);
            setPagination(null);
        },
    });

    const { axiosQuery: getCardsForSearchAll } = useAxios({
        api: cardAPI,
        locale,
        onSuccess(result) {
            const user = result.data[0]?.userable;
            if (!user) {
                toast.error("No card found");
                return;
            }
            setSelectedUser({
                id: user?.userable_id,
                type: user?.userable_type,
                name: optionUserLabel(user?.number, user?.translations?.name),
            });
        },
    });

    function fetchCards() {
        getCards({
            params: {
                userable_id: selectedUser?.id,
                userable_type: selectedUser?.type,
                ...filter,
            },
        });
    }

    function fetchCardsByCardNumberOnly() {
        filterAllCardsForm.handleSubmit((data) => {
            getCardsForSearchAll({
                params: data,
            });
        })();
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function onClear() {
        filterAllCardsForm.reset();
        setSelectedUser(null);
        setCards(null);
        setPagination(null);
    }

    useEffect(() => {
        console.log("selecedUser", selectedUser);
        if (selectedUser) {
            fetchCards();
        }
    }, [selectedUser, filter, locale]);

    return (
        <Layout locale={locale} path={"information-center/smart-cards"}>
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">{t("smart cards")}</h2>
                <SearchCardForm setSelectedUser={setSelectedUser} />
                <div className="mt-4 border-t border-dashed pt-3">
                    <h3 className="ml-0.5">{t("Search by Card Number")}</h3>
                    <div className="mt-2.5 flex flex-wrap items-start gap-2">
                        <FreeInput
                            hasLabel={false}
                            placeholder={t("Card number")}
                            control={filterAllCardsForm.control}
                            name="card_number"
                            min={10}
                            max={10}
                            rules={{
                                required: t("Please enter card number"),
                                minLength: {
                                    value: 10,
                                    message: t(
                                        "Card number must be in 10 digits"
                                    ),
                                },
                                maxLength: {
                                    value: 10,
                                    message: t(
                                        "Card number must be in 10 digits"
                                    ),
                                },
                            }}
                            error={
                                filterAllCardsForm.formState.errors?.card_number
                            }
                        />
                        <Button
                            variant={"outline"}
                            onClick={fetchCardsByCardNumberOnly}
                        >
                            {t("Search")}
                        </Button>
                        <Button
                            variant={"outlineBlack"}
                            className="text-gray-600"
                            onClick={onClear}
                        >
                            {t("Clear")}
                        </Button>
                    </div>
                </div>

                {isArray(cards) && (
                    <>
                        <div className="mb-3 mt-5 flex items-center justify-between gap-5 border-t border-dashed pt-4">
                            <div>
                                <h3>{t("Existing Cards")}</h3>
                                <p className="mt-[2px] font-medium text-gray-600">
                                    {selectedUser?.name}
                                </p>
                            </div>
                            <div className="flex gap-3">
                                {hasPermit("card-create") && (
                                    <Button
                                        size="smallerOnMobile"
                                        className="capitalize"
                                        onClick={() => setOpenCreate(true)}
                                    >
                                        {t("Add ")}
                                        {t("smart card")}
                                    </Button>
                                )}
                                <TableFilterBtn
                                    filter={filter}
                                    onClick={() => setOpenFilter(true)}
                                />
                            </div>
                        </div>
                        <DataTable
                            data={definedData()}
                            columns={definedColumns()}
                            pagination={pagination}
                            setPagination={setPagination}
                            changePage={(arg) =>
                                setFilter({ ...filter, ...arg })
                            }
                            sorted={filter?.order_by}
                            sort={onSort}
                            actionMenu={({ cell }) => (
                                <>
                                    <ActionDropdown>
                                        {hasPermit("card-update") && (
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() =>
                                                    setTargetId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                {t("Edit / View")}
                                            </DropdownMenuItem>
                                        )}
                                        {hasPermit("card-update") &&
                                            cell.row.original.status ===
                                                ACTIVE && (
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() =>
                                                        setDeactivateId(
                                                            cell.row.original.id
                                                        )
                                                    }
                                                >
                                                    {t("Deactivate")}
                                                </DropdownMenuItem>
                                            )}
                                        <DropdownMenuSeparator />
                                        {hasPermit("card-delete") && (
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                {t("Delete")}
                                            </DropdownMenuItem>
                                        )}
                                    </ActionDropdown>
                                </>
                            )}
                        />
                    </>
                )}
                <div className="h-3"></div>
            </Card>
            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <CardForm
                    isCreate={true}
                    close={() => setOpenCreate(false)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    userable={{
                        userable_id: selectedUser?.id,
                        userable_type: selectedUser?.type,
                    }}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <CardForm
                    id={targetId}
                    close={() => setTargetId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>

            {/* update */}
            <Modal open={deactivateId} onOpenChange={setDeactivateId}>
                <CardDeactivateForm
                    id={deactivateId}
                    close={() => setDeactivateId(null)}
                    refresh={fetchCards}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={cardAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchCards}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterCardForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

const SearchCardForm = ({ setSelectedUser }) => {
    const t = useTranslations("common");
    const [opentype, setOpenType] = useState<
        typeof STUDENT | typeof EMPLOYEE | typeof CONTRACTOR | null
    >(null);

    return (
        <>
            <div className="flex gap-3">
                <RaisedButton
                    name={`${t("Select ")}${t("Student")}`}
                    onClick={() => setOpenType(STUDENT)}
                />
                <RaisedButton
                    name={`${t("Select ")}${t("Employee")}`}
                    onClick={() => setOpenType(EMPLOYEE)}
                />
                <RaisedButton
                    name={`${t("Select ")}${t("Coach")}`}
                    onClick={() => setOpenType(CONTRACTOR)}
                />
            </div>

            <Modal
                open={opentype === STUDENT}
                onOpenChange={setOpenType}
                size="large"
            >
                <StudentSearchEngine
                    setSelection={(student) => {
                        if (student) {
                            setSelectedUser({
                                id: student?.id,
                                type: getUserableType(STUDENT),
                                name: optionUserLabel(
                                    student?.student_number,
                                    student?.translations?.name
                                ),
                            });
                        }
                    }}
                    close={() => setOpenType(null)}
                />
            </Modal>
            <Modal
                open={opentype === EMPLOYEE}
                onOpenChange={setOpenType}
                size="large"
            >
                <StaffSearchEngine
                    setSelection={(employee) => {
                        console.log(employee);
                        if (employee) {
                            setSelectedUser({
                                id: employee?.id,
                                type: getUserableType(EMPLOYEE),
                                name: optionUserLabel(
                                    employee?.employee_number,
                                    employee?.translations?.name
                                ),
                            });
                        }
                    }}
                    close={() => setOpenType(null)}
                />
            </Modal>
            <Modal
                open={opentype === CONTRACTOR}
                onOpenChange={setOpenType}
                size="large"
            >
                <TrainerSearchEngine
                    setSelection={(trainer) => {
                        console.log(trainer);
                        if (trainer) {
                            setSelectedUser({
                                id: trainer?.id,
                                type: getUserableType(CONTRACTOR),
                                name: optionUserLabel(
                                    trainer?.contractor_number,
                                    trainer?.translations?.name
                                ),
                            });
                        }
                    }}
                    close={() => setOpenType(null)}
                />
            </Modal>
        </>
    );
};

export default SmartCards;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
