import { isArray } from "lodash";
import FilterSchoolAttendancePeriodOverrideForm from "@/components/forms/attendance/FilterSchoolAttendancePeriodOverrideForm";
import SchoolAttendancePeriodOverrideForm from "@/components/forms/attendance/SchoolAttendancePeriodOverrideForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    schoolAttendancePeriodOverrideAPI,
    TableColumnType,
} from "@/lib/constant";
import { displayTime, toYMD } from "@/lib/utils";

const SchoolAttendancePeriodOverride = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="attendance-management/school-attendance-period-override"
        api={schoolAttendancePeriodOverrideAPI}
        // otherFilterParams={{ includes: [] }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "from",
                    hasSort: true,
                },
                {
                    key: "to",
                    hasSort: true,
                },
                {
                    key: "attendance_from",
                },
                {
                    key: "attendance_to",
                },
                {
                    key: "remarks",
                },
                {
                    key: "priority",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      from: toYMD(item?.from),
                      to: toYMD(item?.to),
                      attendance_from: displayTime(item?.attendance_from),
                      attendance_to: displayTime(item?.attendance_to),
                      remarks: item?.remarks ?? "-",
                      priority: item?.priority,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <SchoolAttendancePeriodOverrideForm {...params} />}
        filterForm={(params) => (
            <FilterSchoolAttendancePeriodOverrideForm {...params} />
        )}
        viewPermit="school-attendance-period-override-view"
        createPermit="school-attendance-period-override-create"
        updatePermit="school-attendance-period-override-update"
        deletePermit="school-attendance-period-override-delete"
    />
);

export default SchoolAttendancePeriodOverride;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
