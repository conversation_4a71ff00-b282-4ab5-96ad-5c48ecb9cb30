import { isArray } from "lodash";
import AttendanceInputForm from "@/components/forms/attendance/AttendanceInputForm";
import FilterAttendanceInputForm from "@/components/forms/attendance/FilterAttendanceInputForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    contractorAttendanceInputAPI,
    TableColumnType,
    DATE_FORMAT,
    CONTRACTOR,
    attendanceInputAPI,
} from "@/lib/constant";
import {
    toYMD,
    combinedNames,
    displayDateTime,
    getUserableType,
} from "@/lib/utils";

const CoachAttendanceInputs = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="attendance-management/coach-attendance-inputs"
        api={contractorAttendanceInputAPI}
        deleteAPI={attendanceInputAPI}
        otherFilterParams={{
            includes: ["attendanceRecordable", "terminal", "card", "updatedBy"],
            attendance_recordable_type: getUserableType(CONTRACTOR),
        }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "number",
                    displayAs: "Coach Number",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                    modify: (value) => (
                        <div className="min-w-[177px]">{value}</div>
                    ),
                },
                {
                    key: "date",
                    hasSort: true,
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "record_datetime",
                    displayAs: "Tapped Card at",
                    hasSort: true,
                    modify: (value) => (
                        <span className="text-[14px]">{value}</span>
                    ),
                },
                {
                    key: "terminal",
                    modify: (value) => (
                        <span className="text-[14px]">{value}</span>
                    ),
                },
                {
                    key: "remarks",
                    displayAs: "Remarks",
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "updated_by",
                    displayAs: "Created/Updated By",
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      number: item?.attendance_recordable?.number,
                      date: toYMD(item?.date),
                      name: combinedNames(
                          item?.attendance_recordable?.translations?.name
                      ),
                      terminal: item?.terminal?.name ?? "-",
                      remarks: item.remarks ?? "-",
                      record_datetime: displayDateTime(
                          item?.record_datetime,
                          DATE_FORMAT.forDisplay
                      ),
                      updated_by: item?.updated_by?.name ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <AttendanceInputForm {...params} type={CONTRACTOR} />}
        filterForm={(params) => (
            <FilterAttendanceInputForm {...params} type={CONTRACTOR} />
        )}
        viewPermit="contractor-attendance-input-view"
        createPermit="attendance-input-create"
        deletePermit="attendance-input-delete"
    />
);

export default CoachAttendanceInputs;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
