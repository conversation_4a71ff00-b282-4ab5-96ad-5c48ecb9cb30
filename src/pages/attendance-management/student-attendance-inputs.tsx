import { isArray } from "lodash";
import AttendanceInputForm from "@/components/forms/attendance/AttendanceInputForm";
import FilterAttendanceInputForm from "@/components/forms/attendance/FilterAttendanceInputForm";
import {
    studentAttendanceInputAPI,
    TableColumnType,
    DATE_FORMAT,
    STUDENT,
    attendanceInputAPI,
    studentAPI,
    DEFAULT_FILTER_PARAMS,
} from "@/lib/constant";
import {
    toYMD,
    combinedNames,
    displayDateTime,
    getUserableType,
} from "@/lib/utils";
import { useEffect, useState } from "react";
import React from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { refreshForUpdate } from "@/lib/utils";
import { useUserProfile } from "@/lib/store";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useForm } from "react-hook-form";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const StudentAttendanceInputs = ({ locale }) => {
    useCheckViewPermit("student-attendance-input-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const defaultFilterParams = {
        ...DEFAULT_FILTER_PARAMS,
        includes: ["attendanceRecordable", "terminal", "card", "updatedBy"],
        attendance_recordable_type: getUserableType(STUDENT),
        order_by: {
            record_datetime: "desc",
        },
    };

    const { asyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
    });

    const [filter, setFilter] =
        useState<Record<string, any>>(defaultFilterParams);

    const form = useForm({
        defaultValues: {
            attendance_recordable_id: filter?.attendance_recordable_id ?? "",
        },
    });

    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getStudentAttendance } = useAxios({
        api: studentAttendanceInputAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    const columns: TableColumnType[] = [
        {
            key: "number",
            displayAs: "Student Number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
            modify: (value) => <div className="min-w-[177px]">{value}</div>,
        },
        {
            key: "date",
            hasSort: true,
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
        {
            key: "record_datetime",
            displayAs: "Tapped Card at",
            hasSort: true,
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "terminal",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "remarks",
            displayAs: "Remarks",
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
        {
            key: "updated_by",
            displayAs: "Created/Updated By",
        },
    ];

    useEffect(() => {
        getStudentAttendance({
            params: { ...defaultFilterParams, ...filter },
        });
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  number: item?.attendance_recordable?.number,
                  date: toYMD(item?.date),
                  name: combinedNames(
                      item?.attendance_recordable?.translations?.name
                  ),
                  terminal: item?.terminal?.name ?? "-",
                  remarks: item.remarks ?? "-",
                  record_datetime: displayDateTime(
                      item?.record_datetime,
                      DATE_FORMAT.forDisplay
                  ),
                  updated_by: item?.updated_by?.name ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
    }

    return (
        <Layout
            locale={locale}
            path="attendance-management/student-attendance-inputs"
        >
            <Card styleClass="table-card">
                <h2>School Attendance Inputs (Student)</h2>
                <div className="my-3 flex w-full flex-wrap items-center justify-between gap-3">
                    <FreeSelectAsync
                        control={form.control}
                        name="name"
                        placeholder="Type to search student"
                        minWidth={300}
                        hasLabel={false}
                        loadOptions={loadAsyncOptions}
                        value={asyncOptions.find(
                            (option) =>
                                option.value ===
                                form.getValues("attendance_recordable_id")
                        )}
                        onChange={(option) => {
                            form.setValue(
                                "attendance_recordable_id",
                                option?.value ?? ""
                            );
                            setFilter({
                                ...filter,
                                page: 1,
                                name: option?.label ?? undefined,
                                attendance_recordable_id:
                                    option?.value ?? undefined,
                            });
                        }}
                    />
                    <div className="flex items-center gap-x-3">
                        {hasPermit("attendance-input-create") && (
                            <Button onClick={() => setOpenCreate(true)}>
                                Add Attendance
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                            excludeFields={
                                defaultFilterParams
                                    ? Object.keys(defaultFilterParams)
                                    : undefined
                            }
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterAttendanceInputForm
                        type={STUDENT}
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit("attendance-input-delete") && (
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    Delete
                                </DropdownMenuItem>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <AttendanceInputForm
                    isCreate={true}
                    type={STUDENT}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={attendanceInputAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterAttendanceInputForm
                    type={STUDENT}
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default StudentAttendanceInputs;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
