import { useEffect, useState } from "react";
import { flatten } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import { DatePicker } from "@/components/ui/DatePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import MultiTypeTargetPicker from "@/components/ui/PickersWithPaginatedTable/MultiTypeTargetPicker";
import {
    STUDENT,
    EMPLOYEE,
    CONTRACTOR,
    attendancePostingAPI,
} from "@/lib/constant";
import { useCheckViewPermit, useAxios, useSubmit } from "@/lib/hook";
import {
    toYMD,
    showBackendFormError,
    isTypeStudent,
    isTypeEmployee,
    isTypeContractor,
} from "@/lib/utils";
import FormDivider from "@/components/ui/FormDivider";

const AttendancePosting = ({ locale }) => {
    useCheckViewPermit("trigger-attendance-posting");

    const form = useForm<any>({
        defaultValues: {
            date: "",
            student_ids: [],
            employee_ids: [],
            contractor_ids: [],
        },
    });

    const [receiversChunk, setReceiversChunk] = useState<any[]>([]);

    const { axiosPost: attendancePosting, error: postError } = useAxios({
        api: attendancePostingAPI,
        onSuccess: () => {
            form.reset();
            setReceiversChunk([]);
        },
        toastMsg: "Posted successfully",
    });

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                flatten(receiversChunk).forEach((receiver) => {
                    if (isTypeStudent(receiver.type)) {
                        data.student_ids.push(receiver.target_id);
                    } else if (isTypeEmployee(receiver.type)) {
                        data.employee_ids.push(receiver.target_id);
                    } else if (isTypeContractor(receiver.type)) {
                        data.contractor_ids.push(receiver.target_id);
                    }
                });
                data.is_all_students = data.is_all_students ? 1 : 0;
                data.is_all_employees = data.is_all_employees ? 1 : 0;
                data.is_all_contractors = data.is_all_contractors ? 1 : 0;
                data.date = toYMD(data.date);
                attendancePosting(data);
            })
        );
    }

    return (
        <Layout locale={locale} path="attendance-management/attendance-posting">
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">Attendance Posting</h2>

                <Form {...form}>
                    <form onSubmit={onSubmit} className="grid gap-2">
                        <div className="max-w-[200px]">
                            <DatePicker
                                control={form.control}
                                name={"date"}
                                label="date*"
                            />
                        </div>
                        <FormDivider hasColSpan={false} />

                        <MultiTypeTargetPicker
                            title="User List"
                            targetsChunk={receiversChunk}
                            setTargetsChunk={setReceiversChunk}
                            targetTypes={[STUDENT]}
                        />

                        <div className="ml-1 flex flex-wrap gap-5">
                            <FormCheckbox
                                control={form.control}
                                name="is_all_students"
                                label={"All Students"}
                            />

                            {/*<FormCheckbox*/}
                            {/*    control={form.control}*/}
                            {/*    name="is_all_employees"*/}
                            {/*    label={"All Employees"}*/}
                            {/*/>*/}

                            {/*<FormCheckbox*/}
                            {/*    control={form.control}*/}
                            {/*    name="is_all_contractors"*/}
                            {/*    label={"All Contractors"}*/}
                            {/*/>*/}
                        </div>

                        <Button
                            disabled={!form.watch("date")}
                            type="submit"
                            className="ml-auto mt-5"
                        >
                            POST
                        </Button>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default AttendancePosting;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
