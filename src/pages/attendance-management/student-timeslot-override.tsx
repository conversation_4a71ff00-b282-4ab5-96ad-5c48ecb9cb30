import { useEffect, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
    timeslotOverrideAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import {
    displayTime,
    getTableSelection,
    isValueTrue,
    optionUserLabel,
    refreshForUpdate,
} from "@/lib/utils";
import TimeslotOverrideForm from "@/components/forms/attendance/TimeslotOverrideForm";
import FilterTimeslotOverrideForm from "@/components/forms/attendance/FilterTimeslotOverrideForm";
import { useUserProfile } from "@/lib/store";
import { DialogFooter } from "@/components/base-ui/dialog";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const StudentTimeslotOverride = ({ locale }) => {
    useCheckViewPermit("timeslot-override-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [openBulkDelete, setOpenBulkDelete] = useState(false);
    const [bulkDeleteIds, setBulkDeleteIds] = useState([]);

    const { data, axiosQuery: getTimetable } = useAxios({
        api: timeslotOverrideAPI,
        locale,
        onSuccess: (result) => {
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
        },
    });

    const { axiosPost: bulkDelete } = useAxios({
        api: timeslotOverrideAPI + "/batch-delete",
        locale,
        toastMsg: "Deleted successfully",
        onSuccess: () => {
            refreshForUpdate(filter, setFilter);
            setOpenBulkDelete(false);
            setBulkDeleteIds([]);
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "date",
            hasSort: true,
            modify: (value) => (
                <span className="whitespace-nowrap">{value}</span>
            ),
        },
        {
            key: "student",
            modify: (value) => <div className="min-w-[240px]">{value}</div>,
        },
        {
            key: "period",
            hasSort: true,
            modify: (value) => <div className="pl-1">{value}</div>,
        },
        {
            key: "placeholder",
            hasSort: true,
        },
        {
            key: "attendance_from",
            hasSort: true,
        },
        {
            key: "attendance_to",
            hasSort: true,
        },
        {
            key: "class_attendance_required",
            hasSort: true,
            modify: (value) => (
                <div className="min-w-[152px] capitalize">{String(value)}</div>
            ),
        },
        {
            key: "inherit_from_school_attendance",
            hasSort: true,
            modify: (value) => (
                <div className="min-w-[162px] capitalize">{String(value)}</div>
            ),
        },
        {
            key: "is_empty",
            hasSort: true,
            modify: (value) => (
                <div className="capitalize">{String(value)}</div>
            ),
        },
    ];

    useEffect(() => {
        getTimetable({
            params: {
                ...filter,
                includes: ["student"],
            },
        });
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  date: item?.date,
                  period: item?.period,
                  placeholder: item?.placeholder ?? "-",
                  attendance_from: displayTime(item?.attendance_from),
                  attendance_to: displayTime(item?.attendance_to),
                  class_attendance_required: isValueTrue(
                      item?.class_attendance_required
                  ),
                  inherit_from_school_attendance: isValueTrue(
                      item?.inherit_from_school_attendance
                  ),
                  is_empty: isValueTrue(item?.is_empty),
                  student: optionUserLabel(
                      item?.student?.student_number,
                      item?.student?.translations?.name
                  ),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection: any = getTableSelection(
            selectedIds,
            navigatedResults
        );
        setBulkDeleteIds(_selection?.map((item) => item.id) ?? []);
    }

    function onBulkDelete() {
        console.log(bulkDeleteIds);
        bulkDelete({ ids: bulkDeleteIds });
        setOpenBulkDelete(false);
        setBulkDeleteIds([]);
    }

    return (
        <Layout
            locale={locale}
            path="attendance-management/student-timeslot-override"
        >
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>Timeslot Override</h2>
                    <div className="flex items-center gap-x-3">
                        {hasPermit("timeslot-override-create") && (
                            <Button onClick={() => setOpenCreate(true)}>
                                Add Timeslot Override
                            </Button>
                        )}
                        {hasPermit("timeslot-override-delete") && (
                            <Button
                                variant={"outline"}
                                className="border-red-500 text-red-600 hover:bg-red-50"
                                onClick={() =>
                                    bulkDeleteIds.length > 0 &&
                                    setOpenBulkDelete(true)
                                }
                            >
                                Bulk Delete
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterTimeslotOverrideForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    onMultiSelect={onMultiSelect}
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => {
                        setFilter({ ...filter, ...arg });
                    }}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={
                        hasPermit("timeslot-override-delete")
                            ? ({ cell }) => (
                                  <ActionDropdown>
                                      <DropdownMenuItem
                                          className="c-text-size text-red-600"
                                          onClick={() =>
                                              setTargetDeleteId(
                                                  cell.row.original.id
                                              )
                                          }
                                      >
                                          Delete
                                      </DropdownMenuItem>
                                  </ActionDropdown>
                              )
                            : undefined
                    }
                />
            </Card>

            {/* bulk create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="large">
                <TimeslotOverrideForm
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setOpenCreate(false)}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={timeslotOverrideAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterTimeslotOverrideForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* bulk delete */}
            <Modal open={openBulkDelete} onOpenChange={setOpenBulkDelete}>
                <>
                    <p className="mt-3 font-medium">
                        Are you sure you want to delete these?
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setOpenBulkDelete(false)}
                        >
                            Cancel
                        </Button>
                        <Button onClick={onBulkDelete}>Confirm</Button>
                    </DialogFooter>
                </>
            </Modal>
        </Layout>
    );
};

export default StudentTimeslotOverride;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
