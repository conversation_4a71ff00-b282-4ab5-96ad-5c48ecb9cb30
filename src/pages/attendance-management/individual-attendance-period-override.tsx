import { isArray } from "lodash";
import FilterIndividualAttendancePeriodOverrideForm from "@/components/forms/attendance/FilterIndividualAttendancePeriodOverrideForm";
import IndividualAttendancePeriodOverrideForm from "@/components/forms/attendance/IndividualAttendancePeriodOverrideForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    individualAttendancePeriodOverrideAPI,
    TableColumnType,
} from "@/lib/constant";
import { toYMD, combinedNames, displayTime } from "@/lib/utils";

const IndividualAttendancePeriodOverride = ({ locale }) => (
    <CommonTablePageWrap
        noCreate={true}
        noDelete={true}
        onlyView={true}
        locale={locale}
        path="attendance-management/individual-attendance-period-override"
        api={individualAttendancePeriodOverrideAPI}
        otherFilterParams={{
            includes: [
                "attendanceRecordable",
                "updatedBy",
                "leaveApplications",
            ],
        }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "number",
                    displayAs: "User Number",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "period",
                    hasSort: true,
                    modify: (value) => (
                        <span className="whitespace-nowrap">{value}</span>
                    ),
                },
                {
                    key: "attendance_from",
                },
                {
                    key: "attendance_to",
                },
                {
                    key: "created_via_leave_application",
                    displayAs: "Created via Leave Application?",
                },
                {
                    key: "updated_by",
                    displayAs: "Created/Updated By",
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      number: item?.attendance_recordable?.number,
                      name: combinedNames(
                          item?.attendance_recordable?.translations?.name
                      ),
                      period: toYMD(item?.period),
                      attendance_from: displayTime(item?.attendance_from),
                      attendance_to: displayTime(item?.attendance_to),
                      created_via_leave_application: item?.leave_application
                          ? "Yes"
                          : "No",
                      updated_by: item?.updated_by?.name ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => (
            <IndividualAttendancePeriodOverrideForm {...params} />
        )}
        filterForm={(params) => (
            <FilterIndividualAttendancePeriodOverrideForm {...params} />
        )}
        viewPermit="attendance-period-override-view"
        createPermit="attendance-period-override-create"
        updatePermit="attendance-period-override-update"
        deletePermit="attendance-period-override-delete"
    />
);

export default IndividualAttendancePeriodOverride;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
