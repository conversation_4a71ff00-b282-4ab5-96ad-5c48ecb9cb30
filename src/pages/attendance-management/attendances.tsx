import { useEffect, useState } from "react";
import { isArray, isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    attendanceAPI,
    semesterSettingAPI,
    semesterClassesAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    STUDENT,
    PRESENT,
    ABSENT,
    LATE,
    semesterClassOrderBy,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import {
    combinedNames,
    toYMD,
    convertDateTime,
    getUserableType,
} from "@/lib/utils";
import clsx from "clsx";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";

const Attendances = ({ locale }) => {
    useCheckViewPermit("attendance-view");

    const form = useForm<any>({
        defaultValues: {
            date: "",
        },
    });

    const defaultFilter = {
        page: 1,
        per_page: 50,
        order_by: {
            updated_at: "desc",
        },
    };
    const [hasSearched, setHasSearched] = useState(false);
    const [pagination, setPagination] = useState();
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [openSearch, setOpenSearch] = useState(false);
    const [filter, setFilter] = useState<Record<string, any>>({
        ...defaultFilter,
    });

    const { data: attendanceData, axiosQuery: getAttendances } = useAxios({
        api: attendanceAPI,
        locale,
        onSuccess: (result) => {
            setHasSearched(true);
            setPagination(result?.pagination);
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    useEffect(() => {
        getSemesterOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    semester_year: "desc",
                },
            },
        });
    }, []);

    const { data: semesterClasses, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
    });

    const classes =
        semesterClasses?.map((data) => ({
            id: data.id,
            name:
                data.class_model.translations.name[locale] ??
                data.class_model.name,
        })) || [];

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    semester_setting_id: form.watch("semester_setting_id"),
                    class_type: PRIMARY,
                    is_active: 1,
                    ...semesterClassOrderBy(locale),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    const columns = [
        {
            key: "number",
            displayAs: "User Number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
        },
        {
            key: "check_in_datetime",
            displayAs: "Checked in at",
        },
        {
            key: "check_in_status",
            displayAs: "Check In Status",
            hasSort: true,
        },
        {
            key: "check_out_datetime",
            displayAs: "Checked out at",
        },
        {
            key: "check_out_status",
            displayAs: "Check Out Status",
            hasSort: true,
        },
        {
            key: "status",
            displayAs: "Status",
            hasSort: true,
        },
    ];

    function definedData() {
        return isArray(attendanceData)
            ? attendanceData.map((item, index) => {
                  return {
                      id: item?.id,
                      number: item?.attendance_recordable?.number,
                      name: combinedNames(
                          item?.attendance_recordable?.translations?.name
                      ),
                      check_in_datetime:
                          convertDateTime(item?.check_in_datetime) ?? "-",
                      check_in_status: (
                          item?.check_in_status ?? "-"
                      ).replaceAll("_", " "),
                      check_out_datetime:
                          convertDateTime(item?.check_out_datetime) ?? "-",
                      check_out_status: (
                          item?.check_out_status ?? "-"
                      ).replaceAll("_", " "),
                      status: item?.status,
                  };
              })
            : [];
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function onFilter() {
        submit((data) => {
            if (data.date) {
                getAttendances({
                    params: {
                        ...filter,
                        includes: ["attendanceRecordable"],
                        date: toYMD(data.date),
                        semester_setting_id: data.semester_setting_id,
                        semester_class_id: data.semester_class_id,
                        attendance_recordable_id:
                            data.attendance_recordable_ids,
                        attendance_recordable_type: getUserableType(STUDENT),
                        // status: data.status,
                    },
                });
            }
        });
    }

    useEffect(() => {
        onFilter();
    }, [filter]);

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    return (
        <Layout locale={locale} path="attendance-management/attendances">
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">School Attendances</h2>

                <Form {...form}>
                    <form className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3">
                        <DatePicker
                            control={form.control}
                            name={"date"}
                            label="date*"
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="semester_class_id"
                            label="Class"
                            options={classes}
                            isDisabled={!form.watch("semester_setting_id")}
                        />

                        {/* <FormSelect
                            control={form.control}
                            name={"status"}
                            isStringOptions={true}
                            isSortByName={false}
                            options={[PRESENT, ABSENT, LATE]}
                        /> */}

                        <div>
                            <div className="c-text-size mb-1.5 font-medium capitalize text-themeLabel">
                                Students
                            </div>
                            <div
                                className={clsx(
                                    "c-text-size min-h-[42px] rounded-md border border-input py-2.5 pl-3.5 pr-2",
                                    form.watch("attendance_recordable_ids")
                                        ?.length > 0
                                        ? ""
                                        : "text-themeGray3"
                                )}
                                onClick={() => setOpenSearch(true)}
                            >
                                {form.watch("attendance_recordable_ids")
                                    ?.length > 0
                                    ? `${form.watch("attendance_recordable_ids").length} student${form.watch("attendance_recordable_ids").length > 1 ? "s" : ""} selected`
                                    : "Select Student"}
                            </div>
                        </div>

                        <Button
                            disabled={!form.watch("date")}
                            onClick={() => {
                                setFilter({
                                    ...defaultFilter,
                                    date: form.watch("date"),
                                });
                                onFilter();
                            }}
                        >
                            Filter
                        </Button>
                    </form>

                    {hasSearched ? (
                        isEmpty(attendanceData) ? (
                            <div className="h-20 text-center text-themeLabel">
                                No Record Found
                            </div>
                        ) : (
                            <div className="grid gap-y-5 overflow-auto pt-3">
                                <DataTable
                                    columns={columns}
                                    data={definedData()}
                                    pagination={pagination}
                                    setPagination={setPagination}
                                    changePage={(arg) =>
                                        setFilter({ ...filter, ...arg })
                                    }
                                    sorted={filter?.order_by}
                                    sort={onSort}
                                />
                            </div>
                        )
                    ) : null}
                </Form>
            </Card>

            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(students) => {
                        form.setValue(
                            "attendance_recordable_ids",
                            students.map((student) => student?.id)
                        );
                    }}
                    close={() => setOpenSearch(false)}
                    reset={() => form.setValue("attendance_recordable_ids", [])}
                />
            </Modal>
        </Layout>
    );
};

export default Attendances;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
