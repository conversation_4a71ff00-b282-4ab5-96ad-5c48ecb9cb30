import { useEffect, useMemo, useState } from "react";
import { isEmpty, isEqual } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FreeSelect from "@/components/ui/FreeSelect";
import {
    ABSENT,
    classAttendanceBulkUpdateAPI,
    classAttendanceGetPeriodsByDateAPI,
    classAttendanceGetStudentsAPI,
    DATE_FORMAT,
    GET_ALL_PARAMS,
    IconProfilePhotoPlaceholder,
    LATE,
    PRESENT,
    selectStyles,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    displayDateTime,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import Modal from "@/components/ui/Modal";
import AttendanceSummaryPrompt from "@/components/forms/attendance/AttendanceSummaryPrompt";
import Select from "react-select";
import FreeCheckbox from "@/components/ui/FreeCheckbox";
import Image from "next/image";
import PersonImagePreview from "@/components/ui/PersonImagePreview";
import { Label } from "@/components/base-ui/label";

const ClassAttendanceTaking = ({ locale }) => {
    useCheckViewPermit("class-attendance-taking-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const form = useForm<any>({
        defaultValues: {
            date: new Date(),
            timeslot_ids: [],
            timeslot_type: null,
            show_student_photo: false,
        },
    });

    const [hasSearched, setHasSearched] = useState(false);
    const [periodOptions, setPeriodOptions] = useState<any[]>([]);
    const [studentAttendances, setStudentAttendances] = useState<any[]>([]);
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [tableTitle, setTableTitle] = useState("");
    const [targetImage, setTargetImage] = useState<any>(null);

    const [openAttendanceSummary, setOpenAttendanceSummary] = useState(false);

    const [attendancePeriods, setAttendancePeriods] = useState({});
    const [attendanceSummary, setAttendanceSummary] = useState({});

    const selectedDate = form.watch("date");

    const isToday = useMemo(() => {
        return (
            selectedDate &&
            new Date(selectedDate).toDateString() === new Date().toDateString()
        );
    }, [selectedDate]);

    const { initLoader } = useSubmit();
    function onFilter(e) {
        if (e) {
            e.preventDefault();
        }
        initLoader(
            form.handleSubmit((data) => {
                data.date = toYMD(data.date);
                getStudentAttendance({
                    params: {
                        date: toYMD(data.date),
                        timeslot_ids: data.timeslot_ids,
                        timeslot_type: data.timeslot_type,
                    },
                });
            })
        );
    }

    function refresh() {
        getStudentAttendance({
            params: {
                date: toYMD(form.getValues("date")),
                timeslot_ids: form.getValues("timeslot_ids"),
                timeslot_type: form.getValues("timeslot_type"),
            },
        });
    }

    const { axiosQuery: getTeachingPeriodsByDate } = useAxios({
        api: classAttendanceGetPeriodsByDateAPI,
        locale,
        onSuccess: (result) => {
            if (!result.data) {
                setPeriodOptions([]);
                return;
            }

            const options = result.data?.map((item) => ({
                value: item.timeslot_id,
                label: item.label,
                canSave: !item.is_disabled,
                timeslotType: item.timeslot_type,
            }));
            setPeriodOptions(options);

            const currentPeriod = result.data.find(
                (option) => option.current_class === true
            );

            if (currentPeriod) {
                form.setValue("timeslot_ids", currentPeriod.timeslot_id);
                form.setValue("timeslot_type", currentPeriod.timeslot_type);
            }
        },
    });

    const { axiosQuery: getStudentAttendance } = useAxios({
        api: classAttendanceGetStudentsAPI,
        locale,
        onSuccess: (result) => {
            if (!result.data) {
                setStudentAttendances([]);
                setHasSearched(true);
                return;
            }

            const { students, periods } = result.data as any;

            setStudentAttendances(students);
            setAttendancePeriods(periods);

            const initialAttendances = {};
            Object.entries(periods).forEach(([periodKey, periodData]) => {
                initialAttendances[periodKey] = {
                    include: true,
                    students: {},
                };

                students.forEach((student) => {
                    initialAttendances[periodKey].students[student.student_id] =
                        {
                            is_editable:
                                student?.class_attendances?.[periodKey]
                                    ?.is_editable ?? true,
                            student_id: student.student_id,
                            class_attendance_status:
                                student?.class_attendances?.[periodKey]
                                    ?.class_attendance_status ?? ABSENT,
                            leave_application_info:
                                student?.class_attendances?.[periodKey]
                                    ?.leave_application_info ?? null,
                        };
                });
            });

            form.setValue("class_attendances", initialAttendances);

            const selectedPeriodLabel = periodOptions?.find((opt) =>
                isEqual(opt.value, form.watch("timeslot_ids"))
            )?.label;

            setHasSearched(true);
            setTableTitle(selectedPeriodLabel);
        },
    });

    const { axiosPost: updateClassAttendance, error: postError } = useAxios({
        api: classAttendanceBulkUpdateAPI,
        onSuccess: (result) => {
            close();
            refresh();

            if (result.data) {
                setOpenAttendanceSummary(true);
                setAttendanceSummary(result.data);
            }
        },
    });

    const handleUpdateAttendance = () => {
        const values = form.getValues();
        const payload = {
            date: toYMD(values.date),
            class_attendances: Object.entries(values.class_attendances || {})
                .filter(([_, v]) => (v as any)?.include)
                .map(([period, data]) => {
                    const typedData = data as {
                        include: boolean;
                        students: Record<
                            number,
                            {
                                student_id: number;
                                class_attendance_status:
                                    | "PRESENT"
                                    | "ABSENT"
                                    | "LATE";
                            }
                        >;
                    };

                    return {
                        period: Number(period),
                        students: Object.values(typedData.students),
                    };
                }),
        };

        updateClassAttendance({ ...payload });
    };

    function defineColumn() {
        const showStudentPhoto = form.watch("show_student_photo");

        const _columns: TableColumnType[] = [
            {
                key: "seat_no",
                modify: (_, cell) => cell.row.original?.seat_no ?? "-",
            },
            ...(showStudentPhoto
                ? [
                      {
                          key: "student_photo",
                          modify: (_, cell) => (
                              <div className="flex h-[80px] items-center justify-center">
                                  {cell.row.original?.student_photo ? (
                                      <Image
                                          src={cell.row.original?.student_photo}
                                          alt=""
                                          width={50}
                                          height={50}
                                          className="mx-auto max-h-full w-auto cursor-pointer rounded-sm"
                                          onClick={() => {
                                              setTargetImage({
                                                  student_name: combinedNames(
                                                      cell.row.original
                                                          ?.student_name_translations
                                                          ?.name
                                                  ),
                                                  url: cell.row.original
                                                      ?.student_photo,
                                              });
                                          }}
                                      />
                                  ) : (
                                      <Image
                                          src={IconProfilePhotoPlaceholder}
                                          alt=""
                                          width={50}
                                          height={50}
                                          className="mx-auto h-[50px] w-[50px] opacity-10"
                                          unoptimized
                                      />
                                  )}
                              </div>
                          ),
                      },
                  ]
                : []),
            {
                key: "student_number",
                modify: (_, cell) => cell.row.original?.student_number ?? "-",
            },
            {
                key: "student_name",
                modify: (_, cell) => (
                    <div className="min-w-[120px] text-[14px]">
                        {
                            cell.row.original?.student_name_translations
                                ?.name?.[locale]
                        }
                    </div>
                ),
            },
            {
                key: "primary_class",
                displayAs: "Primary Class",
                modify: (_, cell) => (
                    <div className="min-w-[100px] text-[14px]">
                        {cell.row.original?.current_class ?? "-"}
                    </div>
                ),
            },
            {
                key: "school_check_in_datetime",
                displayAs: "School Check In Datetime",
                modify: (_, cell) => (
                    <span className="max-w-[300px] whitespace-nowrap text-[14px]">
                        {displayDateTime(
                            cell.row.original?.school_check_in_datetime,
                            DATE_FORMAT.forDisplay
                        )}
                    </span>
                ),
            },
            ...(!isToday
                ? [
                      {
                          key: "leave_application_info",
                          displayAs: "Leave Application",
                          modify: (_, cell) => {
                              const student = cell.row.original;
                              const studentId = student?.student_id;
                              const classAttendances =
                                  form.getValues().class_attendances;

                              for (const periodKey of Object.keys(
                                  classAttendances || {}
                              )) {
                                  const periodData =
                                      classAttendances[periodKey];
                                  const studentAttendance =
                                      periodData?.students?.[studentId];
                                  const leaveInfo =
                                      studentAttendance?.leave_application_info;
                                  if (leaveInfo) {
                                      return leaveInfo;
                                  }
                              }

                              return "-";
                          },
                      },
                  ]
                : []),
            ...Object.entries(attendancePeriods).flatMap(
                ([periodKey, periodData]: any) => [
                    {
                        key: `class_attendance_${periodKey}`,
                        modifyHeader: () => (
                            <div className="flex min-w-max flex-col items-center gap-1">
                                <span className="text-sm font-semibold">
                                    {periodData.label}
                                </span>
                                {Object.keys(attendancePeriods).length > 1 &&
                                    isToday && (
                                        <FreeCheckbox
                                            label="Class Attendance"
                                            control={form.control}
                                            name={`class_attendances.${periodKey}.include`}
                                        />
                                    )}
                            </div>
                        ),
                        modify: (_, cell) => {
                            const student = cell.row.original;
                            const studentId = student?.student_id;
                            const isEditable =
                                student?.class_attendances?.[periodKey]
                                    ?.is_editable !== false;

                            return (
                                <div className="min-w-[180px]">
                                    <FreeSelect
                                        control={form.control}
                                        isStringOptions
                                        name={`class_attendances.${periodKey}.students.${studentId}.class_attendance_status`}
                                        options={[ABSENT, LATE, PRESENT]}
                                        hasLabel={false}
                                        isDisabled={!isEditable}
                                        error={
                                            form.formState.errors
                                                ?.class_attendances?.[periodKey]
                                                ?.students?.[studentId]
                                                ?.class_attendance_status
                                        }
                                    />
                                </div>
                            );
                        },
                    },
                    {
                        key: `taken_by_${periodKey}`,
                        displayAs: "Taken By",
                        modify: (_, cell) => {
                            const student = cell.row.original;
                            const takenBy =
                                student?.class_attendances?.[periodKey]
                                    ?.updated_by_employee
                                    ?.employee_name_translations?.[locale] ??
                                "-";
                            const takenAt =
                                displayDateTime(
                                    student?.class_attendances?.[periodKey]
                                        ?.updated_by_employee
                                        ?.attendance_recorded_at,
                                    DATE_FORMAT.timeDisplay
                                ) ?? "-";
                            return (
                                <div className="min-w-[120px] text-[14px]">
                                    <div className="">{takenBy}</div>
                                    <div className="text-gray-500">
                                        {takenAt}
                                    </div>
                                </div>
                            );
                        },
                    },
                ]
            ),
        ];
        setColumns(_columns);
    }

    function definedData() {
        const classAttendances = form.watch("class_attendances");

        return studentAttendances.map((student) => {
            const studentId = student.student_id;

            const hasAbsentPeriod = Object.values(classAttendances || {}).some(
                (periodData: any) => {
                    const studentAttendance = periodData.students?.[studentId];
                    return (
                        studentAttendance?.class_attendance_status === "ABSENT"
                    );
                }
            );

            const hasLeaveApplicationInfo = Object.values(
                classAttendances || {}
            ).some((periodData: any) => {
                const studentAttendance = periodData.students?.[studentId];
                return studentAttendance?.leave_application_info;
            });

            return {
                ...student,
                hasAbsentPeriod,
                hasLeaveApplicationInfo,
            };
        });
    }

    useEffect(() => {
        if (form.watch("date")) {
            getTeachingPeriodsByDate({
                params: {
                    ...GET_ALL_PARAMS,
                    date: toYMD(form.watch("date")),
                },
            });
        }
    }, [form.watch("date")]);

    useEffect(() => {
        if (studentAttendances) {
            defineColumn();
        }
    }, [studentAttendances]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <Layout
            locale={locale}
            path="attendance-management/class-attendance-taking"
        >
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">Class Attendance Taking</h2>
                <Form {...form}>
                    <form
                        className="mb-4 grid items-start gap-x-3 gap-y-4 lg:grid-cols-3"
                        onSubmit={onFilter}
                    >
                        <DatePicker
                            control={form.control}
                            name={"date"}
                            label={"date*"}
                            disableDateAfterToday={true}
                            onChange={() => {
                                form.setValue("timeslot_ids", []);
                                setStudentAttendances([]);
                                setHasSearched(false);
                            }}
                        />
                        <div className="lg:col-span-2">
                            <Label className="label">Period*</Label>
                            <Select
                                isClearable
                                options={periodOptions}
                                value={
                                    periodOptions?.find(
                                        (option) =>
                                            JSON.stringify(option.value) ===
                                            JSON.stringify(
                                                form.watch("timeslot_ids")
                                            )
                                    ) ?? null
                                }
                                onChange={(selectedOption) => {
                                    form.setValue(
                                        "timeslot_ids",
                                        selectedOption?.value ?? null
                                    );
                                    form.setValue(
                                        "timeslot_type",
                                        selectedOption?.timeslotType ?? null
                                    );
                                }}
                                placeholder="Select Period"
                                styles={selectStyles}
                            />
                        </div>
                        <div className="flex items-center justify-between gap-x-5 px-1">
                            <FreeCheckbox
                                label="Show Student photo"
                                control={form.control}
                                name={`show_student_photo`}
                            />
                        </div>
                        <div className="lg:col-span-2">
                            <Button
                                type="submit"
                                variant={"outline"}
                                disabled={
                                    !form.watch("date") ||
                                    !form.watch("timeslot_ids")
                                }
                                className="lg:ml-auto"
                            >
                                Filter
                            </Button>
                        </div>
                    </form>

                    {hasSearched ? (
                        isEmpty(form.getValues("class_attendances")) ? (
                            <div className="h-20 pt-5 text-center text-themeLabel">
                                No Record Found
                            </div>
                        ) : (
                            <>
                                <div className="grid overflow-auto pb-8">
                                    {tableTitle && (
                                        <div className="report-table-title">
                                            {tableTitle}
                                        </div>
                                    )}

                                    <DataTable
                                        columns={columns}
                                        data={definedData()}
                                        styleClass="lg:overflow-visible pb-24 lg:pb-0"
                                    />

                                    <div className="mb-10 mt-2">
                                        <div className="text-[14px] text-gray-500">
                                            <span className="font-medium">
                                                {studentAttendances?.length}
                                            </span>{" "}
                                            students found
                                        </div>
                                    </div>
                                </div>
                                {hasPermit(
                                    "class-attendance-taking-bulk-update"
                                ) &&
                                    isToday &&
                                    periodOptions?.find(
                                        (opt) =>
                                            JSON.stringify(opt.value) ===
                                            JSON.stringify(
                                                form.watch("timeslot_ids")
                                            )
                                    )?.canSave && (
                                        <Button
                                            onClick={handleUpdateAttendance}
                                            className="ml-auto mt-4 px-8"
                                        >
                                            Save
                                        </Button>
                                    )}
                            </>
                        )
                    ) : null}
                </Form>
            </Card>

            {/* student photo */}
            <Modal
                open={targetImage != null}
                onOpenChange={(value) => {
                    if (value === false) {
                        setTargetImage(null);
                    }
                }}
            >
                <PersonImagePreview
                    studentName={targetImage?.student_name}
                    url={targetImage?.url}
                />
            </Modal>

            {/* Attendance summary */}
            <Modal
                open={openAttendanceSummary}
                onOpenChange={setOpenAttendanceSummary}
            >
                <AttendanceSummaryPrompt
                    attendanceSummary={attendanceSummary}
                    close={() => setOpenAttendanceSummary(false)}
                />
            </Modal>
        </Layout>
    );
};

export default ClassAttendanceTaking;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
