import { capitalize, isArray } from "lodash";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    APPROVED,
    leaveApplicationsAPI,
    PENDING,
    REJECTED,
    STUDENT,
    TableColumnType,
} from "@/lib/constant";
import { getUserableType, isValueTrue, optionUserLabel } from "@/lib/utils";
import clsx from "clsx";
import FilterLeaveApplicationForm from "@/components/forms/attendance/FilterLeaveApplicationForm";

const LeaveApplications = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="attendance-management/leave-applications/leave-applications"
        api={leaveApplicationsAPI}
        otherFilterParams={{
            leave_applicable_type: getUserableType(STUDENT),
            includes: [
                "leaveApplicable",
                "leaveApplicationType",
                "leaveApplicationPeriods",
            ],
        }}
        definedColumn={(): TableColumnType[] => {
            return [
                {
                    key: "date",
                    displayAs: "leave date",
                    modify: (value) => (
                        <div className="min-w-[100px]">{value}</div>
                    ),
                },
                {
                    key: "student_number",
                },
                {
                    key: "student_name",
                    modify: (value) => (
                        <div className="min-w-[250px]">{value}</div>
                    ),
                },
                {
                    key: "leave_application_type",
                    displayAs: "Leave Type",
                    modify: (value) => (
                        <div className="min-w-[100px]">{value}</div>
                    ),
                },
                {
                    key: "average_point_deduction",
                },
                {
                    key: "conduct_point_deduction",
                },
                {
                    key: "is_present",
                    displayAs: "Is Present",
                },
                {
                    key: "reason",
                    modify: (value) => (
                        <div className="min-w-[122px]">{value}</div>
                    ),
                },
                {
                    key: "status",
                    modify: (value) => (
                        <span
                            className={clsx(
                                value === APPROVED && "text-green-600",
                                value === PENDING && "text-yellow-500",
                                value === REJECTED && "text-red-500"
                            )}
                        >
                            {capitalize(value ?? "-")}
                        </span>
                    ),
                },
            ];
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      student_number: item?.leave_applicable?.student_number,
                      student_name: optionUserLabel(
                          item?.leave_applicable?.userable_reference_number,
                          item?.leave_applicable?.translations?.name
                      ),
                      leave_application_type:
                          item?.leave_application_type?.translations?.name?.[
                              locale
                          ] ?? "-",
                      date: item?.leave_application_periods?.[0]?.date ?? "-",
                      average_point_deduction:
                          item?.average_point_deduction ?? 0,
                      conduct_point_deduction:
                          item?.conduct_point_deduction ?? 0,
                      is_present: isValueTrue(item?.is_present) ? "YES" : "NO",
                      reason: item?.reason ?? "-",
                      remarks: item?.remarks ?? "-",
                      status: item?.status ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "is_active"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={() => <></>}
        filterForm={(params) => <FilterLeaveApplicationForm {...params} />}
        viewPermit="leave-application-view"
        // updatePermit="leave-application-update"
        deletePermit="leave-application-delete"
    />
);

export default LeaveApplications;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
