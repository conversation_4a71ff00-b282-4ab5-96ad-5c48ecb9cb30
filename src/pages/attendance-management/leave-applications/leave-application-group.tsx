import { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { indexOf } from "lodash";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormDivider from "@/components/ui/FormDivider";
import FormFileInput from "@/components/ui/FormFileInput";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import Modal from "@/components/ui/Modal";
import RaisedButton from "@/components/ui/RaisedButton";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    FULL,
    GET_ALL_PARAMS,
    leaveApplicationsAPI,
    leaveApplicationTypeAPI,
    PENDING,
    periodGroupByStudentAPI,
    STUDENT,
} from "@/lib/constant";
import {
    useAxios,
    useCheckViewPermit,
    usePaginatedTable,
    useSubmit,
} from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { combinedNames, showBackendFormError, toYMD } from "@/lib/utils";
import { X } from "lucide-react";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import PaginatedTableFilter from "@/components/ui/PaginatedTableFilter";

const LeaveApplicationGroup = ({ locale }) => {
    useCheckViewPermit("leave-application-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudents, setSelectedStudents] = useState<any[]>([]);
    const [targetsChunk, setTargetsChunk] = useState<any[]>([]);

    const [leaveApplicationTypeOptions, setLeaveApplicationTypeOptions] =
        useState<any[]>([]);
    const [periods, setPeriods] = useState<any>();
    const [currentFileUrl, setCurrentFileUrl] = useState("");
    const [dateRange, setDateRange] = useState<any>({
        startDate: new Date(),
        endDate: new Date(),
        key: "selection",
    });

    const form = useForm<any>({
        defaultValues: {
            leave_application_type_id: "",
            from_date: "",
            to_date: "",
            reason: "",
            status: PENDING,
            remarks: "",
            average_point_deduction: 0,
            conduct_point_deduction: 0,
            is_present: false,
            proof: null,
        },
    });

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
        resetClonedFilter,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    const { append: appendStudent, remove: removeStudent } = useFieldArray({
        control: form.control,
        name: "students",
    });

    const studentColumns = [
        {
            key: "student_number",
        },
        {
            key: "student_name",

            modify: (value, cell) => {
                return (
                    <div className="grid min-w-[200px] gap-y-1">
                        <span>{value}</span>
                    </div>
                );
            },
        },
        {
            key: "class_name",
            displayAs: "Class",
        },
        {
            key: "_",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students")?.map((student) => student.id),
                    cell.row.id
                );
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            removeStudent(index);
                            onRemove(cell.row.original?.id);
                        }}
                    />
                );
            },
        },
    ];

    function onAddStudent(selection) {
        const currentIds = form
            .getValues()
            .students?.map((student) => student.id);

        const newStudents = selection
            .filter((student) => !currentIds?.includes(student.id.toString()))
            .map((student) => ({
                id: student?.id,
                student_number: student?.student_number,
                name: combinedNames(student?.translations?.name),
            }));

        const updatedRecords = form.getValues("students");
        // setTargetsChunk(chunk(updatedRecords, CHUNKED_FILTER_PARAMS.per_page));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        onAdd(selection);
    }

    const { axiosQuery: getLeaveApplicationTypes } = useAxios({
        api: leaveApplicationTypeAPI,
        locale,
        onSuccess: (result) => {
            const options = result.data.map((res) => ({
                ...res,
                name: res.translations.name[locale],
            }));
            setLeaveApplicationTypeOptions(options);
        },
    });

    const { axiosMultipartPost: createLeaveApplication, error: postError } =
        useAxios({
            api: leaveApplicationsAPI,
            onSuccess: () => {
                form.reset();
                form.setValue("proof", null);
                setCurrentFileUrl("");
                setDateRange({
                    startDate: new Date(),
                    endDate: new Date(),
                    key: "selection",
                });
                setTargetsChunk([]);
                setPeriods([]);
                setSelectedStudents([]);
            },
        });

    const {
        axiosPost: getStudentPeriodGroupLabel,
        error: postPeriodGroupError,
    } = useAxios({
        api: periodGroupByStudentAPI,
        noToast: true,
        onSuccess: (result) => {
            if (result?.data) {
                setPeriods(result?.data);
            }
        },
        onError: () => {
            form.setValue("students", []);
            form.reset();
            form.setValue("proof", null);
            setDateRange({
                startDate: new Date(),
                endDate: new Date(),
                key: "selection",
            });
            setTargetsChunk([]);
        },
    }); //TODO: api - need to add logic when adding students with no timetable.

    const handleApplicationTypeChange = (selected: any) => {
        if (!selected) {
            form.setValue("average_point_deduction", "");
            form.setValue("conduct_point_deduction", "");
            form.setValue("is_present", false);
        } else {
            const applicationTypeData = leaveApplicationTypeOptions.find(
                (type) => selected == type.id
            );
            form.setValue(
                "average_point_deduction",
                applicationTypeData.average_point_deduction
            );
            form.setValue(
                "conduct_point_deduction",
                applicationTypeData.conduct_point_deduction
            );
            form.setValue("is_present", applicationTypeData.is_present);
        }
    };

    function fetchStudentPeriodGroupLabel(student_ids) {
        getStudentPeriodGroupLabel({
            student_ids: student_ids,
        });
    }

    const handleSelectAll = (val, groupId, period) => {
        const periodIds =
            period?.period_labels
                ?.filter((p) => p.can_apply_leave)
                ?.map((p) => p.id) ?? [];

        periodIds.forEach((id) => {
            form.setValue(
                `period_group_students.${groupId}.period_label_ids.${id}`,
                val
            );
        });

        form.setValue(
            `period_group_students.${groupId}.select_all_periods`,
            val
        );
    };

    const handleCheckboxChange = (id, checked, groupId, period) => {
        form.setValue(
            `period_group_students.${groupId}.period_label_ids.${id}`,
            checked
        );

        const periodIds =
            period?.period_labels
                ?.filter((p) => p.can_apply_leave)
                ?.map((p) => p.id) ?? [];
        const currentValues = periodIds.map((pid) =>
            form.getValues(
                `period_group_students.${groupId}.period_label_ids.${pid}`
            )
        );

        const allChecked = currentValues.every((val) => val === true);
        form.setValue(
            `period_group_students.${groupId}.select_all_periods`,
            allChecked
        );
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                // const periodGroupStudents = Object.entries(targetsChunk).map(
                //     ([groupId, students], index) => {
                //         const periodLabelIds =
                //             data.period_group_students?.[groupId]
                //                 ?.period_label_ids ?? [];

                //         const studentIds = students.map(
                //             (student) => student.id
                //         );

                //         return {
                //             period_group_id: parseInt(groupId, 10),
                //             period_label_ids: periodLabelIds.length
                //                 ? periodLabelIds
                //                 : [],
                //             student_ids: studentIds.length ? studentIds : [],
                //         };
                //     }
                // );

                const periodGroupStudents = Object.entries(targetsChunk).map(
                    ([groupId, students], index) => {
                        const studentIds = students.map(
                            (student) => student.id
                        );

                        const periodLabelMap =
                            data.period_group_students?.[groupId]
                                ?.period_label_ids ?? {};

                        const selectedLabelIds = Object.entries(periodLabelMap)
                            .filter(([_, checked]) => checked)
                            .map(([id]) => parseInt(id, 10));

                        return {
                            period_group_id: parseInt(groupId, 10),
                            period_label_ids: selectedLabelIds,
                            student_ids: studentIds,
                        };
                    }
                );

                if (dateRange) {
                    data.from_date = toYMD(dateRange.startDate);
                    data.to_date = toYMD(dateRange.endDate);
                }

                data.period_group_students = periodGroupStudents;

                if (data.proof) {
                    data.proof = data.proof?.[0];
                } else {
                    data.proof = null;
                }

                data.is_present = data.is_present ? 1 : 0;
                delete data.students;

                createLeaveApplication(data);
            })
        );
    }

    useEffect(() => {
        setCurrentFileUrl(form.getValues("proof"));
    }, [form.getValues("proof")]);

    useEffect(() => {
        showBackendFormError(form, postError || postPeriodGroupError);
    }, [postError, postPeriodGroupError]);

    useEffect(() => {
        const students = form.getValues().students;
        if (students?.length > 0) {
            const student_ids = students.map((student) => student.id);
            fetchStudentPeriodGroupLabel(student_ids);
        } else {
            form.resetField("period_group_students");
            setPeriods([]);
            setTargetsChunk([]);
        }
    }, [form.watch("students")]);

    useEffect(() => {
        if (periods?.length > 0) {
            const groupedStudents = periods.reduce((acc, group) => {
                acc[group.period_group_id] = group.students ?? [];
                return acc;
            }, {});

            setTargetsChunk(groupedStudents);
        }
    }, [periods]);

    useEffect(() => {
        getLeaveApplicationTypes({ params: GET_ALL_PARAMS });
    }, []);

    return (
        <Layout
            path="attendance-management/leave-applications/leave-application-group"
            locale={locale}
        >
            <Card styleClass="max-w-screen-2xl mx-auto">
                <div className="pb-7 lg:px-2">
                    <h2 className="mb-4 pt-2">Leave Application (Group)</h2>
                    <RaisedButton
                        name="Select Students"
                        onClick={() => setOpenSearch(true)}
                    />

                    {selectedStudents && periods && (
                        <div className="pt-5">
                            <Form {...form}>
                                <form
                                    onSubmit={onSubmit}
                                    className="mb-6 grid gap-x-3 gap-y-4 lg:grid-cols-3"
                                >
                                    <div className="lg:col-span-1">
                                        <DateRangePicker
                                            label="Date Range*"
                                            range={dateRange}
                                            setRange={setDateRange}
                                            error={
                                                form.formState.errors?.from_date
                                                    ?.message ||
                                                form.formState.errors?.to_date
                                                    ?.message
                                            }
                                        />
                                    </div>

                                    <FormSelect
                                        control={form.control}
                                        name={"leave_application_type_id"}
                                        label="Leave Type*"
                                        options={leaveApplicationTypeOptions}
                                        onChange={(selected) =>
                                            handleApplicationTypeChange(
                                                selected
                                            )
                                        }
                                    />

                                    <FormFileInput
                                        name="proof"
                                        currentFileUrl={currentFileUrl}
                                        register={form.register}
                                        errors={form.formState.errors}
                                    />

                                    <FormInput
                                        control={form.control}
                                        name="reason"
                                        label="reason*"
                                    />

                                    <FormInputDecimal
                                        control={form.control}
                                        type="number"
                                        step="0.01"
                                        name="conduct_point_deduction"
                                        label="Conduct Point Deduction (Per Period)"
                                    />
                                    <FormInputDecimal
                                        control={form.control}
                                        type="number"
                                        step="0.01"
                                        name="average_point_deduction"
                                        label="Average Point Deduction (Per Period)"
                                    />

                                    <div className="lg:col-span-2">
                                        <FormTextarea
                                            control={form.control}
                                            name="remarks"
                                        />
                                    </div>

                                    <div className="mt-3 lg:mt-7">
                                        <FormCheckbox
                                            control={form.control}
                                            name="is_present"
                                            styleClass="mt-3 ml-1"
                                        />
                                    </div>

                                    <div className="lg:col-span-3">
                                        <FormDivider />
                                        {Object.entries(targetsChunk).map(
                                            ([groupId, students], index) => {
                                                if (students.length === 0)
                                                    return null;

                                                const period = periods.find(
                                                    (p) =>
                                                        p.period_group_id.toString() ===
                                                        groupId
                                                );
                                                const periodGroupName =
                                                    period?.period_group_name ??
                                                    `Period Group ${groupId}`;

                                                return (
                                                    <div
                                                        key={groupId}
                                                        className="pb-3 pt-3"
                                                    >
                                                        <h3 className="ml-0.5 text-themeGreenDark">
                                                            <span>
                                                                {
                                                                    periodGroupName
                                                                }
                                                            </span>
                                                            {form.formState
                                                                .errors
                                                                ?.period_group_students && (
                                                                <div className="warning-text mt-1">
                                                                    This field
                                                                    is required
                                                                </div>
                                                            )}
                                                        </h3>

                                                        <div className="z-10 mb-4 w-full flex-wrap items-center justify-between gap-3 pt-3">
                                                            <div>
                                                                <div className="grid gap-x-3 gap-y-2.5 px-1 sm:grid-cols-3 md:grid-cols-4 lg:col-span-3 lg:grid-cols-10">
                                                                    {period?.period_labels
                                                                        ?.filter(
                                                                            (
                                                                                p
                                                                            ) =>
                                                                                p.can_apply_leave
                                                                        )
                                                                        ?.map(
                                                                            (
                                                                                p
                                                                            ) => {
                                                                                return (
                                                                                    <FormCheckbox
                                                                                        key={
                                                                                            p.id
                                                                                        }
                                                                                        control={
                                                                                            form.control
                                                                                        }
                                                                                        name={`period_group_students.${groupId}.period_label_ids.${p.id}`}
                                                                                        label={
                                                                                            p
                                                                                                .translations
                                                                                                ?.name?.[
                                                                                                locale
                                                                                            ]
                                                                                        }
                                                                                        onChange={(
                                                                                            val
                                                                                        ) =>
                                                                                            handleCheckboxChange(
                                                                                                p.id,
                                                                                                val,
                                                                                                groupId,
                                                                                                period
                                                                                            )
                                                                                        }
                                                                                    />
                                                                                );
                                                                            }
                                                                        )}
                                                                </div>

                                                                <div className="mt-2.5 flex flex-col gap-y-2 pl-1 lg:col-span-3">
                                                                    <FormCheckbox
                                                                        control={
                                                                            form.control
                                                                        }
                                                                        name={`period_group_students.${groupId}.select_all_periods`}
                                                                        label="Select All"
                                                                        onChange={(
                                                                            val
                                                                        ) =>
                                                                            handleSelectAll(
                                                                                val,
                                                                                groupId,
                                                                                period
                                                                            )
                                                                        }
                                                                    />
                                                                </div>

                                                                {/* <PaginatedTableFilter
                                                                    type={
                                                                        STUDENT
                                                                    }
                                                                    filter={
                                                                        filter
                                                                    }
                                                                    setFilter={
                                                                        setFilter
                                                                    }
                                                                    reset={
                                                                        resetClonedFilter
                                                                    }
                                                                /> */}
                                                            </div>
                                                        </div>

                                                        <DataTable
                                                            isSmaller
                                                            columns={
                                                                studentColumns
                                                            }
                                                            data={
                                                                clonedFilteredChunk ??
                                                                students
                                                            }
                                                            hasPerPage={false}
                                                            sorted={
                                                                filter?.order_by
                                                            }
                                                            sort={onSort}
                                                            styleClass="overflow-visible"
                                                        />
                                                    </div>
                                                );
                                            }
                                        )}
                                    </div>

                                    {hasPermit("leave-application-create") && (
                                        <div className="flex justify-end lg:col-span-3">
                                            <Button type="submit">Save</Button>
                                        </div>
                                    )}
                                </form>
                            </Form>
                        </div>
                    )}
                </div>
            </Card>

            {/* search students */}
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(selection) => onAddStudent(selection)}
                    close={() => setOpenSearch(false)}
                    otherFilterParams={{
                        response: FULL,
                        includes: [
                            "currentSemesterPrimaryClass.semesterSetting",
                            "currentSemesterPrimaryClass.semesterClass.classModel",
                        ],
                    }}
                />
            </Modal>
        </Layout>
    );
};

export default LeaveApplicationGroup;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
