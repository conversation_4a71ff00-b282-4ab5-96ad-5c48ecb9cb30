import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import { capitalize } from "lodash";
import FilterLeaveApplicationHistoryForm from "@/components/forms/attendance/FilterLeaveApplicationHistoryForm";
import LeaveApplicationHistoryForm from "@/components/forms/attendance/LeaveApplicationHistoryForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DateRangePicker from "@/components/ui/DateRangePicker";
import DeletePrompt from "@/components/ui/DeletePrompt";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormDivider from "@/components/ui/FormDivider";
import FormFileInput from "@/components/ui/FormFileInput";
import FormInput from "@/components/ui/FormInput";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    APPROVED,
    DEFAULT_FILTER_PARAMS,
    FULL,
    GET_ALL_PARAMS,
    leaveApplicationHistoryAPI,
    leaveApplicationsAPI,
    leaveApplicationTypeAPI,
    PENDING,
    periodGroupByStudentAPI,
    REJECTED,
    STUDENT,
    studentAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    combineSemesterClass,
    getUserableType,
    optionUserLabel,
    refreshForUpdate,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import clsx from "clsx";
import { Check } from "lucide-react";
import LeaveApplicationStatusForm from "@/components/forms/attendance/LeaveApplicationStatusForm";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import InfoCard from "@/components/ui/InfoCard";

const LeaveApplicationIndividual = ({ locale }) => {
    useCheckViewPermit("leave-application-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const [openFilter, setOpenFilter] = useState(false);
    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [selectedStudent, setSelectedStudent] = useState<any>(null);
    const [leaveApplicationTypeOptions, setLeaveApplicationTypeOptions] =
        useState<any[]>([]);
    const [period, setPeriod] = useState<any>();
    const [currentFileUrl, setCurrentFileUrl] = useState("");
    const [dateRange, setDateRange] = useState<any>({
        startDate: new Date(),
        endDate: new Date(),
        key: "selection",
    });

    const [leaveApplicationHistory, setLeaveApplicationHistory] = useState<any>(
        []
    );
    const [pagination, setPagination] = useState(null);
    const [targetCreate, setTargetCreate] = useState(null);
    const [targetId, setTargetId] = useState(null);
    const [targetStatusId, setTargetStatusId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const form = useForm<any>({
        defaultValues: {
            student_id: null,
            leave_application_type_id: "",
            from_date: "",
            to_date: "",
            period_label_ids: [],
            reason: "",
            remarks: "",
            average_point_deduction: 0,
            conduct_point_deduction: 0,
            is_present: false,
            proof: null,
        },
    });

    const { axiosQuery: getLeaveApplicationTypes } = useAxios({
        api: leaveApplicationTypeAPI,
        locale,
        onSuccess: (result) => {
            const options = result.data.map((res) => ({
                ...res,
                name: res.translations.name[locale],
            }));
            setLeaveApplicationTypeOptions(options);
        },
    });

    const { axiosMultipartPost: createLeaveApplication, error: postError } =
        useAxios({
            api: leaveApplicationsAPI,
            onSuccess: () => {
                const studentId = form.getValues("student_id");
                form.reset();
                form.setValue("student_id", studentId);
                form.setValue("proof", null);
                form.setValue("period_label_ids", []);
                setDateRange({
                    startDate: new Date(),
                    endDate: new Date(),
                    key: "selection",
                });
                fetchLeaveApplicationHistory(true);
            },
        });

    const {
        axiosPost: getStudentPeriodGroupLabel,
        error: postPeriodGroupError,
    } = useAxios({
        api: periodGroupByStudentAPI,
        noToast: true,
        onSuccess: (result) => {
            if (result?.data) {
                const studentPeriod = result.data?.[0];
                setPeriod(studentPeriod);
                fetchLeaveApplicationHistory();
            }
        },
    });

    const { axiosPost: getLeaveApplicationHistory, error: postHistoryError } =
        useAxios({
            api: leaveApplicationHistoryAPI,
            locale,
            noToast: true,
            onSuccess: (result) => {
                setPagination(result?.pagination);
                setLeaveApplicationHistory(result.data);
            },
            onError: () => {
                setPagination(null);
                setLeaveApplicationHistory([]);
            },
        });

    const handleApplicationTypeChange = (selected: any) => {
        if (!selected) {
            form.setValue("average_point_deduction", "");
            form.setValue("conduct_point_deduction", "");
            form.setValue("is_present", false);
        } else {
            const applicationTypeData = leaveApplicationTypeOptions.find(
                (type) => selected == type.id
            );
            form.setValue(
                "average_point_deduction",
                applicationTypeData.average_point_deduction
            );
            form.setValue(
                "conduct_point_deduction",
                applicationTypeData.conduct_point_deduction
            );
            form.setValue("is_present", applicationTypeData.is_present);
        }
    };

    function fetchStudentPeriodGroupLabel() {
        getStudentPeriodGroupLabel({
            student_ids: [form.getValues("student_id")],
        });
    }

    function fetchLeaveApplicationHistory(isRefresh = false) {
        getLeaveApplicationHistory({
            ...(isRefresh ? DEFAULT_FILTER_PARAMS : filter),
            leave_applicable_type: getUserableType(STUDENT),
            leave_applicable_id: form.getValues("student_id"),
        });
    }

    function closeForm() {
        setTargetCreate(null);
        setTargetId(null);
    }

    function definedColumns() {
        const periodColumns = period?.period_labels?.map((p) => ({
            key: `${p?.id}`,
            hasSort: false,
            modifyHeader: (value) => (
                <div className="py-1 text-center">
                    {p?.translations?.name?.[locale]}
                </div>
            ),
        }));

        const columns: TableColumnType[] = [
            {
                key: "leave_application_type",
                displayAs: "Leave Type",
                hasSort: false,
            },
            {
                key: "date",
                hasSort: false,
                modify: (value) => (
                    <div className="whitespace-nowrap text-[14px]">{value}</div>
                ),
            },
            ...periodColumns,
            {
                key: "reason",
                hasSort: false,
            },
            {
                key: "status",
                hasSort: true,
                modify: (value) => (
                    <span
                        className={clsx(
                            value === APPROVED && "text-green-600",
                            value === PENDING && "text-yellow-500",
                            value === REJECTED && "text-red-500"
                        )}
                    >
                        {capitalize(value ?? "-")}
                    </span>
                ),
            },
        ];
        return columns;
    }

    function definedData() {
        return leaveApplicationHistory?.map((data) => {
            const periodData = period?.period_labels?.reduce((acc, p) => {
                const isApplied = data?.periods?.includes(p.period);
                acc[`${p.id}`] = isApplied ? (
                    <Check size={18} className="text-themeGreen" />
                ) : (
                    ""
                );
                return acc;
            }, {});

            return {
                id: data?.id,
                leave_application_id: data?.leave_application_id ?? null,
                leave_application_type: data?.leave_application_type?.[locale],
                date: data?.date ?? "-",
                reason: data?.reason ?? "-",
                status: data?.status ?? "-",
                absent_period_ids: data?.periods ?? [],
                ...periodData,
            };
        });
    }

    const handleSelectAll = (val) => {
        const newSelectAll = val;
        const periodIds =
            period?.period_labels
                ?.filter((p) => p.can_apply_leave)
                ?.map((p) => p.id) ?? [];
        periodIds.forEach((id) => {
            form.setValue(`period_label_ids.${id}`, newSelectAll);
        });
    };

    const handleCheckboxChange = (id, checked) => {
        form.setValue(`period_label_ids.${id}`, checked);

        if (!checked) {
            form.setValue("select_all_periods", false);
        } else {
            const periodIds =
                period?.period_labels
                    ?.filter((p) => p.can_apply_leave)
                    ?.map((p) => p.id) ?? [];
            const allChecked = periodIds.every((pid) =>
                form.getValues(`period_label_ids.${pid}`)
            );
            form.setValue("select_all_periods", allChecked);
        }
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                const selectedIds = Object.entries(data.period_label_ids || {})
                    .filter(([_, isChecked]) => isChecked)
                    .map(([id]) => Number(id));

                const periodGroupStudents: any[] = [];
                periodGroupStudents.push({
                    period_group_id: period?.period_group_id,
                    period_label_ids: selectedIds,
                    student_ids: [form.getValues("student_id")],
                });

                if (dateRange) {
                    data.from_date = toYMD(dateRange.startDate);
                    data.to_date = toYMD(dateRange.endDate);
                }

                data.period_group_students = periodGroupStudents;

                if (data.proof) {
                    data.proof = data.proof?.[0];
                } else {
                    data.proof = null;
                }

                data.is_present = data.is_present ? 1 : 0;
                delete data.period_label_ids;

                createLeaveApplication(data);
            })
        );
    }

    const { asyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
        showGuardians: true,
        params: {
            response: FULL,
            includes: [
                "currentSemesterPrimaryClass.semesterSetting",
                "currentSemesterPrimaryClass.semesterClass.classModel",
            ],
        },
        optionFormatter: (item) => ({
            value: item?.id,
            name: combinedNames(item?.translations?.name),
            student_number: item?.student_number,
            class: combineSemesterClass(item?.current_primary_class),
            label: optionUserLabel(
                item?.student_number,
                item?.translations?.name
            ),
            grade: capitalize(
                item?.current_primary_class?.semester_class?.class_model?.grade
                    ?.translations?.name?.[locale]
            ),
        }),
    });

    useEffect(() => {
        if (form.getValues("student_id")) {
            fetchLeaveApplicationHistory();
        }
    }, [filter]);

    useEffect(() => {
        setCurrentFileUrl(form.getValues("proof"));
    }, [form.getValues("proof")]);

    useEffect(() => {
        showBackendFormError(
            form,
            postError || postHistoryError || postPeriodGroupError
        );
    }, [postError, postHistoryError, postPeriodGroupError]);

    useEffect(() => {
        if (form.getValues("student_id")) {
            fetchStudentPeriodGroupLabel();
        } else {
            form.reset();
        }
    }, [form.watch("student_id")]);

    useEffect(() => {
        getLeaveApplicationTypes({ params: GET_ALL_PARAMS });
    }, []);

    return (
        <Layout
            path="attendance-management/leave-applications/leave-application-individual"
            locale={locale}
        >
            <Card styleClass="max-w-screen-2xl mx-auto">
                <div className="pb-7 lg:px-2">
                    <h2 className="mb-4 pt-2">
                        Leave Application (Individual)
                    </h2>
                    <div className="max-w-[500px]">
                        <FreeSelectAsync
                            control={form.control}
                            name="student_id"
                            label="Student*"
                            placeholder="Type to search student"
                            minWidth={300}
                            loadOptions={loadAsyncOptions}
                            value={asyncOptions.find(
                                (option) =>
                                    option.value ===
                                    form.getValues("student_id")
                            )}
                            onChange={(selected) => {
                                if (selected) {
                                    setSelectedStudent(selected);
                                } else {
                                    setSelectedStudent(null);
                                }
                            }}
                        />
                    </div>
                    {selectedStudent && (
                        <div className="pt-5">
                            <InfoCard
                                title="Student information"
                                data={{
                                    name: selectedStudent?.name,
                                    student_number:
                                        selectedStudent?.student_number,
                                    class: selectedStudent?.class,
                                }}
                                cardStyleClass="lg:min-w-[400px]"
                            />
                        </div>
                    )}
                    {form.watch("student_id") && period && (
                        <div className="pt-5">
                            <Form {...form}>
                                <form
                                    onSubmit={onSubmit}
                                    className="mb-4 grid gap-x-3 gap-y-4 lg:grid-cols-3"
                                >
                                    <div className="lg:col-span-1">
                                        <DateRangePicker
                                            label="Date Range*"
                                            range={dateRange}
                                            setRange={setDateRange}
                                            error={
                                                form.formState.errors?.from_date
                                                    ?.message ||
                                                form.formState.errors?.to_date
                                                    ?.message
                                            }
                                        />
                                    </div>

                                    <FormSelect
                                        control={form.control}
                                        name={"leave_application_type_id"}
                                        label="Leave Type*"
                                        options={leaveApplicationTypeOptions}
                                        onChange={(selected) =>
                                            handleApplicationTypeChange(
                                                selected
                                            )
                                        }
                                    />

                                    <FormFileInput
                                        name="proof"
                                        currentFileUrl={currentFileUrl}
                                        register={form.register}
                                        errors={form.formState.errors}
                                    />

                                    {/* <FormSelect
                                        isMulti={true}
                                        hasSelectAll={true}
                                        control={form.control}
                                        isSortByName={false}
                                        name="period_label_ids"
                                        label="absent periods*"
                                        options={
                                            period?.period_labels?.map((p) => ({
                                                id: p?.id,
                                                name: `${p?.translations?.name?.[locale]} (${p.time})`,
                                            })) ?? []
                                        }
                                        hideSelectedOptions={false}
                                        displayItemSelected={true}
                                        maxMenuHeight={425}
                                    /> */}
                                    <div className="my-1 border-y border-dashed pb-2 pt-3 lg:col-span-3">
                                        <div className="c-text-size mb-3.5 font-medium text-themeLabel">
                                            <span>Periods*</span>
                                            {form.formState.errors
                                                ?.period_group_students && (
                                                <div className="warning-text mt-1">
                                                    This field is required
                                                </div>
                                            )}
                                        </div>
                                        <div className="mb-2.5 grid grid-cols-2 gap-x-3 gap-y-2.5 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-10">
                                            {period?.period_labels
                                                ?.filter(
                                                    (p) => p.can_apply_leave
                                                )
                                                .map((p) => {
                                                    return (
                                                        <FormCheckbox
                                                            key={p.id}
                                                            control={
                                                                form.control
                                                            }
                                                            name={`period_label_ids.${p.id}`} // Unique name per period
                                                            label={`${p.translations?.name?.[locale]}`}
                                                            onChange={(val) =>
                                                                handleCheckboxChange(
                                                                    p.id,
                                                                    val
                                                                )
                                                            }
                                                        />
                                                    );
                                                })}
                                        </div>

                                        <FormCheckbox
                                            control={form.control}
                                            name="select_all_periods"
                                            label="Select All"
                                            onChange={(val) =>
                                                handleSelectAll(val)
                                            }
                                        />
                                    </div>

                                    <FormInput
                                        control={form.control}
                                        name="reason"
                                        label="reason*"
                                    />

                                    <FormInputDecimal
                                        control={form.control}
                                        type="number"
                                        step="0.01"
                                        name="average_point_deduction"
                                        label="Average Point Deduction (Per Period)"
                                    />

                                    <FormInputDecimal
                                        control={form.control}
                                        type="number"
                                        step="0.01"
                                        name="conduct_point_deduction"
                                        label="Conduct Point Deduction (Per Period)"
                                    />

                                    <div className="lg:col-span-2">
                                        <FormTextarea
                                            control={form.control}
                                            name="remarks"
                                            rows={1}
                                        />
                                    </div>

                                    <div className="mt-3 lg:mt-7">
                                        <FormCheckbox
                                            control={form.control}
                                            name="is_present"
                                            styleClass="ml-1"
                                        />
                                    </div>

                                    {hasPermit("leave-application-create") && (
                                        <div className="lg:col-span-3">
                                            <Button type="submit">Save</Button>
                                        </div>
                                    )}
                                </form>
                            </Form>

                            <FormDivider />
                            <div className="mb-3 mt-3 flex w-full flex-wrap items-end justify-between gap-3 lg:gap-x-3.5 lg:px-1">
                                <h3>Attendances</h3>
                                <div className="flex">
                                    <TableFilterBtn
                                        filter={filter}
                                        onClick={() => setOpenFilter(true)}
                                    />
                                </div>
                            </div>

                            <DataTable
                                columns={definedColumns()}
                                data={definedData()}
                                pagination={pagination}
                                setPagination={setPagination}
                                noCellMinWidth={true}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                styleClass="overflow-visible"
                                actionMenu={({ cell }) => {
                                    const leave_application_id =
                                        cell.row.original.leave_application_id;

                                    return (
                                        <>
                                            <ActionDropdown>
                                                {hasPermit(
                                                    "leave-application-create"
                                                ) &&
                                                    !leave_application_id && (
                                                        <DropdownMenuItem
                                                            className="c-text-size"
                                                            onClick={() =>
                                                                setTargetCreate(
                                                                    cell.row
                                                                        .original
                                                                )
                                                            }
                                                        >
                                                            Create Leave
                                                            Application
                                                        </DropdownMenuItem>
                                                    )}
                                                {hasPermit(
                                                    "leave-application-update"
                                                ) &&
                                                    leave_application_id && (
                                                        <DropdownMenuItem
                                                            className="c-text-size"
                                                            onClick={() =>
                                                                setTargetId(
                                                                    leave_application_id
                                                                )
                                                            }
                                                        >
                                                            Edit / View
                                                        </DropdownMenuItem>
                                                    )}
                                                {hasPermit(
                                                    "leave-application-approve"
                                                ) &&
                                                    leave_application_id && (
                                                        <DropdownMenuItem
                                                            className="c-text-size"
                                                            onClick={() =>
                                                                setTargetStatusId(
                                                                    leave_application_id
                                                                )
                                                            }
                                                        >
                                                            Change Status
                                                        </DropdownMenuItem>
                                                    )}
                                                {hasPermit(
                                                    "leave-application-delete"
                                                ) &&
                                                    leave_application_id && (
                                                        <>
                                                            <DropdownMenuSeparator />
                                                            <DropdownMenuItem
                                                                className="c-text-size text-red-600"
                                                                onClick={() =>
                                                                    setTargetDeleteId(
                                                                        leave_application_id
                                                                    )
                                                                }
                                                            >
                                                                Delete
                                                            </DropdownMenuItem>
                                                        </>
                                                    )}
                                            </ActionDropdown>
                                        </>
                                    );
                                }}
                            />
                        </div>
                    )}
                </div>
            </Card>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterLeaveApplicationHistoryForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* create */}
            <Modal
                open={targetCreate}
                onOpenChange={setTargetCreate}
                size="medium"
            >
                <LeaveApplicationHistoryForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                    createData={targetCreate}
                    allPeriods={period}
                    selectedStudent={selectedStudent}
                    leaveApplicationTypes={leaveApplicationTypeOptions}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <LeaveApplicationHistoryForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                    allPeriods={period}
                    selectedStudent={selectedStudent}
                    leaveApplicationTypes={leaveApplicationTypeOptions}
                />
            </Modal>

            {/* change status */}
            <Modal open={targetStatusId} onOpenChange={setTargetStatusId}>
                <LeaveApplicationStatusForm
                    id={targetStatusId}
                    status={
                        leaveApplicationHistory?.find(
                            (item) =>
                                item.leave_application_id == targetStatusId
                        )?.status
                    }
                    refresh={() => {
                        refreshForUpdate(filter, setFilter);
                        setTargetStatusId(null);
                    }}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={leaveApplicationsAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={() => fetchLeaveApplicationHistory(true)}
                />
            </Modal>
        </Layout>
    );
};

export default LeaveApplicationIndividual;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
