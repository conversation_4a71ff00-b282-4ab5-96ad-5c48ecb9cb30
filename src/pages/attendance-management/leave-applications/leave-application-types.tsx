import { isArray } from "lodash";
import LeaveApplicationTypeForm from "@/components/forms/attendance/LeaveApplicationTypeForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { leaveApplicationTypeAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns, isValueTrue } from "@/lib/utils";

const LeaveApplicationTypes = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="attendance-management/leave-applications/leave-application-types"
        api={leaveApplicationTypeAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
                {
                    key: "is_present",
                    displayAs: "Present",
                    hasSort: true,
                },
                {
                    key: "conduct_point_deduction",
                    displayAs: "Conduct Point Deduction",
                    hasSort: true,
                },
                {
                    key: "average_point_deduction",
                    displayAs: "Average Point Deduction",
                    hasSort: true,
                },
                {
                    key: "display_in_report_card",
                    displayAs: "Display in Report Card",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      is_present: isValueTrue(item?.is_present) ? "YES" : "NO",
                      conduct_point_deduction: item?.conduct_point_deduction,
                      average_point_deduction: item?.average_point_deduction,
                      display_in_report_card: isValueTrue(
                          item?.display_in_report_card
                      )
                          ? "YES"
                          : "NO",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return [
                "is_present",
                "conduct_point_deduction",
                "average_point_deduction",
                "display_in_report_card",
            ].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <LeaveApplicationTypeForm {...params} />}
        viewPermit="leave-application-type-view"
        createPermit="leave-application-type-create"
        updatePermit="leave-application-type-update"
        deletePermit="leave-application-type-delete"
    />
);

export default LeaveApplicationTypes;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
