import React, { useEffect, useState } from "react";
import { capitalize, isArray, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FileInputExcel from "@/components/ui/FileInputExcel";
import {
    enrollmentSessionsAPI,
    enrollmentPrePaymentTemplateAPI,
    GET_ALL_PARAMS,
    TableColumnType,
    enrollmentPrePayInsertAPI,
    enrollmentPrePayImportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { downloadFile } from "@/lib/utils";
import FreeSelect from "@/components/ui/FreeSelect";
import { useTranslations } from "next-intl";

const ImportNewStudents = ({ locale }) => {
    useCheckViewPermit("enrollment-create");

    const hasPermit = useUserProfile((state) => state.hasPermit);
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [fileCleared, setFileCleared] = useState(0);
    const [showTable, setShowTable] = useState(false);
    const [columns, setColumns] = useState<any[]>([]);
    const [data, setData] = useState<any[]>([]);
    const [displayData, setDisplayData] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            enrollment_session_id: null,
        },
    });

    function definedColumn(subjects) {
        const subjectColumns = subjects
            ?.filter((subject) => subject.name && subject.code)
            ?.map((subject) => ({
                key: subject?.code,
                displayAs: Object.values(subject?.name).join("/"),
            }));

        const _columns: TableColumnType[] = [
            {
                key: "number",
                displayAs: "No.",
                modify: (value) => <div className="text-center">{value}</div>,
            },
            {
                key: "exam_slip_number",
            },
            {
                key: "status",
            },
            {
                key: "expiry_date",
                modify: (value) => (
                    <span className="whitespace-nowrap text-sm">{value}</span>
                ),
            },
            {
                key: "register_date",
                modify: (value) => (
                    <span className="whitespace-nowrap text-sm">{value}</span>
                ),
            },
            ...(activeLanguages?.map((lang) => ({
                key: `Student Name (${lang?.name})`,
                modify: (value) => <div className="min-w-[120px]">{value}</div>,
            })) ?? []),
            {
                key: "gender",
            },
            {
                key: "nric",
                displayAs: "NRIC",
            },

            {
                key: "passport_number",
            },
            {
                key: "foreigner",
                modify: (value) => capitalize(value),
            },
            {
                key: "nationality",
            },
            {
                key: "religion",
            },
            {
                key: "address",
                modify: (value) => (
                    <div className="min-w-[120px] text-[14px] leading-tight">
                        {value}
                    </div>
                ),
            },
            {
                key: "hostel",
                modify: (value) => capitalize(value),
            },
            {
                key: "reason",
            },
            {
                key: "dietary_restriction",
            },
            {
                key: "health_concern",
            },
            {
                key: "have_siblings",
                modify: (value) => capitalize(value),
            },
            {
                key: "primary_school",
                modify: (value) => <div className="min-w-[120px]">{value}</div>,
            },
            {
                key: "total_average",
            },
            ...subjectColumns,
            {
                key: "conduct",
            },
            {
                key: "guardian_type",
            },
            {
                key: "guardian_name",
                modify: (value) => <div className="min-w-[120px]">{value}</div>,
            },
            {
                key: "guardian_phone_number",
            },

            {
                key: "guardian_email",
            },
            {
                key: "remarks",
                modify: (value) => (
                    <div className="min-w-[120px] text-[14px] leading-tight">
                        {value}
                    </div>
                ),
            },
            {
                key: "error",
                displayAs: "Error Message",
                modify: (list) => (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li
                                      key={index}
                                      className="table-item-li text-[13px] text-red-600 marker:text-red-300"
                                  >
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                ),
            },
            {
                key: "warning",
                displayAs: "Warning Message",
                modify: (list) => (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li
                                      key={index}
                                      className="table-item-li text-[13px] text-orange-500"
                                  >
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                ),
            },
            {
                key: "_",
                hasSort: false,
                modify: (_, cell) => {
                    const index = cell.row.index;
                    return (
                        <div className="flex h-full items-center">
                            <div
                                className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                                onClick={() => handleRemoveRow(index)}
                            >
                                <X size={14} className="text-themeGreenDark" />
                            </div>
                        </div>
                    );
                },
            },
        ];

        return _columns;
    }

    function definedData(responseData, subjects) {
        return isArray(responseData)
            ? responseData.map((item) => {
                  const newItem = {
                      number: item?.number ?? "-",
                      exam_slip_number: item?.exam_slip_number ?? "-",
                      gender: item?.gender ?? "-",
                      address: item?.address ?? "-",
                      conduct: item?.conduct ?? "-",
                      guardian_type: item?.guardian_type ?? "-",
                      guardian_email: item?.guardian_email ?? "-",
                      guardian_name: item?.guardian_name ?? "-",
                      guardian_phone_number: item?.guardian_phone_number ?? "-",
                      dietary_restriction: item?.dietary_restriction ?? "-",
                      health_concern: item?.health_concern ?? "-",
                      hostel: item?.hostel?.toString() ?? "-",
                      reason: item?.hostel_reason ?? "-",
                      have_siblings: item?.have_siblings?.toString() ?? "-",
                      foreigner: item?.foreigner?.toString() ?? "-",
                      nric: item?.nric ?? "-",
                      passport_number: item?.passport_number ?? "-",
                      primary_school: item?.primary_school ?? "-",
                      nationality: item?.nationality ?? "-",
                      religion: item?.religion ?? "-",
                      status: item?.status ?? "-",
                      register_date: item?.register_date ?? "-",
                      expiry_date: item?.expiry_date ?? "-",
                      total_average: item?.total_average ?? "-",
                      remarks: item?.remarks ?? "-",
                      error: item?.errors ? Object.values(item?.errors) : null,
                      warning: item?.warnings
                          ? Object.values(item?.warnings)
                          : null,
                  };
                  activeLanguages?.forEach((lang) => {
                      newItem[`Student Name (${lang?.name})`] =
                          item?.[`student_name_${lang?.code}`] ?? "-";
                  });
                  subjects?.forEach((subject) => {
                      if (subject?.name && subject?.code) {
                          newItem[subject?.code] = item?.[subject?.code] ?? "-";
                      }
                  });
                  return newItem;
              })
            : [];
    }

    const {
        data: enrollmentSessionOptions,
        axiosQuery: getEnrollmentSessions,
    } = useAxios({
        api: enrollmentSessionsAPI,
        locale,
    });

    const { axiosQuery: getPrePayTemplate } = useAxios({
        api: enrollmentPrePaymentTemplateAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosPost: bulkInsertEnrollment } = useAxios({
        api: enrollmentPrePayInsertAPI,
        onSuccess: () => onClear(),
        toastMsg: "Imported successfully",
    });

    function onDownloadPrePayTemplate() {
        getPrePayTemplate({
            params: {
                enrollment_session_id: form.getValues("enrollment_session_id"),
            },
        });
    }

    const handleImportData = (response) => {
        setShowTable(false);
        setColumns([]);
        setData([]);
        setDisplayData([]);
        if (response?.data?.length > 0) {
            const subjects = Object.values(response?.subject_lists ?? {});
            setColumns(definedColumn(subjects));
            setData(response?.data ?? []);
            setDisplayData(definedData(response?.data, subjects));
            setShowTable(true);
        }
    };

    const handleRemoveRow = (index) => {
        const updatedData = data.filter((_, i) => i !== index);
        setData(updatedData);

        if (updatedData.length === 0) {
            setShowTable(false);
        }
    };

    function onClear() {
        setData([]);
        setShowTable(false);
        setFileCleared(fileCleared + 1);
    }

    function onSubmit() {
        console.log("submit data:", data);

        const hasErrors = data.some((row) => !isEmpty(row.errors));

        if (hasErrors) {
            toast.error(
                "Please revise the data to resolve all errors before submitting"
            );
            return;
        }
        bulkInsertEnrollment(
            {
                enrollment_session_id: form.getValues("enrollment_session_id"),
                enrollments: data,
            },
            { showErrorInToast: true }
        );
    }

    useEffect(() => {
        getEnrollmentSessions({
            params: { ...GET_ALL_PARAMS, is_active: 1 },
        });
    }, []);

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path={"enrollment/import-new-students"}>
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">{t("Import New Students")}</h2>
                <div className="grid gap-4 pb-3">
                    <div className="w-full max-w-[370px]">
                        <FreeSelect
                            control={form.control}
                            hasLabel={false}
                            name="enrollment_session_id"
                            placeholder={t("Select Enrollment Session")}
                            options={enrollmentSessionOptions}
                        />
                    </div>
                    {form.watch("enrollment_session_id") && (
                        <div className="flex flex-wrap items-start gap-3 lg:flex-nowrap">
                            {hasPermit(
                                "enrollment-download-pre-payment-template"
                            ) && (
                                <Button
                                    variant={"outline"}
                                    onClick={onDownloadPrePayTemplate}
                                >
                                    {t("Download Template")}
                                </Button>
                            )}

                            {hasPermit(
                                "enrollment-import-template-validation"
                            ) && (
                                <FileInputExcel
                                    api={enrollmentPrePayImportAPI}
                                    handleImportData={handleImportData}
                                    clearTrigger={fileCleared}
                                    extraImportParams={{
                                        enrollment_session_id: form.watch(
                                            "enrollment_session_id"
                                        ),
                                    }}
                                />
                            )}
                        </div>
                    )}
                </div>
                {showTable && (
                    <>
                        <div className="overflow-x-auto pt-4">
                            <DataTable columns={columns} data={displayData} />
                        </div>

                        <div className="mt-4 flex justify-end gap-2">
                            <Button
                                type="reset"
                                variant="outline"
                                className="ml-auto"
                                onClick={onClear}
                            >
                                {t("Cancel")}
                            </Button>
                            {hasPermit(
                                "enrollment-bulk-save-imported-data"
                            ) && (
                                <Button type="submit" onClick={onSubmit}>
                                    {t("Submit")}
                                </Button>
                            )}
                        </div>
                    </>
                )}
            </Card>
        </Layout>
    );
};

export default ImportNewStudents;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
