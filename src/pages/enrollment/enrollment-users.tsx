import { isArray } from "lodash";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { enrollmentUsersAPI } from "@/lib/constant";
import FilterEnrollmentUserForm from "@/components/forms/enrollment/FilterEnrollmentUserForm";
import EnrollmentUserForm from "@/components/forms/enrollment/EnrollmentUserForm";
import { combinedNames, optionUserLabel } from "@/lib/utils";

const EnrollmentUsers = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="enrollment/enrollment-users"
        api={enrollmentUsersAPI}
        otherFilterParams={{
            includes: ["enrollments"],
        }}
        definedColumn={() => [
            { key: "name", hasSort: true },
            { key: "email", hasSort: true },
            { key: "phone_number", hasSort: true },
            { key: "nric", displayAs: "NRIC", hasSort: true },
            {
                key: "students",
                modify(list) {
                    return (
                        <ul>
                            {list.length > 0
                                ? list.map((student, index) => (
                                      <li key={index} className="table-item-li">
                                          <span>{student}</span>
                                      </li>
                                  ))
                                : "-"}
                        </ul>
                    );
                },
            },
        ]}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      email: item?.email ?? "-",
                      name: combinedNames(item?.translations?.name),
                      phone_number: item?.phone_number ?? "-",
                      nric: item?.nric ?? "-",
                      students:
                          item?.enrollments.map(
                              (student) =>
                                  `${combinedNames(student?.translations?.name)} - ${student?.nric ?? student?.passport_number ?? ""}`
                          ) || [],
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <EnrollmentUserForm {...params} />}
        filterForm={(params) => <FilterEnrollmentUserForm {...params} />}
        viewPermit="enrollment-user-view"
        createPermit="enrollment-user-create"
        updatePermit="enrollment-user-update"
        noCreate={true}
        noDelete={true}
    />
);

export default EnrollmentUsers;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
