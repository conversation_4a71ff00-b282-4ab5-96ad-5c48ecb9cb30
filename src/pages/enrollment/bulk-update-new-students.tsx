import React, { useEffect, useState } from "react";
import { capitalize, isArray, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FileInputExcel from "@/components/ui/FileInputExcel";
import {
    enrollmentSessionsAPI,
    enrollmentPostPaymentTemplateAPI,
    GET_ALL_PARAMS,
    TableColumnType,
    enrollmentPostPayInsertAPI,
    enrollmentPostPayImportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { downloadFile } from "@/lib/utils";
import FreeSelect from "@/components/ui/FreeSelect";
import { useTranslations } from "next-intl";

const BulkUpdateNewStudents = ({ locale }) => {
    useCheckViewPermit("enrollment-create");

    const hasPermit = useUserProfile((state) => state.hasPermit);
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [fileCleared, setFileCleared] = useState(0);
    const [showTable, setShowTable] = useState(false);
    const [columns, setColumns] = useState<any[]>([]);
    const [data, setData] = useState<any[]>([]);
    const [displayData, setDisplayData] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            enrollment_session_id: null,
        },
    });

    function definedColumn(subjects) {
        const subjectColumns = subjects
            ?.filter((subject) => subject.name && subject.code)
            ?.map((subject) => ({
                key: subject?.code,
                displayAs: Object.values(subject?.name).join("/"),
            }));

        const _columns: TableColumnType[] = [
            {
                key: "number",
                displayAs: "No.",
            },
            {
                key: "exam_slip_number",
            },
            ...(activeLanguages?.map((lang) => ({
                key: `Student Name (${lang?.name})`,
            })) ?? []),
            {
                key: "nric",
                displayAs: "NRIC",
            },
            {
                key: "passport_number",
            },
            {
                key: "total_average",
            },
            ...subjectColumns,
            {
                key: "status",
            },

            {
                key: "hostel",
                modify: (value) => capitalize(value),
            },
            {
                key: "error",
                displayAs: "Error Message",
                modify: (list) => (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li
                                      key={index}
                                      className="table-item-li text-[13px] text-red-600 marker:text-red-300"
                                  >
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                ),
            },
            {
                key: "warning",
                displayAs: "Warning Message",
                modify: (list) => (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li
                                      key={index}
                                      className="table-item-li text-[13px] text-orange-500"
                                  >
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                ),
            },
            {
                key: "_",
                hasSort: false,
                modify: (_, cell) => {
                    const index = cell.row.index;
                    return (
                        <div className="flex h-full items-center">
                            <div
                                className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                                onClick={() => handleRemoveRow(index)}
                            >
                                <X size={14} className="text-themeGreenDark" />
                            </div>
                        </div>
                    );
                },
            },
        ];

        return _columns;
    }

    function definedData(responseData, subjects) {
        return isArray(responseData)
            ? responseData.map((item) => {
                  const newItem = {
                      number: item?.number ?? "-",
                      exam_slip_number: item?.exam_slip_number ?? "-",
                      hostel: item?.hostel?.toString() ?? "-",
                      nric: item?.nric ?? "-",
                      passport_number: item?.passport_number ?? "-",
                      primary_school: item?.primary_school ?? "-",
                      status: item?.status ?? "-",
                      total_average: item?.total_average ?? "-",
                      error: item?.errors ? Object.values(item?.errors) : null,
                      warning: item?.warnings
                          ? Object.values(item?.warnings)
                          : null,
                  };
                  activeLanguages?.forEach((lang) => {
                      newItem[`Student Name (${lang?.name})`] =
                          item?.[`student_name_${lang?.code}`] ?? "-";
                  });
                  subjects?.forEach((subject) => {
                      if (subject?.name && subject?.code) {
                          newItem[subject?.code] = item?.[subject?.code] ?? "-";
                      }
                  });
                  return newItem;
              })
            : [];
    }

    const {
        data: enrollmentSessionOptions,
        axiosQuery: getEnrollmentSessions,
    } = useAxios({
        api: enrollmentSessionsAPI,
        locale,
    });

    const { axiosQuery: getPostPayTemplate } = useAxios({
        api: enrollmentPostPaymentTemplateAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosPost: bulkInsertEnrollment } = useAxios({
        api: enrollmentPostPayInsertAPI,
        onSuccess: () => onClear(),
        toastMsg: "Imported successfully",
    });

    function onDownloadPostPayTemplate() {
        getPostPayTemplate({
            params: {
                enrollment_session_id: form.getValues("enrollment_session_id"),
            },
        });
    }

    const handleImportData = (response) => {
        setShowTable(false);
        setColumns([]);
        setData([]);
        setDisplayData([]);
        if (response?.data?.length > 0) {
            const subjects = Object.values(response?.subject_lists ?? {});
            setColumns(definedColumn(subjects));
            setData(response?.data ?? []);
            setDisplayData(definedData(response?.data, subjects));
            setShowTable(true);
        }
    };

    const handleRemoveRow = (index) => {
        const updatedData = data.filter((_, i) => i !== index);
        setData(updatedData);

        if (updatedData.length === 0) {
            setShowTable(false);
        }
    };

    function onClear() {
        setData([]);
        setShowTable(false);
        setFileCleared(fileCleared + 1);
    }

    function onSubmit() {
        console.log("submit data:", data);

        const hasErrors = data.some((row) => !isEmpty(row.errors));

        if (hasErrors) {
            toast.error(
                "Please revise the data to resolve all errors before submitting"
            );
            return;
        }
        bulkInsertEnrollment(
            {
                enrollment_session_id: form.getValues("enrollment_session_id"),
                enrollments: data,
            },
            { showErrorInToast: true }
        );
    }

    useEffect(() => {
        getEnrollmentSessions({
            params: { ...GET_ALL_PARAMS, is_active: 1 },
        });
    }, []);

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path={"enrollment/bulk-update-new-students"}>
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">
                    {t("Bulk Update New Students")}
                </h2>
                <div className="grid gap-4 pb-3">
                    <div className="w-full max-w-[370px]">
                        <FreeSelect
                            control={form.control}
                            hasLabel={false}
                            name="enrollment_session_id"
                            placeholder={t("Select Enrollment Session")}
                            options={enrollmentSessionOptions}
                        />
                    </div>
                    {form.watch("enrollment_session_id") && (
                        <div className="flex flex-wrap items-start gap-3 lg:flex-nowrap">
                            {hasPermit(
                                "enrollment-download-post-payment-template"
                            ) && (
                                <Button
                                    variant={"outline"}
                                    onClick={onDownloadPostPayTemplate}
                                >
                                    {t("Download Template")}
                                </Button>
                            )}

                            {hasPermit(
                                "enrollment-import-post-payment-template-validation"
                            ) && (
                                <FileInputExcel
                                    api={enrollmentPostPayImportAPI}
                                    handleImportData={handleImportData}
                                    clearTrigger={fileCleared}
                                    extraImportParams={{
                                        enrollment_session_id: form.watch(
                                            "enrollment_session_id"
                                        ),
                                    }}
                                />
                            )}
                        </div>
                    )}
                </div>
                {showTable && (
                    <>
                        <div className="overflow-x-auto pt-4">
                            <DataTable columns={columns} data={displayData} />
                        </div>

                        <div className="mt-4 flex justify-end gap-2">
                            <Button
                                type="reset"
                                variant="outline"
                                className="ml-auto"
                                onClick={onClear}
                            >
                                {t("Cancel")}
                            </Button>
                            {hasPermit(
                                "enrollment-bulk-save-post-payment-imported-data"
                            ) && (
                                <Button type="submit" onClick={onSubmit}>
                                    {t("Submit")}
                                </Button>
                            )}
                        </div>
                    </>
                )}
            </Card>
        </Layout>
    );
};

export default BulkUpdateNewStudents;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
