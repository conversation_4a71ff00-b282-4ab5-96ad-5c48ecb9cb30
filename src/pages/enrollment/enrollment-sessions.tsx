import { useEffect, useState } from "react";
import React from "react";
import { isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    INACTIVE,
    TableColumnType,
    enrollmentSessionsAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { displayDateTime, refreshForUpdate } from "@/lib/utils";
import { useUserProfile } from "@/lib/store";
import FilterEnrollmentSessionForm from "@/components/forms/enrollment/FilterEnrollmentSessionForm";
import EnrollmentSessionForm from "@/components/forms/enrollment/EnrollmentSessionForm";
import DeletePrompt from "@/components/ui/DeletePrompt";
import { useTranslations } from "next-intl";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const EnrollmentSessions = ({ locale }) => {
    useCheckViewPermit("enrollment-session-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");
    const t_es = useTranslations("enrollment_session");

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getEnrollmentSessions } = useAxios({
        api: enrollmentSessionsAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    const columns: TableColumnType[] = [
        {
            key: "name",
            hasSort: true,
        },
        {
            key: "code",
            hasSort: true,
        },
        {
            key: "course",
        },
        {
            key: "admission_grade",
        },
        {
            key: "eligible_period",
            displayAs: t_es("Eligible Period"),
            modify(item) {
                return (
                    <div className="whitespace-nowrap text-[14px] leading-none">
                        {displayDateTime(
                            item?.from_date,
                            DATE_FORMAT.DMY,
                            locale
                        )}
                        {" - "}
                        {displayDateTime(
                            item?.to_date,
                            DATE_FORMAT.DMY,
                            locale
                        )}
                    </div>
                );
            },
        },
        {
            key: "is_active",
            displayAs: t("Status"),
            hasSort: true,
            modify(value) {
                return <div className={`cell-status ${value}`}>{t(value)}</div>;
            },
        },
    ];

    function fetchEnrollmentSessions() {
        getEnrollmentSessions({
            params: {
                ...filter,
                includes: ["examSubjects", "course", "admissionGrade"],
            },
        });
    }

    useEffect(() => {
        fetchEnrollmentSessions();
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  code: item?.code,
                  name: item?.name,
                  course: item?.course?.translations?.name?.[locale] ?? "-",
                  admission_grade:
                      item?.admission_grade?.translations?.name?.[locale] ??
                      "-",
                  eligible_period: {
                      from_date: item?.from_date,
                      to_date: item?.to_date,
                  },
                  is_active: item
                      ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                      : "",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    return (
        <Layout locale={locale} path="enrollment/enrollment-sessions">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>{t_es("Enrollment Sessions")}</h2>
                    <div className="flex items-center gap-x-3">
                        {hasPermit("enrollment-session-create") && (
                            <Button onClick={() => setOpenCreate(true)}>
                                {t("Add ")}
                                {t_es("Enrollment Session")}
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterEnrollmentSessionForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            <DropdownMenuItem
                                className="c-text-size"
                                onClick={() => {
                                    setTargetId(cell.row.original.id);
                                }}
                            >
                                {t("Edit / View")}
                            </DropdownMenuItem>
                            {hasPermit("enrollment-session-delete") && (
                                <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() => {
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            );
                                        }}
                                    >
                                        {t("Delete")}
                                    </DropdownMenuItem>
                                </>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <EnrollmentSessionForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <EnrollmentSessionForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={enrollmentSessionsAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchEnrollmentSessions}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterEnrollmentSessionForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default EnrollmentSessions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
