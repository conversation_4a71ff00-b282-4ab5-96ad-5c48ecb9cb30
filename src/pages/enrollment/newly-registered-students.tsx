import { useEffect, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import Layout from "@/components/Layout";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    PAID,
    PENDING,
    TableColumnType,
    UNPAID,
    enrollmentAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import {
    combinedNames,
    getNameColumns,
    refreshForUpdate,
    strStartCase,
} from "@/lib/utils";
import { useLanguages, useUserProfile } from "@/lib/store";
import FilterNewlyRegisteredStudentForm from "@/components/forms/enrollment/FilterNewlyRegisteredStudentForm";
import NewlyRegisteredStudentForm from "@/components/forms/enrollment/NewlyRegisteredStudentForm";
import NewlyRegisteredStudentView from "@/components/forms/enrollment/NewlyRegisteredStudentView";
import clsx from "clsx";
import DeletePrompt from "@/components/ui/DeletePrompt";
import ExtendRegistrationExpiryForm from "@/components/forms/enrollment/ExtendRegistrationExpiryForm";
import ChangeEnrollmentStatusForm from "@/components/forms/enrollment/ChangeEnrollmentStatusForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const NewlyRegisteredStudents = ({ locale }) => {
    useCheckViewPermit("enrollment-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 50,
        order_by: {
            created_at: "desc",
        },
    });
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);
    const [targetId, setTargetId] = useState(null);
    const [editTargetId, setEditTargetId] = useState(null);
    const [statusTargetId, setStatusTargetId] = useState(null);
    const [extendTargetId, setExtendTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getRegisteredStudents } = useAxios({
        api: enrollmentAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function definedColumn() {
        return [
            {
                key: "number",
                displayAs: "No.",
                modify: (value) => <div className="text-center">{value}</div>,
            },
            ...getNameColumns(activeLanguages),
            {
                key: "nric",
                displayAs: "IC Number",
                hasSort: true,
            },
            {
                key: "passport_number",
                hasSort: true,
                modify: (value) => <div className="text-[13px]">{value}</div>,
            },
            {
                key: "gender",
                hasSort: true,
            },
            {
                key: "admission_grade",
            },
            // {
            //     key: "guardian_name",
            //     modify: (value) => <div className="min-w-[110px]">{value}</div>,
            // },
            // {
            //     key: "guardian_email",
            // },
            // {
            //     key: "guardian_phone",
            // },
            {
                key: "is_hostel",
                hasSort: true,
            },
            {
                key: "is_foreigner",
                hasSort: true,
            },
            {
                key: "payment_status",
                hasSort: true,
                modify: (value) => (
                    <span
                        className={clsx(
                            value === UNPAID
                                ? "text-red-600"
                                : value === PAID
                                  ? "text-themeGreen2"
                                  : value === PENDING
                                    ? "text-orange-500"
                                    : ""
                        )}
                    >
                        {t(value)}
                    </span>
                ),
            },
            {
                key: "enrollment_status",
                hasSort: true,
            },
            {
                key: "expiry_date",
                hasSort: true,
                modify: (value) => (
                    <span className="whitespace-nowrap text-sm">{value}</span>
                ),
            },
            {
                key: "registration_date",
                hasSort: true,
                modify: (value) => (
                    <span className="whitespace-nowrap text-sm">{value}</span>
                ),
            },
        ];
    }

    function fetchEnrollmentSessions() {
        getRegisteredStudents({
            params: {
                ...filter,
                includes: ["enrollmentUser", "admissionGrade"],
            },
        });
    }

    useEffect(() => {
        if (data && activeLanguages) {
            const _columns = definedColumn();
            setColumns(_columns);
        }
    }, [data, activeLanguages]);

    useEffect(() => {
        fetchEnrollmentSessions();
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item, index) => ({
                  number: index + 1 + (filter.page - 1) * filter.per_page,
                  id: item?.id,
                  ...item?.translations?.name,
                  nric: item?.nric ?? "-",
                  passport_number: item?.passport_number ?? "-",
                  gender: t(strStartCase(item?.gender ?? "-")),
                  admission_grade:
                      combinedNames(
                          item?.admission_grade?.translations?.name
                      ) ?? "-",
                  guardian_name: item?.enrollment_user?.name ?? "-",
                  guardian_email: item?.enrollment_user?.email ?? "-",
                  guardian_phone: item?.enrollment_user?.phone_number ?? "-",
                  payment_status: (item?.payment_status ?? "-").replaceAll(
                      "_",
                      " "
                  ),
                  is_hostel:
                      item?.is_hostel === null
                          ? "-"
                          : item?.is_hostel
                            ? t("Yes")
                            : t("No"),
                  is_foreigner:
                      item?.is_foreigner === null
                          ? "-"
                          : item?.is_foreigner
                            ? t("Yes")
                            : t("No"),
                  enrollment_status: t(item?.enrollment_status ?? "-"),
                  expiry_date: item?.expiry_date ?? "-",
                  registration_date: item?.registration_date ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: activeLanguages?.map((lang) => lang?.code)?.includes(name)
                ? { name: { [name]: direction } }
                : { [name]: direction },
        });
    }

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path="enrollment/newly-registered-students">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>{t("Newly Registered Students")}</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterNewlyRegisteredStudentForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {/* <DropdownMenuItem
                                className="c-text-size"
                                onClick={() => {
                                    setEditTargetId(cell.row.original.id);
                                }}
                            >
                                View
                            </DropdownMenuItem> */}
                            <DropdownMenuItem
                                className="c-text-size"
                                onClick={() => {
                                    setEditTargetId(cell.row.original.id);
                                }}
                            >
                                {t("Edit / View")}
                            </DropdownMenuItem>
                            {hasPermit("enrollment-update") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() => {
                                        setStatusTargetId(cell.row.original.id);
                                    }}
                                >
                                    {t("Change Status")}
                                </DropdownMenuItem>
                            )}
                            {hasPermit("enrollment-extend-expiry") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() => {
                                        setExtendTargetId(cell.row.original.id);
                                    }}
                                >
                                    {t("Extend Expiry Date")}
                                </DropdownMenuItem>
                            )}
                            {hasPermit("enrollment-delete") && (
                                <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        {t("Delete")}
                                    </DropdownMenuItem>
                                </>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* view */}
            <Modal open={targetId} onOpenChange={setTargetId} size="large">
                <NewlyRegisteredStudentView
                    id={targetId}
                    close={() => setTargetId(null)}
                />
            </Modal>

            {/* update */}
            <Modal
                open={editTargetId}
                onOpenChange={setEditTargetId}
                size="medium"
            >
                <NewlyRegisteredStudentForm
                    id={editTargetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setEditTargetId(null)}
                />
            </Modal>

            {/* change status */}
            <Modal
                open={statusTargetId}
                onOpenChange={setStatusTargetId}
                canOverflow={true}
            >
                <ChangeEnrollmentStatusForm
                    id={statusTargetId}
                    data={data?.find((item) => item?.id == statusTargetId)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setStatusTargetId(null)}
                />
            </Modal>

            {/* extend expiry */}
            <Modal open={extendTargetId} onOpenChange={setExtendTargetId}>
                <ExtendRegistrationExpiryForm
                    id={extendTargetId}
                    data={data?.find((item) => item?.id == extendTargetId)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setExtendTargetId(null)}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={enrollmentAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter} size="medium">
                <FilterNewlyRegisteredStudentForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default NewlyRegisteredStudents;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
