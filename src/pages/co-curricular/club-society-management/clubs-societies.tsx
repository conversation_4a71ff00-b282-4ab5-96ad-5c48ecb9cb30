import { isArray } from "lodash";
import FilterSocietyForm from "@/components/forms/co-curricular/FilterSocietyForm";
import SocietyForm from "@/components/forms/co-curricular/SocietyForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { societyAPI, TableColumnType } from "@/lib/constant";

const ClubsSocieties = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="co-curricular/club-society-management/clubs-societies"
        api={societyAPI}
        definedColumn={(activeLanguages) => {
            return [
                {
                    key: "code",
                },
                ...activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `Name ( ${lang?.name} )`,
                        hasSort: true,
                    })
                ),
                {
                    key: "category",
                },
            ];
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      code: item?.code,
                      category: item?.category?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <SocietyForm {...params} />}
        filterForm={(params) => <FilterSocietyForm {...params} />}
        viewPermit="club-view"
        createPermit="club-create"
        updatePermit="club-update"
        deletePermit="club-delete"
    />
);

export default ClubsSocieties;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
