import FilterSocietyCategoryForm from "@/components/forms/co-curricular/FilterSocietyCategoryForm";
import SocietyCategoryForm from "@/components/forms/co-curricular/SocietyCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, societyCategoryAPI } from "@/lib/constant";

const ClubSocietyCategories = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="co-curricular/club-society-management/club-society-categories"
        api={societyCategoryAPI}
        definedColumn={(activeLanguages) => {
            const _nameColumns = activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );
            const _columns: TableColumnType[] = [..._nameColumns];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <SocietyCategoryForm {...params} />}
        filterForm={(params) => <FilterSocietyCategoryForm {...params} />}
        viewPermit="club-category-view"
        createPermit="club-category-create"
        updatePermit="club-category-update"
        deletePermit="club-category-delete"
    />
);

export default ClubSocietyCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
