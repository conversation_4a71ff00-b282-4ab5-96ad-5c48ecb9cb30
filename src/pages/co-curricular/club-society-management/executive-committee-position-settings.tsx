import { isArray, lowerCase } from "lodash";
import ExecutiveCommitteePositionForm from "@/components/forms/co-curricular/ExecutiveCommitteePositionForm";
import FilterExecutiveCommitteePositionForm from "@/components/forms/co-curricular/FilterExecutiveCommitteePositionForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    INACTIVE,
    societyPositionAPI,
    TableColumnType,
} from "@/lib/constant";

const ExecutiveCommitteePositions = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="co-curricular/club-society-management/executive-committee-position-settings"
        api={societyPositionAPI}
        definedColumn={(activeLanguages) => {
            return [
                {
                    key: "code",
                },
                ...activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `Name ( ${lang?.name} )`,
                        hasSort: true,
                    })
                ),
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      code: item?.code,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "is_active"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <ExecutiveCommitteePositionForm {...params} />}
        filterForm={(params) => (
            <FilterExecutiveCommitteePositionForm {...params} />
        )}
        viewPermit="master-society-position-view"
        createPermit="master-society-position-create"
        updatePermit="master-society-position-update"
        deletePermit="master-society-position-delete"
    />
);

export default ExecutiveCommitteePositions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
