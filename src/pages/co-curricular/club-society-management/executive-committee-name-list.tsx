import { useEffect, useState } from "react";
import { isArray, isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import {
    DEFAULT_FILTER_PARAMS,
    FULL,
    GET_ALL_PARAMS,
    semesterClassesAPI,
    semesterClassOrderBy,
    semesterSettingAPI,
    SOCIETY,
    societyPositionAPI,
    studentAPI,
    studentSocietyPositionAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import {
    combinedNames,
    combineSemesterClass,
    showBackendFormError,
} from "@/lib/utils";

const ExecutiveCommitteeNameList = ({ locale }) => {
    useCheckViewPermit("student-society-position-update");

    const form = useForm<any>({
        defaultValues: {
            semester_class_id: "",
            students: [],
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [selectedClass, setSelectedClass] = useState<any>();
    const [hasSearched, setHasSearched] = useState(false);
    const [pagination, setPagination] = useState();
    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClasses, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
    });

    const { data: studentData, axiosQuery: getStudents } = useAxios({
        api: studentAPI,
        locale,
        onSuccess: (result) => {
            setHasSearched(true);
            setPagination(result?.pagination);
            getSocietyPositionOptions({
                params: GET_ALL_PARAMS,
            });
        },
    });

    const {
        data: societyPositionOptions,
        axiosQuery: getSocietyPositionOptions,
    } = useAxios({
        api: societyPositionAPI,
        locale,
    });

    const { axiosPut: saveSocietyPositionsNameList, error: putError } =
        useAxios({
            api: studentSocietyPositionAPI,
            toastMsg: "Updated successfully",
        });

    const societyClassOptions =
        semesterClasses?.map((data) => ({
            id: data.id,
            name: `${data.class_model.code} - ${
                data.class_model.translations.name[locale] ??
                data.class_model.name
            }`,
        })) || [];

    const columns = [
        {
            key: "index",
            displayAs: "No",
        },
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
        },
        {
            key: "class",
            hasSort: true,
        },
        {
            key: "society_positions",
            displayAs: "Position",
            modify: (_, cell) => {
                const index = form
                    .watch("students")
                    .findIndex((student) => student.student_id === cell.row.id);

                return (
                    <div className="h-full min-w-[200px]">
                        <FormSelect
                            control={form.control}
                            name={`students[${index}].position_ids`}
                            options={societyPositionOptions}
                            isMulti={true}
                            hasLabel={false}
                        />
                    </div>
                );
            },
        },
    ];

    function definedData() {
        return isArray(studentData)
            ? studentData.map((item, index) => {
                  return {
                      id: item?.id,
                      index: index + 1,
                      student_number: item?.student_number || "-",
                      name: combinedNames(item?.translations?.name) || "-",
                      class: combineSemesterClass(item?.current_primary_class),
                      society_positions: item?.society_positions?.map(
                          (position) => position?.society_position?.id
                      ),
                  };
              })
            : [];
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function onFilter() {
        submit((data) => {
            if (data.semester_class_id) {
                getStudents({
                    params: {
                        ...filter,
                        response: FULL,
                        includes: [
                            "societyPositions.societyPosition",
                            "currentSemesterPrimaryClass.semesterSetting",
                            "currentSemesterPrimaryClass.semesterClass.classModel",
                        ],
                        semester_class_id: data.semester_class_id,
                        is_active: 1,
                    },
                });
            }
        });
    }

    function onSaveNameList() {
        submit((data) => {
            delete data.semester_setting_id;
            saveSocietyPositionsNameList({ data });
        });
    }

    useEffect(() => {
        onFilter();
    }, [filter]);

    useEffect(() => {
        if (isArray(studentData)) {
            const updatedStudents = studentData.map((item) => ({
                student_id: item.id,
                position_ids: item?.society_positions?.map(
                    (position) => position?.society_position?.id
                ),
            }));
            form.setValue("students", updatedStudents);
        }
    }, [studentData]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            setSelectedClass(null);
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    includes: ["semesterSetting", "classModel"],
                    semester_setting_id: form.watch("semester_setting_id"),
                    class_type: SOCIETY,
                    is_active: 1,
                    ...semesterClassOrderBy(locale),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    useEffect(() => {
        getSemesterOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    semester_year: "desc",
                },
            },
        });
    }, []);

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    return (
        <Layout
            locale={locale}
            path="co-curricular/club-society-management/executive-committee-name-list"
        >
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">
                    Executive Committee Name List
                </h2>

                <Form {...form}>
                    <form className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="semester_class_id"
                            label="club/society*"
                            options={societyClassOptions}
                            isDisabled={!form.watch("semester_setting_id")}
                        />

                        <Button
                            disabled={
                                !form.watch("semester_setting_id") ||
                                !form.watch("semester_class_id")
                            }
                            onClick={() => {
                                setFilter({
                                    ...DEFAULT_FILTER_PARAMS,
                                    semester_class_id:
                                        form.watch("semester_class_id"),
                                });
                                onFilter();
                            }}
                        >
                            Filter
                        </Button>
                    </form>

                    {hasSearched ? (
                        isEmpty(studentData) ? (
                            <div className="h-20 text-center text-themeLabel">
                                No Record Found
                            </div>
                        ) : (
                            <div className="grid gap-y-5 overflow-auto pt-3">
                                <DataTable
                                    columns={columns}
                                    data={definedData()}
                                    pagination={pagination}
                                    setPagination={setPagination}
                                    changePage={(arg) =>
                                        setFilter({ ...filter, ...arg })
                                    }
                                    styleClass="overflow-visible"
                                    sorted={filter?.order_by}
                                    sort={onSort}
                                />

                                <div className="mb-2 flex justify-end gap-x-3">
                                    <Button onClick={onSaveNameList}>
                                        Save
                                    </Button>
                                </div>
                            </div>
                        )
                    ) : null}
                </Form>
            </Card>
        </Layout>
    );
};

export default ExecutiveCommitteeNameList;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
