import { useEffect, useState } from "react";
import { format } from "date-fns";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterTrainerForm from "@/components/forms/co-curricular/FilterTrainerForm";
import TrainerForm from "@/components/forms/co-curricular/TrainerForm";
import TrainerDownloadReportForm from "@/components/forms/co-curricular/TrainersDownloadReportForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    COCURRICULUM,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
    trainerAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { getNameColumns, refreshForUpdate } from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const CoachManagement = ({ locale }) => {
    useCheckViewPermit("contractor-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const { activeLanguages } = useLanguages();
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [openReportDownload, setOpenReportDownload] = useState<any>(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getTrainers } = useAxios({
        api: trainerAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function defineColumn(activeLanguages) {
        const _columns: TableColumnType[] = [
            {
                key: "contractor_number",
                displayAs: "Coach Number",
                hasSort: true,
            },
            ...getNameColumns(activeLanguages, true),
            {
                key: "employed_date",
                hasSort: true,
                modify: (value, cell) => (
                    <span className="whitespace-nowrap text-sm">
                        {value ? format(value, DATE_FORMAT.DMY) : "-"}
                    </span>
                ),
            },
            {
                key: "resignation_date",
                hasSort: true,
                modify: (value, cell) => (
                    <span className="whitespace-nowrap text-sm">
                        {value ? format(value, DATE_FORMAT.DMY) : "-"}
                    </span>
                ),
            },
            {
                key: "status",
                hasSort: true,
                modify: (value) => (
                    <div className={`cell-status ${value.toLowerCase()}`}>
                        {value}
                    </div>
                ),
            },
        ];

        setColumns(_columns);
    }

    function definedData(data) {
        return Array.isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  contractor_number: item?.contractor_number,
                  ...item?.translations?.name,
                  employed_date: item?.employed_date,
                  resignation_date: item?.resignation_date,
                  status: item?.status,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        const list = [
            "contractor_number",
            "employed_date",
            "resignation_date",
            "status",
        ];
        setFilter({
            ...filter,
            order_by: list.includes(name)
                ? { [name]: direction }
                : {
                      name: { [name]: direction },
                  },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    function fetchTrainers() {
        getTrainers({
            params: {
                department: COCURRICULUM,
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchTrainers();
    }, [filter, locale]);

    useEffect(() => {
        if (data && activeLanguages) {
            defineColumn(activeLanguages);
        }
    }, [data, activeLanguages]);

    return (
        <Layout locale={locale} path="co-curricular/coach-management">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>Coach Management</h2>
                    <div className="flex flex-wrap gap-3">
                        {hasPermit("contractor-create") && (
                            <Button onClick={() => setOpenCreate(true)}>
                                Add New Coach
                            </Button>
                        )}
                        {hasPermit("cocurriculum-trainer-detail-report") && (
                            <Button
                                onClick={() => setOpenReportDownload(true)}
                                variant={"outline"}
                            >
                                Download Report
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                            excludeFields={["includes"]}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterTrainerForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit("contractor-update") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setTargetId(cell.row.original.id)
                                    }
                                >
                                    Edit / View
                                </DropdownMenuItem>
                            )}

                            <DropdownMenuSeparator />
                            {hasPermit("contractor-delete") && (
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    Delete
                                </DropdownMenuItem>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <TrainerForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <TrainerForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* report */}
            <Modal
                open={openReportDownload}
                onOpenChange={setOpenReportDownload}
            >
                <TrainerDownloadReportForm />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={trainerAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchTrainers}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterTrainerForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default CoachManagement;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
