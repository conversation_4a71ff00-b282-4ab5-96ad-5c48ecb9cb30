import { useEffect, useState } from "react";
import React from "react";
import { isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import AssignTimetablePeriodGroupForm from "@/components/forms/timetable/AssignTimetablePeriodGroupForm";
import FilterTimetableForm from "@/components/forms/timetable/FilterTimetableForm";
import TimetableEditor from "@/components/forms/timetable/TimetableEditor";
import TimetableForm from "@/components/forms/timetable/TimetableForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DEFAULT_FILTER_PARAMS,
    INACTIVE,
    TableColumnType,
    timetableAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import {
    getSemesterClassName,
    refreshForUpdate,
    replaceAll,
} from "@/lib/utils";
import { useUserProfile } from "@/lib/store";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import { LucidePrinter } from "lucide-react";
import { useTranslations } from "next-intl";

const Timetables = ({ locale }) => {
    useCheckViewPermit("timetable-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState<any>();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetIdWithTimetable, setTargetIdWithTimetable] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getTimetable } = useAxios({
        api: timetableAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    const columns: TableColumnType[] = [
        {
            key: "name",
            hasSort: true,
        },
        {
            key: "semester_class",
        },
        {
            key: "period_group",
            modify: (value) => value ?? "-",
        },
        {
            key: "is_active",
            displayAs: "Status",
            hasSort: true,
            modify(value) {
                return <div className={`cell-status ${value}`}>{value}</div>;
            },
        },
    ];

    useEffect(() => {
        getTimetable({
            params: {
                ...filter,
                includes: [
                    "semesterClass.classModel",
                    "semesterClass.semesterSetting",
                    "semesterClass.studentClasses",
                    "periodGroup",
                ],
            },
        });
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  name: item?.name,
                  semester_class: getSemesterClassName(item?.semester_class),
                  period_group: item?.period_group?.name ?? null,
                  is_active: item
                      ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                      : "",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    const t = useTranslations("common");

    function print() {
        const printArea = document.querySelector(".printable");
        if (!printArea) return;
        printArea.classList.add("print-show");

        setTimeout(() => {
            window.print();
            window.onafterprint = () => {
                printArea.classList.remove("print-show");
            };
            printArea.classList.remove("print-show");
        }, 10);
    }

    return (
        <Layout locale={locale} path="timetable/class-timetables">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>Timetables</h2>
                    <div className="flex items-center gap-x-3">
                        {hasPermit("timetable-create") && (
                            <Button onClick={() => setOpenCreate(true)}>
                                Add Timetable
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                        {/* <LucidePrinter
                            size={20}
                            className="mx-1 cursor-pointer text-themeGreen"
                            onClick={print}
                        /> */}
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterTimetableForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {cell.row.original.period_group === null &&
                            hasPermit("timetable-assign-period-group") ? (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() => {
                                        setTargetId(cell.row.original.id);
                                    }}
                                >
                                    Assign Period Group
                                </DropdownMenuItem>
                            ) : (
                                hasPermit("timetable-view") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() => {
                                            setTargetIdWithTimetable(
                                                cell.row.original.id
                                            );
                                        }}
                                    >
                                        Edit / View Timetable
                                    </DropdownMenuItem>
                                )
                            )}
                            {/* API did not support delete currently */}
                            {/* <DropdownMenuSeparator />
                            <DropdownMenuItem
                                className="c-text-size text-red-600"
                                onClick={() =>
                                    setTargetDeleteId(cell.row.original.id)
                                }
                            >
                                Delete
                            </DropdownMenuItem> */}
                        </ActionDropdown>
                    )}
                />
            </Card>

            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <TimetableForm
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            <Modal open={targetId} onOpenChange={setTargetId}>
                <AssignTimetablePeriodGroupForm
                    id={targetId}
                    name={data?.find((item) => item?.id === targetId)?.name}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {targetIdWithTimetable && (
                <TimetableEditor
                    id={targetIdWithTimetable}
                    semesterClass={
                        data?.find((item) => item?.id === targetIdWithTimetable)
                            ?.semester_class
                    }
                    periodGroup={
                        data?.find((item) => item?.id === targetIdWithTimetable)
                            ?.period_group?.translations?.name
                    }
                    refresh={() => {
                        setFilter({
                            ...filter,
                        });
                    }}
                    close={() => setTargetIdWithTimetable(null)}
                />
            )}

            {/* delete */}
            {/* <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={timetableAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal> */}

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterTimetableForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* <div className="printable">
                <h1 className="mb-4 text-2xl font-bold">Timetable</h1>
                <table className="print-table">
                    <thead>
                        <tr className="text-left capitalize">
                            {columns.map((col, index) => (
                                <th key={index}>
                                    {t(
                                        replaceAll(
                                            col.displayAs || col.key,
                                            "_",
                                            " "
                                        )
                                    )}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {definedData(data).map((row, index) => (
                            <tr key={index}>
                                {columns.map((col, index) => (
                                    <td key={index} className="capitalize">
                                        {row[col.key]}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
                <p className="ml-1 mt-4">{pagination?.per_page} items</p>{" "}
            </div> */}
        </Layout>
    );
};

export default Timetables;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
