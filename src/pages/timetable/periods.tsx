import FilterPeriodForm from "@/components/forms/timetable/FilterPeriodForm";
import PeriodForm from "@/components/forms/timetable/PeriodForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { periodAPI, TableColumnType } from "@/lib/constant";
import { displayTime } from "@/lib/utils";

const Periods = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="timetable/periods"
        api={periodAPI}
        otherFilterParams={{
            includes: ["periodGroup"],
        }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "period",
                    hasSort: true,
                },
                {
                    key: "period_group_name",
                    hasSort: true,
                },
                {
                    key: "display_group",
                    hasSort: true,
                },
                {
                    key: "day",
                    hasSort: true,
                },
                {
                    key: "from_time",
                    displayAs: "From",
                    hasSort: true,
                },
                {
                    key: "to_time",
                    displayAs: "To",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      period: item?.period,
                      period_group_name: item?.period_group?.name,
                      display_group: item?.display_group,
                      day: item?.day,
                      from_time: displayTime(item?.from_time),
                      to_time: displayTime(item?.to_time),
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <PeriodForm {...params} />}
        filterForm={(params) => <FilterPeriodForm {...params} />}
        viewPermit="period-view"
        // createPermit="period-save"
        // updatePermit="period-save"
        // deletePermit="period-group-save" // TODO: only period-view and period-save available now
    />
);

export default Periods;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
