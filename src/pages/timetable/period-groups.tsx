import { isArray } from "lodash";
import FilterPeriodGroupForm from "@/components/forms/timetable/FilterPeriodGroupForm";
import PeriodGroupForm from "@/components/forms/timetable/PeriodGroupForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { periodGroupAPI, TableColumnType } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const PeriodGroups = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="timetable/period-groups"
        api={periodGroupAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
                {
                    key: "number_of_periods",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      number_of_periods: item?.number_of_periods,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "number_of_periods"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <PeriodGroupForm {...params} />}
        filterForm={(params) => <FilterPeriodGroupForm {...params} />}
        viewPermit="period-group-view"
        // createPermit="period-group-create"
        // updatePermit="period-group-update"
        // deletePermit="period-group-delete"
    />
);

export default PeriodGroups;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
