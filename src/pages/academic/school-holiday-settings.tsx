import { format } from "date-fns";
import FilterSchoolHolidaySettingForm from "@/components/forms/class-subject-management/FilterSchoolHolidaySettingForm";
import SchoolHolidaySettingForm from "@/components/forms/class-subject-management/SchoolHolidaySettingForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { nav, religionAPI, TableColumnType, DATE_FORMAT } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const Religions = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path={"academic/school-holiday-settings"}
        api={religionAPI} //TODO: schoolHolidaySettingsAPI
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages, false),
                // {
                //     key: "date",
                //     modify: (value, cell) => (
                //         <span className="whitespace-nowrap text-sm">
                //             {format(value, DATE_FORMAT.DMY) ?? ""}
                //         </span>
                //     ),
                // },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      date: item?.date,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <SchoolHolidaySettingForm {...params} />}
        filterForm={(params) => <FilterSchoolHolidaySettingForm {...params} />}
        viewPermit={"school-holiday-settings-view"} // TODO: change to correct value
    />
);

export default Religions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
