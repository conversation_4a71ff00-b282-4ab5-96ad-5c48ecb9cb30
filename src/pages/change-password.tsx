import { useEffect } from "react";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FreeInput from "@/components/ui/FreeInput";
import LoaderOverlay from "@/components/ui/LoaderOverlay";
import { changePasswordAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const ChangePassword = () => {
    const router = useRouter();

    const form = useForm({
        defaultValues: {
            password: "",
            password_confirmation: "",
        },
    });

    const {
        axiosPost: changePassword,
        error,
        isLoading,
    } = useAxios({
        api: changePasswordAPI,
        noToast: true,
        onSuccess() {
            router.replace("/");
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            changePassword(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, error);
    }, [error]);

    return (
        <div className="mx-auto flex h-screen flex-col items-center justify-center px-10 py-20">
            <h2 className="mb-2 mt-7 text-2xl text-themeGreenDark">
                Change Password
            </h2>
            <Form {...form}>
                <form
                    className="grid w-full max-w-[360px] gap-y-3"
                    onSubmit={onSubmit}
                >
                    <p className="mb-1 text-center text-gray-500">
                        Please change your password to proceed
                    </p>
                    <FreeInput
                        control={form.control}
                        type={"password"}
                        name="password"
                        hasLabel={false}
                        placeholder="Enter new password"
                        error={form.formState.errors?.password}
                    />
                    <FreeInput
                        control={form.control}
                        type={"password"}
                        name="password_confirmation"
                        hasLabel={false}
                        placeholder="Enter new password confirmation"
                        error={form.formState.errors?.password}
                    />
                    <Button type="submit" className="w-full">
                        Submit
                    </Button>
                </form>
                {isLoading && <LoaderOverlay />}
            </Form>
        </div>
    );
};

export default ChangePassword;
