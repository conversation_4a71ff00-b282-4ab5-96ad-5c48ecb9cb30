import { useEffect, useState } from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FreeInput from "@/components/ui/FreeInput";
import LoaderOverlay from "@/components/ui/LoaderOverlay";
import {
    requestResetPasswordOtpAPI,
    resetPasswordAPI,
    schoolAppName,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const ResetPassword = () => {
    const [type, setType] = useState<"email" | "password">("email");
    const [email, setEmail] = useState<string>();

    const title = `Reset Password | ${schoolAppName}`;

    return (
        <>
            <Head>
                <title>{title}</title>
            </Head>
            <div className="mx-auto flex h-screen flex-col items-center justify-center px-10 py-20">
                <h2 className="mb-2 mt-7 text-2xl text-themeGreenDark">
                    Reset Password
                </h2>
                {type === "email" && (
                    <EmailForm
                        onContinue={(_email: string) => {
                            setEmail(_email);
                            setType("password");
                        }}
                    />
                )}
                {type === "password" && (
                    <PasswordsForm
                        email={email}
                        onBack={() => setType("email")}
                    />
                )}
            </div>
        </>
    );
};

const EmailForm = ({ onContinue }) => {
    const router = useRouter();

    const form = useForm({
        defaultValues: {
            email: "",
        },
    });

    const {
        axiosPublicPost: requestOTP,
        error,
        isLoading,
    } = useAxios({
        api: requestResetPasswordOtpAPI,
        noToast: true,
        onSuccess() {
            onContinue(form.getValues("email"));
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            requestOTP(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, error);
    }, [error]);

    return (
        <Form {...form}>
            <form
                className="grid w-full max-w-[360px] gap-y-3"
                onSubmit={onSubmit}
            >
                <p className="text-center text-gray-500">
                    Enter your email address to receive OTP to reset your
                    password
                </p>
                <FormInput
                    type="email"
                    control={form.control}
                    name="email"
                    hasLabel={false}
                    placeholder="Enter email"
                />
                <Button type="submit" className="w-full">
                    Continue
                </Button>
                <Button
                    variant={"outline"}
                    className="w-full"
                    onClick={() => router.replace("/login")}
                >
                    Back
                </Button>
            </form>
            {isLoading && <LoaderOverlay />}
        </Form>
    );
};

const PasswordsForm = ({ email, onBack }) => {
    const router = useRouter();

    const form = useForm({
        defaultValues: {
            email,
            otp: "",
            password: "",
            password_confirmation: "",
        },
    });

    const {
        axiosPublicPost: resetPassword,
        error,
        isLoading,
    } = useAxios({
        api: resetPasswordAPI,
        noToast: true,
        onSuccess() {
            router.replace("/login");
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log("data", data);
            resetPassword(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, error);
    }, [error]);

    return (
        <Form {...form}>
            <form
                className="grid w-full max-w-[360px] gap-y-3"
                onSubmit={onSubmit}
            >
                <p className="text-center text-gray-500">
                    An OTP has been sent to your email address
                </p>
                <FormInput
                    control={form.control}
                    name="otp"
                    hasLabel={false}
                    placeholder="Enter OTP"
                />
                <FreeInput
                    control={form.control}
                    type={"password"}
                    name="password"
                    hasLabel={false}
                    placeholder="Enter new password"
                    error={form.formState.errors?.password}
                />
                <FreeInput
                    control={form.control}
                    type={"password"}
                    name="password_confirmation"
                    hasLabel={false}
                    placeholder="Enter new password confirmation"
                    error={form.formState.errors?.password}
                />
                <Button type="submit" className="w-full">
                    Reset Password
                </Button>
                <Button variant={"outline"} className="w-full" onClick={onBack}>
                    Back
                </Button>
            </form>
            {isLoading && <LoaderOverlay />}
        </Form>
    );
};

export default ResetPassword;
