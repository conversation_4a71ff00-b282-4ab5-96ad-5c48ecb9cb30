import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray, lowerCase, toLower } from "lodash";
import Image from "next/image";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterStudentForm from "@/components/forms/FilterStudentForm";
import StudentForm from "@/components/forms/StudentForm";
import StudentLeaveReturnForm from "@/components/forms/student/StudentLeaveReturnForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import ProfileCard from "@/components/ui/ProfileCard";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DEFAULT_FILTER_PARAMS,
    FULL,
    GET_ALL_PARAMS,
    INACTIVE,
    IconProfilePhotoPlaceholder,
    LEAVE,
    RETURN,
    STUDENT,
    TableColumnType,
    semesterSettingAPI,
    studentAPI,
    studentProfileParams,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { getNameColumns, optionUserLabel } from "@/lib/utils";
import PersonImagePreview from "@/components/ui/PersonImagePreview";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import { useTranslations } from "next-intl";

const Students = ({ locale }) => {
    useCheckViewPermit("student-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const t = useTranslations("common");

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
        order_by: {
            student_number: "desc",
        },
    });
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);
    const [openStatus, setOpenStatus] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetImage, setTargetImage] = useState<any>(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [targetProfile, setTargetProfile] = useState<any>(null);
    const [targetStatusId, setTargetStatusId] = useState(null);
    const [actionType, setActionType] = useState(null);
    const [currentSemesterId, setCurrentSemesterId] = useState<any>(null);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data: studentsData, axiosQuery: getStudents } = useAxios({
        api: studentAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    const { axiosQuery: getSemesterSettings } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (res) => {
            const currentSemester = res.data.find(
                (semester) => semester?.is_current_semester
            );
            if (currentSemester?.id) {
                setCurrentSemesterId(currentSemester.id);
                setFilter({
                    ...filter,
                    semester_setting_id: currentSemester.id,
                });
            }
        },
    });

    useEffect(() => {
        getSemesterSettings({
            params: { ...GET_ALL_PARAMS },
        });
    }, []);

    function fetchStudents() {
        getStudents({
            params: {
                response: FULL,
                includes: [
                    "currentSemesterPrimaryClass.semesterSetting",
                    "currentSemesterPrimaryClass.semesterClass.classModel",
                ],
                semester_setting_id: currentSemesterId,
                ...filter,
            },
        });
    }

    useEffect(() => {
        if (!currentSemesterId) return;
        fetchStudents();
    }, [filter, locale]);

    useEffect(() => {
        if (studentsData && activeLanguages) {
            const _columns: TableColumnType[] = [
                {
                    key: "student_photo",
                    modify: (data) => (
                        <div className="flex h-[80px] items-center justify-center">
                            {data?.url ? (
                                <Image
                                    src={data.url}
                                    alt=""
                                    width={50}
                                    height={50}
                                    className="mx-auto max-h-full w-auto cursor-pointer rounded-sm"
                                    onClick={() => {
                                        setTargetImage({
                                            student_name: data.name,
                                            url: data?.url,
                                        });
                                    }}
                                />
                            ) : (
                                <Image
                                    src={IconProfilePhotoPlaceholder}
                                    alt=""
                                    width={50}
                                    height={50}
                                    className="mx-auto h-[50px] w-[50px] opacity-10"
                                    unoptimized
                                />
                            )}
                        </div>
                    ),
                },
                {
                    key: "student_number",
                    hasSort: true,
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "gender",
                    hasSort: true,
                },
                {
                    key: "date_of_birth",
                    hasSort: true,
                },
                {
                    key: "semester_class",
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "leave_status",
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{t(value)}</div>
                    ),
                },
            ];
            setColumns(_columns);
        }
    }, [studentsData, activeLanguages]);

    function semesterClassDisplay(semesterClass) {
        if (!semesterClass) return "-";
        const _class = semesterClass?.class_model?.name ?? "";
        const _semester = semesterClass?.semester_setting?.name ?? "-";
        return `${_class} (${_semester})`;
    }

    function definedData() {
        return isArray(studentsData)
            ? studentsData.map((item) => ({
                  id: item?.id,
                  student_photo: {
                      url: item?.photo,
                      name: optionUserLabel(
                          item?.student_number,
                          item?.translations?.name
                      ),
                  },
                  student_number: item?.student_number,
                  ...item?.translations?.name,
                  gender: t(capitalize(item?.gender)),
                  date_of_birth: item?.date_of_birth,
                  semester_class: semesterClassDisplay(
                      item?.selected_semester_class
                  ),
                  is_active: lowerCase(item?.is_active ? ACTIVE : INACTIVE),
                  leave_status: item?.leave_status ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        const list = ["student_number", "gender", "date_of_birth", "is_active"];
        setFilter({
            ...filter,
            order_by: list.includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } },
        });
    }

    const { axiosQuery: getStudentData } = useAxios({
        api: studentAPI,
        locale,
        onSuccess: (result) => {
            setTargetProfile(result.data);
        },
    });

    function getStudentProfile(id: number) {
        getStudentData({
            id,
            params: studentProfileParams,
        });
    }

    return (
        <Layout locale={locale} path="student-management/students">
            <Card styleClass="table-card">
                <div className="table-page-top">
                    <h2 className="capitalize">{t("students")}</h2>

                    {hasPermit("student-create") && (
                        <Button
                            size="smallerOnMobile"
                            className="ml-auto capitalize"
                            onClick={() => setOpenCreate(true)}
                        >
                            {t("Add ")}
                            {t("student")}
                        </Button>
                    )}
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterStudentForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => {
                        const { is_active, id } = cell.row.original;

                        const openStatusModal = (actionType) => {
                            setTargetStatusId(id);
                            setActionType(actionType);
                            setOpenStatus(true);
                        };

                        return (
                            <>
                                <ActionDropdown>
                                    {hasPermit("student-view") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                getStudentProfile(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {t("View")}
                                        </DropdownMenuItem>
                                    )}
                                    {/* TODO: update permission for nondirect-guardian-update when available */}
                                    {(hasPermit("student-update") ||
                                        hasPermit(
                                            "nondirect-guardian-update"
                                        )) && (
                                        <>
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() =>
                                                    setTargetId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                {t("Edit")}
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                        </>
                                    )}

                                    {/* Hide first until API done */}
                                    {hasPermit("student-update") &&
                                        is_active == toLower(ACTIVE) && ( //TODO: change to leave_status after API update
                                            <>
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() =>
                                                        openStatusModal(LEAVE)
                                                    }
                                                >
                                                    {t("Leave")}
                                                </DropdownMenuItem>
                                            </>
                                        )}
                                    {hasPermit("student-update") &&
                                        is_active == toLower(INACTIVE) && ( //TODO: change to leave_status after API update
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() =>
                                                    openStatusModal(RETURN)
                                                }
                                            >
                                                {t("Rejoin")}
                                            </DropdownMenuItem>
                                        )}
                                    <DropdownMenuSeparator />

                                    {hasPermit("student-delete") && (
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setTargetDeleteId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {t("Delete")}
                                        </DropdownMenuItem>
                                    )}
                                </ActionDropdown>
                            </>
                        );
                    }}
                />
            </Card>

            {/* student photo */}
            <Modal
                open={targetImage != null}
                onOpenChange={(value) => {
                    if (value === false) {
                        setTargetImage(null);
                    }
                }}
            >
                <PersonImagePreview
                    studentName={targetImage?.student_name}
                    url={targetImage?.url}
                />
            </Modal>

            {/* view */}
            <Modal
                open={targetProfile}
                onOpenChange={setTargetProfile}
                size="large"
            >
                <ProfileCard
                    title="Student Profile"
                    data={targetProfile!}
                    type={STUDENT}
                />
            </Modal>

            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <StudentForm
                    isCreate={true}
                    close={() => setOpenCreate(false)}
                    refresh={() =>
                        setFilter({
                            page: 1,
                            per_page: filter?.per_page,
                            semester_setting_id: filter?.semester_setting_id,
                            order_by: {
                                updated_at: "desc",
                            },
                        })
                    }
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <StudentForm
                    id={targetId}
                    close={() => setTargetId(null)}
                    refresh={() =>
                        setFilter({
                            page: 1,
                            per_page: filter?.per_page,
                            semester_setting_id: filter?.semester_setting_id,
                            order_by: {
                                updated_at: "desc",
                            },
                        })
                    }
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={studentAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchStudents}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter} size="medium">
                <FilterStudentForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* TODO: After API update */}
            {/* Change status */}
            <Modal open={openStatus} onOpenChange={setOpenStatus}>
                <StudentLeaveReturnForm
                    id={targetStatusId}
                    actionType={actionType}
                    close={() => setOpenStatus(false)}
                    refresh={fetchStudents}
                />
            </Modal>
        </Layout>
    );
};

export default Students;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
