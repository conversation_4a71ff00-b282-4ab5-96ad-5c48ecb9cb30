import React from "react";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";

const Payments = ({ locale }) => {
    return (
        <Layout locale={locale} path={"payments"}>
            <h2>New Payments</h2>
            <div className="flex flex-col lg:w-full lg:flex-row lg:space-x-5">
                <Card styleClass="mt-3 grow lg:h-fit bg-themeGreen3 lg:bg-themeGreen3 p-3 rounded-xl">
                    <div className="text-[16px] font-bold text-themeGreenDark lg:text-[20px]">
                        Amount
                    </div>

                    <div className="mb-5 grow items-center justify-between gap-x-3 text-themeGreenDark">
                        <div>
                            <span className="align-center mr-2 text-[20px] font-semibold lg:text-[24px]">
                                RM
                            </span>
                            <span className="text-[32px] font-bold lg:text-[40px]">
                                3,500
                            </span>
                        </div>
                        <hr />
                    </div>
                </Card>
                <Card styleClass="mt-3 grow lg:h-fit p-3 rounded-xl">
                    <div className="text-[16px] font-bold text-themeGreenDark lg:text-[20px]">
                        Account Balance
                    </div>

                    <div className="mb-5 flex-grow items-center justify-between gap-3">
                        <div className="text-themeGreenDark">
                            <span className="align-center mr-2 text-[20px] font-semibold lg:text-[24px]">
                                RM
                            </span>
                            <span className="text-[32px] font-bold lg:text-[40px]">
                                300,789
                            </span>
                        </div>
                    </div>
                </Card>
            </div>
        </Layout>
    );
};

export default Payments;
