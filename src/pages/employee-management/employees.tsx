import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterEmployeeForm from "@/components/forms/FilterEmployeeForm";
import EmployeeForm from "@/components/forms/employee/EmployeeForm";
import EmployeeStatusForm from "@/components/forms/employee/EmployeeStatusForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import ProfileCard from "@/components/ui/ProfileCard";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import Tabs from "@/components/ui/Tabs";
import {
    DEFAULT_FILTER_PARAMS,
    EMPLOYEE,
    REINSTATE,
    RESIGN,
    RESIGNED,
    TRANSFER,
    TableColumnType,
    WORKING,
    employeeAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { getNameColumns } from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const Employee = ({ locale }) => {
    useCheckViewPermit("employee-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const _teacher = t("Teacher");
    const _staff = t("Staff");

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [employeeType, setEmployeeType] = useState<
        typeof _teacher | typeof _staff
    >(_teacher);
    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);
    const [openStatus, setOpenStatus] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [targetProfile, setTargetProfile] = useState<any>(null);
    const [targetStatusId, setTargetStatusId] = useState(null);
    const [actionType, setActionType] = useState(null);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data, axiosQuery: getEmployees } = useAxios({
        api: employeeAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchEmployees() {
        getEmployees({
            params: {
                ...filter,
                order_by: { status: "desc", ...(filter?.order_by ?? {}) },
            },
        });
    }

    useEffect(() => {
        if (filter.is_teacher === undefined) return;
        fetchEmployees();
    }, [filter, locale]);

    useEffect(() => {
        const isTeacher = employeeType === _teacher;

        if (filter.is_teacher === isTeacher) return;

        setFilter({
            ...filter,
            page: 1,
            is_teacher: isTeacher ? 1 : 0,
        });
    }, [employeeType]);

    useEffect(() => {
        if (data && activeLanguages) {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
                {
                    key: "job_title",
                    hasSort: true,
                    modify: (value) => (
                        <div className="min-w-[80px]">{value}</div>
                    ),
                },
                // {
                //     key: "employee_number",
                //     hasSort: true,
                //     modify(value, cell) {
                //         return <span className="text-[14px]">{value}</span>;
                //     },
                // },
                // {
                //     key: "badge_no",
                //     hasSort: true,
                //     modify(value, cell) {
                //         return (
                //             <span className="text-[14px]">{value ?? "-"}</span>
                //         );
                //     },
                // },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value) => capitalize(t(value)),
                },
            ];
            setColumns(_columns);
        }
    }, [data, activeLanguages, t]);

    function definedData() {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  ...item?.translations?.name,
                  employee_number: item?.employee_number,
                  job_title: item?.job_title?.name || "-",
                  nric: item?.nric,
                  status: item?.status ?? "-",
                  badge_no: item?.badge_no,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: [
                "nric",
                "badge_no",
                "employee_number",
                "status",
                "job_title",
            ].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } },
        });
    }

    const { axiosQuery: getEmployeeData } = useAxios({
        api: employeeAPI,
        locale,
        onSuccess: (result) => {
            setTargetProfile(result.data);
        },
    });

    function getProfile(id: number) {
        getEmployeeData({ id });
    }

    return (
        <Layout locale={locale} path="employee-management/employees">
            <Card styleClass="table-card">
                <div className="table-page-top">
                    <h2 className="capitalize">{t("employees")}</h2>
                    {hasPermit("employee-create") && (
                        <Button
                            size="smallerOnMobile"
                            className="ml-auto capitalize"
                            onClick={() => setOpenCreate(true)}
                        >
                            {t("Add ")}
                            {t("employee")}
                        </Button>
                    )}
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                        excludeFields={["is_teacher"]}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterEmployeeForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                        isTeacher={employeeType === _teacher}
                    />
                </FilterFormWrapper>

                <div className="-mb-1 ml-1 mt-2">
                    <Tabs
                        list={[_teacher, _staff]}
                        selected={employeeType}
                        setSelected={setEmployeeType}
                    />
                </div>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => {
                        const { status, id } = cell.row.original;

                        const openStatusModal = (actionType) => {
                            setTargetStatusId(id);
                            setActionType(actionType);
                            setOpenStatus(true);
                        };

                        return (
                            <>
                                <ActionDropdown>
                                    {hasPermit("employee-view") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => getProfile(id)}
                                        >
                                            {t("View")}
                                        </DropdownMenuItem>
                                    )}
                                    {hasPermit("employee-update") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => setTargetId(id)}
                                        >
                                            {t("Edit")}
                                        </DropdownMenuItem>
                                    )}

                                    {hasPermit("employee-update") &&
                                        status === WORKING && (
                                            <>
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() =>
                                                        openStatusModal(RESIGN)
                                                    }
                                                >
                                                    {t("Resign")}
                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() =>
                                                        openStatusModal(
                                                            TRANSFER
                                                        )
                                                    }
                                                >
                                                    {t("Update Job Title")}
                                                </DropdownMenuItem>
                                            </>
                                        )}
                                    {hasPermit("employee-update") &&
                                        status === RESIGNED && (
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() =>
                                                    openStatusModal(REINSTATE)
                                                }
                                            >
                                                {t("Reinstate")}
                                            </DropdownMenuItem>
                                        )}

                                    {hasPermit("employee-delete") && (
                                        <>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(id)
                                                }
                                            >
                                                {t("Delete")}
                                            </DropdownMenuItem>
                                        </>
                                    )}
                                </ActionDropdown>
                            </>
                        );
                    }}
                />
            </Card>

            {/* view */}
            <Modal
                open={targetProfile}
                onOpenChange={setTargetProfile}
                size="medium"
            >
                <ProfileCard
                    title="Employee Profile"
                    data={targetProfile!}
                    type={EMPLOYEE}
                />
            </Modal>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <EmployeeForm
                    isCreate={true}
                    close={() => setOpenCreate(false)}
                    refresh={() => {
                        setFilter({
                            is_teacher: filter.is_teacher,
                            per_page: filter.per_page,
                            page: 1,
                            order_by: { updated_at: "desc" },
                        });
                    }}
                    isTeacher={employeeType === _teacher}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <EmployeeForm
                    id={targetId}
                    close={() => setTargetId(null)}
                    refresh={() => {
                        setFilter({
                            is_teacher: filter.is_teacher,
                            per_page: filter.per_page,
                            page: 1,
                            order_by: { updated_at: "desc" },
                        });
                    }}
                    isTeacher={employeeType === _teacher}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={employeeAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchEmployees}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter} size="medium">
                <FilterEmployeeForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                    isTeacher={employeeType === _teacher}
                />
            </Modal>

            {/* Change status */}
            <Modal open={openStatus} onOpenChange={setOpenStatus}>
                <EmployeeStatusForm
                    id={targetStatusId}
                    actionType={actionType}
                    close={() => setOpenStatus(false)}
                    refresh={fetchEmployees}
                />
            </Modal>
        </Layout>
    );
};

export default Employee;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
