import { axiosInstance } from "@/lib/api";

export default function handler(req, res) {
    const { provider, id, token } = req.query;

    if (req.method === "POST") {
        const data = req.body;
        const endpoint = `${process.env.NEXT_PUBLIC_API_URL}payment-gateway/${provider}/callback/${id}/${token}`;

        axiosInstance
            .post(endpoint, data)
            .then((res) => {
                res.status(200).json(response.data);
            })
            .catch((error) => {
                if (error.response) {
                    res.status(error.response.status).json({
                        message: "Error",
                        error: error.response.data,
                    });
                } else if (error.request) {
                    res.status(500).json({
                        message: "No response received",
                        error: error.message,
                    });
                } else {
                    res.status(500).json({
                        message: "Error setting up the request",
                        error: error.message,
                    });
                }
            });
    } else {
        res.setHeader("Content-Type", "text/html");
        res.status(200).send(`
            <html>
            <body style="display:flex;justify-content:center;align-items:center">
                <h1 style="padding:20px;font-family:Arial;text-align:center">Redirecting you...</h1>
            </body>
            </html>
        `);
    }
}
