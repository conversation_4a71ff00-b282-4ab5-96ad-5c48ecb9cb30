import { useLocale } from "next-intl";
import { useEffect, useRef, useState } from "react";
import React from "react";
import clsx from "clsx";
import DOMPurify from "isomorphic-dompurify";
import { groupBy } from "lodash";
import { Circle, Paperclip } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import Card from "@/components/ui/Card";
import Modal from "@/components/ui/Modal";
import SelectWithRef from "@/components/ui/SelectWithRef";
import { axiosInstance } from "@/lib/api";
import { attendanceDashboard, inboxAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";

const IconStudentsBlue = "/icons/dashboard/icon-students-blue.svg";
const IconTeachersOrange = "/icons/dashboard/icon-teachers-orange.svg";
const IconParentsYellow = "/icons/dashboard/icon-parents-yellow.svg";

const Home = ({ locale }) => {
    const monthRef = useRef<any>();

    return (
        <Layout locale={locale} path={""}>
            <div className="flex flex-col lg:flex-row">
                <div className="lg: flex-grow lg:h-full">
                    <div className="flex h-full flex-col">
                        {/* <Card styleClass="h-fit mt-8">
                            <h2 className="mb-5">Attendance</h2>
                            <div className={clsx("flex flex-row space-x-3")}>
                                {Object.entries(attendanceDashboard).map(
                                    ([key, value]) => (
                                        <AttendanceCard
                                            key={key}
                                            name={value}
                                        />
                                    )
                                )}
                            </div>
                        </Card> */}

                        {/* <Card
                            styleClass="mt-8 lg:h-fit bg-themeGreen3 lg:bg-themeGreen3 p-3 rounded-xl"
                            isWhiteBg={false}
                        >
                            <h2 className="mb-5">Wallet Balance</h2>
                            <div className="flex text-[10px] text-themeGreen lg:text-[16px]">
                                Your Balance
                            </div>

                            <div className="mb-5 flex flex-grow flex-col gap-3 md:flex-row lg:items-center lg:justify-between">
                                <div className="text-themeGreenDark">
                                    <span className="align-center mr-2 text-[20px] font-semibold lg:text-[24px]">
                                        RM
                                    </span>
                                    <span className="text-[32px] font-bold lg:text-[40px]">
                                        2,350,400
                                    </span>
                                </div>
                                <Button>View Wallet</Button>
                            </div>
                        </Card> */}
                    </div>
                </div>

                {/* <div className="mt-8 lg:ml-8 lg:mt-0 lg:h-full lg:w-2/5 lg:flex-shrink">
                    <Card styleClass="lg:h-fit">
                        <div className="mb-5 flex flex-grow items-center justify-between gap-3">
                            <h2>Activity Calendar</h2>
                            <SelectWithRef
                                label={"Select Month"}
                                options={["January"]}
                                ref={monthRef}
                            />
                        </div>
                        <div className="flex-column flex">
                            <ActivityCard />
                        </div>
                    </Card>

                    <Card styleClass="lg:mt-8">
                        <div className="flex items-center justify-between">
                            <h2 className="mb-5">To Do List</h2>
                        </div>
                        <div className="flex-column flex">
                            <ToDoList />
                        </div>
                    </Card>
                </div> */}
            </div>
        </Layout>
    );
};

const AttendanceCard = ({ name }) => {
    let settings;
    switch (name) {
        case attendanceDashboard.students:
            settings = {
                bgColor: "bg-themeBlueLight",
                textColor: "text-themeBlue",
                icon: IconStudentsBlue,
            };
            break;
        case attendanceDashboard.teachers:
            settings = {
                bgColor: "bg-themeOrangeLight",
                textColor: "text-themeOrange",
                icon: IconTeachersOrange,
            };
            break;
        case attendanceDashboard.parents:
            settings = {
                bgColor: "bg-themeYellowLight",
                textColor: "text-themeYellow",
                icon: IconParentsYellow,
            };
            break;
    }

    return (
        <div
            className={`${settings.bgColor} mb-5 flex flex-1 flex-grow flex-col items-center rounded-xl py-3 lg:flex-row lg:items-start lg:px-3`}
        >
            <Image
                src={settings.icon}
                alt={`${name} icon`}
                className="m-2 w-[25px] lg:w-[32px] xl:w-[36px]"
                unoptimized
            />

            <div
                className={`${settings.textColor} px-2 text-center lg:text-left`}
            >
                <div className="text-[14px] capitalize leading-[21px] lg:text-[16px] lg:leading-[27px] xl:text-[18px]">
                    {name}
                </div>
                <div className="text-[16px] font-bold leading-[24px] lg:text-[18px] lg:leading-[30px] xl:text-[20px]">
                    147
                    <span className="text-[10px] leading-[15px] lg:text-[12px] lg:leading-[21px] xl:text-[14px]">
                        /150
                    </span>
                </div>
            </div>
        </div>
    );
};

// dummy date
const today = new Date();
const tomorrow = new Date(today);
tomorrow.setDate(tomorrow.getDate() + 1);
const weekAfterToday = new Date(today);
weekAfterToday.setDate(weekAfterToday.getDate() + 7);

function getDayOfWeek(date) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    return daysOfWeek[date.getDay()];
}

function getMonth(date) {
    const months = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
    ];
    return months[date.getMonth()];
}

const ActivityCard = ({}) => {
    const activityLists = [
        {
            name: "Biology",
            venue: "Class 2 Magneta",
            date: today,
            time: "8:00 - 9:00",
        },
        {
            name: "French",
            venue: "Class 1 Violet",
            date: today,
            time: "10:00 - 12:00",
        },
        {
            name: "Board Meeting",
            venue: "Class 2 Magneta",
            date: tomorrow,
            time: "15:00 - 16:00",
        },
        {
            name: "Science Homework Due",
            venue: "Meeting Hall 1",
            date: tomorrow,
            time: "8:00 - 9:00",
        },
    ];

    return (
        <div className="mb-5 w-full">
            {activityLists?.length > 0 &&
                activityLists?.map((activity, index) => {
                    const currentDate = new Date(activity.date);
                    const prevDate =
                        index > 0
                            ? new Date(activityLists[index - 1].date)
                            : null;

                    const differentDate =
                        prevDate &&
                        currentDate.toDateString() !== prevDate.toDateString()
                            ? true
                            : false;

                    return (
                        <div key={index}>
                            <div
                                className={`flex flex-row space-x-3 ${
                                    differentDate ? "mt-7" : "mt-2"
                                }`}
                            >
                                <div className="w-8 justify-self-center text-themeGray lg:w-10">
                                    <div className="text-[10px] lg:text-[15px]">
                                        {getDayOfWeek(activity.date)}
                                    </div>
                                    <div className="text-[16px] font-semibold lg:text-[18px]">
                                        {activity.date.getDate() < 10
                                            ? `0${activity.date.getDate()}`
                                            : activity.date.getDate()}
                                    </div>
                                </div>
                                <div className="flex-grow rounded-xl bg-gray-50 px-5 py-4">
                                    <div className="text-[14px] font-semibold lg:text-[18px]">
                                        {activity.name}
                                    </div>
                                    <div className="flex justify-between pt-2 text-[10px] text-themeGreen lg:text-[14px]">
                                        <div>{activity.venue}</div>
                                        <div>{activity.time}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
        </div>
    );
};

const ToDoList = () => {
    const [toDoLists, setToDoLists] = useState([
        {
            id: 1,
            name: "Lesson Plan June",
            date: today,
            checked: true,
        },
        {
            id: 2,
            name: "Assign Science Homework",
            date: tomorrow,
            checked: false,
        },
        {
            id: 3,
            name: "Meeting Report",
            date: weekAfterToday,
            checked: false,
        },
        // Add more options as needed
    ]);

    const onChangeToDo = (id) => {
        const updatedToDoLists = toDoLists.map((todo) => {
            if (todo.id === id) {
                return { ...todo, checked: !todo.checked };
            } else {
                return todo;
            }
        });

        setToDoLists(updatedToDoLists);
    };

    return (
        <div className="w-full">
            {toDoLists?.length > 0 &&
                toDoLists?.map((todos) => {
                    const difference_ms =
                        todos.date.getTime() - today.getTime();
                    const daysLeft = Math.floor(
                        difference_ms / (1000 * 60 * 60 * 24)
                    );

                    return (
                        <div
                            key={todos.id}
                            className="mb-2 flex flex-row rounded-xl bg-gray-50"
                        >
                            <div
                                className={`flex flex-col items-center rounded-bl-xl rounded-tl-xl px-4 py-3 text-white ${
                                    todos.checked
                                        ? "bg-themeGray"
                                        : "bg-themeGreen"
                                }`}
                            >
                                <div className="text-[16px] font-semibold lg:text-[18px]">
                                    {todos.date.getDate() < 10
                                        ? `0${todos.date.getDate()}`
                                        : todos.date.getDate()}
                                </div>
                                <div className="text-[10px] lg:text-[14px]">
                                    {getMonth(todos.date)}
                                </div>
                            </div>
                            <div className="flex-grow rounded-br-xl rounded-tr-xl px-3 py-3">
                                <div className="flex items-center justify-between">
                                    <div className="flex flex-col">
                                        <div
                                            className={`text-[14px] font-semibold lg:text-[18px] ${
                                                todos.checked &&
                                                "text-themeGray"
                                            }`}
                                        >
                                            {todos.name}
                                        </div>
                                        <div
                                            className={`flex justify-between pt-2 text-[10px] lg:text-[14px] ${
                                                todos.checked
                                                    ? "text-themeGray"
                                                    : "text-themeGreen"
                                            }`}
                                        >
                                            {daysLeft} Days Left
                                        </div>
                                    </div>
                                    <div>
                                        <Circle
                                            className={`aspect-square h-6 w-6 rounded-full border ${
                                                todos.checked
                                                    ? "border-[5px] border-themeGreen bg-white text-white"
                                                    : "border-themeGray5 bg-themeGray4 text-themeGray4"
                                            } `}
                                            onClick={() =>
                                                onChangeToDo(todos.id)
                                            }
                                        ></Circle>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
        </div>
    );
};

export default Home;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
