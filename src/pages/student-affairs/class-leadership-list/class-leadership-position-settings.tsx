import { lowerCase } from "lodash";
import ClassLeadershipPositionSettingForm from "@/components/forms/student-affairs/ClassLeadershipPositionSettingForm";
import FilterClassLeadershipPositionSettingForm from "@/components/forms/student-affairs/FilterClassLeadershipPositionSettingForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    ACTIVE,
    INACTIVE,
    leadershipPositionSettingsAPI,
} from "@/lib/constant";

const ClassLeadershipPositionSettings = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/class-leadership-list/class-leadership-position-settings"
        api={leadershipPositionSettingsAPI}
        definedColumn={(activeLanguages) => {
            const _nameColumns = activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );

            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    displayAs: "Sequence",
                    hasSort: true,
                },
                ..._nameColumns,
                {
                    key: "is_active",
                    displayAs: "Status",
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <ClassLeadershipPositionSettingForm {...params} />}
        filterForm={(params) => (
            <FilterClassLeadershipPositionSettingForm {...params} />
        )}
        viewPermit="master-leadership-position-view"
        createPermit="master-leadership-position-create"
        updatePermit="master-leadership-position-update"
        deletePermit="master-leadership-position-delete"
    />
);

export default ClassLeadershipPositionSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
