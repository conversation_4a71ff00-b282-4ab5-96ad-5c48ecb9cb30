import { useEffect, useState } from "react";
import { isArray } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import FilterClassLeadershipRecordForm from "@/components/forms/student-affairs/FilterClassLeadershipRecordForm";
import SearchClassForm from "@/components/forms/student-affairs/SearchClassForm";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FreeSelect from "@/components/ui/FreeSelect";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    TableColumnType,
    GET_ALL_PARAMS,
    studentAPI,
    leadershipPositionSettingsAPI,
    leadershipPositionRecordsAPI,
    leadershipPositionBulkCreateRecordsAPI,
    SIMPLE,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { combinedNames, showBackendFormError } from "@/lib/utils";
import { useUserProfile } from "@/lib/store";

const ClassLeaderships = ({ locale }) => {
    useCheckViewPermit("leadership-position-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [filterLeadershipPositionType, setFilterLeadershipPositionType] =
        useState<any>(null);
    const [openFilter, setOpenFilter] = useState(false);
    const [selectedClass, setSelectedClass] = useState<any>();
    const [selectedClassName, setSelectedClassName] = useState<any>();

    const form = useForm<any>({
        defaultValues: {
            leadership_type: [],
            student: [],
        },
    });

    const { data: leadershipTypes, axiosQuery: getLeadershipPositionSettings } =
        useAxios({
            api: leadershipPositionSettingsAPI,
            locale,
        });

    const {
        data: leadershipPositionRecords,
        axiosQuery: getLeadershipPositionRecords,
    } = useAxios({
        api: leadershipPositionRecordsAPI,
        locale,
    });

    const { data: classStudents, axiosQuery: getClassStudents } = useAxios({
        api: studentAPI,
        locale,
    });

    function updateFormValues() {
        if (!leadershipTypes || !classStudents || !leadershipPositionRecords)
            return;

        let leadershipPositionTypes = leadershipTypes;

        if (filterLeadershipPositionType) {
            leadershipPositionTypes = leadershipTypes.filter(
                (leadershipType) =>
                    leadershipType.id === filterLeadershipPositionType
            );
        }

        const updatedValues = leadershipPositionTypes.map((leadershipType) => {
            const matchedRecord = leadershipPositionRecords?.find(
                (record) =>
                    record?.leadership_position?.id === leadershipType?.id
            );

            return {
                leadership_type: {
                    id: leadershipType.id,
                    name:
                        leadershipType.translations.name[locale] ??
                        leadershipType.name,
                },
                student: matchedRecord ? matchedRecord.student.id : null,
            };
        });

        form.reset({
            leadership_type: updatedValues.map((item) => item.leadership_type),
            student: updatedValues.map((item) => item.student),
        });
    }

    function fetchLeadershipPositionRecords() {
        getLeadershipPositionRecords({
            params: {
                includes: ["leadershipPosition", "student"],
                semester_class_id: selectedClass,
                order_by: {
                    leadership_position: {
                        sequence: "desc",
                    },
                },
                per_page: 200,
            },
        });
    }

    async function handleClassSelection(classId: string, className?: string) {
        setSelectedClass(classId);
        if (className) {
            setSelectedClassName(className);
        }

        try {
            await getClassStudents({
                params: {
                    ...GET_ALL_PARAMS,
                    response: SIMPLE,
                    includes: [
                        "currentSemesterPrimaryClass.semesterSetting",
                        "currentSemesterPrimaryClass.semesterClass.classModel",
                    ],
                    semester_class_id: classId,
                },
            });

            await getLeadershipPositionRecords({
                params: {
                    includes: ["leadershipPosition", "student"],
                    semester_class_id: classId,
                    order_by: {
                        leadership_position: {
                            sequence: "desc",
                        },
                    },
                    per_page: 200,
                },
            });

            updateFormValues();
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    }

    function definedColumns() {
        const _columns: TableColumnType[] = [
            {
                key: "leadership_type",
                displayAs: "Leadership Position Type",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    const leadershipType = form.getValues(
                        `leadership_type[${index}]`
                    );
                    return <span>{leadershipType?.name}</span>;
                },
            },
            {
                key: "student",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    return (
                        <div className="z-50 h-full min-w-[120px]">
                            <FreeSelect
                                hasLabel={false}
                                control={form.control}
                                name={`student[${index}]`}
                                options={classStudents?.map((student) => ({
                                    ...student,
                                    name: `${student.student_number ? ` ${student.student_number} -` : ""} 
                                ${combinedNames(student?.translations?.name)}`,
                                }))}
                                error={
                                    form.formState.errors?.students?.[index]
                                        ?.type
                                }
                            />
                        </div>
                    );
                },
            },
        ];
        return _columns;
    }

    function definedData() {
        if (!Array.isArray(leadershipTypes)) return [];

        let leadershipPositionTypes = leadershipTypes;
        if (filterLeadershipPositionType) {
            leadershipPositionTypes = leadershipTypes.filter(
                (leadershipType) =>
                    leadershipType.id === filterLeadershipPositionType
            );
        }

        return leadershipPositionTypes.map((leadershipType) => ({
            leadership_type:
                leadershipType.translations.name[locale] ?? leadershipType.name,
            student: null,
        }));
    }

    const { axiosPost: bulkCreateLeadershipPositions, error: postError } =
        useAxios({
            api: leadershipPositionBulkCreateRecordsAPI,
            toastMsg: "Assigned successfully",
            onSuccess: () => {
                fetchLeadershipPositionRecords();
            },
        });

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            const leadershipPositionsData = data.leadership_type.map(
                (type, index) => {
                    return {
                        id: type.id,
                        student_id: data.student[index] || null,
                    };
                }
            );

            const formattedData = {
                semester_class_id: selectedClass,
                leadership_positions: leadershipPositionsData,
            };

            bulkCreateLeadershipPositions(formattedData);
        })();
    }

    useEffect(() => {
        getLeadershipPositionSettings({
            params: {
                ...GET_ALL_PARAMS,
                is_active: 1,
                order_by: {
                    sequence: "desc",
                },
            },
        });
    }, []);

    useEffect(() => {
        if (classStudents && leadershipPositionRecords && leadershipTypes) {
            updateFormValues();
        }
    }, [
        classStudents,
        leadershipPositionRecords,
        leadershipTypes,
        filterLeadershipPositionType,
        locale,
    ]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <Layout
            locale={locale}
            path="student-affairs/class-leadership-list/class-leaderships"
        >
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">Class Leaderships</h2>

                <SearchClassForm
                    locale={locale}
                    onClassSelect={handleClassSelection}
                />

                {selectedClass &&
                    isArray(leadershipTypes) &&
                    isArray(classStudents) && (
                        <>
                            <div className="mb-2 mt-5 flex items-center justify-between gap-5 border-t border-dashed pl-0.5 pt-3">
                                <h3>
                                    Class Leadership Assignment (
                                    {selectedClassName})
                                </h3>
                                <div className="flex gap-3">
                                    <TableFilterBtn
                                        filter={{
                                            leadership_position_id:
                                                filterLeadershipPositionType,
                                        }}
                                        onClick={() => setOpenFilter(true)}
                                    />
                                </div>
                            </div>
                            <DataTable
                                data={definedData()}
                                columns={definedColumns()}
                                canOverflow={true}
                            />

                            {hasPermit("leadership-position-record-create") && (
                                <Button
                                    size="smallerOnMobile"
                                    className="ml-auto mt-5"
                                    onClick={onSubmit}
                                >
                                    Save
                                </Button>
                            )}
                            <div className="h-10"></div>
                        </>
                    )}
                <div className="h-3"></div>
            </Card>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterClassLeadershipRecordForm
                    leadershipTypes={leadershipTypes}
                    filterLeadershipPositionType={filterLeadershipPositionType}
                    setFilterLeadershipPositionType={
                        setFilterLeadershipPositionType
                    }
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default ClassLeaderships;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
