import ComprehensiveAssessmentCategoryForm from "@/components/forms/student-affairs/ComprehensiveAssessmentCategoryForm";
import FilterComprehensiveAssessmentCategoryForm from "@/components/forms/student-affairs/FilterComprehensiveAssessmentCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    comprehensiveAssessmentCategoryAPI,
} from "@/lib/constant";

const ComprehensiveAssessmentCategories = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/comprehensive-quality-assessment/comprehensive-assessment-categories"
        api={comprehensiveAssessmentCategoryAPI}
        definedColumn={(activeLanguages) => {
            return activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <ComprehensiveAssessmentCategoryForm {...params} />}
        filterForm={(params) => (
            <FilterComprehensiveAssessmentCategoryForm {...params} />
        )}
        viewPermit="comprehensive-assessment-category-view"
        createPermit="comprehensive-assessment-category-create"
        updatePermit="comprehensive-assessment-category-update"
        deletePermit="comprehensive-assessment-category-delete"
    />
);

export default ComprehensiveAssessmentCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
