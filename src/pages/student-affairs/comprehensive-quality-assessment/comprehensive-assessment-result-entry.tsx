import { useEffect, useState } from "react";
import clsx from "clsx";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    studentAPI,
    comprehensiveAssessmentResultEntryAPI,
    comprehensiveAssessmentCategoryAPI,
    comprehensiveAssessmentQuestionAPI,
    NEED_IMPROVEMENT,
    ACHIEVED,
    semesterSettingAPI,
    semesterClassesAPI,
    semesterClassPrimaryDropdownFilter,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { optionUserLabel } from "@/lib/utils";
import { isEmpty } from "lodash";
import { useTranslations } from "next-intl";

const ComprehensiveAssessmentResultEntry = ({ locale }) => {
    useCheckViewPermit("comprehensive-assessment-record-view");
    const form = useForm<any>({
        defaultValues: {
            students: [],
            semester_class_id: "",
            comprehensive_assessment_category_id: "",
        },
    });
    const [selectedClass, setSelectedClass] = useState<any>();
    const [assessmentRecords, setAssessmentRecords] = useState<any>([]);

    const { data: classStudents, axiosQuery: getClassStudents } = useAxios({
        api: studentAPI,
        locale,
    });

    const [selectedAssessmentCategory, setSelectedAssessmentCategory] =
        useState<any>();

    const {
        data: assessmentQuestions,
        axiosQuery: getComprehensiveAssessmentQuestions,
    } = useAxios({
        api: comprehensiveAssessmentQuestionAPI,
        locale,
    });

    const { axiosQuery: getComprehensiveAssessmentRecords } = useAxios({
        api: comprehensiveAssessmentResultEntryAPI,
        locale,
        onSuccess: (result) => {
            setAssessmentRecords(result.data);
        },
    });

    const { axiosPost: updateComprehensiveAssessmentResult, error: postError } =
        useAxios({
            api: comprehensiveAssessmentResultEntryAPI,
            toastMsg: "Updated successfully",
            onSuccess: () => {
                fetchAssessmentRecords();
            },
        });

    function fetchAssessmentRecords() {
        console.log("selectedclass", selectedClass);
        getComprehensiveAssessmentRecords({
            params: {
                ...GET_ALL_PARAMS,
                semester_class_id: selectedClass,
                comprehensive_assessment_category_id:
                    selectedAssessmentCategory,
            },
        });
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data) => {
                const formattedData: {
                    semester_class_id: number | null;
                    comprehensive_assessment_category_id: number | null;
                    students: {
                        student_id: number;
                        questions: {
                            question_id: number;
                            result: string;
                        }[];
                    }[];
                } = {
                    semester_class_id: selectedClass,
                    comprehensive_assessment_category_id:
                        selectedAssessmentCategory,
                    students: [],
                };

                classStudents.forEach((student) => {
                    const studentQuestions = assessmentQuestions.map(
                        (question) => ({
                            question_id: question.id,
                            result: data[`result_${student.id}_${question.id}`],
                        })
                    );

                    formattedData.students.push({
                        student_id: student.id,
                        questions: studentQuestions,
                    });
                });

                updateComprehensiveAssessmentResult(formattedData);
            })
        );
    }

    useEffect(() => {
        if (selectedClass) {
            setAssessmentRecords([]);
            getClassStudents({
                params: {
                    ...GET_ALL_PARAMS,
                    semester_class_id: selectedClass,
                    only_active_class: 1,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [selectedClass]);

    useEffect(() => {
        if (selectedAssessmentCategory) {
            setAssessmentRecords([]);
            getComprehensiveAssessmentQuestions({
                params: {
                    ...GET_ALL_PARAMS,
                    comprehensive_assessment_category_id:
                        selectedAssessmentCategory,
                },
            });
        }
    }, [selectedAssessmentCategory]);

    useEffect(() => {
        if (assessmentRecords && assessmentQuestions) {
            assessmentRecords.forEach((record) => {
                record.questions.forEach((question) => {
                    const result = question.result ?? ACHIEVED;
                    form.setValue(
                        `result_${record.student_id}_${question.question_id}`,
                        result
                    );
                });
            });
        }
    }, [assessmentRecords, assessmentQuestions]);

    const t = useTranslations("common");

    return (
        <Layout
            locale={locale}
            path="student-affairs/comprehensive-quality-assessment/comprehensive-assessment-result-entry"
        >
            <Card styleClass="table-card">
                <h2 className="mb-4 capitalize">
                    {t("Comprehensive Assessment Result Entry")}
                </h2>
                <SearchClassStudentAndRecordForm
                    locale={locale}
                    setSelectedClass={setSelectedClass}
                    setSelectedAssessmentCategory={
                        setSelectedAssessmentCategory
                    }
                    onFilter={fetchAssessmentRecords}
                />

                {selectedClass &&
                    selectedAssessmentCategory &&
                    !isEmpty(assessmentRecords) && (
                        <>
                            <h3 className="mb-4 ml-0.5 border-t border-dashed pt-4">
                                {t("Students Assessment Results")}
                            </h3>

                            <ComprehensiveAssessmentResultEntryTableForm
                                form={form}
                                classStudents={classStudents}
                                assessmentQuestions={assessmentQuestions}
                                onSubmit={onSubmit}
                            />
                        </>
                    )}
            </Card>
        </Layout>
    );
};

const SearchClassStudentAndRecordForm = ({
    locale,
    setSelectedClass,
    setSelectedAssessmentCategory,
    onFilter,
}) => {
    const form = useForm({
        defaultValues: {
            semester_setting_id: undefined,
            semester_class_id: undefined,
            comprehensive_assessment_category_id: undefined,
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
                const currentSemesterFromDate = new Date(currentSemester.from);
                const semOptions = response.data.filter((option) => {
                    const fromDate = new Date(option.from);
                    return fromDate >= currentSemesterFromDate;
                });

                setSemesterOptions(semOptions);
            } else {
                setSemesterOptions(response.data);
            }
        },
    });

    const { data: semesterClasses, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
    });

    const classes =
        semesterClasses?.map((data) => ({
            id: data.id,
            name:
                data.class_model.translations.name[locale] ??
                data.class_model.name,
        })) || [];

    const { data: assessmentCategories, axiosQuery: getAssessmentCategories } =
        useAxios({
            api: comprehensiveAssessmentCategoryAPI,
            locale,
        });

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            form.setValue("semester_class_id", undefined);
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                    is_active: 1,
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    useEffect(() => {
        getSemesterOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    semester_year: "desc",
                },
            },
        });
        getAssessmentCategories({ params: GET_ALL_PARAMS });
    }, []);

    const t = useTranslations("common");

    return (
        <Form {...form}>
            <form className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3">
                <FormSelect
                    control={form.control}
                    name="semester_setting_id"
                    label={t("semester") + "*"}
                    options={semesterOptions}
                    isSortByName={false}
                    onChange={() => {
                        form.setValue("semester_class_id", undefined);
                        setSelectedClass(null);
                    }}
                />

                <FormSelect
                    control={form.control}
                    name="semester_class_id"
                    label={t("semester class") + "*"}
                    options={classes}
                    isDisabled={!form.watch("semester_setting_id")}
                    onChange={(selected) => {
                        setSelectedClass(selected);
                    }}
                />

                <FormSelect
                    control={form.control}
                    name="comprehensive_assessment_category_id"
                    label={t("category") + "*"}
                    options={assessmentCategories}
                    isDisabled={!form.watch("semester_class_id")}
                    onChange={(selected) => {
                        setSelectedAssessmentCategory(selected);
                    }}
                />
                <div className="lg:col-span-3">
                    <Button
                        disabled={
                            !form.watch("semester_setting_id") ||
                            !form.watch("semester_class_id") ||
                            !form.watch("comprehensive_assessment_category_id")
                        }
                        onClick={onFilter}
                        variant={"outline"}
                        className="ml-auto"
                    >
                        {t("Filter")}
                    </Button>
                </div>
            </form>
        </Form>
    );
};

const ComprehensiveAssessmentResultEntryTableForm = ({
    form,
    classStudents,
    assessmentQuestions,
    onSubmit,
}) => {
    const t = useTranslations("common");
    return (
        <Form {...form}>
            {classStudents?.length > 0 && assessmentQuestions?.length > 0 ? (
                <>
                    <div className="mb-7 rounded-md border">
                        {classStudents?.map((student, index) => (
                            <div key={student?.id} className="c-text-size">
                                <div
                                    className={clsx(
                                        "c-text-size bg-gray-50 px-4 py-2.5 font-medium text-gray-600",
                                        index === 0 && "rounded-t-md"
                                    )}
                                >
                                    {optionUserLabel(
                                        student?.student_number,
                                        student?.translations?.name
                                    )}
                                </div>
                                <div className="grid gap-y-1 py-1.5">
                                    {assessmentQuestions?.map((item) => (
                                        <div
                                            key={item?.id}
                                            className="flex items-center gap-x-5 px-4 transition hover:bg-gray-50"
                                        >
                                            <div className="flex-grow">
                                                {item.question}
                                            </div>
                                            <div className="min-w-[140px]">
                                                <FormSelect
                                                    hasLabel={false}
                                                    control={form.control}
                                                    name={`result_${student.id}_${item.id}`}
                                                    isStringOptions={true}
                                                    options={[
                                                        ACHIEVED,
                                                        NEED_IMPROVEMENT,
                                                    ]}
                                                    isClearable={false}
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                    <Button
                        size="smallerOnMobile"
                        className="mb-5 ml-auto min-w-[100px]"
                        onClick={onSubmit}
                    >
                        {t("Save")}
                    </Button>
                </>
            ) : (
                <p className="mb-3 ml-0.5 text-themeLabel">{t("No data")}</p>
            )}
        </Form>
    );
};

export default ComprehensiveAssessmentResultEntry;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
