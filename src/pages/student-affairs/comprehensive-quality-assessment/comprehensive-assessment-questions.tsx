import ComprehensiveAssessmentQuestionForm from "@/components/forms/student-affairs/ComprehensiveAssessmentQuestionForm";
import FilterComprehensiveAssessmentQuestionForm from "@/components/forms/student-affairs/FilterComprehensiveAssessmentQuestionForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    comprehensiveAssessmentQuestionAPI,
} from "@/lib/constant";
import { isArray } from "lodash";

const ComprehensiveAssessmentQuestions = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/comprehensive-quality-assessment/comprehensive-assessment-questions"
        api={comprehensiveAssessmentQuestionAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...activeLanguages.map((lang) => ({
                    key: lang.code,
                    displayAs: `Question ( ${lang?.name} )`,
                    hasSort: true,
                })),
                {
                    key: "comprehensive_assessment_category_name",
                    displayAs: "Comprehensive Assessment Category",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.question,
                      comprehensive_assessment_category_name:
                          item?.comprehensive_assessment_category?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "comprehensive_assessment_category_name"
                ? { [name]: direction }
                : { question: { [name]: direction } };
        }}
        form={(params) => <ComprehensiveAssessmentQuestionForm {...params} />}
        filterForm={(params) => (
            <FilterComprehensiveAssessmentQuestionForm {...params} />
        )}
        viewPermit="comprehensive-assessment-question-view"
        createPermit="comprehensive-assessment-question-create"
        updatePermit="comprehensive-assessment-question-update"
        deletePermit="comprehensive-assessment-question-delete"
    />
);

export default ComprehensiveAssessmentQuestions;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
