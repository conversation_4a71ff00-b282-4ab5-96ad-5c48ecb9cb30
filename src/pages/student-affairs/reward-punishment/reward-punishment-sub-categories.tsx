import FilterRewardPunishmentSubCategoryForm from "@/components/forms/student-affairs/FilterRewardPunishmentSubCategoryForm";
import RewardPunishmentSubCategoryForm from "@/components/forms/student-affairs/RewardPunishmentSubCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    rewardPunishmentSubCategoriesAPI,
} from "@/lib/constant";

const RewardPunishmentSubCategories = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/reward-punishment/reward-punishment-sub-categories"
        api={rewardPunishmentSubCategoriesAPI}
        definedColumn={(activeLanguages) => {
            return activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <RewardPunishmentSubCategoryForm {...params} />}
        filterForm={(params) => (
            <FilterRewardPunishmentSubCategoryForm {...params} />
        )}
        viewPermit="master-reward-punishment-sub-category-view"
        createPermit="master-reward-punishment-sub-category-create"
        updatePermit="master-reward-punishment-sub-category-update"
        deletePermit="master-reward-punishment-sub-category-delete"
    />
);

export default RewardPunishmentSubCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
