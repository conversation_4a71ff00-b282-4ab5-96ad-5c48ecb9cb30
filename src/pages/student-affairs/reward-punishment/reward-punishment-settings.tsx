import { lowerCase } from "lodash";
import FilterRewardPunishmentSettingForm from "@/components/forms/student-affairs/FilterRewardPunishmentSettingForm";
import RewardPunishmentSettingForm from "@/components/forms/student-affairs/RewardPunishmentSettingForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    ACTIVE,
    INACTIVE,
    rewardPunishmentSettingsAPI,
} from "@/lib/constant";
import { getNameColumns, strStartCase } from "@/lib/utils";

const RewardPunishmentSettings = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/reward-punishment/reward-punishment-settings"
        api={rewardPunishmentSettingsAPI}
        otherFilterParams={{ includes: ["meritDemeritSettings"] }}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                ...getNameColumns(activeLanguages),
                {
                    key: "category",
                    hasSort: true,
                },
                {
                    key: "sub_category",
                    hasSort: true,
                },
                {
                    key: "average_exam_marks",
                    hasSort: true,
                },
                {
                    key: "merit_demerit_settings",
                    displayAs: "Merit/Demerit",
                },
                {
                    key: "conduct_marks",
                    hasSort: true,
                },
                {
                    key: "display_in_report_card",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                      category: item?.category?.translations?.name?.[locale],
                      sub_category:
                          item?.sub_category?.translations?.name?.[locale] ??
                          "-",
                      average_exam_marks: item?.average_exam_marks,
                      conduct_marks: item?.conduct_marks,
                      display_in_report_card: strStartCase(
                          item?.display_in_report_card ? "YES" : "NO"
                      ),
                      is_active: lowerCase(item.is_active ? ACTIVE : INACTIVE),
                      merit_demerit_settings:
                          item?.merit_demerit_settings?.[0]?.name ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "category" || name === "sub_category"
                ? { name: { [name]: direction } }
                : { [name]: direction };
        }}
        form={(params) => <RewardPunishmentSettingForm {...params} />}
        filterForm={(params) => (
            <FilterRewardPunishmentSettingForm {...params} />
        )}
        formSize="medium"
        viewPermit="reward-punishment-view"
        createPermit="reward-punishment-create"
        updatePermit="reward-punishment-update"
        deletePermit="reward-punishment-delete"
    />
);

export default RewardPunishmentSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
