import { useEffect, useState } from "react";
import React from "react";
import { DropdownMenuSeparator } from "@radix-ui/react-dropdown-menu";
import { capitalize, isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import FilterRewardPunishmentRecordForm from "@/components/forms/student-affairs/FilterRewardPunishmentRecordForm";
import RewardPunishmentRecordBulkChangeStatusForm from "@/components/forms/student-affairs/RewardPunishmentRecordBulkChangeStatusForm";
import RewardPunishmentRecordForm from "@/components/forms/student-affairs/RewardPunishmentRecordForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    CONFIRMED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    TableColumnType,
    rewardPunishmentRecordsAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    combinedNamesCell,
    displayDateTime,
    getTableSelection,
    refreshForUpdate,
} from "@/lib/utils";
import clsx from "clsx";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const RewardPunishmentRecords = ({ locale }) => {
    useCheckViewPermit("reward-punishment-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
    });
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);
    const [openBulkChangeStatus, setOpenBulkChangeStatus] = useState(false);

    const [targetEditId, setTargetEditId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const [selection, setSelection] = useState<any[]>([]);

    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data, axiosQuery: getRewardPunishmentRecords } = useAxios({
        api: rewardPunishmentRecordsAPI,
        locale,
        onSuccess: (result) => {
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
        },
    });

    function fetchRecords() {
        getRewardPunishmentRecords({
            params: {
                ...filter,
                includes: [
                    "studentLatestPrimaryClasses",
                    "rewardPunishment.meritDemeritSettings",
                ],
            },
        });
    }

    useEffect(() => {
        fetchRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data && activeLanguages) {
            const _columns: TableColumnType[] = [
                {
                    key: "date",
                    hasSort: true,
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
                {
                    key: "student_number",
                    displayAs: "Student Number",
                    hasSort: true,
                },
                {
                    key: "student_name",
                    displayAs: "Student Name",
                    hasSort: true,
                    modify(value) {
                        return combinedNamesCell(value);
                    },
                },
                {
                    key: "class",
                },
                {
                    key: "merit_demerit_settings",
                    displayAs: "Merit/ Demerit",
                    modify: (value) => (
                        <div className="min-w-[110px]">{value}</div>
                    ),
                },
                {
                    key: "reward_punishment_name",
                    displayAs: "Reward/ Punishment",
                    hasSort: true,
                },
                {
                    key: "average_exam_marks",
                    hasSort: true,
                },
                {
                    key: "conduct_marks",
                    hasSort: true,
                },
                {
                    key: "display_in_report_card",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value) => (
                        <span
                            className={clsx(
                                value === CONFIRMED && "text-green-600",
                                value === DRAFT && "text-yellow-500"
                            )}
                        >
                            {capitalize(value)}
                        </span>
                    ),
                },
                {
                    key: "notification_sent_at",
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [data, activeLanguages]);

    function definedData() {
        return isArray(data)
            ? data.map((item) => {
                  return {
                      id: item?.id,
                      student_number: item?.student?.student_number,
                      student_name: Object.values(
                          item?.student?.translations?.name ?? {}
                      ),
                      class: item?.student_class?.semester_class?.class_model
                          ?.name?.[locale],
                      merit_demerit_settings:
                          item?.reward_punishment?.merit_demerit_settings
                              ?.map(
                                  (setting) =>
                                      setting.translations?.name?.[locale]
                              )
                              .join(", "),
                      reward_punishment_name:
                          item?.reward_punishment?.translations?.name?.[
                              locale
                          ] ?? item?.reward_punishment?.name,
                      average_exam_marks: item?.average_exam_marks,
                      conduct_marks: item?.conduct_marks,
                      display_in_report_card: lowerCase(
                          item?.display_in_report_card ? "YES" : "NO"
                      ),
                      date: displayDateTime(item?.date, DATE_FORMAT.DMY),
                      status: item?.status ?? "-",
                      notification_sent_at: displayDateTime(
                          item?.notification_sent_at,
                          DATE_FORMAT.DMY
                      ),
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, navigatedResults);
        setSelection(_selection);
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetEditId(null);
    }

    return (
        <Layout
            locale={locale}
            path="student-affairs/reward-punishment/reward-punishment-records"
        >
            <Card styleClass="table-card">
                <div className="table-page-top">
                    <h2 className="capitalize">Reward/ Punishment Records</h2>
                    {hasPermit("reward-punishment-record-create") && (
                        <Button
                            size="smallerOnMobile"
                            className="ml-auto"
                            onClick={() => setOpenCreate(true)}
                        >
                            Add Reward/ Punishment
                        </Button>
                    )}

                    {hasPermit("reward-punishment-record-update") && (
                        <Button
                            variant="outline"
                            size="smallerOnMobile"
                            disabled={selection?.length < 1}
                            onClick={() => setOpenBulkChangeStatus(true)}
                        >
                            Bulk Change Status
                        </Button>
                    )}

                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterRewardPunishmentRecordForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    onMultiSelect={onMultiSelect}
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => {
                        const id = cell.row.original.id;
                        const status = cell.row.original.status;
                        return (
                            <>
                                <ActionDropdown>
                                    {status.toLowerCase() ===
                                        DRAFT.toLowerCase() &&
                                    hasPermit(
                                        "reward-punishment-record-update"
                                    ) ? (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => setTargetEditId(id)}
                                        >
                                            Edit
                                        </DropdownMenuItem>
                                    ) : (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetEditId(id);
                                            }}
                                        >
                                            View
                                        </DropdownMenuItem>
                                    )}
                                    {status.toLowerCase() ===
                                        DRAFT.toLowerCase() &&
                                        hasPermit(
                                            "reward-punishment-record-delete"
                                        ) && (
                                            <>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    className="c-text-size text-red-600"
                                                    onClick={() =>
                                                        setTargetDeleteId(id)
                                                    }
                                                >
                                                    Delete
                                                </DropdownMenuItem>
                                            </>
                                        )}
                                </ActionDropdown>
                            </>
                        );
                    }}
                />
            </Card>

            {/* create */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <RewardPunishmentRecordForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* edit */}
            <Modal
                open={targetEditId}
                onOpenChange={setTargetEditId}
                size="medium"
            >
                <RewardPunishmentRecordForm
                    id={targetEditId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* bulk change status */}
            <Modal
                open={openBulkChangeStatus}
                onOpenChange={setOpenBulkChangeStatus}
            >
                <RewardPunishmentRecordBulkChangeStatusForm
                    records={selection}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setOpenBulkChangeStatus(false)}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={rewardPunishmentRecordsAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchRecords}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterRewardPunishmentRecordForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default RewardPunishmentRecords;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
