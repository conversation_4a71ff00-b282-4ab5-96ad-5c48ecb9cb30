import FilterRewardPunishmentCategoryForm from "@/components/forms/student-affairs/FilterRewardPunishmentCategoryForm";
import RewardPunishmentCategoryForm from "@/components/forms/student-affairs/RewardPunishmentCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, rewardPunishmentCategoriesAPI } from "@/lib/constant";

const RewardPunishmentCategories = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/reward-punishment/reward-punishment-categories"
        api={rewardPunishmentCategoriesAPI}
        definedColumn={(activeLanguages) => {
            return activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <RewardPunishmentCategoryForm {...params} />}
        filterForm={(params) => (
            <FilterRewardPunishmentCategoryForm {...params} />
        )}
        viewPermit="master-reward-punishment-category-view"
        createPermit="master-reward-punishment-category-create"
        updatePermit="master-reward-punishment-category-update"
        deletePermit="master-reward-punishment-category-delete"
    />
);

export default RewardPunishmentCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
