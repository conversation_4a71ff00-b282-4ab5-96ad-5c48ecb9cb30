import FilterMeritDemeritSettingForm from "@/components/forms/student-affairs/FilterMeritDemeritSettingForm";
import MeritDemeritSettingForm from "@/components/forms/student-affairs/MeritDemeritSettingForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, meritDemeritSettingsAPI } from "@/lib/constant";

const MeritDemeritSettings = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/reward-punishment/merit-demerit-settings"
        api={meritDemeritSettingsAPI}
        definedColumn={(activeLanguages) => {
            const _nameColumns = activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );
            const _columns: TableColumnType[] = [
                {
                    key: "type",
                    hasSort: true,
                },
                ..._nameColumns,
                {
                    key: "average_exam_marks",
                    hasSort: true,
                },
                {
                    key: "conduct_marks",
                    hasSort: true,
                },
            ];

            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      type: item?.type,
                      average_exam_marks: item?.average_exam_marks,
                      conduct_marks: item?.conduct_marks,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <MeritDemeritSettingForm {...params} />}
        filterForm={(params) => <FilterMeritDemeritSettingForm {...params} />}
        viewPermit="merit-demerit-setting-view"
        createPermit="merit-demerit-setting-create"
        updatePermit="merit-demerit-setting-update"
        deletePermit="merit-demerit-setting-delete"
    />
);

export default MeritDemeritSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
