import { lowerCase } from "lodash";
import AwardForm from "@/components/forms/student-affairs/AwardForm";
import FilterAwardForm from "@/components/forms/student-affairs/FilterAwardForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, ACTIVE, INACTIVE, awardAPI } from "@/lib/constant";

const Awards = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/special-competition-special-activity/awards"
        api={awardAPI}
        definedColumn={(activeLanguages) => {
            const _nameColumns = activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );

            const _columns: TableColumnType[] = [
                {
                    key: "sequence",
                    displayAs: "Sequence",
                    hasSort: true,
                },
                ..._nameColumns,
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      sequence: item.sequence,
                      is_active: item
                          ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                          : "",
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "sequence"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <AwardForm {...params} />}
        filterForm={(params) => <FilterAwardForm {...params} />}
        viewPermit="master-award-view"
        createPermit="master-award-create"
        updatePermit="master-award-update"
        deletePermit="master-award-delete"
    />
);

export default Awards;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
