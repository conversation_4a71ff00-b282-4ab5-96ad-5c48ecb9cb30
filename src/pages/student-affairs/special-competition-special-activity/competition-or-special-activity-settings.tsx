import { capitalize } from "lodash";
import CompetitionSpecialActivityForm from "@/components/forms/student-affairs/CompetitionSpecialActivityForm";
import FilterSpecialCompetitionForm from "@/components/forms/student-affairs/FilterSpecialCompetitionForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, specialCompetitionAPI } from "@/lib/constant";
import { formatUnderscores } from "@/lib/utils";

const SpecialCompetitionSpecialActivity = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="student-affairs/special-competition-special-activity/competition-or-special-activity-settings"
        api={specialCompetitionAPI}
        otherFilterParams={{
            includes: ["department", "records.student", "records.award"],
        }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "date",
                    hasSort: true,
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "name",
                    hasSort: true,
                },

                {
                    key: "department",
                    hasSort: true,
                },
                {
                    key: "records",
                    modify(list) {
                        return (
                            <ul>
                                {list?.length > 0
                                    ? list.map((record, index) => {
                                          const type = capitalize(
                                              formatUnderscores(
                                                  record?.type_of_bonus
                                              )
                                          );
                                          return (
                                              <li
                                                  key={index}
                                                  className="table-item-li"
                                              >
                                                  <span>{`${
                                                      record?.student
                                                          ?.translations
                                                          ?.name?.[locale]
                                                  } - ${
                                                      record?.award
                                                          ?.translations
                                                          ?.name?.[locale]
                                                  }`}</span>
                                                  <br />{" "}
                                                  <span className="text-[14px] text-gray-500">
                                                      {type}
                                                  </span>
                                                  <span className="whitespace-nowrap text-[14px] text-gray-500">
                                                      {record?.mark
                                                          ? ` ( Mark: ${record.mark} )`
                                                          : ""}
                                                  </span>
                                              </li>
                                          );
                                      })
                                    : "-"}
                            </ul>
                        );
                    },
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      name: item?.name,
                      date: item?.date,
                      department: item?.department?.name,
                      records: item?.records,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { name: { [name]: direction } };
        }}
        form={(params) => <CompetitionSpecialActivityForm {...params} />}
        filterForm={(params) => <FilterSpecialCompetitionForm {...params} />}
        formSize="medium"
        viewPermit="competition-view"
        createPermit="competition-create"
        updatePermit="competition-update"
        deletePermit="competition-delete"
    />
);

export default SpecialCompetitionSpecialActivity;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
