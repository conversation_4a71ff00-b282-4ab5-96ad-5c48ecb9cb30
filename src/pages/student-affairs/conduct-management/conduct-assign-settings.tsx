import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/base-ui/table";
import Card from "@/components/ui/Card";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    gradingSchemeAPI,
    CONDUCT,
    conductSettingAPI,
    conductSettingSemesterClassTeachersAPI,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import { useLocale } from "next-intl";

const ConductAssignSettings = ({ locale }) => {
    useCheckViewPermit("conduct-setting-view");

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
            semester_class_id: "",
            grading_scheme_id: "",
            conduct_setting_teachers: [],
        },
    });

    const [isLoading, setIsLoading] = useState(false);

    const { data: teacherList, axiosQuery: getTeacherList } = useAxios({
        api: `${conductSettingSemesterClassTeachersAPI}/${form.watch("semester_class_id")}`,
        locale,
        onSuccess: () => getConductAssignSettings(),
        onError: () => setIsLoading(false),
    });

    const {
        data: conductGradingSchemes,
        axiosQuery: getConductGradingSchemes,
    } = useAxios({
        api: gradingSchemeAPI,
        locale,
    });

    const {
        data: conductAssignSettings,
        axiosQuery: getConductAssignSettings,
    } = useAxios({
        api: `${conductSettingAPI}/first-by-semester-class-id/${form.watch("semester_class_id")}`,
        locale,
        onSuccess: (res) => {
            if (res.data) {
                const gradingSchemeId = res.data.grading_scheme?.id || "";
                form.setValue("grading_scheme_id", gradingSchemeId);
            }
            setIsLoading(false);
        },
        onError: () => setIsLoading(false),
    });

    const { axiosPost: updateConductAssignSettings, error: postError } =
        useAxios({
            api: conductSettingAPI,
            locale,
            onSuccess: () => {
                getConductAssignSettings();
            },
            toastMsg: "Updated Successfully",
        });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
                const currentSemesterFromDate = new Date(currentSemester.from);
                const semOptions = response.data.filter((option) => {
                    const fromDate = new Date(option.from);
                    return fromDate >= currentSemesterFromDate;
                });

                setSemesterOptions(semOptions);
            } else {
                setSemesterOptions(response.data);
            }
        },
    });

    const { data: semesterClasses, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
    });

    const classes =
        semesterClasses?.map((data) => ({
            id: data.id,
            name:
                data.class_model.translations.name[locale] ??
                data.class_model.name,
        })) || [];

    function onFilter() {
        form.clearErrors();

        setIsLoading(true);
        getTeacherList({
            params: {
                ...GET_ALL_PARAMS,
            },
        });
    }

    const { initLoader } = useSubmit();

    function onSubmit() {
        form.clearErrors();
        initLoader(
            form.handleSubmit((data) => {
                const formattedData: {
                    semester_class_id: number | null;
                    grading_scheme_id: number | null;
                    conduct_setting_teachers: {
                        employee_id: number;
                        is_homeroom_teacher: boolean;
                        is_active: boolean;
                    }[];
                } = {
                    semester_class_id: data.semester_class_id,
                    grading_scheme_id: data.grading_scheme_id,
                    conduct_setting_teachers: [],
                };

                teacherList.forEach((teacher) => {
                    formattedData.conduct_setting_teachers.push({
                        employee_id: teacher.id,
                        is_homeroom_teacher:
                            data[`is_homeroom_teacher_${teacher.id}`] || false,
                        is_active: data[`is_active_${teacher.id}`] || false,
                    });
                });

                updateConductAssignSettings(formattedData);
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    semester_year: "desc",
                },
            },
        });

        getConductGradingSchemes({
            params: { is_active: 1, type: CONDUCT },
        });
    }, []);

    useEffect(() => {
        if (teacherList) {
            teacherList.forEach((teacher) => {
                const teacherId = teacher.id;
                form.setValue(
                    `is_homeroom_teacher_${teacherId}`,
                    teacher.is_homeroom_teacher
                );

                const matchingTeacherSetting =
                    conductAssignSettings?.conduct_setting_teachers.find(
                        (teacherSetting) =>
                            teacherSetting.employee?.id === teacherId
                    );

                if (matchingTeacherSetting) {
                    form.setValue(
                        `is_active_${teacherId}`,
                        matchingTeacherSetting.is_active
                    );
                }
            });
        }
    }, [conductAssignSettings, teacherList]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                    is_active: 1,
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    return (
        <Layout
            locale={locale}
            path="student-affairs/conduct-management/conduct-assign-settings"
        >
            <Card styleClass="table-card">
                <h2 className="mb-6 capitalize">Conduct Assign Settings</h2>

                <Form {...form}>
                    <div className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="semester_setting_id"
                            label="semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="semester_class_id"
                            label="semester class*"
                            options={classes}
                            isDisabled={!form.watch("semester_setting_id")}
                        />

                        <Button
                            variant="outline"
                            onClick={onFilter}
                            disabled={
                                !form.watch("semester_setting_id") ||
                                !form.watch("semester_class_id")
                            }
                        >
                            Filter
                        </Button>
                    </div>

                    {!isLoading &&
                    form.watch("semester_class_id") &&
                    teacherList ? (
                        isEmpty(teacherList) ? (
                            <div className="h-20 text-center text-themeLabel">
                                No Record Found
                            </div>
                        ) : (
                            <>
                                <ConductAssignTeacherTableForm
                                    form={form}
                                    teacherList={teacherList}
                                    conductGradingSchemes={
                                        conductGradingSchemes
                                    }
                                />

                                <Button
                                    size="smallerOnMobile"
                                    className="ml-auto"
                                    onClick={onSubmit}
                                >
                                    Save
                                </Button>
                            </>
                        )
                    ) : null}
                </Form>
            </Card>
        </Layout>
    );
};

const ConductAssignTeacherTableForm = ({
    form,
    teacherList,
    conductGradingSchemes,
}) => {
    const locale = useLocale();

    return (
        <div className="pb-5">
            <div className="mb-5 mt-7 flex items-center justify-between gap-5 border-t border-dashed pt-5">
                <div className="lg: min-w-[500px]">
                    <FormSelect
                        control={form.control}
                        name="grading_scheme_id"
                        label="Conduct Grading Scheme*"
                        options={conductGradingSchemes}
                    />
                </div>
            </div>

            <div className="c-table-wrap">
                <Table className="c-table smaller">
                    <TableHeader>
                        <TableRow className="capitalize">
                            <TableHead>employee number</TableHead>
                            <TableHead>Teacher</TableHead>
                            <TableHead>Subjects</TableHead>
                            <TableHead>
                                <div className="text-center">
                                    is homeroom teacher
                                </div>
                            </TableHead>
                            <TableHead>
                                <div className="whitespace-nowrap">
                                    in charge
                                </div>
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {teacherList.map((teacher, index) => (
                            <TableRow key={index}>
                                <TableCell>
                                    {teacher.employee_number || "-"}
                                </TableCell>

                                <TableCell>{teacher?.name?.[locale]}</TableCell>

                                <TableCell>
                                    {teacher.teaching_subjects.map(
                                        (subject, index) => (
                                            <div
                                                key={index}
                                                className="leading-tight"
                                            >
                                                {subject?.[locale]}
                                                {index !==
                                                    teacher.teaching_subjects
                                                        .length -
                                                        1 && ","}
                                            </div>
                                        )
                                    )}
                                </TableCell>

                                <TableCell>
                                    <div className="text-center capitalize">
                                        {form.watch(
                                            `is_homeroom_teacher_${teacher.id}`
                                        ) ? (
                                            <span className="font-semibold text-green-600">
                                                YES
                                            </span>
                                        ) : (
                                            "NO"
                                        )}
                                    </div>
                                </TableCell>

                                <TableCell>
                                    <div className="mx-auto w-fit">
                                        <FormCheckbox
                                            control={form.control}
                                            name={`is_active_${teacher.id}`}
                                            hasLabel={false}
                                        />
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
};

export default ConductAssignSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
