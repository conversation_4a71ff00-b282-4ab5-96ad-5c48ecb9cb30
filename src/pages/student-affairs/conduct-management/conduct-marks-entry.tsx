import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import {
    conductRecordsAPI,
    conductSettingTeachersAPI,
    GET_ALL_PARAMS,
    semesterSettingAPI,
    conductSettingTeachersSemesterClassAPI,
    studentAPI,
    DRAFT,
    conductDeadlineAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import FormSelect from "@/components/ui/FormSelect";
import { Form } from "@/components/base-ui/form";
import {
    combinedNames,
    getNameColumns,
    optionUserLabel,
    showBackendFormError,
} from "@/lib/utils";
import DataTable from "@/components/ui/DataTable";
import FreeInputInterger from "@/components/ui/FreeInputInterger";
import { Button } from "@/components/base-ui/button";
import { useLanguages } from "@/lib/store";
import { isEmpty } from "lodash";

const ConductMarksEntry = ({ locale }) => {
    useCheckViewPermit("conduct-record-view");

    const form = useForm<any>({
        defaultValues: {
            teacher_id: "",
            grade: "",
            semester_setting_id: "",
            semester_class_id: "",
        },
    });

    const [semesterClassOptions, setSemesterClassOptions] = useState<any>([]);
    const [conductRecords, setConductRecords] = useState<any>(null);
    const [conductTeacherId, setConductTeacherId] = useState<any>(null);
    const [isOutOfPeriod, setIsOutOfPeriod] = useState(false);

    const { data: semesterOptions, axiosQuery: getSemesters } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester?.id);
            }
        },
    });

    const { data: teacherOptions, axiosQuery: getTeachers } = useAxios({
        api: conductSettingTeachersAPI,
        locale,
    });

    const {
        data: teacherSemesterClassOptions,
        axiosQuery: getTeacherSemesterClasses,
    } = useAxios({
        api: conductSettingTeachersSemesterClassAPI,
        locale,
    });

    const { data: students, axiosQuery: getStudents } = useAxios({
        api: studentAPI,
        locale,
    });

    const { axiosQuery: getConductRecord } = useAxios({
        api: conductRecordsAPI,
        locale,
        onSuccess: (result) => {
            setConductRecords(result.data ?? []);
        },
    });

    const { axiosQuery: getConductDeadline } = useAxios({
        api: conductDeadlineAPI,
        onSuccess: (result) => {
            if (!result.data || result.data.length === 0) {
                setIsOutOfPeriod(false);
                return;
            }
            if (!result.data?.[0]?.from || !result.data?.[0]?.to) {
                setIsOutOfPeriod(false);
                return;
            }
            try {
                const today = new Date();
                const start = new Date(result.data?.[0]?.from);
                const end = new Date(result.data?.[0]?.to);

                const clearTime = (date) =>
                    new Date(
                        date.getFullYear(),
                        date.getMonth(),
                        date.getDate()
                    );

                const isInRange =
                    clearTime(today) >= clearTime(start) &&
                    clearTime(today) <= clearTime(end);
                setIsOutOfPeriod(!isInRange);
            } catch (error) {
                console.error("Invalid date input:", error);
                form.setValue("grade", null);
                form.setValue("semester_class_id", null);
                setConductRecords(null);
            }
        },
        onError: () => {
            form.setValue("grade", null);
            form.setValue("semester_class_id", null);
            setConductRecords(null);
        },
    });

    function fetchTeacherSemesterClasses() {
        if (!form.getValues("teacher_id")) return;
        if (!form.getValues("semester_setting_id")) return;

        getTeacherSemesterClasses({
            params: {
                employee_id: form.getValues("teacher_id"),
                semester_setting_id: form.getValues("semester_setting_id"),
            },
        });
    }

    function getSemesterClassOptions(grade) {
        const filteredOptions = teacherSemesterClassOptions?.filter(
            (option) => combinedNames(option.grade) === grade
        );
        const semesterClasses = filteredOptions.flatMap(
            (option) => option.semester_classes
        );
        setSemesterClassOptions(semesterClasses);
    }

    function fetchConductRecord() {
        setConductRecords(null);

        const semesterClassId = form.getValues("semester_class_id");
        if (!semesterClassId) return;

        const teacherId = semesterClassOptions.find(
            (option) => option.semester_class?.id === semesterClassId
        )?.conduct_setting_teacher_id;

        setConductTeacherId(teacherId);

        getConductRecord({
            params: {
                includes: ["student"],
                conduct_setting_teacher_id: teacherId,
            },
        });

        getStudents({
            params: {
                ...GET_ALL_PARAMS,
                includes: ["user"],
                semester_class_id: form.watch("semester_class_id"),
                only_active_class: 1,
                order_by: {
                    name: "asc",
                },
            },
        });
        const selectedSemesterClass = semesterClassOptions.find(
            (item) => item?.semester_class?.id === semesterClassId
        );
        const selectedClassId =
            selectedSemesterClass?.semester_class?.class_model?.id;

        getConductDeadline({
            params: {
                semester_setting_id: form.getValues("semester_setting_id"),
                class_id: selectedClassId,
            },
        });
    }
    useEffect(() => {
        getSemesters({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getTeachers();
    }, []);

    return (
        <Layout
            locale={locale}
            path="student-affairs/conduct-management/conduct-marks-entry"
        >
            <Card styleClass="table-card">
                <h2 className="mb-6 capitalize">Conduct Marks Entry</h2>
                <Form {...form}>
                    <form className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            isClearable={false}
                            onChange={() => {
                                form.setValue("grade", null);
                                form.setValue("semester_class_id", "");
                                setConductRecords(null);
                                fetchTeacherSemesterClasses();
                            }}
                        />

                        <div className="lg:col-span-2">
                            <FormSelect
                                control={form.control}
                                name={"teacher_id"}
                                label="Teacher*"
                                options={
                                    teacherOptions?.map((teacher) => ({
                                        id: teacher?.id,
                                        name: optionUserLabel(
                                            teacher?.employee_number,
                                            teacher?.translations?.name
                                        ),
                                    })) ?? []
                                }
                                onChange={() => {
                                    form.setValue("grade", null);
                                    form.setValue("semester_class_id", null);
                                    setConductRecords(null);
                                    fetchTeacherSemesterClasses();
                                }}
                            />
                        </div>

                        {teacherSemesterClassOptions &&
                            teacherSemesterClassOptions?.length === 0 && (
                                <div className="c-text-size ml-0.5 mt-1 text-gray-500 lg:col-span-3">
                                    No class found for this teacher in the
                                    selected semester
                                </div>
                            )}

                        {teacherSemesterClassOptions?.length > 0 && (
                            <>
                                <FormSelect
                                    control={form.control}
                                    name="grade"
                                    label="Grade*"
                                    isDisabled={
                                        !form.watch("semester_setting_id") ||
                                        !form.watch("teacher_id")
                                    }
                                    isStringOptions={true}
                                    options={teacherSemesterClassOptions?.map(
                                        (option) => combinedNames(option.grade)
                                    )}
                                    isSortByName={false}
                                    onChange={(grade) => {
                                        form.setValue("semester_class_id", "");
                                        getSemesterClassOptions(grade);
                                        setConductRecords(null);
                                    }}
                                />

                                <div className="lg:col-span-2">
                                    <FormSelect
                                        control={form.control}
                                        name={"semester_class_id"}
                                        label="Class*"
                                        isDisabled={!form.watch("grade")}
                                        options={semesterClassOptions.map(
                                            (option) => ({
                                                id: option.semester_class?.id,
                                                name: combinedNames(
                                                    option.semester_class
                                                        ?.class_model
                                                        ?.translations?.name
                                                ),
                                            })
                                        )}
                                        onChange={fetchConductRecord}
                                    />
                                </div>
                            </>
                        )}
                    </form>
                </Form>

                {conductRecords && students && (
                    <ConductRecordResult
                        teacherId={conductTeacherId}
                        students={students}
                        records={conductRecords}
                        refresh={fetchConductRecord}
                        isOutOfPeriod={isOutOfPeriod}
                    />
                )}
            </Card>
        </Layout>
    );
};

const ConductRecordResult = ({
    teacherId,
    students,
    records,
    refresh,
    isOutOfPeriod,
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const recordForm = useForm<any>({
        defaultValues: {
            conduct_records: [],
        },
    });

    function formatRecord(records) {
        return students?.map((student) => {
            const studentRecord = records?.find(
                (record) => record?.student?.id == student?.id
            );

            return {
                student_number: student?.student_number,
                ...(student?.translations?.name ?? {}),
                student_id: student?.id,
                status: isEmpty(studentRecord?.status)
                    ? DRAFT
                    : studentRecord?.status,
                marks: studentRecord?.marks ?? "",
            };
        });
    }

    function getColumns() {
        return [
            {
                key: "student_number",
            },
            ...getNameColumns(activeLanguages, false),
            {
                key: "marks",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    return (
                        <div className="h-full min-w-[150px]">
                            <FreeInputInterger
                                hasLabel={false}
                                control={recordForm.control}
                                error={
                                    recordForm.formState.errors
                                        ?.conduct_records?.[index]?.marks
                                }
                                disabled={
                                    isOutOfPeriod ||
                                    recordForm.getValues("conduct_records")?.[
                                        index
                                    ]?.status !== DRAFT
                                }
                                name={`conduct_records[${index}].marks`}
                                onChange={(val) => {
                                    val = Number(val);

                                    if (val.toString().includes(".")) {
                                        val = Math.round(val);
                                    }

                                    if (val > 100) {
                                        val = 100;
                                    }

                                    recordForm.setValue(
                                        `conduct_records[${index}].marks`,
                                        val
                                    );
                                }}
                            />
                        </div>
                    );
                },
            },
        ];
    }

    const { axiosPost: updateConductRecord, error: postError } = useAxios({
        api: `${conductRecordsAPI}/${teacherId}`,
        toastMsg: "Updated successfully",
        onSuccess: refresh,
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        recordForm.clearErrors();
        initLoader(
            recordForm.handleSubmit((data: any) => {
                console.log(data);
                data.conduct_records = data.conduct_records?.map((record) => ({
                    student_id: record.student_id,
                    marks: record.marks,
                }));
                updateConductRecord(data);
            })
        );
    }

    useEffect(() => {
        if (records && students) {
            recordForm.reset({
                conduct_records: formatRecord(records),
            });
        }
    }, [records, students]);

    useEffect(() => {
        showBackendFormError(recordForm, postError);
    }, [postError]);

    return (
        <Form {...recordForm}>
            <form onSubmit={onSubmit} className="pt-2 lg:col-span-2">
                {isOutOfPeriod && (
                    <div className="bg-yellow-100 px-3 py-2 text-[14px] text-gray-700">
                        Conduct marks entry is out of period
                    </div>
                )}
                <DataTable
                    columns={getColumns()}
                    data={recordForm.watch("conduct_records")}
                />
                <div className="mb-10 mt-2">
                    <div className="text-[14px] text-gray-500">
                        <span className="font-medium">
                            {recordForm.watch("conduct_records")?.length}
                        </span>{" "}
                        students found
                    </div>
                </div>
                {!isOutOfPeriod && (
                    <Button type="submit" className="ml-auto mt-5">
                        Save
                    </Button>
                )}
            </form>
        </Form>
    );
};

export default ConductMarksEntry;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
