import Layout from "@/components/Layout";
import ConductDeadlineForm from "@/components/forms/student-affairs/ConductDeadlineForm";
import Card from "@/components/ui/Card";
import { useCheckViewPermit } from "@/lib/hook";

const ConductDeadline = ({ locale }) => {
    useCheckViewPermit("conduct-deadline-view");

    return (
        <Layout
            path="student-affairs/conduct-management/conduct-deadline"
            locale={locale}
        >
            <Card styleClass="table-card">
                <ConductDeadlineForm />
            </Card>
        </Layout>
    );
};

export default ConductDeadline;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
