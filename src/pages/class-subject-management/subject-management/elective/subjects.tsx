import { capitalize } from "lodash";
import FilterSubjectForm from "@/components/forms/class-subject-management/subject-management/FilterSubjectForm";
import SubjectForm from "@/components/forms/class-subject-management/subject-management/SubjectForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { ELECTIVE, TableColumnType, subjectAPI } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Subjects = ({ locale }) => {
    const t = useTranslations("common");

    return (
        <CommonTablePageWrap
            locale={locale}
            heading={`${t("Subjects")} (${t("Elective")})`}
            path={
                "class-subject-management/subject-management/elective/subjects"
            }
            api={subjectAPI}
            otherFilterParams={{ types: [ELECTIVE] }}
            definedColumn={(activeLanguages) => {
                const _nameColumns = activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `Name ( ${lang?.name} )`,
                        hasSort: true,
                    })
                );
                const _columns: TableColumnType[] = [
                    {
                        key: "code",
                        hasSort: true,
                    },
                    ..._nameColumns,
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          type: capitalize(replaceAll(item?.type, "_", " ")),
                          club: item?.club?.name ?? "-",
                          code: item?.code,
                          ...item?.translations?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return ["code", "type", "club"].includes(name)
                    ? { [name]: direction }
                    : { name: { [name]: direction } };
            }}
            form={(params) => <SubjectForm {...params} type={ELECTIVE} />}
            filterForm={(params) => (
                <FilterSubjectForm {...params} type={ELECTIVE} />
            )}
            viewPermit="subject-view"
            createPermit="subject-create"
            updatePermit="subject-update"
            deletePermit="subject-delete"
        />
    );
};

export default Subjects;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
