import { capitalize } from "lodash";
import FilterSubjectCategoryForm from "@/components/forms/class-subject-management/subject-management/FilterSubjectCategoryForm";
import SubjectCategoryForm from "@/components/forms/class-subject-management/subject-management/SubjectCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { employeeJobTitlesAPI, TableColumnType } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";

const SubjectCategories = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path={"class-subject-management/subject-management/subject-categories"}
        api={employeeJobTitlesAPI} // TODO: chg to subjectCategory api
        definedColumn={(activeLanguages) => {
            const _nameColumns = activeLanguages.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Name ( ${lang?.name} )`,
                    hasSort: true,
                })
            );
            const _columns: TableColumnType[] = [..._nameColumns];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "type"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <SubjectCategoryForm {...params} />}
        filterForm={(params) => <FilterSubjectCategoryForm {...params} />}
        viewPermit={"subject-categories-view"} // TODO: change to correct value
    />
);

export default SubjectCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
