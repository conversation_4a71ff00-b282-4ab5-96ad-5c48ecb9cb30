import React from "react";
import Layout from "@/components/Layout";
import { ELECTIVE } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";
import { useLocale } from "next-intl";
import ClassSubjectsPage from "@/components/ui/class-management/ClassSubjectPage";

const ClassSubjects = () => {
    const locale = useLocale();
    useCheckViewPermit("elective-class-subject-view");

    return (
        <Layout
            locale={locale}
            path={
                "class-subject-management/class-management/elective/class-subjects"
            }
        >
            <ClassSubjectsPage classType={ELECTIVE} />
        </Layout>
    );
};

export default ClassSubjects;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
