import ClassForm from "@/components/forms/class-subject-management/class-management/ClassForm";
import FilterClassForm from "@/components/forms/class-subject-management/class-management/FilterClassForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { ELECTIVE, TableColumnType, classesAPI } from "@/lib/constant";
import { getStreamName, strStartCase } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Classes = ({ locale }) => {
    const t = useTranslations("common");
    return (
        <CommonTablePageWrap
            locale={locale}
            heading={`${t("classes")} (${t("Elective")})`}
            path={"class-subject-management/class-management/elective/classes"}
            api={classesAPI}
            otherFilterParams={{ type: ELECTIVE }}
            definedColumn={(activeLanguages) => {
                const _nameColumns = activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `${t("class")} ( ${t(lang?.name)} )`,
                        hasSort: true,
                    })
                );
                const _columns: TableColumnType[] = [
                    { key: "code" },
                    ..._nameColumns,
                    { key: "stream" },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          code: item?.code ?? "-",
                          stream: t(getStreamName(item?.stream)),
                          ...item?.translations?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return ["type"].includes(name)
                    ? { [name]: direction }
                    : { name: { [name]: direction } };
            }}
            form={(params) => <ClassForm {...params} classType={ELECTIVE} />}
            filterForm={(params) => (
                <FilterClassForm {...params} type={ELECTIVE} />
            )}
            formSize="medium"
            viewPermit="elective-class-view"
            createPermit="elective-class-create"
            updatePermit="elective-class-update"
            deletePermit="elective-class-delete"
        />
    );
};

export default Classes;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
