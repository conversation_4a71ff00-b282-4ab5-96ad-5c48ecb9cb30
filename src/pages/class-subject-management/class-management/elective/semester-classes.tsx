import Layout from "@/components/Layout";
import { ELECTIVE } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";
import SemesterClassesPage from "@/components/ui/class-management/SemesterClassPage";

const SemesterClasses = ({ locale }) => {
    useCheckViewPermit("elective-semester-class-view");

    return (
        <Layout
            locale={locale}
            path="class-subject-management/class-management/elective/semester-classes"
        >
            <SemesterClassesPage classType={ELECTIVE} />
        </Layout>
    );
};

export default SemesterClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
