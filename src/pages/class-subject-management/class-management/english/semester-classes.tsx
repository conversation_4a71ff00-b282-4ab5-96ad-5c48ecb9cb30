import React from "react";
import Layout from "@/components/Layout";
import { useCheckViewPermit } from "@/lib/hook";
import SemesterClassesPage from "@/components/ui/class-management/SemesterClassPage";
import { ENGLISH } from "@/lib/constant";

const SemesterClasses = ({ locale }) => {
    useCheckViewPermit("semester-class-view");

    return (
        <Layout
            locale={locale}
            path="class-subject-management/class-management/english/semester-classes"
        >
            <SemesterClassesPage classType={ENGLISH} />
        </Layout>
    );
};

export default SemesterClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
