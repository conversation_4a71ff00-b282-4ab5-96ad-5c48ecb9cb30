import ClassForm from "@/components/forms/class-subject-management/class-management/ClassForm";
import FilterClassForm from "@/components/forms/class-subject-management/class-management/FilterClassForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { ENGLISH, TableColumnType, classesAPI } from "@/lib/constant";
import { getStreamName, strStartCase } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Classes = ({ locale }) => {
    const t = useTranslations("common");
    return (
        <CommonTablePageWrap
            locale={locale}
            heading={`${t("classes")} (${t("English")})`}
            path={"class-subject-management/class-management/english/classes"}
            api={classesAPI}
            otherFilterParams={{ type: ENGLISH }}
            definedColumn={(activeLanguages) => {
                const _nameColumns = activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `${t("class")} ( ${t(lang?.name)} )`,
                        hasSort: true,
                    })
                );
                const _columns: TableColumnType[] = [
                    { key: "code" },
                    ..._nameColumns,
                    { key: "stream" },
                    { key: "english_level" },
                    { key: "grade" },
                ];
                return _columns;
            }}
            definedData={(data) => {
                function getEnglishLevelName(value) {
                    if (!value) return "-";
                    if (value === "PRE_INTERMEDIATE") return "Pre-Intermediate";
                    return strStartCase(t(value.replaceAll("_", " ")));
                }
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          code: item?.code ?? "-",
                          stream: t(getStreamName(item?.stream)),
                          english_level: getEnglishLevelName(
                              item?.english_level
                          ),
                          grade: item?.grade?.name ?? "-",
                          ...item?.translations?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return ["type"].includes(name)
                    ? { [name]: direction }
                    : { name: { [name]: direction } };
            }}
            form={(params) => <ClassForm {...params} classType={ENGLISH} />}
            filterForm={(params) => (
                <FilterClassForm {...params} type={ENGLISH} />
            )}
            formSize="medium"
            viewPermit="english-class-view"
            createPermit="english-class-create"
            updatePermit="english-class-update"
            deletePermit="english-class-delete"
        />
    );
};

export default Classes;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
