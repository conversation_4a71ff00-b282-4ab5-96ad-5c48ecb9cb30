import Layout from "@/components/Layout";
import { PRIMARY } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";
import SemesterClassesPage from "@/components/ui/class-management/SemesterClassPage";

const SemesterClasses = ({ locale }) => {
    useCheckViewPermit("semester-class-view");

    return (
        <Layout
            locale={locale}
            path="class-subject-management/class-management/primary/semester-classes"
        >
            <SemesterClassesPage classType={PRIMARY} />
        </Layout>
    );
};

export default SemesterClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
