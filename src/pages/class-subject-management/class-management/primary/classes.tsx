import ClassForm from "@/components/forms/class-subject-management/class-management/ClassForm";
import FilterClassForm from "@/components/forms/class-subject-management/class-management/FilterClassForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { PRIMARY, TableColumnType, classesAPI } from "@/lib/constant";
import { getStreamName, strStartCase } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Classes = ({ locale }) => {
    const t = useTranslations("common");
    return (
        <CommonTablePageWrap
            locale={locale}
            heading={`${t("classes")} (${t("primary_class_type")})`}
            path={"class-subject-management/class-management/primary/classes"}
            api={classesAPI}
            otherFilterParams={{ type: PRIMARY }}
            definedColumn={(activeLanguages) => {
                const _nameColumns = activeLanguages.map(
                    (lang): TableColumnType => ({
                        key: lang.code,
                        displayAs: `${t("class")} ( ${t(lang?.name)} )`,
                        hasSort: true,
                    })
                );
                const _columns: TableColumnType[] = [
                    { key: "code" },
                    ..._nameColumns,
                    { key: "stream" },
                    { key: "grade" },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          code: item?.code ?? "-",
                          stream: t(getStreamName(item?.stream)),
                          grade: item?.grade?.name ?? "-",
                          ...item?.translations?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return ["type"].includes(name)
                    ? { [name]: direction }
                    : { name: { [name]: direction } };
            }}
            form={(params) => <ClassForm {...params} classType={PRIMARY} />}
            filterForm={(params) => (
                <FilterClassForm {...params} type={PRIMARY} />
            )}
            formSize="medium"
            viewPermit="primary-class-view"
            createPermit="primary-class-create"
            updatePermit="primary-class-update"
            deletePermit="primary-class-delete"
        />
    );
};

export default Classes;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
