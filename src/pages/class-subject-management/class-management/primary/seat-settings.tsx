import { useCheckViewPermit } from "@/lib/hook";
import Layout from "@/components/Layout";
import SeatSettingsEditor from "@/components/ui/SeatSettingsEditor";

const SeatSettings = ({ locale }) => {
    useCheckViewPermit("class-seat-assignment-view");

    return (
        <Layout
            locale={locale}
            path="class-subject-management/class-management/primary/seat-settings"
        >
            <SeatSettingsEditor />
        </Layout>
    );
};

export default SeatSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
