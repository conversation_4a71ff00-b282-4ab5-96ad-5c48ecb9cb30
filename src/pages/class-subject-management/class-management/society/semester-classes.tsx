import Layout from "@/components/Layout";
import { SOCIETY } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";
import SemesterClassesPage from "@/components/ui/class-management/SemesterClassPage";

const SemesterClasses = ({ locale }) => {
    useCheckViewPermit("society-semester-class-view");

    return (
        <Layout
            locale={locale}
            path="class-subject-management/class-management/society/semester-classes"
        >
            <SemesterClassesPage classType={SOCIETY} />
        </Layout>
    );
};

export default SemesterClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
