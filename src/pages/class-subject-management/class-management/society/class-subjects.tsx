import React from "react";
import Layout from "@/components/Layout";
import { SOCIETY } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";
import { useLocale } from "next-intl";
import ClassSubjectsPage from "@/components/ui/class-management/ClassSubjectPage";

const ClassSubjects = () => {
    const locale = useLocale();
    useCheckViewPermit("society-class-subject-view");

    return (
        <Layout
            locale={locale}
            path={
                "class-subject-management/class-management/society/class-subjects"
            }
        >
            <ClassSubjectsPage classType={SOCIETY} />
        </Layout>
    );
};

export default ClassSubjects;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
