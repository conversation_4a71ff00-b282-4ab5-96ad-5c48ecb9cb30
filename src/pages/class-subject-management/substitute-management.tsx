import { useEffect, useState } from "react";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    displayDateTime,
    formatNumberForRead,
    refreshForUpdate,
} from "@/lib/utils";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterSubstituteManagementForm from "@/components/forms/class-subject-management/substitute-management/FilterSubstituteManagementForm";
import SubstituteManagementForm from "@/components/forms/class-subject-management/substitute-management/SubstituteManagementForm";
import Layout from "@/components/Layout";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
    appCurrencySymbol,
    substituteRecordAPI,
} from "@/lib/constant";
import SubstituteSingleRecordForm from "@/components/forms/class-subject-management/substitute-management/SubstituteSingleRecordForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const SubstituteManagement = ({ locale }) => {
    useCheckViewPermit("substitute-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);
    const [openCreate, setOpenCreate] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getSubstituteRecords } = useAxios({
        api: substituteRecordAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "date",
                modify(value) {
                    return (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    );
                },
            },
            {
                key: "requestor_name",
                displayAs: "Requestor",
                hasSort: true,
            },
            {
                key: "substitute_teacher_name",
                displayAs: "Substitute Teacher",
                hasSort: true,
            },
            {
                key: "class_name",
                displayAs: "class",
                hasSort: true,
            },
            {
                key: "subject_name",
                displayAs: "subject",
                hasSort: true,
            },
            {
                key: "period",
                modify: (value) => (
                    <span className="whitespace-nowrap text-[14px]">
                        {value}
                    </span>
                ),
            },
            {
                key: "allowance",
                displayAs: `${t("allowance")} (${appCurrencySymbol})`,
                hasSort: true,
                modify: (value) => (
                    <span className="whitespace-nowrap text-[14px]">
                        {value}
                    </span>
                ),
            },
        ];
        return _columns;
    }

    function definedData(data) {
        return Array.isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  date: displayDateTime(item?.substitute_date, DATE_FORMAT.DMY),
                  requestor_name: combinedNames(
                      item?.requestor?.translations?.name
                  ),
                  substitute_teacher_name: combinedNames(
                      item?.substitute_teacher?.translations?.name
                  ),
                  class_name: item?.class_name?.[locale],
                  subject_name: item?.subject_name?.[locale],
                  period: `${item?.period_label} (${item?.from_time.slice(0, 5)} - ${item?.to_time.slice(0, 5)})`,
                  allowance: formatNumberForRead(item?.allowance),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    function fetchSubstituteRecords() {
        getSubstituteRecords({
            params: {
                includes: ["substituteTeacher", "requestor"],
                order_by: {
                    created_at: "desc",
                },
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchSubstituteRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    const t = useTranslations("common");

    return (
        <>
            <Layout
                locale={locale}
                path="class-subject-management/substitute-management"
            >
                <Card styleClass="table-card">
                    <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                        <h2>{t("Substitute Management")}</h2>
                        <div className="flex items-center gap-x-3">
                            {hasPermit(
                                "substitute-record-bulk-create-update"
                            ) && (
                                <Button onClick={() => setOpenCreate(true)}>
                                    {t("Bulk Assign Substitute Teachers")}
                                </Button>
                            )}
                            {/* <TableFilterBtn
                                filter={filter}
                                onClick={() => setOpenFilter(true)}
                            /> */}
                            <FilterChevronButton />
                        </div>
                    </div>

                    <FilterFormWrapper>
                        <FilterSubstituteManagementForm
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                        />
                    </FilterFormWrapper>

                    <DataTable
                        columns={defineColumn()}
                        data={definedData(data)}
                        pagination={pagination}
                        setPagination={setPagination}
                        changePage={(arg) => setFilter({ ...filter, ...arg })}
                        sorted={filter?.order_by}
                        sort={onSort}
                        actionMenu={({ cell }) => (
                            <ActionDropdown>
                                {/* {hasPermit("substitute-record-update") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        Edit / View
                                    </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator /> */}
                                {hasPermit("substitute-record-delete") && (
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        {t("Delete")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        )}
                    />
                </Card>

                {/* create */}
                <Modal
                    open={openCreate}
                    onOpenChange={setOpenCreate}
                    size="large"
                >
                    <SubstituteManagementForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId} size="large">
                    <SubstituteSingleRecordForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={substituteRecordAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={fetchSubstituteRecords}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterSubstituteManagementForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </Layout>
        </>
    );
};

export default SubstituteManagement;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
