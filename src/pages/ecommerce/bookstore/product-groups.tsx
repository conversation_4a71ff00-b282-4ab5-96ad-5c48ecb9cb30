import { isArray } from "lodash";
import FilterProductGroupForm from "@/components/forms/ecommerce/FilterProductGroupForm";
import ProductGroupForm from "@/components/forms/ecommerce/ProductGroupForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { BOOKSHOP, productGroupAPI, TableColumnType } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const BookStoreProductGroups = ({ locale }) => {
    useCheckViewPermit("bookstore-view");

    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/bookstore/product-groups"}
            heading="Bookstore Product Groups"
            api={productGroupAPI}
            otherFilterParams={{ type: BOOKSHOP, includes: ["products"] }}
            useTableDataForForm={true}
            definedColumn={(): TableColumnType[] => [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "products_count",
                },
            ]}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name,
                          products_count: item?.products?.length,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => <ProductGroupForm {...params} type={BOOKSHOP} />}
            filterForm={(params) => <FilterProductGroupForm {...params} />}
            viewPermit="product-group-view"
            createPermit="product-group-create"
            updatePermit="product-group-update"
            deletePermit="product-group-delete"
        />
    );
};

export default BookStoreProductGroups;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
