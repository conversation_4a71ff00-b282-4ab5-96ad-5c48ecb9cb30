import { useEffect } from "react";
import React from "react";
import Layout from "@/components/Layout";
import Card from "@/components/ui/Card";
import {
    BOOKSHOP,
    GET_ALL_PARAMS,
    productGroupAPI,
    productTagAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import ProductPage from "@/components/forms/ecommerce/ProductPage";

const BookStoreProducts = ({ locale }) => {
    useCheckViewPermit("bookstore-view");

    const { data: tagOptions, axiosQuery: getTagOptions } = useAxios({
        api: productTagAPI,
        locale,
    });

    const { data: groupOptions, axiosQuery: getGroupOptions } = useAxios({
        api: productGroupAPI,
        locale,
    });

    useEffect(() => {
        getTagOptions({
            params: {
                ...GET_ALL_PARAMS,
                type: BOOKSHOP,
                order_by: {
                    name: "asc",
                },
            },
        });
        getGroupOptions({
            params: {
                ...GET_ALL_PARAMS,
                type: BOOKSHOP,
                order_by: {
                    name: "asc",
                },
            },
        });
    }, []);

    return (
        <Layout locale={locale} path="ecommerce/bookstore/products">
            <Card styleClass="table-card">
                {groupOptions && tagOptions && (
                    <ProductPage
                        type={BOOKSHOP}
                        groupOptions={groupOptions}
                        tagOptions={tagOptions}
                    />
                )}
            </Card>
        </Layout>
    );
};

export default BookStoreProducts;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
