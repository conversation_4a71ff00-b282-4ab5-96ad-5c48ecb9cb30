import { isArray } from "lodash";
import FilterProductTagForm from "@/components/forms/ecommerce/FilterProductTagsForm";
import ProductTagForm from "@/components/forms/ecommerce/ProductTagForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { BOOKSHOP, productTagAPI, TableColumnType } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const BookStoreProductTags = ({ locale }) => {
    {
        useCheckViewPermit("bookstore-view");

        return (
            <CommonTablePageWrap
                locale={locale}
                path={"ecommerce/bookstore/product-tags"}
                heading="Bookstore Product Tags"
                api={productTagAPI}
                otherFilterParams={{
                    type: BOOKSHOP,
                    includes_count: ["products", "targets"],
                }}
                useTableDataForForm={true}
                definedColumn={(): TableColumnType[] => [
                    {
                        key: "name",
                        hasSort: true,
                    },
                    {
                        key: "products_count",
                    },
                    {
                        key: "users_count",
                    },
                ]}
                definedData={(data) => {
                    return isArray(data)
                        ? data.map((item) => ({
                              id: item?.id,
                              name: item?.name,
                              products_count: item?.products_count,
                              users_count: item?.targets_count,
                          }))
                        : [];
                }}
                orderBy={(name, direction) => {
                    return { [name]: direction };
                }}
                form={(params) => (
                    <ProductTagForm {...params} type={BOOKSHOP} />
                )}
                filterForm={(params) => <FilterProductTagForm {...params} />}
                formSize="medium"
                viewPermit="product-tag-view"
                createPermit="product-tag-create"
                updatePermit="product-tag-update"
                deletePermit="product-tag-delete"
            />
        );
    }
};

export default BookStoreProductTags;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
