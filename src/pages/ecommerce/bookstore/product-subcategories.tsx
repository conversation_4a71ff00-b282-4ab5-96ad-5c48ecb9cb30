import { isArray } from "lodash";
import FilterProductSubCategoryForm from "@/components/forms/ecommerce/FilterProductSubCategory";
import ProductSubcategoryForm from "@/components/forms/ecommerce/ProductSubcategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    BOOKSHOP,
    productSubCategoryAPI,
    TableColumnType,
} from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const BookStoreProductSubCategories = ({ locale }) => {
    useCheckViewPermit("bookstore-view");

    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/bookstore/product-subcategories"}
            heading="Bookstore Product Sub Categories"
            api={productSubCategoryAPI}
            otherFilterParams={{ type: BOOKSHOP }}
            definedColumn={(): TableColumnType[] => [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "category",
                },
                {
                    key: "sequence",
                    hasSort: true,
                },
            ]}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name,
                          category: item?.category?.name,
                          sequence: item?.sequence,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => (
                <ProductSubcategoryForm {...params} type={BOOKSHOP} />
            )}
            filterForm={(params) => (
                <FilterProductSubCategoryForm {...params} type={BOOKSHOP} />
            )}
            viewPermit="product-category-admin-view"
            createPermit="product-category-create"
            updatePermit="product-category-update"
            deletePermit="product-category-delete"
        />
    );
};

export default BookStoreProductSubCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
