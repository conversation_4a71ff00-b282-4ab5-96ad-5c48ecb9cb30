import Layout from "@/components/Layout";
import AssignProducts from "@/components/ui/AssignProducts";
import { BOOKSHOP, nav } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const AssignProductsForBookStore = ({ locale }) => {
    useCheckViewPermit("product-update");
    useCheckViewPermit("bookstore-view");

    return (
        <Layout locale={locale} path={"ecommerce/bookstore/assign-products"}>
            <AssignProducts
                merchantType={BOOKSHOP}
                api={"/products/available-date/bulk-update"}
            />
        </Layout>
    );
};

export default AssignProductsForBookStore;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
