import { isArray } from "lodash";
import FilterProductCategoryForm from "@/components/forms/ecommerce/FilterProductCategory";
import ProductCategoryForm from "@/components/forms/ecommerce/ProductCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { BOOKSHOP, productCategoryAPI, TableColumnType } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const BookStoreProductCategories = ({ locale }) => {
    useCheckViewPermit("bookstore-view");

    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/bookstore/product-categories"}
            heading="Bookstore Product Categories"
            api={productCategoryAPI}
            otherFilterParams={{ type: BOOKSHOP }}
            definedColumn={(): TableColumnType[] => [
                {
                    key: "name",
                    hasSort: true,
                },
            ]}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => (
                <ProductCategoryForm {...params} type={BOOKSHOP} />
            )}
            filterForm={(params) => <FilterProductCategoryForm {...params} />}
            viewPermit="product-category-admin-view"
            createPermit="product-category-create"
            updatePermit="product-category-update"
            deletePermit="product-category-delete"
        />
    );
};

export default BookStoreProductCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
