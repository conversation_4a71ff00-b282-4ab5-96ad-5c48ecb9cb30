import { isArray } from "lodash";
import FilterProductCategoryForm from "@/components/forms/ecommerce/FilterProductCategory";
import ProductCategoryForm from "@/components/forms/ecommerce/ProductCategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { CANTEEN, productCategoryAPI, TableColumnType } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const CanteenProductCategories = ({ locale }) => {
    useCheckViewPermit("canteen-view");
    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/canteen/product-categories"}
            heading="Canteen Product Categories"
            api={productCategoryAPI}
            otherFilterParams={{ type: CANTEEN }}
            definedColumn={(): TableColumnType[] => [
                {
                    key: "name",
                    hasSort: true,
                },
            ]}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => (
                <ProductCategoryForm {...params} type={CANTEEN} />
            )}
            filterForm={(params) => <FilterProductCategoryForm {...params} />}
            viewPermit="product-category-admin-view"
            createPermit="product-category-create"
            updatePermit="product-category-update"
            deletePermit="product-category-delete"
        />
    );
};

export default CanteenProductCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
