import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { capitalize, isArray } from "lodash";
import Layout from "@/components/Layout";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import FilterOrderForm from "@/components/forms/ecommerce/FilterOrderForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import InfoCard from "@/components/ui/InfoCard";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    appCurrencySymbol,
    CANTEEN,
    DATE_FORMAT,
    orderAPI,
    STUDENT,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    displayDateTime,
    formatDate,
    getTypeFromUserModel,
    getUserableType,
    optionUserLabel,
} from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const CanteenOrders = ({ locale }) => {
    useCheckViewPermit("canteen-view");
    useCheckViewPermit("order-admin-view");

    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
    });
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);
    const [targetId, setTargetId] = useState(null);

    const { data, axiosQuery: getOrders } = useAxios({
        api: orderAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchOrders() {
        getOrders({
            params: {
                merchant_type: CANTEEN,
                includes: [
                    "items.billingDocumentLineItem.billingDocument.payments.paymentMethod",
                ],
                order_by: {
                    created_at: "desc",
                },
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchOrders();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "created_at",
                displayAs: "Purchase Date",
                hasSort: true,
                modify: (value) => (
                    <span className="whitespace-nowrap text-[14px]">
                        {value}
                    </span>
                ),
            },
            {
                key: "order_reference_number",
                hasSort: true,
            },
            {
                key: "buyer",
                modify: (value) => (
                    <div className="min-w-[240px] text-[14px]">{value}</div>
                ),
            },
            {
                key: "status",
                hasSort: true,
                modify: (value) => (
                    <span
                        className={clsx(
                            value === "COMPLETED" && "text-green-600",
                            value === "PROCESSING" && "text-yellow-500",
                            value === "CANCELED" && "text-red-500"
                        )}
                    >
                        {capitalize(value)}
                    </span>
                ),
            },
            {
                key: "payment_status",
                hasSort: true,
                modify: (value) => capitalize(value),
            },
            {
                key: "amount",
                displayAs: `Total Price (${appCurrencySymbol})`,
                modify: (value) => <div className="min-w-[80px]">{value}</div>,
            },
            {
                key: "invoice",
                modify: (item) => (
                    <a
                        className="whitespace-nowrap border-b border-gray-700 text-[14px]"
                        href={item?.url}
                        target="_blank"
                    >
                        {item?.name}
                    </a>
                ),
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  invoice: {
                      name: item?.billing_document?.reference_number ?? "-",
                      url: item?.billing_document?.receipt_url,
                  },
                  order_reference_number: item?.order_reference_number,
                  status: item?.status,
                  payment_status: item?.payment_status,
                  amount: item?.amount_after_tax,
                  created_at: displayDateTime(
                      item.created_at,
                      DATE_FORMAT.forDisplay
                  ),
                  buyer:
                      optionUserLabel(
                          item?.buyer_userable_type === getUserableType(STUDENT)
                              ? item?.buyer?.student_number
                              : item?.buyer?.employee_number,
                          item?.buyer?.translations?.name
                      ) +
                      ` (${getTypeFromUserModel(item?.buyer_userable_type)})`,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function formatDisplayData(data) {
        return {
            order_reference_number: data?.order_reference_number,
            buyer:
                optionUserLabel(
                    data?.buyer_userable_type === getUserableType(STUDENT)
                        ? data?.buyer?.student_number
                        : data?.buyer?.employee_number,
                    data?.buyer?.translations?.name
                ) + ` (${getTypeFromUserModel(data?.buyer_userable_type)})`,
            status: data?.status,
            payment_status: data?.payment_status,
            total_price: appCurrencySymbol + " " + data?.amount_before_tax,
            purchase_date: displayDateTime(
                data?.created_at,
                DATE_FORMAT.forDisplay
            ),
        };
    }

    return (
        <Layout locale={locale} path="ecommerce/canteen/orders">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>Canteen Orders</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                        excludeFields={["includes"]}
                    /> */}

                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterOrderForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit("hostel-in-out-record-update") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setTargetId(cell.row.original.id)
                                    }
                                >
                                    View
                                </DropdownMenuItem>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* view */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <div className="min-w-[600px]">
                    <h3 className="mb-3">Order Details</h3>
                    <InfoCard
                        noBorder
                        data={formatDisplayData(
                            data?.find((item) => item.id == targetId)
                        )}
                    />
                    <div className="mb-2 mt-6 text-[14px] font-medium text-themeLabel">
                        Ordered Items
                    </div>
                    <DataTable
                        columns={[
                            {
                                key: "product_name",
                            },
                            {
                                key: "product_unit_price",
                                displayAs: `Unit Price (${appCurrencySymbol})`,
                            },
                            {
                                key: "quantity",
                            },
                            {
                                key: "amount_before_tax",
                                displayAs: `Total Price (${appCurrencySymbol})`,
                                modify: (value) => value ?? "-",
                            },
                            {
                                key: "product_delivery_date",
                                displayAs: "Delivery Date",
                                modify: (value) => (
                                    <span className="text-[14px]">
                                        {formatDate(value, DATE_FORMAT.DMY)}
                                    </span>
                                ),
                            },
                        ]}
                        data={data?.find((item) => item.id == targetId)?.items}
                    />
                </div>
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterOrderForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default CanteenOrders;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
