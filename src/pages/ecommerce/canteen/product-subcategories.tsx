import { isArray } from "lodash";
import FilterProductSubCategoryForm from "@/components/forms/ecommerce/FilterProductSubCategory";
import ProductSubcategoryForm from "@/components/forms/ecommerce/ProductSubcategoryForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    CANTEEN,
    nav,
    productSubCategoryAPI,
    TableColumnType,
} from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const CanteenProductSubCategories = ({ locale }) => {
    useCheckViewPermit("canteen-view");

    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/canteen/product-subcategories"}
            heading="Canteen Product Sub Categories"
            api={productSubCategoryAPI}
            otherFilterParams={{ type: CANTEEN }}
            definedColumn={(): TableColumnType[] => [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "category",
                },
                {
                    key: "sequence",
                    hasSort: true,
                },
            ]}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name,
                          category: item?.category?.name,
                          sequence: item?.sequence,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => (
                <ProductSubcategoryForm {...params} type={CANTEEN} />
            )}
            filterForm={(params) => (
                <FilterProductSubCategoryForm {...params} type={CANTEEN} />
            )}
            viewPermit="product-category-admin-view"
            createPermit="product-category-create"
            updatePermit="product-category-update"
            deletePermit="product-category-delete"
        />
    );
};

export default CanteenProductSubCategories;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
