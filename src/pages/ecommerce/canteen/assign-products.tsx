import Layout from "@/components/Layout";
import AssignProducts from "@/components/ui/AssignProducts";
import { CANTEEN, nav } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const AssignProductsForCanteen = ({ locale }) => {
    useCheckViewPermit("canteen-view");
    useCheckViewPermit("product-update");

    return (
        <Layout locale={locale} path={"ecommerce/canteen/assign-products"}>
            <AssignProducts
                merchantType={CANTEEN}
                api="/products/delivery-date/bulk-update"
            />
        </Layout>
    );
};

export default AssignProductsForCanteen;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
