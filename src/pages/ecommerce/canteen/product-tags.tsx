import { isArray } from "lodash";
import FilterProductTagForm from "@/components/forms/ecommerce/FilterProductTagsForm";
import ProductTagForm from "@/components/forms/ecommerce/ProductTagForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { CANTEEN, productTagAPI, TableColumnType } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const CanteenProductTags = ({ locale }) => {
    useCheckViewPermit("canteen-view");
    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/canteen/product-tags"}
            heading="Canteen Product Tags"
            api={productTagAPI}
            otherFilterParams={{
                type: CANTEEN,
                includes_count: ["products", "targets"],
            }}
            useTableDataForForm={true}
            definedColumn={(): TableColumnType[] => [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "products_count",
                },
                {
                    key: "users_count",
                },
            ]}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name,
                          products_count: item?.products_count,
                          users_count: item?.targets_count,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => <ProductTagForm {...params} type={CANTEEN} />}
            filterForm={(params) => <FilterProductTagForm {...params} />}
            formSize="medium"
            viewPermit="product-tag-view"
            createPermit="product-tag-create"
            updatePermit="product-tag-update"
            deletePermit="product-tag-delete"
        />
    );
};

export default CanteenProductTags;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
