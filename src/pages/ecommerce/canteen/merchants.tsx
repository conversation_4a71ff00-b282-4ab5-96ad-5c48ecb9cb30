import { isArray, lowerCase } from "lodash";
import FilterMerchantForm from "@/components/forms/ecommerce/FilterMerchantForm";
import MerchantForm from "@/components/forms/ecommerce/MerchantForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    CANTEEN,
    INACTIVE,
    merchantAPI,
    TableColumnType,
    view_all_merchant,
} from "@/lib/constant";
import { useLanguages, useUserProfile } from "@/lib/store";
import { getNameColumns } from "@/lib/utils";
import { useCheckViewPermit } from "@/lib/hook";

const CanteenMerchants = ({ locale }) => {
    useCheckViewPermit("canteen-view");

    const { userProfile, hasPermit, hasMerchantType } = useUserProfile(
        (state) => state
    );
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    return (
        <CommonTablePageWrap
            locale={locale}
            path={"ecommerce/canteen/merchants"}
            heading="Canteen Merchants"
            api={merchantAPI}
            otherFilterParams={{ type: CANTEEN }}
            definedColumn={(): TableColumnType[] => [
                ...getNameColumns(activeLanguages, true),
                {
                    key: "label",
                    hasSort: true,
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ]}
            definedData={(data) => {
                let merchantData = isArray(data) ? data : [];

                if (hasMerchantType()) {
                    merchantData = merchantData?.filter((merchant) =>
                        userProfile?.userables?.some(
                            (userable) => userable.userable_id === merchant.id
                        )
                    );
                }

                return merchantData.map((item) => ({
                    id: item?.id,
                    ...item?.translations?.name,
                    label: item?.label,
                    type: item?.type,
                    is_active: item
                        ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                        : "",
                }));
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => <MerchantForm {...params} type={CANTEEN} />}
            filterForm={(params) => <FilterMerchantForm {...params} />}
            formSize="medium"
            hidePagination={hasMerchantType() && !hasPermit(view_all_merchant)}
            viewPermit="merchant-view"
            createPermit="merchant-create"
            updatePermit="merchant-update"
            deletePermit="merchant-delete"
        />
    );
};

export default CanteenMerchants;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
