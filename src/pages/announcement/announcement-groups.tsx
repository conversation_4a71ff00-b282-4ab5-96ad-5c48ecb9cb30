import { isArray, lowerCase } from "lodash";
import AnnouncementGroupForm from "@/components/forms/announcement/AnnouncementGroupForm";
import FilterAnnouncementGroupForm from "@/components/forms/announcement/FilterAnnouncementGroupForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    ACTIVE,
    announcementGroupAPI,
    INACTIVE,
    TableColumnType,
} from "@/lib/constant";

const AnnouncementGroups = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="announcement/announcement-groups"
        api={announcementGroupAPI}
        otherFilterParams={{ includes_count: ["students", "employees"] }}
        useTableDataForForm={true}
        definedColumn={(): TableColumnType[] => [
            { key: "name", hasSort: true },
            { key: "students_count" },
            { key: "employees_count" },
            {
                key: "is_active",
                displayAs: "Status",
                hasSort: true,
                modify: (value) => (
                    <div className={`cell-status ${value}`}>{value}</div>
                ),
            },
        ]}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      name: item?.name,
                      students_count: item?.students_count,
                      employees_count: item?.employees_count,
                      is_active: lowerCase(item.is_active ? ACTIVE : INACTIVE),
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <AnnouncementGroupForm {...params} />}
        filterForm={(params) => <FilterAnnouncementGroupForm {...params} />}
        formSize="medium"
        viewPermit="announcement-group-view"
        createPermit="announcement-group-create"
        updatePermit="announcement-group-update"
        deletePermit="announcement-group-delete"
    />
);

export default AnnouncementGroups;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
