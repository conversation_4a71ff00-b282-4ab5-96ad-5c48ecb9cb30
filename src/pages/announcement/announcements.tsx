import { useEffect, useState } from "react";
import React from "react";
import DOMPurify from "isomorphic-dompurify";
import { capitalize, isArray } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterAnnouncementForm from "@/components/forms/announcement/AnnouncementFilterForm";
import AnnouncementForm from "@/components/forms/announcement/AnnouncementForm";
import ViewAnnouncement from "@/components/forms/announcement/ViewAnnouncement";
import ActionDropdown from "@/components/ui/ActionDropdown";
import CancelPrompt from "@/components/ui/CancelPrompt";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    announcementAPI,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    displayDateTime,
    isTypeEmployee,
    optionUserLabel,
    refreshForUpdate,
} from "@/lib/utils";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import FilterChevronButton from "@/components/ui/FilterChevronButton";

const Announcements = ({ locale }) => {
    useCheckViewPermit("announcement-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [viewId, setViewId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [cancelTargetId, setCancelTargetId] = useState(null);

    const { data, axiosQuery: getAnnouncements } = useAxios({
        api: announcementAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchAnnouncements() {
        getAnnouncements({
            params: {
                includes: ["approvedBy.userables", "createdBy.userables"],
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchAnnouncements();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            const _columns: TableColumnType[] = [
                {
                    key: "title",
                    hasSort: true,
                    modify: (value) => (
                        <div className="c-text-ellipsis text-[14px]">
                            {value}
                        </div>
                    ),
                },
                {
                    key: "message",
                    hasSort: true,
                    modify: (value) => (
                        <div
                            className="c-text-ellipsis max-w-[300px] text-[14px]"
                            dangerouslySetInnerHTML={{
                                __html: DOMPurify.sanitize(value),
                            }}
                        />
                    ),
                },
                { key: "status", hasSort: true },
                {
                    key: "scheduled_time",
                    hasSort: true,
                    modify: (value) => (
                        <div className="text-[13px]">{value}</div>
                    ),
                },
                {
                    key: "sent_at",
                    hasSort: true,
                    modify: (value) => (
                        <div className="text-[13px]">{value}</div>
                    ),
                },
                {
                    key: "created_by",
                    modify: (value) => (
                        <div className="text-[13px]">{value}</div>
                    ),
                },
                {
                    key: "created_at",
                    hasSort: true,
                    modify: (value) => (
                        <div className="text-[13px]">{value}</div>
                    ),
                },
                {
                    key: "approved_by",
                    modify: (value) => (
                        <div className="text-[13px]">{value}</div>
                    ),
                },
                {
                    key: "recipient_count",
                    displayAs: "Recipients Reached",
                    hasSort: true,
                },
            ];
            setColumns(_columns);
        }
    }, [data]);

    function definedData() {
        return isArray(data)
            ? data.map((item) => {
                  const createdBy = item?.created_by?.userables?.find(
                      (userable) =>
                          isTypeEmployee(userable?.user_type_description)
                  );
                  const approvedBy = item?.approved_by?.userables?.find(
                      (userable) =>
                          isTypeEmployee(userable?.user_type_description)
                  );

                  return {
                      id: item?.id,
                      title: item?.title,
                      message: item?.message,
                      status: capitalize(item?.status),
                      scheduled_time: displayDateTime(
                          item?.scheduled_time,
                          DATE_FORMAT.forDisplay
                      ),
                      sent_at: displayDateTime(
                          item?.sent_at,
                          DATE_FORMAT.forDisplay
                      ),
                      created_at: displayDateTime(
                          item?.created_at,
                          DATE_FORMAT.forDisplay
                      ),
                      created_by: optionUserLabel(
                          createdBy?.number,
                          createdBy?.translations?.name
                      ),
                      approved_by: optionUserLabel(
                          approvedBy?.number,
                          approvedBy?.translations?.name
                      ),
                      recipient_count: item?.recipient_count,
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    return (
        <Layout locale={locale} path="announcement/announcements">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">Announcements</h2>
                    <Button
                        size="smallerOnMobile"
                        className="ml-auto capitalize"
                        onClick={() => setOpenCreate(true)}
                    >
                        Add Announcements
                    </Button>
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["includes"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterAnnouncementForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                {hasPermit("announcement-view") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setViewId(cell.row.original.id)
                                        }
                                    >
                                        View
                                    </DropdownMenuItem>
                                )}
                                {cell.row.original.status !== "Sent" && (
                                    <>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setCancelTargetId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Cancel
                                        </DropdownMenuItem>
                                    </>
                                )}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    Delete
                                </DropdownMenuItem>
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <AnnouncementForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setOpenCreate(false)}
                />
            </Modal>

            {/* view */}
            <Modal open={viewId} onOpenChange={setViewId} size="medium">
                <ViewAnnouncement id={viewId} close={() => setViewId(null)} />
            </Modal>

            {/* cancel */}
            <Modal open={cancelTargetId} onOpenChange={setCancelTargetId}>
                <CancelPrompt
                    id={cancelTargetId}
                    api={`${announcementAPI}/${cancelTargetId}/cancel`}
                    close={() => setCancelTargetId(null)}
                    refresh={getAnnouncements}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={announcementAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={getAnnouncements}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterAnnouncementForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default Announcements;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
