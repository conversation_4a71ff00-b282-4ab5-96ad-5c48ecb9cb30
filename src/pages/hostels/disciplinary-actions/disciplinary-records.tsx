import { useEffect, useState } from "react";
import { isArray } from "lodash";
import { useRouter } from "next/router";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    TableColumnType,
    DATE_FORMAT,
    hostelDisciplinaryRecordsAPI,
    studentAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    combinedNamesCell,
    refreshForUpdate,
} from "@/lib/utils";
import { format } from "date-fns";
import HostelDisciplinaryRecordForm from "@/components/forms/hostel/HostelDisciplinaryRecordForm";
import FilterHostelDisciplinaryRecordForm from "@/components/forms/hostel/FilterHostelDisciplinaryRecordForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const DisciplinaryRecords = ({ locale }) => {
    useCheckViewPermit("hostel-reward-punishment-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const router = useRouter();
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
    });
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getDisciplinaryRecords } = useAxios({
        api: hostelDisciplinaryRecordsAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "date",
                modify: (value) => (
                    <span className="whitespace-nowrap text-sm">
                        {format(value, DATE_FORMAT.DMY)}
                    </span>
                ),
            },
            {
                key: "person_in_charge",
                modify: (value) => <div className="min-w-[120px]">{value}</div>,
            },
            {
                key: "student_number",
            },
            {
                key: "student_name",
                modify: (value) => <div className="min-w-[100px]">{value}</div>,
            },
            {
                key: "hostel_merit_demerit",
                displayAs: "Merit/ Demerit",
            },
            {
                key: "hostel_reward_punishment",
            },
            {
                key: "points",
            },
            {
                key: "remark",
                modify: (value) => <span className="text-[14px]">{value}</span>,
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  student_number: item?.student?.student_number,
                  student_name: combinedNames(
                      item?.student?.translations?.name
                  ),
                  date: item?.date,
                  hostel_merit_demerit:
                      item?.hostel_reward_punishment_setting
                          ?.hostel_merit_demerit_setting?.name,
                  hostel_reward_punishment:
                      item?.hostel_reward_punishment_setting?.name,
                  person_in_charge: item?.person_in_charge?.name ?? "-",
                  remark: item?.remark,
                  points: item?.hostel_reward_punishment_setting.points,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setTargetId(null);
    }

    function fetchDisciplinaryRecords() {
        getDisciplinaryRecords({
            params: {
                order_by: { date: "desc" },
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchDisciplinaryRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <>
            <Layout
                locale={locale}
                path="hostels/disciplinary-actions/disciplinary-records"
            >
                <Card styleClass="table-card">
                    <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                        <div className="mr-auto grid gap-2 capitalize">
                            <h2>{t("disciplinary records")}</h2>
                        </div>
                        {hasPermit(
                            "hostel-reward-punishment-record-create"
                        ) && (
                            <Button
                                className="capitalize"
                                onClick={() =>
                                    router.push(
                                        "/hostels/disciplinary-actions/create-disciplinary-record"
                                    )
                                }
                            >
                                {t("create disciplinary record")}
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>

                    <FilterFormWrapper>
                        <FilterHostelDisciplinaryRecordForm
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                        />
                    </FilterFormWrapper>

                    <DataTable
                        columns={columns}
                        data={definedData(data)}
                        pagination={pagination}
                        setPagination={setPagination}
                        changePage={(arg) => setFilter({ ...filter, ...arg })}
                        sorted={filter?.order_by}
                        sort={onSort}
                        actionMenu={({ cell }) => (
                            <ActionDropdown>
                                {hasPermit(
                                    "hostel-reward-punishment-record-update"
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        {t("Edit / View")}
                                    </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                {hasPermit(
                                    "hostel-reward-punishment-record-delete"
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        {t("Delete")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        )}
                    />
                </Card>

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                    <HostelDisciplinaryRecordForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={hostelDisciplinaryRecordsAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={fetchDisciplinaryRecords}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterHostelDisciplinaryRecordForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </Layout>
        </>
    );
};

export default DisciplinaryRecords;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
