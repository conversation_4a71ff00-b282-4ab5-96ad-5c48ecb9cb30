import React from "react";
import Layout from "@/components/Layout";
import { useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import CreateHostelDisciplinaryRecordForm from "@/components/forms/hostel/CreateHostelDisciplinaryRecordForm";

const CreateDisciplinaryRecord = ({ locale }) => {
    useCheckViewPermit("hostel-reward-punishment-record-create");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    return (
        <Layout
            locale={locale}
            path="hostels/disciplinary-actions/create-disciplinary-record"
        >
            {hasPermit("hostel-reward-punishment-record-create") && (
                <CreateHostelDisciplinaryRecordForm />
            )}
        </Layout>
    );
};

export default CreateDisciplinaryRecord;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
