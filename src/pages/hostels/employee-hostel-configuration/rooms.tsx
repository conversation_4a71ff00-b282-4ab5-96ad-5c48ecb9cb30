import { capitalize, lowerCase } from "lodash";
import FilterHostelRoomForm from "@/components/forms/hostel/FilterHostelRoomForm";
import HostelRoomForm from "@/components/forms/hostel/HostelRoomForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    hostelRoomsAPI,
    TableColumnType,
    ACTIVE,
    INACTIVE,
    EMPLOYEE,
} from "@/lib/constant";
import { useTranslations } from "next-intl";

const Rooms = ({ locale }) => {
    const t = useTranslations("common");

    return (
        <CommonTablePageWrap
            locale={locale}
            path="hostels/employee-hostel-configuration/rooms"
            heading={`${t("employee ")}${t("hostel ")}${t("rooms")}`}
            api={hostelRoomsAPI}
            otherFilterParams={{ hostel_block_type: EMPLOYEE }}
            definedColumn={() => {
                const _columns: TableColumnType[] = [
                    {
                        key: "hostel_block",
                        displayAs: "block",
                        hasSort: true,
                    },
                    {
                        key: "name",
                        displayAs: "room",
                        hasSort: true,
                    },
                    {
                        key: "gender",
                        hasSort: true,
                        modify: (value) => t(capitalize(value)),
                    },
                    {
                        key: "capacity",
                        hasSort: true,
                    },
                    {
                        key: "is_active",
                        displayAs: "Status",
                        hasSort: true,
                        modify: (value) => (
                            <div className={`cell-status ${value}`}>
                                {t(value)}
                            </div>
                        ),
                    },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => {
                          const {
                              hostel_block,
                              is_active,
                              remarks,
                              capacity,
                              ...rest
                          } = item;
                          return {
                              hostel_block: item?.hostel_block?.name,
                              capacity: item?.capacity ?? "-",
                              is_active: lowerCase(
                                  item.is_active ? ACTIVE : INACTIVE
                              ),
                              ...rest,
                          };
                      })
                    : [];
            }}
            orderBy={(name, direction) => {
                return name === "hostel_block"
                    ? { name: { [name]: direction } }
                    : { [name]: direction };
            }}
            form={(params) => (
                <HostelRoomForm hostelType={EMPLOYEE} {...params} />
            )}
            filterForm={(params) => (
                <FilterHostelRoomForm type={EMPLOYEE} {...params} />
            )}
            viewPermit="hostel-room-view"
            createPermit="hostel-room-create"
            updatePermit="hostel-room-update"
            deletePermit="hostel-room-delete"
        />
    );
};

export default Rooms;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
