import FilterHostelBlockForm from "@/components/forms/hostel/FilterHostelBlockForm";
import HostelBlockForm from "@/components/forms/hostel/HostelBlockForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { hostelBlocksAPI, TableColumnType, EMPLOYEE } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Blocks = ({ locale }) => {
    const t = useTranslations("common");

    return (
        <CommonTablePageWrap
            locale={locale}
            path="hostels/employee-hostel-configuration/blocks"
            heading={`${t("employee ")}${t("hostel ")}${t("blocks")}`}
            api={hostelBlocksAPI}
            otherFilterParams={{ type: EMPLOYEE }}
            definedColumn={(activeLanguages) => {
                const _columns: TableColumnType[] = [
                    {
                        key: "code",
                        hasSort: false,
                    },
                    ...getNameColumns(activeLanguages),
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          code: item?.code,
                          ...item?.translations?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return name === "type"
                    ? { type: direction }
                    : { name: { [name]: direction } };
            }}
            form={(params) => (
                <HostelBlockForm hostelType={EMPLOYEE} {...params} />
            )}
            filterForm={(params) => (
                <FilterHostelBlockForm type={EMPLOYEE} {...params} />
            )}
            viewPermit="hostel-block-view"
            createPermit="hostel-block-create"
            updatePermit="hostel-block-update"
            deletePermit="hostel-block-delete"
        />
    );
};

export default Blocks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
