import { useEffect, useState } from "react";
import { format } from "date-fns";
import { capitalize, isArray } from "lodash";
import { RotateCcw } from "lucide-react";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import FilterHostelSavingAccountForm from "@/components/forms/hostel/FilterHostelSavingAccountForm";
import HostelSavingAccountForm from "@/components/forms/hostel/HostelSavingAccountForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import InfoCard from "@/components/ui/InfoCard";
import Modal from "@/components/ui/Modal";
import RaisedButton from "@/components/ui/RaisedButton";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    appCurrencySymbol,
    CONFIRMED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    DEPOSIT,
    DRAFT,
    FULL,
    hostelSavingAccountBillingDocumentAPI,
    hostelSavingAccountGetBalanceAPI,
    hostelSavingAccountTransactionsAPI,
    TableColumnType,
    VOIDED,
    WITHDRAWAL,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLoading, useUserProfile } from "@/lib/store";
import {
    combinedNames,
    combineSemesterClass,
    formatNumberForRead,
    optionUserLabel,
    toYMD,
} from "@/lib/utils";
import clsx from "clsx";
import HostelTransactionChangeStatusPrompt from "@/components/forms/hostel/HostelTransactionChangeStatusPrompt";
import { useTranslations } from "next-intl";

const HostelSavingAccount = ({ locale }) => {
    useCheckViewPermit("hostel-savings-account-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const t = useTranslations("common");

    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const today = new Date();

    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<any>(null);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        period_from: toYMD(oneYearAgo),
        period_to: toYMD(today),
    });
    const [pagination, setPagination] = useState();
    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);
    const [targetVoidId, setTargetVoidId] = useState(null);

    const [type, setType] = useState<"DEPOSIT" | "WITHDRAWAL">(DEPOSIT);

    const { data: availableBalance, axiosQuery: getAvailableBalance } =
        useAxios({
            api: hostelSavingAccountGetBalanceAPI,
            locale,
        });

    const { data: transactionRecords, axiosQuery: getTransactionsRecord } =
        useAxios({
            api: hostelSavingAccountTransactionsAPI,
            locale,
            onSuccess: (result) => {
                setPagination(result?.pagination);
            },
        });

    const columns: TableColumnType[] = [
        {
            key: "billing_document_reference",
            displayAs: "document number",
        },
        {
            key: "payment_reference",
            displayAs: "payment reference no",
        },
        {
            key: "transaction_date",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "type",
            hasSort: true,
            modify: (value) => t(value),
        },
        {
            key: "amount_before_tax",
            hasSort: true,
            displayAs: `${t("Amount")} (${appCurrencySymbol})`,
        },
        // {
        //     key: "post_balance",
        //     displayAs: `Balance (${appCurrencySymbol})`,
        // },
        {
            key: "bank",
        },
        {
            key: "description",
        },
        {
            key: "remarks",
        },
        {
            key: "created_by",
        },
        {
            key: "status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === CONFIRMED && "text-green-600",
                        value === DRAFT && "text-yellow-500",
                        value === VOIDED && "text-red-500"
                    )}
                >
                    {capitalize(t(value))}
                </span>
            ),
        },
    ];

    function definedData() {
        return isArray(transactionRecords)
            ? transactionRecords.map((item: any) => {
                  return {
                      id: item?.billing_document_id,
                      billing_document_reference:
                          item?.billing_document_reference ?? "-",
                      type: capitalize(item?.type) ?? "-",
                      transaction_date: item?.transaction_date
                          ? format(item?.transaction_date, DATE_FORMAT.DMY)
                          : "-",
                      currency: appCurrencySymbol,
                      amount_before_tax: formatNumberForRead(
                          item?.amount_before_tax
                      ),
                      post_balance: formatNumberForRead(item?.post_balance),
                      bank: item?.bank?.name ?? "-",
                      payment_reference: item?.payment_reference ?? "-",
                      description: item?.description ?? "-",
                      remarks: item?.remarks ?? "-",
                      created_by: optionUserLabel(
                          item?.created_by?.employee_number,
                          item?.created_by?.translations?.name
                      ),
                      status: item?.status ?? "-",
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function formattedStudent(student) {
        return {
            student_number: student?.student_number,
            name: combinedNames(student?.translations?.name),
            class: combineSemesterClass(student?.current_primary_class),
            available_balance: availableBalance
                ? appCurrencySymbol + " " + availableBalance?.balance
                : "-",
        };
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetVoidId(null);
    }

    function fetchRecordandBalance(isRefresh = false) {
        const params = isRefresh
            ? {
                  ...DEFAULT_FILTER_PARAMS,
                  period_from: toYMD(oneYearAgo),
                  period_to: toYMD(today),
              }
            : filter;

        getTransactionsRecord({
            params: {
                ...params,
                student_id: selectedStudent?.id,
            },
        });

        getAvailableBalance({
            params: { student_id: selectedStudent?.id },
        });
    }

    function refresh() {
        fetchRecordandBalance(true);
    }

    const handleOpenCreate = (actionType) => {
        setType(actionType);
        setOpenCreate(true);
    };

    useEffect(() => {
        if (selectedStudent?.id) {
            getTransactionsRecord({
                params: {
                    ...filter,
                    student_id: selectedStudent?.id,
                },
            });
        }
    }, [filter]);

    useEffect(() => {
        if (selectedStudent?.id) {
            fetchRecordandBalance(false);
        }
    }, [selectedStudent]);

    return (
        <Layout path="hostels/saving-account" locale={locale}>
            <Card styleClass="max-w-screen-2xl mx-auto">
                <div className="pb-7 lg:px-2">
                    <h2 className="mb-5 pt-2">{t("Hostel Saving Account")}</h2>

                    <RaisedButton
                        name={t("Select ") + t("Student")}
                        onClick={() => setOpenSearch(true)}
                    />

                    {selectedStudent && (
                        <div className="pt-5">
                            <InfoCard
                                title={t("Student information")}
                                data={formattedStudent(selectedStudent)}
                                cardStyleClass="lg:min-w-[400px]"
                            />
                        </div>
                    )}

                    {selectedStudent && (
                        <div className="pt-5">
                            <div className="flex items-end gap-x-3 pb-3">
                                <h3 className="mb-1">
                                    {t("Transaction History")}
                                </h3>
                                <Button
                                    type="button"
                                    variant="outline"
                                    className="ml-auto flex items-center gap-x-1"
                                    onClick={refresh}
                                >
                                    <RotateCcw size={16} />
                                    <span>{t("Refresh")}</span>
                                </Button>

                                {hasPermit(
                                    "hostel-savings-account-deposit"
                                ) && (
                                    <Button
                                        size="smallerOnMobile"
                                        onClick={() =>
                                            handleOpenCreate(DEPOSIT)
                                        }
                                    >
                                        {t("Deposit")}
                                    </Button>
                                )}
                                {hasPermit(
                                    "hostel-savings-account-withdraw"
                                ) && (
                                    <Button
                                        size="smallerOnMobile"
                                        onClick={() =>
                                            handleOpenCreate(WITHDRAWAL)
                                        }
                                    >
                                        {t("Withdrawal")}
                                    </Button>
                                )}
                                <TableFilterBtn
                                    filter={filter}
                                    onClick={() => setOpenFilter(true)}
                                />
                            </div>

                            <DataTable
                                columns={columns}
                                data={definedData()}
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                sorted={filter?.order_by}
                                sort={onSort}
                                actionMenu={({ cell }) => {
                                    const id = cell.row.original.id;
                                    const status = cell.row.original.status;
                                    return (
                                        <>
                                            <ActionDropdown>
                                                {hasPermit(
                                                    "hostel-savings-account-void"
                                                ) &&
                                                    status === CONFIRMED && (
                                                        <DropdownMenuItem
                                                            className="c-text-size"
                                                            onClick={() =>
                                                                setTargetVoidId(
                                                                    id
                                                                )
                                                            }
                                                        >
                                                            {t(
                                                                "Void Transaction"
                                                            )}
                                                        </DropdownMenuItem>
                                                    )}
                                            </ActionDropdown>
                                        </>
                                    );
                                }}
                            />
                        </div>
                    )}
                </div>
            </Card>

            {/* search student */}
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    setSelection={(student) => {
                        if (student) {
                            setSelectedStudent(student);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                    otherFilterParams={{
                        response: FULL,
                        is_hostel: 1,
                        includes: [
                            "currentSemesterPrimaryClass.semesterSetting",
                            "currentSemesterPrimaryClass.semesterClass.classModel",
                        ],
                    }}
                />
            </Modal>

            {/* create (deposit/withdrawal) */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <HostelSavingAccountForm
                    studentData={selectedStudent}
                    type={type}
                    refresh={refresh}
                    close={closeForm}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterHostelSavingAccountForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* Void Billing Document */}
            <Modal open={targetVoidId} onOpenChange={setTargetVoidId}>
                <HostelTransactionChangeStatusPrompt
                    id={targetVoidId}
                    api={`${hostelSavingAccountBillingDocumentAPI}/${targetVoidId}/void`}
                    close={() => setTargetVoidId(null)}
                    refresh={refresh}
                />
            </Modal>
        </Layout>
    );
};

export default HostelSavingAccount;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
