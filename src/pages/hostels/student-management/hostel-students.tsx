import { useEffect, useState } from "react";
import Image from "next/image";
import React from "react";
import { isArray } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import FilterHostelStudentForm from "@/components/forms/hostel/FilterHostelStudentForm";
import HostelBulkMarkDisciplinaryForm from "@/components/forms/hostel/HostelBulkMarkDisciplinaryForm";
import StudentBulkMarkDepartureForm from "@/components/forms/hostel/StudentBulkMarkDeparture";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import ProfileCard from "@/components/ui/ProfileCard";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
    hostelStudentAPIFilter,
    studentAPI,
    STUDENT,
    FULL,
    IconProfilePhotoPlaceholder,
    nonDirectGuardianUpdatePermit,
    studentProfileParams,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    combinedNamesCell,
    combineSemesterClass,
    displayDateTime,
    getTableSelection,
    optionUserLabel,
    refreshForUpdate,
} from "@/lib/utils";
import PersonImagePreview from "@/components/ui/PersonImagePreview";
import NonDirectGuardianForm from "@/components/forms/hostel/NonDirectGuardianForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const HostelStudents = ({ locale }) => {
    useCheckViewPermit("hostel-student");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const t = useTranslations("common");

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        ...hostelStudentAPIFilter,
    });
    const [pagination, setPagination] = useState();

    const [markDisciplinary, setMarkDisciplinary] = useState(false);
    const [markDeparture, setMarkDeparture] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetProfile, setTargetProfile] = useState<any>(null);
    const [targetImage, setTargetImage] = useState<any>(null);
    const [targetId, setTargetId] = useState(null);

    const [selection, setSelection] = useState<any[]>([]);

    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data: hostelStudents, axiosQuery: getHostelStudents } = useAxios({
        api: studentAPI,
        locale,
        onSuccess: (result) => {
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
        },
    });

    useEffect(() => {
        getHostelStudents({
            params: {
                ...filter,
                response: FULL,
                includes: [
                    "guardians",
                    "currentSemesterPrimaryClass.semesterSetting",
                    "currentSemesterPrimaryClass.semesterClass.classModel",
                    "activeHostelBedAssignments.bed.hostelRoom.hostelBlock",
                ],
            },
        });
    }, [filter, locale]);

    useEffect(() => {
        if (hostelStudents && activeLanguages) {
            const _columns: TableColumnType[] = [
                {
                    key: "student_photo",
                    modify: (data) => (
                        <div className="flex h-[80px] items-center justify-center">
                            {data?.url ? (
                                <Image
                                    src={data.url}
                                    alt=""
                                    width={50}
                                    height={50}
                                    className="mx-auto max-h-full w-auto cursor-pointer rounded-sm"
                                    onClick={() => {
                                        setTargetImage({
                                            student_name: data.name,
                                            url: data?.url,
                                        });
                                    }}
                                />
                            ) : (
                                <Image
                                    src={IconProfilePhotoPlaceholder}
                                    alt=""
                                    width={50}
                                    height={50}
                                    className="mx-auto h-[50px] w-[50px] opacity-10"
                                    unoptimized
                                />
                            )}
                        </div>
                    ),
                },
                {
                    key: "block",
                },
                {
                    key: "room",
                },
                {
                    key: "bed",
                },
                {
                    key: "student_number",
                    hasSort: true,
                },
                {
                    key: "student_name",
                    modify(value, cell) {
                        return combinedNamesCell(value);
                    },
                },
                {
                    key: "semester_class",
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "start_date",
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [hostelStudents, activeLanguages]);

    function definedData() {
        return isArray(hostelStudents)
            ? hostelStudents.map((item) => {
                  const hostelBed = item?.active_hostel_bed_assignment?.bed;
                  return {
                      id: item?.id,
                      student_photo: {
                          url: item?.photo,
                          name: optionUserLabel(
                              item?.student_number,
                              item?.translations?.name
                          ),
                      },
                      bed: hostelBed?.name ?? "-",
                      room: hostelBed?.hostel_room?.name ?? "-",
                      block: hostelBed?.hostel_room?.hostel_block?.name ?? "-",
                      student_number: item?.student_number,
                      student_name: Object.values(
                          item?.translations?.name ?? {}
                      ),
                      semester_class: combineSemesterClass(
                          item?.current_primary_class
                      ),
                      start_date: displayDateTime(
                          item?.active_hostel_bed_assignment?.start_date,
                          DATE_FORMAT.DMY
                      ),
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, navigatedResults);
        setSelection(_selection);
    }

    const { axiosQuery: getStudentData } = useAxios({
        api: studentAPI,
        locale,
        onSuccess: (result) => {
            setTargetProfile(result.data);
            //setTargetProfile(formatStudentData(result.data));
        },
    });

    function getStudentProfile(id: number) {
        getStudentData({
            id,
            params: studentProfileParams,
        });
    }

    return (
        <Layout
            locale={locale}
            path="hostels/student-management/hostel-students"
        >
            <Card styleClass="table-card">
                <div className="mb-3 flex flex-wrap items-center gap-3">
                    <h2 className="capitalize">{t("hostel students")}</h2>
                    {hasPermit("hostel-in-out-record-create") && (
                        <Button
                            className="ml-auto"
                            size="smallerOnMobile"
                            disabled={selection?.length < 1}
                            onClick={() => setMarkDeparture(true)}
                        >
                            {t("Bulk Mark ")}
                            {t("Departure")}
                        </Button>
                    )}
                    {hasPermit("hostel-merit-demerit-setting-create") && (
                        <Button
                            size="smallerOnMobile"
                            disabled={selection?.length < 1}
                            onClick={() => setMarkDisciplinary(true)}
                        >
                            {t("Bulk Mark ")}
                            {t("Disciplinary")}
                        </Button>
                    )}
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["is_hostel", "includes", "fields"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterHostelStudentForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                        isHostel={true}
                    />
                </FilterFormWrapper>

                <DataTable
                    onMultiSelect={onMultiSelect}
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        getStudentProfile(cell.row.original.id)
                                    }
                                >
                                    {t("View")}
                                </DropdownMenuItem>
                                {hasPermit(nonDirectGuardianUpdatePermit) && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        {t("Edit ")}
                                        {t("Guardian")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* edit non-direct guardian */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <NonDirectGuardianForm
                    id={targetId}
                    close={() => setTargetId(null)}
                />
            </Modal>

            {/* student photo */}
            <Modal
                open={targetImage != null}
                onOpenChange={(value) => {
                    if (value === false) {
                        setTargetImage(null);
                    }
                }}
            >
                <PersonImagePreview
                    studentName={targetImage?.student_name}
                    url={targetImage?.url}
                />
            </Modal>

            {/* view */}
            <Modal
                open={targetProfile}
                onOpenChange={setTargetProfile}
                size="large"
            >
                <ProfileCard
                    title="Student Profile"
                    data={targetProfile!}
                    type={STUDENT}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterHostelStudentForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                    isHostel={true}
                />
            </Modal>

            <Modal
                open={markDeparture}
                onOpenChange={setMarkDeparture}
                size="large"
            >
                <StudentBulkMarkDepartureForm
                    students={selection}
                    close={() => setMarkDeparture(false)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>
            <Modal
                open={markDisciplinary}
                onOpenChange={setMarkDisciplinary}
                size="large"
            >
                <HostelBulkMarkDisciplinaryForm
                    students={selection}
                    close={() => setMarkDisciplinary(false)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>
        </Layout>
    );
};

export default HostelStudents;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
