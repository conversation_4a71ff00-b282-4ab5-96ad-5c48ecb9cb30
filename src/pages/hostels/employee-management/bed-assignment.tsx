import Layout from "@/components/Layout";
import BedAssignmentForms from "@/components/ui/BedAssignmentForms";
import { EMPLOYEE } from "@/lib/constant";
import { useCheckViewPermit } from "@/lib/hook";

const BedAssignment = ({ locale }) => {
    useCheckViewPermit("hostel-bed-assignment-assign");
    return (
        <Layout
            locale={locale}
            path="hostels/employee-management/bed-assignment"
        >
            <BedAssignmentForms type={EMPLOYEE} />
        </Layout>
    );
};

export default BedAssignment;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
