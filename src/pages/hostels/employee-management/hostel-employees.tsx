import { useEffect, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import Layout from "@/components/Layout";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import FilterHostelEmployeeForm from "@/components/forms/hostel/FilterHostelEmployeeForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import ProfileCard from "@/components/ui/ProfileCard";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
    employeeAPI,
    hostelEmployeeAPIFilter,
    EMPLOYEE,
    FULL,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { combinedNamesCell, displayDateTime } from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const HostelEmployees = ({ locale }) => {
    useCheckViewPermit("hostel-employee");
    const t = useTranslations("common");

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        ...hostelEmployeeAPIFilter,
    });
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);

    const [targetProfile, setTargetProfile] = useState<any>(null);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data, axiosQuery: getHostelEmployees } = useAxios({
        api: employeeAPI,
        locale,
        onSuccess: (result) => {
            setPagination(result?.pagination);
        },
    });

    useEffect(() => {
        getHostelEmployees({
            params: { response: FULL, ...filter },
        });
    }, [filter, locale]);

    useEffect(() => {
        if (data && activeLanguages) {
            const _columns: TableColumnType[] = [
                {
                    key: "block",
                },
                {
                    key: "room",
                },
                {
                    key: "bed",
                },
                {
                    key: "employee_number",
                    hasSort: true,
                },
                {
                    key: "employee_name",
                    modify(value, cell) {
                        return combinedNamesCell(value);
                    },
                },
                {
                    key: "start_date",
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [data, activeLanguages]);

    function definedData() {
        return isArray(data)
            ? data.map((item) => {
                  const hostelBed = item?.active_hostel_bed_assignment?.bed;
                  return {
                      id: item?.id,
                      bed: hostelBed?.name ?? "-",
                      room: hostelBed?.hostel_room?.name ?? "-",
                      block: hostelBed?.hostel_room?.hostel_block?.name ?? "-",
                      employee_number: item?.employee_number,
                      employee_name: Object.values(
                          item?.translations?.name ?? {}
                      ),
                      start_date: displayDateTime(
                          item?.active_hostel_bed_assignment?.start_date,
                          DATE_FORMAT.DMY
                      ),
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    const { axiosQuery: getEmployeeData } = useAxios({
        api: employeeAPI,
        locale,
        onSuccess: (result) => {
            setTargetProfile(result.data);
        },
    });

    function getEmployeeProfile(id: number) {
        getEmployeeData({ id });
    }

    return (
        <Layout
            locale={locale}
            path="hostels/employee-management/hostel-employees"
        >
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">{t("hostel employees")}</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["is_hostel", "includes", "fields"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterHostelEmployeeForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                        isHostel={true}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        getEmployeeProfile(cell.row.original.id)
                                    }
                                >
                                    {t("View")}
                                </DropdownMenuItem>
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* view */}
            <Modal
                open={targetProfile}
                onOpenChange={setTargetProfile}
                size="medium"
            >
                <ProfileCard
                    title={t("Employee Profile")}
                    data={targetProfile!}
                    type={EMPLOYEE}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterHostelEmployeeForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                    isHostel={true}
                />
            </Modal>
        </Layout>
    );
};

export default HostelEmployees;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
