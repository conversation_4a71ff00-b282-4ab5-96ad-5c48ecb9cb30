import React from "react";
import Layout from "@/components/Layout";
import CreateDepartureRecordForm from "@/components/forms/hostel/CreateDepartureRecord";
import { useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";

const CreateDepartureRecord = ({ locale }) => {
    useCheckViewPermit("hostel-in-out-record-view");
    const userProfile = useUserProfile((state) => state.userProfile);
    const hasPermit = useUserProfile((state) => state.hasPermit);

    return (
        <Layout
            locale={locale}
            path="hostels/arrival-and-departure/create-departure-record"
        >
            {userProfile && hasPermit("hostel-in-out-record-create") && (
                <CreateDepartureRecordForm />
            )}
        </Layout>
    );
};

export default CreateDepartureRecord;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
