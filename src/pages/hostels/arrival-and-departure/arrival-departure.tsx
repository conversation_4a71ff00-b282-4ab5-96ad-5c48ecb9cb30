import { useEffect, useState } from "react";
import React from "react";
import { capitalize } from "lodash";
import { useRouter } from "next/router";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ArrivalDepartureForm from "@/components/forms/hostel/ArrivalDepartureForm";
import FilterHostelArrivalDepartureForm from "@/components/forms/hostel/FilterHostelArrivalDepartureForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    DEFAULT_FILTER_PARAMS,
    hostelArrivalDep<PERSON>ureAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNamesCell,
    convertDateTime,
    isTypeEmployee,
    optionUserLabel,
} from "@/lib/utils";
import clsx from "clsx";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const ArrivalDeparture = ({ locale }) => {
    useCheckViewPermit("hostel-in-out-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const router = useRouter();

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        order_by: {
            check_in_datetime: "desc",
        },
        includes: [
            "bed.hostelRoom.hostelBlock",
            "checkOutBy.userables",
            "checkInBy.userables",
        ],
    });
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getInOutRecords } = useAxios({
        api: hostelArrivalDepartureAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchInOutRecords() {
        getInOutRecords({ params: filter });
    }

    useEffect(() => {
        fetchInOutRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "bed",
                modify: (value) => <div className="min-w-16">{value}</div>,
            },
            {
                key: "student_number",
            },
            {
                key: "student_name",
                modify(value, cell) {
                    return combinedNamesCell(value);
                },
            },
            {
                key: "check_out_datetime",
                displayAs: "departure date time",
                hasSort: true,
                modify: (value) => {
                    return <span className="text-[14px]">{value ?? "--"}</span>;
                },
            },
            {
                key: "check_out_by",
                displayAs: "checked out by",
                modify: (value) => {
                    return (
                        <div
                            className={clsx(
                                "text-[14px]",
                                value && "min-w-[200px]"
                            )}
                        >
                            {value ?? "-"}
                        </div>
                    );
                },
            },
            {
                key: "check_in_datetime",
                hasSort: true,
                displayAs: "arrival date time",
                modify: (value) => {
                    return <span className="text-[14px]">{value ?? "-"}</span>;
                },
            },
            {
                key: "check_in_by",
                displayAs: "checked in by",
                modify: (value) => {
                    return (
                        <div
                            className={clsx(
                                "text-[14px]",
                                value && "min-w-[200px]"
                            )}
                        >
                            {value ?? "--"}
                        </div>
                    );
                },
            },
            {
                key: "type",
                modify: (value) => t(value),
            },
            {
                key: "card_no",
                displayAs: "card number",
                modify: (value) => {
                    return <span>{value ?? "-"}</span>;
                },
            },
            {
                key: "remarks",
                modify: (value) => {
                    return (
                        <div
                            className={clsx(
                                "text-[14px]",
                                value && "min-w-[120px]"
                            )}
                        >
                            {value ?? "-"}
                        </div>
                    );
                },
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return Array.isArray(data)
            ? data.map((item) => {
                  const checkedInBy = item?.check_in_by?.userables?.find(
                      (userable) =>
                          isTypeEmployee(userable?.user_type_description)
                  );
                  const checkedOutBy = item?.check_out_by?.userables?.find(
                      (userable) =>
                          isTypeEmployee(userable?.user_type_description)
                  );

                  return {
                      id: item?.id,
                      block: item.hostel_room_bed?.hostel_room?.hostel_block
                          ?.name,
                      room: item.hostel_room_bed?.hostel_room?.name,
                      bed: item.hostel_room_bed?.name,
                      student_name: Object.values(
                          item?.student?.translations?.name ?? {}
                      ),
                      student_number: item.student?.student_number,
                      gender: capitalize(item.student?.gender),
                      check_in_datetime: item.check_in_datetime
                          ? convertDateTime(item.check_in_datetime)
                          : null,
                      check_in_by: checkedInBy
                          ? optionUserLabel(
                                checkedInBy?.number,
                                checkedInBy?.translations?.name
                            )
                          : null,
                      check_out_datetime: convertDateTime(
                          item.check_out_datetime
                      ),
                      check_out_by: checkedOutBy
                          ? optionUserLabel(
                                checkedOutBy?.number,
                                checkedOutBy?.translations?.name
                            )
                          : null,
                      remarks: item?.reason,
                      type: item?.type,
                      card_no: item?.card_no,
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setTargetId(null);
    }

    return (
        <Layout
            locale={locale}
            path="hostels/arrival-and-departure/arrival-departure"
        >
            <Card styleClass="table-card">
                <div className="mb-3 flex flex-wrap items-center gap-3">
                    <h2 className="capitalize">
                        {t("arrival/departure records")}
                    </h2>
                    {hasPermit("hostel-in-out-record-create") && (
                        <Button
                            className="lg:ml-auto"
                            onClick={() =>
                                router.push(
                                    "/hostels/arrival-and-departure/create-departure-record"
                                )
                            }
                        >
                            {t("Add Departure")}
                        </Button>
                    )}

                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                        excludeFields={["includes"]}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterHostelArrivalDepartureForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit("hostel-in-out-record-update") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setTargetId(cell.row.original.id)
                                    }
                                >
                                    {t("Edit / View")}
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            {hasPermit("hostel-in-out-record-delete") && (
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    {t("Delete")}
                                </DropdownMenuItem>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <ArrivalDepartureForm
                    id={targetId}
                    refresh={fetchInOutRecords}
                    close={closeForm}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={hostelArrivalDepartureAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchInOutRecords}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterHostelArrivalDepartureForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default ArrivalDeparture;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
