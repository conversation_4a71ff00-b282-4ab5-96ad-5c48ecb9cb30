import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import FilterHostelArrivalDepartureForm from "@/components/forms/hostel/FilterHostelArrivalDepartureForm";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FreeTextArea from "@/components/ui/FreeTextArea";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    DEFAULT_FILTER_PARAMS,
    hostelArrivalDepartureAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    combinedNamesCell,
    convertDateTime,
    getTableSelection,
    toUTC,
} from "@/lib/utils";
import FreeTable from "@/components/ui/FreeTable";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const BulkMarkArrival = ({ locale }) => {
    useCheckViewPermit("hostel-in-out-record-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const t = useTranslations("common");

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        check_in_datetime: "",
    });
    const [pagination, setPagination] = useState();

    const [selection, setSelection] = useState<any[]>([]);
    const [openBulkMark, setOpenBulkMark] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);
    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);
    const [clearCount, setClearCount] = useState(0);

    const form = useForm<any>({
        defaultValues: {
            records: [],
        },
    });

    const { data, axiosQuery: getDepartureRecords } = useAxios({
        api: hostelArrivalDepartureAPI,
        locale,
        onSuccess: (result) => {
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
        },
    });

    const { axiosPatch: bulkMark } = useAxios({
        api: hostelArrivalDepartureAPI,
        toastMsg: t("Arrival marked successfully"),
        onSuccess: () => {
            refresh();
            clearCheckboxes();
            setOpenBulkMark(false);
        },
    });

    function clearCheckboxes() {
        setSelection([]);
        setClearCount(clearCount + 1);
    }

    useEffect(() => {
        getDepartureRecords({ params: filter });
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "student_number",
                hasSort: false,
            },
            {
                key: "student_name",
                modify(value, cell) {
                    return combinedNamesCell(value);
                },
            },
            {
                key: "gender",
                modify: (value) => t(value),
            },
            {
                key: "type",
                modify: (value) => t(value),
            },
            {
                key: "signing_guardian",
                modify: (value) => (
                    <div className="min-w-[120px] text-sm">{value}</div>
                ),
            },
            {
                key: "card_no",
                displayAs: "card number",
            },
            {
                key: "departure_date_time",
                modify: (value) => (
                    <span className="whitespace-nowrap text-sm">{value}</span>
                ),
            },
            {
                key: "reason",
                displayAs: "remark",
                modify: (value) => (
                    <div className="min-w-[120px] text-sm">{value}</div>
                ),
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  student_name: Object.values(
                      item?.student?.translations?.name ?? {}
                  ),
                  student_number: item?.student?.student_number,
                  gender: capitalize(item?.student?.gender),
                  type: item?.type,
                  signing_guardian: combinedNames(
                      item?.guardian?.translations?.name
                  ),
                  card_no: item?.card_no ?? "-",
                  departure_date_time: item.check_out_datetime
                      ? convertDateTime(item.check_out_datetime)
                      : "-",
                  reason: item?.reason ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function refresh() {
        getDepartureRecords({ params: filter });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, navigatedResults);
        setSelection(_selection);
        form.setValue(
            "records",
            _selection.map((record) => ({
                id: record?.id,
                check_in_datetime: "",
                reason: "",
            }))
        );
    }

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            const selectionIds = selection.map((item) => item?.id);
            const newData = { ...data };
            newData.records = newData.records.filter((record) =>
                selectionIds.includes(record.id)
            );
            newData.records.forEach(
                (record) => (record.check_in_datetime = toUTC(new Date()))
            );
            console.log("newData", newData);
            bulkMark(newData);
        })();
    }

    return (
        <Layout
            locale={locale}
            path="hostels/arrival-and-departure/bulk-mark-arrival"
        >
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3">
                    <h2>{t("Bulk Mark Arrival")}</h2>
                    {hasPermit("hostel-in-out-record-create") && (
                        <Button
                            size="smallerOnMobile"
                            className="ml-auto"
                            disabled={selection?.length < 1}
                            onClick={() => setOpenBulkMark(true)}
                        >
                            {t("Submit Bulk Mark Arrival")}
                        </Button>
                    )}
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                        excludeFields={["check_in_datetime"]}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterHostelArrivalDepartureForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    onMultiSelect={onMultiSelect}
                    clearCount={clearCount}
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            </Card>

            {/* bulk mark */}
            <Modal
                open={openBulkMark}
                onOpenChange={setOpenBulkMark}
                size="large"
            >
                <h2>{t("Bulk Mark Arrival")}</h2>
                <FreeTable
                    columns={[
                        ...columns,
                        {
                            key: "remark",
                            modify: (_, index) => {
                                return (
                                    <FreeTextArea
                                        control={form.control}
                                        name={`records[${index}].reason`}
                                        error={
                                            form.formState.errors?.records?.[
                                                index
                                            ]?.reason
                                        }
                                    />
                                );
                            },
                        },
                    ]}
                    data={definedData(selection)}
                />
                <Button className="ml-auto mt-2" onClick={onSubmit}>
                    {t("Submit")}
                </Button>
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterHostelArrivalDepartureForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default BulkMarkArrival;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
