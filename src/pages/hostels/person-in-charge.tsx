import { capitalize, isArray } from "lodash";
import { hostelPersonInChargeAPI, TableColumnType } from "@/lib/constant";
import { combinedNames, getAxiosErrorMessage } from "@/lib/utils";
import HostelPersonInChargeForm from "@/components/forms/HostelPersonInChargeForm";
import { useEffect, useState } from "react";
import React from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import { DEFAULT_FILTER_PARAMS } from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { refreshForUpdate } from "@/lib/utils";
import { useUserProfile } from "@/lib/store";
import { DialogFooter } from "@/components/base-ui/dialog";
import toast from "react-hot-toast";
import { useTranslations } from "next-intl";

const HostelPersonInCharge = ({ locale }) => {
    useCheckViewPermit("hostel-person-in-charge-admin-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const t = useTranslations("common");

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);

    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getPIC } = useAxios({
        api: hostelPersonInChargeAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    const columns: TableColumnType[] = [
        {
            key: "employee",
            hasSort: true,
        },
        {
            key: "employee_number",
            modify(value) {
                return <span className="text-[14px]">{value}</span>;
            },
        },
        {
            key: "nric",
            displayAs: "NRIC",
            modify(value) {
                return <span className="text-[14px]">{value ?? "-"}</span>;
            },
        },
        {
            key: "badge_no",
            modify(value) {
                return <span className="text-[14px]">{value ?? "-"}</span>;
            },
        },
        {
            key: "status",
            modify: (value) => capitalize(t(value)),
        },
    ];

    useEffect(() => {
        getPIC({
            params: {
                ...filter,
            },
        });
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.employee?.id,
                  employee: combinedNames(item?.employee?.translations?.name),
                  employee_number: item?.employee?.employee_number,
                  nric: item?.employee?.nric,
                  status: item?.employee?.status ?? "-",
                  badge_no: item?.employee?.badge_no,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by:
                name === "employee"
                    ? { [name]: { name: direction } }
                    : { [name]: direction },
        });
    }

    const { axiosPost: deletePIC } = useAxios({
        api: `${hostelPersonInChargeAPI}/remove`,
        onSuccess: () => {
            refreshForUpdate(filter, setFilter);
            setTargetDeleteId(null);
        },
        onError: (error) => {
            toast.error(getAxiosErrorMessage(error));
        },
    });

    function onDelete() {
        deletePIC({ employee_id: targetDeleteId });
    }

    return (
        <Layout locale={locale} path="hostels/person-in-charge">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 capitalize lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>{t("hostel person in charge")}</h2>
                    {hasPermit("hostel-person-in-charge-admin-add") && (
                        <Button onClick={() => setOpenCreate(true)}>
                            {t("Add ")}
                            {t("person in charge")}
                        </Button>
                    )}
                </div>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit(
                                "hostel-person-in-charge-admin-remove"
                            ) && (
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    {t("Delete")}
                                </DropdownMenuItem>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* add */}
            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <HostelPersonInChargeForm
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setOpenCreate(false)}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <p className="mt-3 font-medium">
                    {t("Are you sure you want to delete this?")}
                </p>
                <DialogFooter className={"mt-2"}>
                    <Button
                        variant="outline"
                        onClick={() => setTargetDeleteId(null)}
                    >
                        {t("Cancel")}
                    </Button>
                    <Button onClick={onDelete}>{t("Confirm")}</Button>
                </DialogFooter>
            </Modal>
        </Layout>
    );
};

export default HostelPersonInCharge;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
