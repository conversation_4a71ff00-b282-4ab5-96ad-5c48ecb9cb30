import FilterHostelRewardPunishmentSettingForm from "@/components/forms/hostel/FilterHostelRewardPunishmentSettingForm";
import HostelRewardPunishmentSettingForm from "@/components/forms/hostel/HostelRewardPunishmentSettingForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    hostelRewardPunishmentSettingsAPI,
} from "@/lib/constant";

const RewardPunishmentSettings = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="hostels/student-hostel-configuration/reward-punishment-settings"
        api={hostelRewardPunishmentSettingsAPI}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "hostel_merit_demerit_setting_id",
                    displayAs: "Merit/ Demerit",
                    hasSort: true,
                },
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "points",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      hostel_merit_demerit_setting_id:
                          item?.hostel_merit_demerit_setting?.name,
                      code: item?.code,
                      name: item?.name,
                      points: item?.points,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <HostelRewardPunishmentSettingForm {...params} />}
        filterForm={(params) => (
            <FilterHostelRewardPunishmentSettingForm {...params} />
        )}
        viewPermit="hostel-reward-punishment-setting-view"
        createPermit="hostel-reward-punishment-setting-create"
        updatePermit="hostel-reward-punishment-setting-update"
        deletePermit="hostel-reward-punishment-setting-delete"
    />
);

export default RewardPunishmentSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
