import { capitalize } from "lodash";
import FilterHostelMeritDemeritSettingForm from "@/components/forms/hostel/FilterHostelMeritDemeritSettingForm";
import HostelMeritDemeritSettingForm from "@/components/forms/hostel/HostelMeritDemeritSettingForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, hostelMeritDemeritSettingsAPI } from "@/lib/constant";
import { useTranslations } from "next-intl";

const MeritDemeritSettings = ({ locale }) => {
    const t = useTranslations("common");
    return (
        <CommonTablePageWrap
            locale={locale}
            path="hostels/student-hostel-configuration/merit-demerit-settings"
            api={hostelMeritDemeritSettingsAPI}
            definedColumn={() => {
                const _columns: TableColumnType[] = [
                    {
                        key: "type",
                        hasSort: true,
                        modify: (value) => capitalize(t(value)),
                    },
                    {
                        key: "name",
                        hasSort: true,
                    },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          type: item?.type,
                          name: item?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            form={(params) => <HostelMeritDemeritSettingForm {...params} />}
            filterForm={(params) => (
                <FilterHostelMeritDemeritSettingForm {...params} />
            )}
            viewPermit="hostel-merit-demerit-setting-view"
            createPermit="hostel-merit-demerit-setting-create"
            updatePermit="hostel-merit-demerit-setting-update"
            deletePermit="hostel-merit-demerit-setting-delete"
        />
    );
};

export default MeritDemeritSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
