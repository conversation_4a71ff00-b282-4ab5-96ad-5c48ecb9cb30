import { lowerCase } from "lodash";
import FilterHostelBedForm from "@/components/forms/hostel/FilterHostelBedForm";
import HostelBedForm from "@/components/forms/hostel/HostelBedForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    TableColumnType,
    ACTIVE,
    INACTIVE,
    hostelBedsAPI,
    STUDENT,
} from "@/lib/constant";
import { useTranslations } from "next-intl";

const Beds = ({ locale }) => {
    const t = useTranslations("common");

    return (
        <CommonTablePageWrap
            locale={locale}
            path="hostels/student-hostel-configuration/beds"
            heading={`${t("student ")}${t("hostel ")}${t("beds")}`}
            api={hostelBedsAPI}
            otherFilterParams={{
                hostel_block_type: STUDENT,
                includes: ["hostelRoom"],
            }}
            definedColumn={() => {
                const _columns: TableColumnType[] = [
                    {
                        key: "block",
                        hasSort: false,
                    },
                    {
                        key: "hostel_room",
                        displayAs: "room",
                        hasSort: true,
                    },
                    {
                        key: "name",
                        displayAs: "bed",
                        hasSort: true,
                    },
                    {
                        key: "is_active",
                        displayAs: "status",
                        hasSort: true,
                        modify: (value) => (
                            <div className={`cell-status ${value}`}>
                                {t(value)}
                            </div>
                        ),
                    },
                    {
                        key: "status",
                        displayAs: "bed availability",
                        hasSort: true,
                        modify: (value) => (
                            <div className={`cell-status ${value}`}>
                                {t(value)}
                            </div>
                        ),
                    },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => {
                          const { hostel_room, is_active, remarks, ...rest } =
                              item;
                          return {
                              block: hostel_room?.hostel_block?.name,
                              hostel_room: hostel_room?.name,
                              is_active: lowerCase(
                                  is_active ? ACTIVE : INACTIVE
                              ),
                              status: lowerCase(item?.status),
                              ...rest,
                          };
                      })
                    : [];
            }}
            orderBy={(name, direction) => {
                return name === "hostel_room"
                    ? { name: { [name]: direction } }
                    : { [name]: direction };
            }}
            form={(params) => (
                <HostelBedForm hostelType={STUDENT} {...params} />
            )}
            filterForm={(params) => (
                <FilterHostelBedForm type={STUDENT} {...params} />
            )}
            viewPermit="hostel-room-bed-view"
            createPermit="hostel-room-bed-create"
            updatePermit="hostel-room-bed-update"
            deletePermit="hostel-room-bed-delete"
        />
    );
};

export default Beds;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
