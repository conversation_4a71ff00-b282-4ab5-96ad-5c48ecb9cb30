import FilterHostelBlockForm from "@/components/forms/hostel/FilterHostelBlockForm";
import HostelBlockForm from "@/components/forms/hostel/HostelBlockForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { hostelBlocksAPI, TableColumnType, STUDENT } from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Blocks = ({ locale }) => {
    const t = useTranslations("common");

    return (
        <CommonTablePageWrap
            locale={locale}
            path="hostels/student-hostel-configuration/blocks"
            heading={`${t("student ")}${t("hostel ")}${t("blocks")}`}
            api={hostelBlocksAPI}
            otherFilterParams={{ type: STUDENT }}
            definedColumn={(activeLanguages) => {
                const _columns: TableColumnType[] = [
                    {
                        key: "code",
                        hasSort: false,
                    },
                    ...getNameColumns(activeLanguages),
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          code: item?.code,
                          ...item?.translations?.name,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { name: { [name]: direction } };
            }}
            form={(params) => (
                <HostelBlockForm hostelType={STUDENT} {...params} />
            )}
            filterForm={(params) => (
                <FilterHostelBlockForm type={STUDENT} {...params} />
            )}
            viewPermit="hostel-block-view"
            createPermit="hostel-block-create"
            updatePermit="hostel-block-update"
            deletePermit="hostel-block-delete"
        />
    );
};

export default Blocks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
