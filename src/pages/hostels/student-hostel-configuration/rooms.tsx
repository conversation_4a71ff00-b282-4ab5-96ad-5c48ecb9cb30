import { capitalize, lowerCase } from "lodash";
import FilterHostelRoomForm from "@/components/forms/hostel/FilterHostelRoomForm";
import HostelRoomForm from "@/components/forms/hostel/HostelRoomForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    hostelRoomsAPI,
    TableColumnType,
    ACTIVE,
    INACTIVE,
    STUDENT,
} from "@/lib/constant";
import { useTranslations } from "next-intl";

const Rooms = ({ locale }) => {
    const t = useTranslations("common");

    return (
        <CommonTablePageWrap
            locale={locale}
            path="hostels/student-hostel-configuration/rooms"
            heading={`${t("student ")}${t("hostel ")}${t("rooms")}`}
            api={hostelRoomsAPI}
            otherFilterParams={{ hostel_block_type: STUDENT }}
            definedColumn={() => {
                const _columns: TableColumnType[] = [
                    {
                        key: "hostel_block",
                        displayAs: "block",
                        hasSort: true,
                    },
                    {
                        key: "name",
                        displayAs: "room",
                        hasSort: true,
                    },
                    {
                        key: "gender",
                        hasSort: true,
                        modify: (value) => t(capitalize(value)),
                    },
                    {
                        key: "capacity",
                        hasSort: true,
                    },
                    {
                        key: "is_active",
                        displayAs: "status",
                        hasSort: true,
                        modify: (value) => (
                            <div className={`cell-status ${value}`}>
                                {t(value)}
                            </div>
                        ),
                    },
                ];
                return _columns;
            }}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => {
                          const {
                              hostel_block,
                              is_active,
                              remarks,
                              capacity,
                              ...rest
                          } = item;
                          return {
                              hostel_block: item?.hostel_block?.name,
                              capacity: item?.capacity ?? "-",
                              is_active: lowerCase(
                                  item.is_active ? ACTIVE : INACTIVE
                              ),
                              ...rest,
                          };
                      })
                    : [];
            }}
            orderBy={(name, direction) => {
                return name === "hostel_block"
                    ? { name: { [name]: direction } }
                    : { [name]: direction };
            }}
            form={(params) => (
                <HostelRoomForm hostelType={STUDENT} {...params} />
            )}
            filterForm={(params) => (
                <FilterHostelRoomForm type={STUDENT} {...params} />
            )}
            viewPermit="hostel-room-view"
            createPermit="hostel-room-create"
            updatePermit="hostel-room-update"
            deletePermit="hostel-room-delete"
        />
    );
};

export default Rooms;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
