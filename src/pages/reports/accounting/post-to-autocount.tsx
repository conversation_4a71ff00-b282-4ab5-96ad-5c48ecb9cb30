import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    accountingReportsPostToAutocountAPI,
    EXCEL,
    firstDayOfCurrentMonth,
    PDF,
    lastDayOfCurrentMonth,
    INVOICE,
    ADVANCE_INVOICE,
    CREDIT_NOTE,
    DEBIT_NOTE,
    FEES,
    WALLET,
    ECOMMERCE,
    HOSTEL_SAVINGS_ACCOUNT,
    ENROLLMENT_EXAM_FEES,
    OTHERS,
    DRAFT,
    CONFIRMED,
    VOIDED,
    POSTED,
    ENROLLMENT_FEES,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { downloadFile, showBackendFormError, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormInput from "@/components/ui/FormInput";
import { useTranslations } from "next-intl";

const PostToAutocount = ({ locale }) => {
    useCheckViewPermit("post-billing-documents-to-autocount");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            payment_date_from: toYMD(firstDayOfCurrentMonth),
            payment_date_to: toYMD(lastDayOfCurrentMonth),
            type: INVOICE,
            sub_type: FEES,
            status: CONFIRMED,
            reference_no: "",
        },
    });

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const { axiosPost: postToAutocount, error: postError } = useAxios({
        api: accountingReportsPostToAutocountAPI,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                console.log("submit", data);
                if (dateRange) {
                    data.payment_date_from = toYMD(dateRange.startDate);
                    data.payment_date_to = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            postToAutocount({
                ...data,
                export_type: type,
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const t = useTranslations("common");

    return (
        <Layout path="reports/accounting/post-to-autocount" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">{t("Post to Autocount")}</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label={t("Payment Date Range") + "*"}
                                range={dateRange}
                                setRange={setDateRange}
                                error={
                                    form.formState.errors?.payment_date_from
                                        ?.message ||
                                    form.formState.errors?.payment_date_to
                                        ?.message
                                }
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name={"type"}
                            isStringOptions={true}
                            options={[
                                INVOICE,
                                ADVANCE_INVOICE,
                                CREDIT_NOTE,
                                DEBIT_NOTE,
                            ]}
                        />

                        <FormSelect
                            control={form.control}
                            name={"sub_type"}
                            isStringOptions={true}
                            options={[
                                FEES,
                                WALLET,
                                ECOMMERCE,
                                HOSTEL_SAVINGS_ACCOUNT,
                                OTHERS,
                                ENROLLMENT_FEES,
                            ]}
                        />

                        <FormSelect
                            control={form.control}
                            name={"status"}
                            isStringOptions={true}
                            options={[DRAFT, CONFIRMED, VOIDED, POSTED]}
                        />

                        <FormInput
                            control={form.control}
                            name={"reference_no"}
                        />

                        <div className="flex flex-wrap gap-3 lg:col-span-3">
                            <Button onClick={() => download(EXCEL)}>
                                {t("Export Excel")}
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default PostToAutocount;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
