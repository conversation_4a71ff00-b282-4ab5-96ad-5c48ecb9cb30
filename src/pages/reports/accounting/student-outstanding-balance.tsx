import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    accountingReportsStudentOutstandingBalanceAPI,
    DATE_FORMAT,
    EXCEL,
    firstDayOfCurrentMonth,
    GET_ALL_PARAMS,
    gradeAPI,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    convertDateToYearMonth,
    convertYearMonthToDate,
    downloadFile,
    toYMD,
} from "@/lib/utils";
import FreeMonthPicker from "@/components/ui/FreeMonthPicker";

const AccountingStudentOutstandingBalance = ({ locale }) => {
    useCheckViewPermit("accounting-student-outstanding-balance-report");
    const today = new Date();

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            date_to: convertDateToYearMonth(firstDayOfCurrentMonth),
            semester_class_ids: [],
        },
    });

    const { data: grades, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: accountingReportsStudentOutstandingBalanceAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: accountingReportsStudentOutstandingBalanceAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGrades({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    function fetchSemesterClasses() {
        const gradeId = form.watch("grade_id");
        if (!gradeId) return;
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassPrimaryDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: gradeId,
            },
        });
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.date_to = toYMD(
                    convertYearMonthToDate(data.date_to, DATE_FORMAT.YMD)
                );
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        fetchSemesterClasses();
    }, [locale]);

    return (
        <Layout
            path="reports/accounting/student-outstanding-balance"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">Student Outstanding Balance</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FreeMonthPicker
                            control={form.control}
                            name="date_to"
                            label="month*"
                            error={form?.formState?.errors?.date_to}
                        />

                        <FormSelect
                            control={form.control}
                            name="grade_id"
                            label="Grade*"
                            options={grades}
                            isSortByName={false}
                            onChange={(gradeId) => {
                                form.setValue("semester_class_id", "");
                                fetchSemesterClasses();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() =>
                                form.setValue("semester_class_ids", "")
                            }
                        />

                        {form.watch("grade_id") && (
                            <div className="lg:col-span-2">
                                <FormSelect
                                    isMulti={true}
                                    hasSelectAll={true}
                                    control={form.control}
                                    name={"semester_class_ids"}
                                    label="Classes*"
                                    isDisabled={!form.watch("grade_id")}
                                    options={
                                        semesterClassOptions?.map((option) => ({
                                            id: option?.id,
                                            name: `${option?.class_model?.name}`,
                                        })) ?? []
                                    }
                                />
                            </div>
                        )}
                        <div className="flex flex-wrap gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>

                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>

                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default AccountingStudentOutstandingBalance;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
