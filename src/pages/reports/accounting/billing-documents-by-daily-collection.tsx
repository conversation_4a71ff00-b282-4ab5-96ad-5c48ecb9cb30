import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    firstDayOfCurrentMonth,
    GET_ALL_PARAMS,
    PDF,
    accountingProductAPI,
    lastDayOfCurrentMonth,
    accountingReportBillingDocumentByDailyCollectionAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";

const BillingDocumentsByDailyCollection = ({ locale }) => {
    useCheckViewPermit("billing-document-by-daily-collection-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            payment_date_from: toYMD(firstDayOfCurrentMonth),
            payment_date_to: toYMD(lastDayOfCurrentMonth),
            product_ids: [],
        },
    });

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: accountingReportBillingDocumentByDailyCollectionAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const {
        data: accountingProductOptions,
        axiosQuery: getAccountingProductOptions,
    } = useAxios({
        api: accountingProductAPI,
        locale,
    });

    useEffect(() => {
        getAccountingProductOptions({
            params: { ...GET_ALL_PARAMS, is_active: 1 },
        });
    }, [locale]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                console.log("submit", data);
                if (dateRange) {
                    data.payment_date_from = toYMD(dateRange.startDate);
                    data.payment_date_to = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    return (
        <Layout
            path="reports/accounting/billing-documents-by-daily-collection"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">
                    Billing Documents - By Daily Collection
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Payment Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                                error={
                                    form.formState.errors?.payment_date_from
                                        ?.message ||
                                    form.formState.errors?.payment_date_to
                                        ?.message
                                }
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name="product_ids"
                            label="Products"
                            isMulti={true}
                            options={accountingProductOptions}
                        />

                        <div className="flex flex-wrap gap-3 lg:col-span-3">
                            <Button onClick={() => download(EXCEL)}>
                                Export Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default BillingDocumentsByDailyCollection;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
