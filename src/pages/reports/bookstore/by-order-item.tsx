import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    appCurrencySymbol,
    bookstoreReportsByOrderItemAPI,
    EXCEL,
    firstDayOfCurrentMonth,
    GET_ALL_PARAMS,
    gradeAPI,
    lastDayOfCurrentMonth,
    PDF,
    semesterSettingAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, formatNumberForRead, toYMD } from "@/lib/utils";

const BookstoreReportByOrderItem = ({ locale }) => {
    useCheckViewPermit("ecommerce-order-items-bookshops-report");
    const { activeLanguages } = useLanguages();

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            grade_id: "",
            start_date: firstDayOfCurrentMonth,
            end_date: lastDayOfCurrentMonth,
        },
    });

    const [total, setTotal] = useState<string | null>(null);
    const [reports, setReports] = useState<any[]>([]);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "product_name",
        },
        {
            key: "product_unit_price",
            displayAs: `Unit Price (${appCurrencySymbol})`,
        },
        {
            key: "total_quantity",
        },
        {
            key: "total",
            displayAs: `Total (${appCurrencySymbol})`,
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: bookstoreReportsByOrderItemAPI,
        locale,
        onSuccess(result) {
            if (!result.data?.items) {
                setReports([]);
                return;
            }
            setTotal(result.data.total);

            const _reports = Object.entries(result.data.items).map(
                ([id, item]: any) => {
                    return {
                        id,
                        ...item,
                    };
                }
            );
            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: bookstoreReportsByOrderItemAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: bookstoreReportsByOrderItemAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.start_date = toYMD(data.start_date);
                data.end_date = toYMD(data.end_date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/bookstore/by-order-item" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Bookstore Report by Order Item</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester"
                            options={semesterOptions}
                            isSortByName={false}
                        />
                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            options={gradeOptions}
                            isSortByName={false}
                        />

                        <DatePicker
                            control={form.control}
                            name="start_date"
                            label="start date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="end_date"
                            label="end date*"
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>

                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <div className="overflow-auto">
                            <DataTable
                                columns={columns}
                                data={reports}
                                extraRow={[
                                    {
                                        value: (
                                            <span className="font-medium text-themeLabel">
                                                TOTAL
                                            </span>
                                        ),
                                        colSpan: 3,
                                    },

                                    { value: formatNumberForRead(total) },
                                ]}
                            />
                        </div>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default BookstoreReportByOrderItem;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
