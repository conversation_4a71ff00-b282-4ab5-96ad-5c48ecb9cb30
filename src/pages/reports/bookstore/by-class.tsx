import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ReceiptDetails from "@/components/forms/report/ReceiptDetails";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import {
    bookstoreReportsByClassAPI,
    DATE_FORMAT,
    EXCEL,
    firstDayOfCurrentMonth,
    generateMonthOptionsWithYMDDateValue,
    GET_ALL_PARAMS,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { displayDateTime, downloadFile, toYMD } from "@/lib/utils";

const BookstoreReportByClass = ({ locale }) => {
    useCheckViewPermit("ecommerce-bookshops-classes-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            semester_class_id: "",
            date: toYMD(firstDayOfCurrentMonth),
        },
    });

    const [reports, setReports] = useState<any[]>([]);
    const [targetId, setTargetId] = useState(null);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "student_number",
        },
        {
            key: "student_name",
        },
        {
            key: "purchase_date",
            modify(value) {
                return <span className="text-[14px]">{value}</span>;
            },
        },
        {
            key: "billing_document_reference_number",
            displayAs: "Receipt No",
        },
        {
            key: "items_names",
            modify(list) {
                return (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                );
            },
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: bookstoreReportsByClassAPI,
        locale,
        onSuccess(result) {
            if (!result.data) {
                setReports([]);
                return;
            }
            const _reports = Object.entries(result.data).map(
                ([id, report]: any) => ({
                    id,
                    ...report,
                    purchase_date: displayDateTime(
                        report?.created_at,
                        DATE_FORMAT.forDisplay
                    ),
                    items_names:
                        (report?.items &&
                            Object.values(report?.items)?.map(
                                (item: any) =>
                                    `${item?.product_name} ${item?.quantity}`
                            )) ??
                        [],
                })
            );
            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: bookstoreReportsByClassAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: bookstoreReportsByClassAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.date = toYMD(data.date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/bookstore/by-class" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Bookstore Report by Class</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="date"
                            label="month*"
                            isSortByName={false}
                            options={generateMonthOptionsWithYMDDateValue()}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() =>
                                form.setValue("semester_class_id", "")
                            }
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="semester class*"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <div className="overflow-auto">
                            <div className="report-table-title">
                                {
                                    semesterClassOptions.find(
                                        (item) =>
                                            item.id ==
                                            form.watch("semester_class_id")
                                    )?.class_model?.name
                                }
                            </div>
                            <DataTable
                                columns={columns}
                                data={reports}
                                actionMenu={({ cell }) => (
                                    <>
                                        <ActionDropdown>
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() => {
                                                    setTargetId(
                                                        cell.row.original.id
                                                    );
                                                }}
                                            >
                                                View
                                            </DropdownMenuItem>
                                        </ActionDropdown>
                                    </>
                                )}
                            />
                        </div>
                    )
                ) : null}
            </Card>
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <ReceiptDetails
                    currentData={reports?.find(
                        (item: any) => item.id === targetId
                    )}
                />
            </Modal>
        </Layout>
    );
};

export default BookstoreReportByClass;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
