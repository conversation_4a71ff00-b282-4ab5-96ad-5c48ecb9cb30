import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import InfoCard from "@/components/ui/InfoCard";
import Modal from "@/components/ui/Modal";
import {
    appCurrencySymbol,
    BOOKSHOP,
    bookstoreReportsByOrderAPI,
    DATE_FORMAT,
    EXCEL,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    PDF,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { displayDateTime, downloadFile, toYMD } from "@/lib/utils";

const BookstoreReportByOrder = ({ locale }) => {
    useCheckViewPermit("ecommerce-bookshops-orders-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            merchant_type: BOOKSHOP,
            from_date: firstDayOfCurrentMonth,
            to_date: lastDayOfCurrentMonth,
        },
    });

    const [reports, setReports] = useState<any[]>([]);
    const [targetId, setTargetId] = useState(null);
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "reference_no",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "order_reference_number",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "buyer_name",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "buyer_number",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "class_name",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "total_amount",
            displayAs: `Total Amount (${appCurrencySymbol})`,
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "created_at",
            displayAs: "Date",
            modify: (value) => (
                <span className="text-[14px]">{value ?? "-"}</span>
            ),
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: bookstoreReportsByOrderAPI,
        locale,
        onSuccess(result) {
            const _reports =
                result.data?.map((report) => ({
                    ...report,
                    created_at: displayDateTime(
                        report?.created_at,
                        DATE_FORMAT.forDisplay
                    ),
                })) ?? [];

            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: bookstoreReportsByOrderAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: bookstoreReportsByOrderAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.from_date = toYMD(data.from_date);
                data.to_date = toYMD(data.to_date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout path="reports/bookstore/by-order" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Bookstore Report by Order</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="from_date"
                            label="start date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="to_date"
                            label="end date*"
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>

                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <div className="overflow-auto">
                            <DataTable
                                columns={columns}
                                data={reports}
                                actionMenu={({ cell }) => (
                                    <>
                                        <ActionDropdown>
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() => {
                                                    setTargetId(
                                                        cell.row.original.id
                                                    );
                                                }}
                                            >
                                                View
                                            </DropdownMenuItem>
                                        </ActionDropdown>
                                    </>
                                )}
                            />
                        </div>
                    )
                ) : null}
            </Card>
            <Modal open={targetId} onOpenChange={setTargetId}>
                <OrderDetails
                    data={reports?.find((item: any) => item.id === targetId)}
                />
            </Modal>
        </Layout>
    );
};

const OrderDetails = ({ data }) => {
    const info: any = {
        reference_no: data?.reference_no,
        order_reference_no: data?.order_reference_no,
        buyer_name: data?.buyer_name,
        class_name: data?.class_name,
        payment_method_name: data?.payment_method_name,
        total_amount: appCurrencySymbol + " " + data?.total_amount,
        date: data?.created_at,
    };
    return (
        <div>
            <div className="mb-3 font-semibold text-themeGreenDark lg:text-lg">
                Order Details
            </div>

            <InfoCard data={info} noBorder />
        </div>
    );
};

export default BookstoreReportByOrder;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
