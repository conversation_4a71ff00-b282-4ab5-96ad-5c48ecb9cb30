import { useState } from "react";
import clsx from "clsx";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import ReceiptDetails from "@/components/forms/report/ReceiptDetails";
import Card from "@/components/ui/Card";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    bookstoreReportsByStudentAPI,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    PDF,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";

const BookstoreReportByStudents = ({ locale }) => {
    useCheckViewPermit("ecommerce-bookshops-students-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            student_ids: [],
            start_date: firstDayOfCurrentMonth,
            end_date: lastDayOfCurrentMonth,
        },
    });

    const [reports, setReports] = useState<any[]>([]);
    const [targetId, setTargetId] = useState(null);
    const [openSearch, setOpenSearch] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "student_number",
        },
        {
            key: "student_name",
        },
        {
            key: "purchase_date",
            modify(value) {
                return <span className="text-[14px]">{value}</span>;
            },
        },
        {
            key: "order_reference_number",
        },
        {
            key: "items_names",
            modify(list) {
                return (
                    <ul>
                        {list?.length > 0
                            ? list.map((item, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{item}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                );
            },
        },
    ];

    const { axiosQuery: getReportsUrl } = useAxios({
        api: bookstoreReportsByStudentAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: bookstoreReportsByStudentAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.start_date = toYMD(data.start_date);
                data.end_date = toYMD(data.end_date);
                query(data);
            })
        );
    }

    function download() {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/bookstore/by-students" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Bookstore Report by Students</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <div>
                            <div className="c-text-size mb-1.5 font-medium capitalize text-themeLabel">
                                Students*
                            </div>
                            <div
                                className={clsx(
                                    "c-text-size min-h-[42px] rounded-md border border-input py-2.5 pl-3.5 pr-2",
                                    form.watch("student_ids")?.length > 0
                                        ? ""
                                        : "text-themeGray3"
                                )}
                                onClick={() => setOpenSearch(true)}
                            >
                                {form.watch("student_ids")?.length > 0
                                    ? `${form.watch("student_ids").length} student${form.watch("student_ids").length > 1 ? "s" : ""} selected`
                                    : "Select Student"}
                            </div>
                        </div>

                        <DatePicker
                            control={form.control}
                            name="start_date"
                            label="start date*"
                        />
                        <DatePicker
                            control={form.control}
                            name="end_date"
                            label="end date*"
                        />

                        <div className="flex items-start gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button onClick={download} variant={"outline"}>
                                Download Report PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>

            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(students) => {
                        form.setValue(
                            "student_ids",
                            students.map((student) => student?.id)
                        );
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>

            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <ReceiptDetails
                    currentData={reports?.find(
                        (item: any) => item.id === targetId
                    )}
                />
            </Modal>
        </Layout>
    );
};

export default BookstoreReportByStudents;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
