import { useEffect, useState } from "react";
import React from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import {
    currentMonth,
    EXCEL,
    PDF,
    TableColumnType,
    semesterSettingAPI,
    GET_ALL_PARAMS,
    libraryReportBySchoolRateOfBorrowAPI,
    monthOptions,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, getFilteredMonthOptions } from "@/lib/utils";

const LibraryReportByRateOfBorrow = ({ locale }) => {
    useCheckViewPermit("library-school-rate-borrow-books-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [reports, setReports] = useState<any[]>([]);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [filteredMonthOptions, setFilterMonthOptions] = useState<any[]>([]);
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            filter_type: "MONTHLY",
            month: currentMonth,
            semester_setting_id: "",
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);

                const filteredMonthOptions = getFilteredMonthOptions(
                    currentSemester.from,
                    currentSemester.to
                );

                setFilterMonthOptions(filteredMonthOptions);
            }
        },
    });

    const generateColumns = (data: any) => {
        const languageColumns = data.book_languages?.map((lang: any) => ({
            key: lang.name,
            displayAs: lang.name,
        }));

        return [
            { key: "class_name", displayAs: "Class" },
            ...languageColumns,
            { key: "total_book_loans" },
        ];
    };

    const preprocessReports = (data) => {
        if (!data.classes || !data.book_languages) return [];

        const languageNames = data.book_languages.map((lang) => lang.name);

        const reports = data.classes.map((classItem) => {
            const languageLoans = languageNames.reduce(
                (acc, lang) => ({ ...acc, [lang]: 0 }),
                {}
            );

            classItem.book_languages.forEach((lang) => {
                languageLoans[lang.name] = lang.total_book_loans;
            });

            return {
                ...classItem,
                ...languageLoans,
            };
        });

        return reports;
    };

    const { axiosQuery: getReports } = useAxios({
        api: libraryReportBySchoolRateOfBorrowAPI,
        locale,
        onSuccess(result) {
            setHasSearched(true);

            if (!result.data) {
                setReports([]);
                return;
            }

            const transformedReports = preprocessReports(result.data);
            setColumns(generateColumns(result.data));
            setReports(transformedReports);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: libraryReportBySchoolRateOfBorrowAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: libraryReportBySchoolRateOfBorrowAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (data.filter_type == "SEMESTER") {
                    delete data.month;
                }
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            const selectedSemester = semesterOptions.find(
                (semester) => semester.id === form.watch("semester_setting_id")
            );

            if (selectedSemester) {
                const { from, to } = selectedSemester;
                const filteredMonthOptions = getFilteredMonthOptions(from, to);
                setFilterMonthOptions(filteredMonthOptions);
            }
        }
    }, [form.watch("semester_setting_id"), locale]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    const filterBy = form.watch("filter_type");

    return (
        <Layout path="reports/library/by-school-rate-of-borrow" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Library Report by School Rate of Borrow
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("month", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="filter_type"
                            label="Filter By*"
                            isStringOptions={true}
                            options={["SEMESTER", "MONTHLY"]}
                        />

                        {filterBy === "MONTHLY" && (
                            <FormSelect
                                control={form.control}
                                name={"month"}
                                label="Month*"
                                options={monthOptions}
                                isSortByName={false}
                                isDisabled={filterBy !== "MONTHLY"}
                            />
                        )}

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <DataTable columns={columns} data={reports} />
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default LibraryReportByRateOfBorrow;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
