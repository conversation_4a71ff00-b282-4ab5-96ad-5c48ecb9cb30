import { useEffect, useState } from "react";
import React from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    firstDayOfCurrentMonth,
    GET_ALL_PARAMS,
    gradeAPI,
    lastDayOfCurrentMonth,
    libraryReportByTopBorrowerAPI,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";

const LibraryReportByTopBorrower = ({ locale }) => {
    useCheckViewPermit("library-top-borrowers-report");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            filter_by: "SEMESTER_CLASS",
            grade_ids: [],
            semester_class_id: "",
            period_loan_date_from: firstDayOfCurrentMonth,
            period_loan_date_to: lastDayOfCurrentMonth,
        },
    });

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [reports, setReports] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const preprocessReports = (data: any) => {
        if (!data.students || !data.book_languages) return [];

        return data.students.map((student: any) => {
            const languageBookLoans = data.book_languages.reduce(
                (acc: any, lang: any) => {
                    acc[lang.name] = 0;
                    return acc;
                },
                {}
            );

            student.book_languages.forEach((lang: any) => {
                languageBookLoans[lang.name] = lang.total_book_loans;
            });

            return { ...student, ...languageBookLoans };
        });
    };

    const generateColumns = (data: any) => {
        const baseColumns: TableColumnType[] = [
            { key: "student_number" },
            { key: "student_name" },
            { key: "class_name", displayAs: "Class" },
        ];

        const languageColumns = data.book_languages?.map((lang: any) => ({
            key: lang.name,
            displayAs: lang.name,
        }));

        return [
            ...baseColumns,
            ...languageColumns,
            { key: "total_book_loans" },
        ];
    };

    const { axiosQuery: getReports } = useAxios({
        api: libraryReportByTopBorrowerAPI,
        locale,
        onSuccess(result) {
            if (!result.data) {
                setReports([]);
                return;
            }

            const transformedReports = preprocessReports(result.data);
            setColumns(generateColumns(result.data));
            setReports(transformedReports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: libraryReportByTopBorrowerAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: libraryReportByTopBorrowerAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (data.filter_by == "GRADE") {
                    delete data.semester_class_id;
                } else if (data.filter_by == "SEMESTER_CLASS") {
                    delete data.grade_ids;
                }
                data.period_loan_date_from = toYMD(data.period_loan_date_from);
                data.period_loan_date_to = toYMD(data.period_loan_date_to);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    const filterBy = form.watch("filter_by");

    return (
        <Layout path="reports/library/by-top-borrower" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Library Report by Top Borrower</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="period_loan_date_from"
                            label="Borrowing Start Date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="period_loan_date_to"
                            label="Borrowing End date*"
                        />

                        <FormSelect
                            control={form.control}
                            name="filter_by"
                            label="Filter By*"
                            isStringOptions={true}
                            options={["GRADE", "SEMESTER_CLASS"]}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        {filterBy === "GRADE" && (
                            <FormSelect
                                isMulti={true}
                                control={form.control}
                                name={"grade_ids"}
                                label="Grade*"
                                options={gradeOptions}
                                isSortByName={false}
                            />
                        )}

                        {filterBy === "SEMESTER_CLASS" && (
                            <FormSelect
                                control={form.control}
                                name={"semester_class_id"}
                                label="Class*"
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                                isDisabled={filterBy !== "SEMESTER_CLASS"}
                            />
                        )}

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <>
                            <div className="flex items-center gap-2 bg-themeGreen3 px-2 py-2">
                                <div className="c-text-size mr-auto font-semibold text-themeGreen2">
                                    Top Borrower (
                                    {toYMD(form.watch("period_loan_date_from"))}{" "}
                                    to{" "}
                                    {toYMD(form.watch("period_loan_date_to"))})
                                </div>
                            </div>
                            <DataTable columns={columns} data={reports} />
                        </>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default LibraryReportByTopBorrower;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
