import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    bookCategoryAPI,
    BORROWED,
    CLASS_NAME,
    EMPLOYEE,
    GET_ALL_PARAMS,
    libraryReportByBorrowReportAPI,
    LOAN_DATE,
    MEMBER_NUMBER,
    NAME,
    OTHERS,
    PDF,
    RETURNED,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    STUDENT,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { toYMD } from "@/lib/utils";

const LibraryReportByBorrowedReport = ({ locale }) => {
    useCheckViewPermit("library-book-borrow-records-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const today = new Date();
    const weekAfterToday = new Date(today);
    weekAfterToday.setDate(weekAfterToday.getDate() + 7);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            loan_statuses: [BORROWED],
            member_type: STUDENT,
            order_by: LOAN_DATE,
            period_loan_date_from: today,
            period_loan_date_to: weekAfterToday,
            semester_class_id: "",
            book_category_id: "",
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: libraryReportByBorrowReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.order_by = data?.order_by?.toLowerCase();
                data.period_loan_date_from = toYMD(data.period_loan_date_from);
                data.period_loan_date_to = toYMD(data.period_loan_date_to);

                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: bookCategoryOptions, axiosQuery: getBookCategoryOptions } =
        useAxios({
            api: bookCategoryAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getBookCategoryOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    return (
        <Layout path="reports/library/by-borrowed-report" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Library Report by Borrowed Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="member_type"
                            label="Member Type*"
                            isStringOptions={true}
                            options={[STUDENT, EMPLOYEE, OTHERS]}
                        />

                        <FormSelect
                            control={form.control}
                            name="order_by"
                            label="Order By*"
                            isStringOptions={true}
                            options={[
                                LOAN_DATE,
                                MEMBER_NUMBER,
                                CLASS_NAME,
                                NAME,
                            ]}
                        />

                        <DatePicker
                            control={form.control}
                            name="period_loan_date_from"
                            label="Borrowing Start Date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="period_loan_date_to"
                            label="Borrowing End date*"
                        />

                        {/* TODO: pending to confirm */}
                        {/* <FormCheckbox
                            control={form.control}
                            name="include_returned"
                            label="Include those have returned book(s)"
                            textStyleClass="text-gray-500 font-medium"
                        /> */}

                        <FormSelect
                            isMulti={true}
                            hasSelectAll={true}
                            control={form.control}
                            name={"loan_statuses"}
                            label="Book Loan Status"
                            options={[BORROWED, RETURNED]}
                        />

                        <FormSelect
                            control={form.control}
                            name={"book_category_id"}
                            label="Book Category"
                            options={bookCategoryOptions}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="Class"
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default LibraryReportByBorrowedReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
