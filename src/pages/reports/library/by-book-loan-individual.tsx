import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import BookSearchEngine from "@/components/ui/search-engines/BookSearchEngine";
import {
    currentYear,
    EXCEL,
    PDF,
    getYearOptions,
    TableColumnType,
    libraryReportByBookLoanIndividualAPI,
    DATE_FORMAT,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { displayDateTime, downloadFile } from "@/lib/utils";

const LibraryReportByBookLoanIndividual = ({ locale }) => {
    useCheckViewPermit("library-book-loans-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [reports, setReports] = useState<any[]>([]);
    const [openSearch, setOpenSearch] = useState(false);
    const [selectedBook, setSelectedBook] = useState<any>(null);
    const [hasSearched, setHasSearched] = useState(false);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            year: currentYear,
            book_id: "",
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "member_number",
        },
        {
            key: "member_name",
        },
        {
            key: "class_name",
            displayAs: "Class",
        },
        {
            key: "book_call_number",
            displayAs: "Book Number (书号)",
        },
        {
            key: "book_title",
            displayAs: "Title",
        },
        {
            key: "loan_date",
        },
        {
            key: "due_date",
        },
        {
            key: "return_date",
        },
        {
            key: "balance",
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: libraryReportByBookLoanIndividualAPI,
        locale,
        onSuccess(result) {
            setHasSearched(true);
            if (!result.data) {
                setReports([]);
                return;
            }

            const mappedData = result.data.book_loans.map((loan) => {
                return {
                    member_number: loan.member_number,
                    member_name: loan.member_name,
                    class_name: loan.class_name ? `${loan.class_name}` : "N/A",
                    book_call_number: result.data.book_call_number,
                    book_title: result.data.book_title,
                    loan_date: displayDateTime(
                        loan?.loan_date,
                        DATE_FORMAT.forDisplay
                    ),
                    due_date: displayDateTime(
                        loan?.due_date,
                        DATE_FORMAT.forDisplay
                    ),
                    return_date:
                        displayDateTime(
                            loan?.return_date,
                            DATE_FORMAT.forDisplay
                        ) || "-",
                    balance: loan.balance,
                };
            });
            setReports(mappedData);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: libraryReportByBookLoanIndividualAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.order_by = { loan_date: "desc" };
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout path="reports/library/by-book-loan-individual" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Library Report by Book Loan</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"year"}
                            label="Year"
                            options={getYearOptions()}
                            isSortByName={false}
                        />

                        <div>
                            <div className="c-text-size mb-1.5 font-medium capitalize text-themeLabel">
                                Book*
                            </div>
                            {
                                <div
                                    className={clsx(
                                        "c-text-size min-h-[42px] rounded-md border border-input px-4 py-2.5",
                                        !selectedBook && "text-themeGray3"
                                    )}
                                    onClick={() => setOpenSearch(true)}
                                >
                                    {selectedBook
                                        ? selectedBook?.title
                                        : "Select Book"}
                                </div>
                            }
                        </div>

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>

                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <DataTable columns={columns} data={reports} />
                    )
                ) : null}
            </Card>

            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <BookSearchEngine
                    setSelection={(book) => {
                        if (book) {
                            form.setValue("book_id", book.id);
                            setSelectedBook(book);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </Layout>
    );
};

export default LibraryReportByBookLoanIndividual;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
