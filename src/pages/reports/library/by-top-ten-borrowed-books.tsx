import { useEffect, useState } from "react";
import React from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import {
    bookLanguagesAPI,
    currentYear,
    currentMonth,
    EXCEL,
    GET_ALL_PARAMS,
    monthOptions,
    PDF,
    getYearOptions,
    TableColumnType,
    libraryReportByTopTenBorrowedBooksAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const LibraryReportByTopTenBorrowedBooks = ({ locale }) => {
    useCheckViewPermit("library-top-borrowed-books-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [reports, setReports] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            filter_by: "MONTHLY",
            year: currentYear,
            month: currentMonth,
            book_language_id: "",
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "book_title",
            displayAs: "title",
        },
        {
            key: "call_number",
        },
        {
            key: "book_classification_name",
            displayAs: "Book Classification",
        },
        {
            key: "book_loans_count",
            displayAs: "Rate of Borrow",
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: libraryReportByTopTenBorrowedBooksAPI,
        locale,
        onSuccess(result) {
            setHasSearched(true);

            if (!result.data) {
                setReports([]);
                return;
            }
            setReports(result.data);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: libraryReportByTopTenBorrowedBooksAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: libraryReportByTopTenBorrowedBooksAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { data: bookLanguageOptions, axiosQuery: getBookLanguageOptions } =
        useAxios({
            api: bookLanguagesAPI,
            locale,
        });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (data.filter_by == "YEARLY") {
                    delete data.month;
                }
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        getBookLanguageOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Layout
            path="reports/library/by-top-ten-borrowed-books"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Library Report by Top Ten Borrowed Books
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="book_language_id"
                            label="Book Language*"
                            options={bookLanguageOptions}
                        />

                        <FormSelect
                            control={form.control}
                            name="filter_by"
                            label="Filter By*"
                            isStringOptions={true}
                            options={["YEARLY", "MONTHLY"]}
                        />

                        <FormSelect
                            control={form.control}
                            name={"year"}
                            label="Year*"
                            options={getYearOptions()}
                            isSortByName={false}
                        />

                        {form.watch("filter_by") === "MONTHLY" && (
                            <FormSelect
                                control={form.control}
                                name={"month"}
                                label="Month*"
                                options={monthOptions}
                                isSortByName={false}
                                isDisabled={
                                    form.watch("filter_by") !== "MONTHLY"
                                }
                            />
                        )}

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <>
                            <div className="flex items-center gap-2 bg-themeGreen3 px-2 py-2">
                                <div className="c-text-size mr-auto font-semibold text-themeGreen2">
                                    Top Ten Books Borrowed in{" "}
                                    {form.watch("month") &&
                                    form.watch("filter_by") === "MONTHLY"
                                        ? `${monthOptions.find((x) => x.id === parseInt(form.watch("month")))?.name + " " || ""}`
                                        : ""}
                                    {form.watch("year")}{" "}
                                </div>
                            </div>
                            <DataTable columns={columns} data={reports} />
                        </>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default LibraryReportByTopTenBorrowedBooks;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
