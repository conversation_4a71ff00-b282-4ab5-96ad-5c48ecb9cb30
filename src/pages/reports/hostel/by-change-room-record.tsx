import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    hostelReportsByChangeRoomRecordAPI,
    PDF,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";

const HostelReportByChangeRoomRecord = ({ locale }) => {
    useCheckViewPermit("hostel-by-change-room-record-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [yearOptions, setYearOptions] = useState<any[]>([]);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            year: "",
            semester_setting_id: "",
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: hostelReportsByChangeRoomRecordAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const currentYear = new Date().getFullYear();
        const years = Array.from({ length: 10 }, (_, i) =>
            (currentYear - i).toString()
        );
        setYearOptions(years);

        if (currentYear) {
            form.setValue("year", currentYear.toString());
        }

        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    return (
        <Layout path="reports/hostel/by-change-room-record" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Hostel Report by Change Room Record</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                        />

                        <FormSelect
                            control={form.control}
                            name="year"
                            label="Change Room Year*"
                            isStringOptions={true}
                            options={yearOptions}
                        />

                        <div className="lg:col-span-3">
                            <Button type="submit" onClick={preview}>
                                Preview PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByChangeRoomRecord;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
