import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import { EXCEL, hostelReportByEmployeeLodgingAPI } from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const HostelReportByEmployeeLodging = ({ locale }) => {
    useCheckViewPermit("hostel-by-employee-lodging-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: hostelReportByEmployeeLodgingAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    return (
        <Layout path="reports/hostel/by-employee-lodging" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Hostel Report by Employee Lodging</h2>

                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <div className="lg:col-span-3">
                            <div className="c-text-size min-h-[42px] max-w-[400px]">
                                <FormSelect
                                    control={form.control}
                                    name={"report_language"}
                                    label="report language*"
                                    options={activeLanguages?.map(
                                        (language) => ({
                                            id: language?.code,
                                            name: language?.name,
                                        })
                                    )}
                                />
                            </div>
                        </div>

                        <div className="lg:col-span-3">
                            <Button onClick={() => download(EXCEL)}>
                                Export Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByEmployeeLodging;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
