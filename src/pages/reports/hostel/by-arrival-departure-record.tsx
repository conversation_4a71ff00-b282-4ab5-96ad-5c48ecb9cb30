import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import { DatePicker } from "@/components/ui/DatePicker";
import FormRadioGroup from "@/components/ui/FormRadioGroup";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    HOME,
    hostelReportByBoardersGoHomeOrOutAPI,
    LEAVE_SCHOOL,
    OUTING,
    PDF,
    RETURNED_HOSTEL,
    semesterSettingAPI,
    YET_RETURN,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { toYMD } from "@/lib/utils";

const HostelReportByArrivalDepartureRecord = ({ locale }) => {
    useCheckViewPermit("hostel-by-boarders-go-home-or-out-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            in_out_type: HOME,
            date_from: "",
            date_to: "",
            reason: YET_RETURN,
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );
            form.setValue("date_from", currentSemester.from);
            form.setValue("date_to", currentSemester.to);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: hostelReportByBoardersGoHomeOrOutAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.date_from = toYMD(data.date_from);
                data.date_to = toYMD(data.date_to);

                if (data.reason === LEAVE_SCHOOL) {
                    data.leave_school = 1;
                } else if (data.reason === RETURNED_HOSTEL) {
                    data.returned = 1;
                } else if (data.reason === YET_RETURN) {
                    data.yet_return = 1;
                }

                delete data.reason;
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Layout
            path="reports/hostel/by-arrival-departure-record"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Hostel Report by Arrival/Departure Record
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="in_out_type"
                            label="Type*"
                            isStringOptions={true}
                            options={[HOME, OUTING]}
                        />

                        <DatePicker
                            control={form.control}
                            name="date_from"
                            label="start date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="date_to"
                            label="end date*"
                        />

                        <div className="flex gap-2 lg:col-span-4">
                            <FormRadioGroup
                                control={form.control}
                                name="reason"
                                hasLabel={false}
                                isStringOptions={true}
                                isHorizontal={true}
                                options={[
                                    LEAVE_SCHOOL,
                                    RETURNED_HOSTEL,
                                    YET_RETURN,
                                ]}
                            />
                        </div>

                        <div className="flex gap-2 lg:col-span-4">
                            <Button onClick={preview}>Preview PDF</Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByArrivalDepartureRecord;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
