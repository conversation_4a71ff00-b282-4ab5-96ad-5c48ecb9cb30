import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    PDF,
    GET_ALL_PARAMS,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    STUDENT,
    hostelBlocksAPI,
    hostelReportRewardPunishmentByBlockAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";

const HostelRewardPunishmentReportByBlock = ({ locale }) => {
    useCheckViewPermit("hostel-report-reward-punishment-by-block");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            block_id: "",
            date_from: "",
            date_to: "",
        },
    });

    const { data: hostelBlocks, axiosQuery: getHostelBlocks } = useAxios({
        api: hostelBlocksAPI,
        locale,
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: hostelReportRewardPunishmentByBlockAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: hostelReportRewardPunishmentByBlockAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.date_from = toYMD(dateRange.startDate);
                    data.date_to = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    useEffect(() => {
        getHostelBlocks({
            params: {
                ...GET_ALL_PARAMS,
                type: STUDENT,
            },
        });
    }, [locale]);

    return (
        <Layout
            path="reports/hostel/reward-punishment/by-block"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Hostel Reward/Punishment Report by Block
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name="block_id"
                            label="Block*"
                            options={hostelBlocks}
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelRewardPunishmentReportByBlock;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
