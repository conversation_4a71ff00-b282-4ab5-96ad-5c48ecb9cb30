import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    gradeAPI,
    hostelReportsByBoardersNameListAPI,
    PDF,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const HostelReportByBoardersNameList = ({ locale }) => {
    useCheckViewPermit("hostel-by-boarders-name-list-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            filter_by: "ALL",
            semester_class_id: "",
            grade_id: "",
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: hostelReportsByBoardersNameListAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: hostelReportsByBoardersNameListAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { initLoader } = useSubmit();

    function download(type: typeof EXCEL | typeof PDF) {
        initLoader(
            form.handleSubmit((data) => {
                if (data.filter_by == "GRADE") {
                    delete data.semester_class_id;
                } else if (data.filter_by == "CLASS") {
                    delete data.grade_id;
                } else {
                    delete data.semester_class_id;
                    delete data.grade_id;
                }

                delete data.filter_by;

                getReportsUrl({
                    params: { ...data, export_type: type },
                });
            })
        );
    }

    function preview() {
        initLoader(
            form.handleSubmit((data) => {
                if (data.filter_by == "GRADE") {
                    delete data.semester_class_id;
                } else if (data.filter_by == "CLASS") {
                    delete data.grade_id;
                } else {
                    delete data.semester_class_id;
                    delete data.grade_id;
                }

                delete data.filter_by;

                getReportsUrlForPreview({
                    params: { ...data, export_type: PDF },
                });
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    const filterBy = form.watch("filter_by");

    return (
        <Layout path="reports/hostel/by-boarders-name-list" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Hostel Report by Boarders Name List</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_id", "");
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="filter_by"
                            label="Filter By*"
                            isStringOptions={true}
                            options={["ALL", "GRADE", "CLASS"]}
                        />

                        {filterBy === "GRADE" && (
                            <FormSelect
                                control={form.control}
                                name={"grade_id"}
                                label="Grade"
                                options={gradeOptions}
                                isDisabled={filterBy !== "GRADE"}
                                isSortByName={false}
                            />
                        )}

                        {filterBy === "CLASS" && (
                            <FormSelect
                                control={form.control}
                                name={"semester_class_id"}
                                label="Class"
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                                isDisabled={filterBy !== "CLASS"}
                            />
                        )}

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByBoardersNameList;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
