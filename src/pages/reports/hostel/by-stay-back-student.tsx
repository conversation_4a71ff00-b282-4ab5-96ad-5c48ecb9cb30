import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    genderOptions,
    hostelReportByBoardersStaybackAPI,
    MALE,
    PDF,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { capitalize } from "lodash";
import { useTranslations } from "next-intl";

const HostelReportByStayBackStudent = ({ locale }) => {
    useCheckViewPermit("hostel-by-boarders-stayback-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            gender: MALE,
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: hostelReportByBoardersStaybackAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    const t = useTranslations("common");

    return (
        <Layout path="reports/hostel/by-stay-back-student" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Hostel Report by Stay Back Student</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-2">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="gender"
                            label={t("gender") + "*"}
                            isSortByName={false}
                            options={genderOptions.map((option) => ({
                                id: option.id,
                                name: capitalize(t(option.name)),
                            }))}
                        />

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByStayBackStudent;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
