import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import { hostelReportsByAvailableBedAPI, PDF } from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";

const HostelReportByAvailableBed = ({ locale }) => {
    useCheckViewPermit("hostel-by-available-bed-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: hostelReportsByAvailableBedAPI,
        locale,
        onSuccess(result) {
            showPDF(result.data.url);
        },
    });

    const showPDF = (url) => {
        window.open(url, "_blank");
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                getReportsUrl({
                    params: { ...data, export_type: PDF },
                });
            })
        );
    }

    return (
        <Layout path="reports/hostel/by-available-bed" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Hostel Report by Room Availability</h2>

                <Form {...form}>
                    <form
                        className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3"
                        onSubmit={onSubmit}
                    >
                        <div className="lg:col-span-3">
                            <div className="c-text-size min-h-[42px] max-w-[400px]">
                                <FormSelect
                                    control={form.control}
                                    name={"report_language"}
                                    label="report language*"
                                    options={activeLanguages?.map(
                                        (language) => ({
                                            id: language?.code,
                                            name: language?.name,
                                        })
                                    )}
                                />
                            </div>
                        </div>

                        <div className="lg:col-span-3">
                            <Button type="submit" onClick={onSubmit}>
                                Preview PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByAvailableBed;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
