import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    hostelReportsByBoardersContactInfoAPI,
    PDF,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";

const HostelReportByBoardersContactNumber = ({ locale }) => {
    useCheckViewPermit("hostel-by-boarders-contact-info-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: hostelReportsByBoardersContactInfoAPI,
        locale,
        onSuccess(result) {
            showPDF(result.data.url);
        },
    });

    const showPDF = (url) => {
        window.open(url, "_blank");
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                getReportsUrl({
                    params: { ...data, export_type: PDF },
                });
            })
        );
    }

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    return (
        <Layout
            path="reports/hostel/by-boarders-contact-number"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Hostel Report by Boarders Contact Number
                </h2>
                <Form {...form}>
                    <form
                        className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3"
                        onSubmit={onSubmit}
                    >
                        <div className="c-text-size min-h-[42px] max-w-[400px]">
                            <FormSelect
                                control={form.control}
                                name={"report_language"}
                                label="report language*"
                                options={activeLanguages?.map((language) => ({
                                    id: language?.code,
                                    name: language?.name,
                                }))}
                            />
                        </div>

                        <div className="c-text-size min-h-[42px] max-w-[400px]">
                            <FormSelect
                                control={form.control}
                                name={"semester_setting_id"}
                                label="Semester*"
                                options={semesterOptions}
                                isSortByName={false}
                            />
                        </div>

                        <div className="flex gap-2 lg:col-span-3">
                            <Button type="submit">Preview PDF</Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default HostelReportByBoardersContactNumber;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
