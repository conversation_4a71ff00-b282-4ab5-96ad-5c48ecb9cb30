import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    PDF,
    GET_ALL_PARAMS,
    semesterSettingAPI,
    semesterClassesAPI,
    semesterClassPrimaryDropdownFilter,
    gradeAPI,
    studentConductReportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const StudentAffairReportByStudentConduct = ({ locale }) => {
    useCheckViewPermit("student-conduct-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            grade_id: "",
            semester_class_id: "",
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    function fetchSemesterClasses() {
        const gradeId = form.watch("grade_id");
        if (!gradeId) return;
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassPrimaryDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: gradeId,
            },
        });
    }

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: studentConductReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data, "_blank");
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: studentConductReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Layout path="reports/student-affair/student-conduct" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Student Affair Report by Student Conduct
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                                fetchSemesterClasses();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            options={gradeOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                                fetchSemesterClasses();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="semester class*"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default StudentAffairReportByStudentConduct;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
