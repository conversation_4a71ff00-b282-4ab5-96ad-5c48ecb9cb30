import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    examAPI,
    GET_ALL_PARAMS,
    gradeAPI,
    PRIMARY,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    subjectScoreAnalysisReportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import BarChart from "@/components/ui/BarChart";
import { isEmpty } from "lodash";

const portraitPrintStyles = `
  @media print {
    @page {
      size: A4 portrait;
      margin: 10mm;
    }   

    .print-eight-in-one {
      break-inside: avoid;
      page-break-inside: avoid;
      height: 240px !important;
      max-height: 240px !important;
      overflow: hidden;
    }

    .print-eight-in-one canvas {
      width: 100% !important;
      height: 100% !important;
    }

    #print-container {
      break-inside: avoid;
      page-break-inside: avoid;
    }

    .grid-cols-2 {
      gap: 0 !important;
    }
  }
`;

const AcademicReportBySubjectScoreAnalysis = ({ locale }) => {
    useCheckViewPermit("subject-analysis-data-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [analysisData, setAnalysisData] = useState({});
    const [hasSearched, setHasSearched] = useState(false);
    const [chartTitle, setChartTitle] = useState("");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            exam_id: "",
            grade_id: "",
            semester_class_id: "",
        },
    });

    const { axiosQuery: getReports } = useAxios({
        api: subjectScoreAnalysisReportAPI,
        locale,
        onSuccess: ({ data }) => {
            setHasSearched(true);

            const reportData = data?.report_data ?? {};
            const title = data?.title ?? "";

            setChartTitle(title);
            setAnalysisData(reportData);
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: examOptions, axiosQuery: getExamOptions } = useAxios({
        api: examAPI,
        locale,
    });

    function fetchSemesterClassOptions() {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: form.watch("grade_id"),
                class_type: PRIMARY,
            },
        });
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
        getExamOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id") && form.watch("grade_id")) {
            fetchSemesterClassOptions();
        }
    }, [form.watch("semester_setting_id"), form.watch("grade_id"), locale]);

    return (
        <Layout path="reports/academic/subject-score-analysis" locale={locale}>
            <style>{portraitPrintStyles}</style>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Academic Report by Subject Score Analysis
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_id", "");
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="exam_id"
                            label="Exam*"
                            options={examOptions}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade"
                            options={gradeOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                                fetchSemesterClassOptions();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="Semester Class*"
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(analysisData) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <>
                            <div className="overflow-auto">
                                <div id="print-container" className="bigger">
                                    <h3 className="mb-5 mt-5 text-center font-noto text-black">
                                        {chartTitle}
                                    </h3>

                                    <div className="grid grid-cols-2">
                                        {Object.entries(analysisData).map(
                                            ([subject, scoreObj]) => (
                                                <div
                                                    key={subject}
                                                    className="print-eight-in-one break-inside-avoid"
                                                >
                                                    <BarChart
                                                        key={subject}
                                                        labels={Object.keys(
                                                            scoreObj as any
                                                        )}
                                                        data={Object.values(
                                                            scoreObj as any
                                                        )}
                                                        title={`${subject}`}
                                                        yLabel=""
                                                        displayValueOnBar={true}
                                                    />
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            </div>

                            <Button
                                onClick={() => window.print()}
                                className="ml-auto"
                            >
                                Print Chart
                            </Button>
                        </>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default AcademicReportBySubjectScoreAnalysis;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
