import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    currentYear,
    EXCEL,
    PDF,
    getYearOptions,
    academyTransferredStudentListReportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const AcademyReportTransferredStudentList = ({ locale }) => {
    useCheckViewPermit("academy-transferred-student-list");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            admission_year: currentYear,
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: academyTransferredStudentListReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: academyTransferredStudentListReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { initLoader } = useSubmit();

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.order_by = { loan_date: "desc" };
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    useEffect(() => {}, [form]);

    return (
        <Layout
            path="reports/academic/transferred-student-list"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Academic Report - Transferred Student List
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"admission_year"}
                            label="Admission Year"
                            options={getYearOptions()}
                            isSortByName={false}
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default AcademyReportTransferredStudentList;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
