import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    gradeAPI,
    netAveragePassingRateReportAPI,
    resultsPostingHeaderAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isEmpty } from "lodash";
import LineChart from "@/components/ui/LineChart";

const AcademicReportByNetAveragePassingRate = ({ locale }) => {
    useCheckViewPermit("net-average-passing-rate-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [groupedPostingHeaderOptions, setGroupedPostingHeaderOptions] =
        useState<any>({});
    const [passingData, setPassingData] = useState({});
    const [hasSearched, setHasSearched] = useState(false);
    const [chartTitle, setChartTitle] = useState("");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            grade_ids: [],
            result_posting_header_ids: [],
        },
    });

    const { axiosQuery: getReports } = useAxios({
        api: netAveragePassingRateReportAPI,
        locale,
        onSuccess: ({ data }) => {
            setHasSearched(true);

            const reportData = data?.report_data ?? {};
            const title = data?.title ?? "";

            setChartTitle(title);
            setPassingData(reportData);
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    function groupByReportCardOutputCode(data: any[]) {
        const grouped: Record<string, typeof data> = {};

        for (const item of data) {
            const code = item.report_card_output_code ?? "";
            if (!grouped[code]) {
                grouped[code] = [];
            }
            grouped[code].push(item);
        }

        return grouped;
    }

    const { data: postingHeaderOptions, axiosQuery: getPostingHeaders } =
        useAxios({
            api: resultsPostingHeaderAPI + "/results-posting-header-by-grade",
            locale,
            onSuccess: (res) => {
                const grouped = groupByReportCardOutputCode(res.data);
                const codeOptions = Object.entries(grouped).map(([code]) => ({
                    id: code,
                    name: code,
                }));

                setGroupedPostingHeaderOptions(codeOptions);
            },
        });

    function fetchPostingHeaders() {
        getPostingHeaders({
            params: {
                ...GET_ALL_PARAMS,
                grade_id: form.getValues("grade_ids"),
                semester_setting_id: form.getValues("semester_setting_id"),
            },
        });
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                const selectedCodes = data.result_posting_headers || [];

                const result_posting_header_ids = (postingHeaderOptions || [])
                    .filter((item) =>
                        selectedCodes.includes(item.report_card_output_code)
                    )
                    .map((item) => item.id);

                data.result_posting_header_ids = result_posting_header_ids;

                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        if (
            form.watch("semester_setting_id") &&
            !isEmpty(form.watch("grade_ids"))
        ) {
            fetchPostingHeaders();
        }
    }, [form.watch("semester_setting_id"), form.watch("grade_ids"), locale]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    const reportLanguage = form.watch("report_language");
    const gradeIds = form.watch("grade_ids") || [];
    const gradeCount = gradeIds.length;

    let xDataLabelSize = "13px";
    let xDataLabelRotation = 45;
    let xDataLabelBottomPadding = 50;

    if (reportLanguage === "zh") {
        if (gradeCount === 1) {
            xDataLabelSize = "13px";
        } else if (gradeCount === 2) {
            xDataLabelSize = "12px";
            xDataLabelRotation = 25;
            xDataLabelBottomPadding = 45;
        } else if (gradeCount >= 3) {
            xDataLabelSize = "12px";
            xDataLabelBottomPadding = 60;
        }
    } else {
        if (gradeCount > 2) {
            xDataLabelSize = "11px";
        } else {
            xDataLabelSize = "13px";
        }
    }

    return (
        <Layout
            path="reports/academic/net-average-passing-rate"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Net Average Passing Rate Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-2">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_ids", []);
                                form.setValue("result_posting_headers", []);
                            }}
                        />

                        <FormSelect
                            isMulti={true}
                            control={form.control}
                            name={"grade_ids"}
                            label="Grades*"
                            options={gradeOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("result_posting_headers", []);
                            }}
                        />

                        <FormSelect
                            isMulti={true}
                            control={form.control}
                            name={"result_posting_headers"}
                            label="Exam*"
                            isDisabled={
                                !form.watch("semester_setting_id") ||
                                isEmpty(form.watch("grade_ids"))
                            }
                            options={groupedPostingHeaderOptions}
                        />

                        <div className="flex gap-2 lg:col-span-2">
                            <Button onClick={view}>View Report</Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(passingData) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <>
                            <div className="overflow-auto">
                                <div id="print-container">
                                    <LineChart
                                        labels={Object.keys(passingData)}
                                        data={Object.keys(passingData).map(
                                            (key) => passingData[key]
                                        )}
                                        title={chartTitle}
                                        yLabel={
                                            form.watch("report_language") ===
                                            "zh"
                                                ? "及格率（%）"
                                                : "Passing Rate (%)"
                                        }
                                        displayValueAtXAxis={true}
                                        xDataLabelSize={xDataLabelSize}
                                        xDataLabelRotation={xDataLabelRotation}
                                        xDataLabelBottomPadding={
                                            xDataLabelBottomPadding
                                        }
                                    />
                                </div>
                            </div>
                            <Button
                                onClick={() => window.print()}
                                className="ml-auto mt-4"
                            >
                                Print Chart
                            </Button>
                        </>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default AcademicReportByNetAveragePassingRate;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
