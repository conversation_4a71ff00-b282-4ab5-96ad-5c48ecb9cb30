import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    examAPI,
    EXCEL,
    GET_ALL_PARAMS,
    gradeAPI,
    examResultByExamAPI,
    PDF,
    PRIMARY,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    examResultByStudentAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const AcademicReportByExaminationResultByStudent = ({ locale }) => {
    useCheckViewPermit("examination-result-by-student-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            exam_id: "",
            filter_by: "GRADE",
            semester_class_ids: [],
            grade_ids: [],
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: examResultByStudentAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: examResultByStudentAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: examOptions, axiosQuery: getExamOptions } = useAxios({
        api: examAPI,
        locale,
    });

    function fetchSemesterClassOptions() {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                class_type: PRIMARY,
            },
        });
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (data.filter_by == "GRADE") {
                    data.grade_ids = [data.grade_ids];
                    delete data.semester_class_ids;
                } else if (data.filter_by == "SEMESTER_CLASS") {
                    delete data.grade_ids;
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
        getExamOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    const filterBy = form.watch("filter_by");

    return (
        <Layout
            path="reports/academic/examination-result-by-student"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Examination Result By Student Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_ids", []);
                                form.setValue("semester_class_ids", "");
                                if (filterBy == "SEMESTER_CLASS") {
                                    fetchSemesterClassOptions();
                                }
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name="exam_id"
                            label="Exam*"
                            options={examOptions}
                        />

                        <FormSelect
                            control={form.control}
                            name="filter_by"
                            label="Filter By*"
                            isStringOptions={true}
                            options={["GRADE", "SEMESTER_CLASS"]}
                            onChange={(value) => {
                                if (value == "SEMESTER_CLASS") {
                                    form.setValue("grade_ids", []);
                                    fetchSemesterClassOptions();
                                }
                            }}
                        />

                        <div className="lg:col-span-2">
                            {filterBy === "GRADE" ? (
                                <FormSelect
                                    control={form.control}
                                    name={"grade_ids"}
                                    label="Grade*"
                                    options={gradeOptions}
                                    isDisabled={filterBy !== "GRADE"}
                                    isSortByName={false}
                                />
                            ) : (
                                <FormSelect
                                    isMulti={true}
                                    control={form.control}
                                    name={"semester_class_ids"}
                                    label="Semester Class*"
                                    options={
                                        semesterClassOptions?.map((option) => ({
                                            id: option?.id,
                                            name: `${option?.class_model?.name}`,
                                        })) ?? []
                                    }
                                    isDisabled={filterBy !== "SEMESTER_CLASS"}
                                />
                            )}
                        </div>

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default AcademicReportByExaminationResultByStudent;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
