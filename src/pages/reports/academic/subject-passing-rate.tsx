import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    examAPI,
    GET_ALL_PARAMS,
    gradeAPI,
    semesterSettingAPI,
    subjectAPI,
    subjectPassingRateReportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isEmpty } from "lodash";
import LineChart from "@/components/ui/LineChart";

const AcademicReportBySubjectScorePassingRate = ({ locale }) => {
    useCheckViewPermit("subject-passing-rate-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [passingData, setPassingData] = useState({});
    const [hasSearched, setHasSearched] = useState(false);
    const [chartTitle, setChartTitle] = useState("");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            exam_id: "",
            grade_id: "",
            subject_id: "",
        },
    });

    const { axiosQuery: getReports } = useAxios({
        api: subjectPassingRateReportAPI,
        locale,
        onSuccess: ({ data }) => {
            setHasSearched(true);

            const reportData = data?.report_data ?? {};
            const title = data?.title ?? "";

            setChartTitle(title);
            setPassingData(reportData);
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: subjectOptions, axiosQuery: getSubjectOptions } = useAxios({
        api: subjectAPI,
        locale,
    });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: examOptions, axiosQuery: getExamOptions } = useAxios({
        api: examAPI,
        locale,
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
        getSubjectOptions({ params: GET_ALL_PARAMS });
        getExamOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Layout path="reports/academic/subject-passing-rate" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Subject Passing Rate Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            options={gradeOptions}
                            isSortByName={false}
                        />

                        <FormSelect
                            control={form.control}
                            name={"subject_id"}
                            label="Subject*"
                            options={
                                subjectOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.code} - ${option?.name}`,
                                })) ?? []
                            }
                        />

                        <FormSelect
                            control={form.control}
                            name="exam_id"
                            label="Result Source Exam*"
                            options={examOptions}
                        />

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(passingData) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <>
                            <div className="overflow-auto">
                                <div id="print-container">
                                    <LineChart
                                        labels={Object.keys(passingData)}
                                        data={Object.keys(passingData).map(
                                            (key) => passingData[key]
                                        )}
                                        title={chartTitle}
                                        yLabel={
                                            form.watch("report_language") ===
                                            "zh"
                                                ? "及格率 (%)"
                                                : "Passing Rate (%)"
                                        }
                                        displayValueAtXAxis={true}
                                    />
                                </div>
                            </div>

                            <Button
                                onClick={() => window.print()}
                                className="ml-auto mt-4"
                            >
                                Print Chart
                            </Button>
                        </>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default AcademicReportBySubjectScorePassingRate;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
