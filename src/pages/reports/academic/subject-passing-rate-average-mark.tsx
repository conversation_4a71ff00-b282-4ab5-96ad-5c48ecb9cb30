import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    examAPI,
    GET_ALL_PARAMS,
    gradeAPI,
    semesterSettingAPI,
    subjectAPI,
    subjectAverageMarkReportAPI,
    subjectPassingRateReportAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isEmpty } from "lodash";
import BarChart from "@/components/ui/BarChart";

const AcademicReportBySubjectScorePassingRateAverageMark = ({ locale }) => {
    useCheckViewPermit("subject-average-mark-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [passingData, setPassingData] = useState({});
    const [averageMarkData, setAverageMarkData] = useState({});
    const [hasSearched, setHasSearched] = useState(false);
    const [passingRateChartTitle, setPassingRateChartTitle] = useState("");
    const [averageMarkChartTitle, setAverageMarkChartTitle] = useState("");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            grade_id: "",
            subject_id: "",
            exam_id: "",
        },
    });

    const { axiosQuery: getPassingRateReport } = useAxios({
        api: subjectPassingRateReportAPI,
        locale,
        onSuccess: ({ data }) => {
            setHasSearched(true);

            const reportData = data?.report_data ?? {};
            const title = data?.title ?? "";

            setPassingRateChartTitle(title);
            setPassingData(reportData);
        },
    });

    const { axiosQuery: getAverageMarkReport } = useAxios({
        api: subjectAverageMarkReportAPI,
        locale,
        onSuccess: ({ data }) => {
            setHasSearched(true);

            const reportData = data?.report_data ?? {};
            const title = data?.title ?? "";

            setAverageMarkChartTitle(title);
            setAverageMarkData(reportData);
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: subjectOptions, axiosQuery: getSubjectOptions } = useAxios({
        api: subjectAPI,
        locale,
        onError: () => close(),
    });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: examOptions, axiosQuery: getExamOptions } = useAxios({
        api: examAPI,
        locale,
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function view() {
        submit((data) => {
            getPassingRateReport({ params: data });
            getAverageMarkReport({ params: data });
        });
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
        getSubjectOptions({ params: GET_ALL_PARAMS });
        getExamOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Layout
            path="reports/academic/subject-passing-rate-average-mark"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Academic Report by Subject Passing Rate/ Average Mark
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            options={gradeOptions}
                            isSortByName={false}
                        />

                        <FormSelect
                            control={form.control}
                            name={"subject_id"}
                            label="Subject*"
                            options={
                                subjectOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.code} - ${option?.name}`,
                                })) ?? []
                            }
                        />

                        <FormSelect
                            control={form.control}
                            name="exam_id"
                            label="Exam*"
                            options={examOptions}
                        />

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(passingData) || isEmpty(averageMarkData) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <>
                            <div className="overflow-auto">
                                <div id="print-container">
                                    <div className="print-two-chart-in-one print:h-[500px]">
                                        <BarChart
                                            labels={Object.keys(passingData)}
                                            data={Object.values(passingData)}
                                            title={passingRateChartTitle}
                                            yLabel={
                                                form.watch(
                                                    "report_language"
                                                ) === "zh"
                                                    ? "及格率 (%)"
                                                    : "Passing Rate (%)"
                                            }
                                            displayValueAtXAxis={true}
                                            yMax={100}
                                        />
                                    </div>

                                    <div className="print-two-chart-in-one mt-[5px] print:h-[500px]">
                                        <BarChart
                                            labels={Object.keys(
                                                averageMarkData
                                            )}
                                            data={Object.values(
                                                averageMarkData
                                            )}
                                            title={averageMarkChartTitle}
                                            yLabel={
                                                form.watch(
                                                    "report_language"
                                                ) === "zh"
                                                    ? "平均分数"
                                                    : "Average Mark"
                                            }
                                            displayValueAtXAxis={true}
                                            yMax={100}
                                        />
                                    </div>
                                </div>
                            </div>
                            <Button
                                onClick={() => window.print()}
                                className="print-btn ml-auto mt-4"
                            >
                                Print Chart
                            </Button>
                        </>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default AcademicReportBySubjectScorePassingRateAverageMark;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
