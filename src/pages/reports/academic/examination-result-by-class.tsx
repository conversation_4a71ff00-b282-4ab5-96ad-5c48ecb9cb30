import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    examResultByClassAPI,
    EXCEL,
    GET_ALL_PARAMS,
    gradeAPI,
    PDF,
    PRIMARY,
    resultsPostingHeaderAPI,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, isValueTrue } from "@/lib/utils";
import FormCheckbox from "@/components/ui/FormCheckbox";

const AcademicReportByExaminationResultByClass = ({ locale }) => {
    useCheckViewPermit("academy-examination-result-by-semester-class-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: null,
            grade_id: null,
            semester_class_id: null,
            results_posting_header_id: null,
            with_position_in_standard: true,
        },
    });

    const { data: postingHeaderOptions, axiosQuery: getPostingHeaders } =
        useAxios({
            api: resultsPostingHeaderAPI + "/results-posting-header-by-grade",
            locale,
        });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: examResultByClassAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: examResultByClassAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    function fetchSemesterClassOptions() {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: form.watch("grade_id"),
                class_type: PRIMARY,
            },
        });
    }

    function fetchPostingHeaders() {
        getPostingHeaders({
            params: {
                ...GET_ALL_PARAMS,
                grade_id: form.getValues("grade_id"),
                semester_setting_id: form.getValues("semester_setting_id"),
            },
        });
    }

    const { initLoader } = useSubmit();

    function download(type: typeof EXCEL | typeof PDF) {
        initLoader(
            form.handleSubmit((data) => {
                if (isValueTrue(data.with_position_in_standard)) {
                    data.with_position_in_standard = 1;
                } else {
                    data.with_position_in_standard = 0;
                }

                getReportsUrl({
                    params: { ...data, export_type: type },
                });
            })
        );
    }

    function preview() {
        initLoader(
            form.handleSubmit((data) => {
                if (isValueTrue(data.with_position_in_standard)) {
                    data.with_position_in_standard = 1;
                } else {
                    data.with_position_in_standard = 0;
                }

                getReportsUrlForPreview({
                    params: { ...data, export_type: PDF },
                });
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id") && form.watch("grade_id")) {
            fetchSemesterClassOptions();
            fetchPostingHeaders();
        }
    }, [form.watch("semester_setting_id"), form.watch("grade_id"), locale]);

    return (
        <Layout path="reports/academic/examination-result" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Examination Result By Class Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("grade_id", "");
                                form.setValue("semester_class_id", "");
                                form.setValue("results_posting_header_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            options={gradeOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                                fetchSemesterClassOptions();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"results_posting_header_id"}
                            label="Exam*"
                            isDisabled={
                                !form.watch("semester_setting_id") ||
                                !form.watch("grade_id")
                            }
                            options={postingHeaderOptions?.map((item) => ({
                                id: item?.id,
                                name: `${item?.report_card_output_code} (${item?.id})`,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="Semester Class*"
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                            isDisabled={!form.watch("grade_id")}
                        />

                        <div className="mt-10">
                            <FormCheckbox
                                control={form.control}
                                name="with_position_in_standard"
                                textStyleClass="text-gray-500 font-medium"
                            />
                        </div>

                        <div className="flex gap-2 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default AcademicReportByExaminationResultByClass;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
