import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    semesterClassesAPI,
    semesterSettingAPI,
    semesterClassDropdownFilter,
    gradeAPI,
    resultsPostingHeaderAPI,
    PRIMARY,
    examReportCardByGroupAPI,
    PDF,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { getUserableType } from "@/lib/utils";
import { isEmpty } from "lodash";

const ExamReportCardReportByGroup = ({ locale }) => {
    useCheckViewPermit("report-card-by-morph-view");

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: null,
            results_posting_header_id: null,
            filter_by: "GRADE",
            grade_id: null,
            semester_class_id: null,
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [noRecordFound, setNoRecordFound] = useState(false);

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
    });

    const { data: postingHeaderOptions, axiosQuery: getPostingHeaders } =
        useAxios({
            api: resultsPostingHeaderAPI + "/results-posting-header-by-grade",
            locale,
        });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    function fetchPostingHeaders() {
        getPostingHeaders({
            params: {
                ...GET_ALL_PARAMS,
                grade_id: form.getValues("grade_id"),
                semester_setting_id: form.getValues("semester_setting_id"),
            },
        });
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id") && form.watch("grade_id")) {
            fetchPostingHeaders();
        }
    }, [form.watch("semester_setting_id"), form.watch("grade_id"), locale]);

    const filterBy = form.watch("filter_by");

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: examReportCardByGroupAPI,
        locale,
        onSuccess(result) {
            if (!isEmpty(result.data)) {
                setNoRecordFound(false);
                window.open(result.data?.[0]?.file_url, "_blank");
            } else {
                setNoRecordFound(true);
            }
        },
    });

    const { initLoader } = useSubmit();

    function fetchSemesterClassOptions() {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: form.watch("grade_id"),
                class_type: PRIMARY,
            },
        });
    }

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.per_page = 1;
                if (data.filter_by == "GRADE") {
                    delete data.semester_class_id;
                    data.report_card_morphable_type = getUserableType("Grade");
                    data.report_card_morphable_id = data.grade_id;
                }

                if (data.filter_by == "SEMESTER_CLASS") {
                    delete data.grade_id;
                    data.report_card_morphable_type =
                        "App\\Models\\" + "SemesterClass";
                    data.report_card_morphable_id = data.semester_class_id;
                }

                delete data.filter_by;
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        if (form.watch("semester_class_id")) {
            fetchSemesterClassOptions();
        }
        if (form.watch("grade_id") && filterBy === "SEMESTER_CLASS") {
            fetchPostingHeaders();
            fetchSemesterClassOptions();
        }
    }, [locale]);

    return (
        <Layout
            path="reports/academic/student-report-card-by-group"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Student Report Card (By Group)</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-2">
                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isClearable={false}
                            isSortByName={false}
                            onChange={() => {
                                setNoRecordFound(false);
                                form.setValue("grade_id", "");
                                form.setValue("semester_class_id", "");
                                form.setValue("results_posting_header_id", "");
                            }}
                        />
                        <FormSelect
                            control={form.control}
                            name="filter_by"
                            label="Filter By*"
                            isStringOptions={true}
                            options={["GRADE", "SEMESTER_CLASS"]}
                            onChange={(value) => {
                                setNoRecordFound(false);
                                if (value == "SEMESTER_CLASS") {
                                    form.setValue("semester_class_id", "");
                                    fetchSemesterClassOptions();
                                }
                            }}
                        />
                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={gradeOptions}
                            onChange={() => {
                                setNoRecordFound(false);
                                form.setValue("semester_class_id", "");
                                form.setValue("results_posting_header_id", "");

                                if (filterBy === "SEMESTER_CLASS") {
                                    fetchPostingHeaders();
                                    fetchSemesterClassOptions();
                                }
                            }}
                        />
                        {filterBy === "SEMESTER_CLASS" && (
                            <FormSelect
                                control={form.control}
                                name={"semester_class_id"}
                                label="Semester Class*"
                                isDisabled={
                                    !form.watch("semester_setting_id") ||
                                    !form.watch("grade_id")
                                }
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                            />
                        )}
                        <FormSelect
                            control={form.control}
                            name={"results_posting_header_id"}
                            label="Exam*"
                            isDisabled={
                                !form.watch("semester_setting_id") ||
                                !form.watch("grade_id")
                            }
                            options={postingHeaderOptions?.map((item) => ({
                                id: item?.id,
                                name: `${item?.report_card_output_code} (${item?.id})`,
                            }))}
                        />
                        <div className="lg:col-span-2">
                            <Button
                                className="mb-4"
                                disabled={
                                    !form.watch("results_posting_header_id")
                                }
                                onClick={preview}
                            >
                                Preview PDF
                            </Button>
                        </div>
                    </form>
                    {noRecordFound ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : null}
                </Form>
            </Card>
        </Layout>
    );
};

export default ExamReportCardReportByGroup;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
