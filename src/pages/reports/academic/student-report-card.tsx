import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    semesterClassesAPI,
    examReportCardAPI,
    semesterSettingAPI,
    semesterClassDropdownFilter,
    gradeAPI,
    resultsPostingHeaderAPI,
    DEFAULT_FILTER_PARAMS,
    IconProfilePhotoPlaceholder,
    PRIMARY,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { combinedNames, downloadFile, optionUserLabel } from "@/lib/utils";
import DataTable from "@/components/ui/DataTable";
import Image from "next/image";
import Modal from "@/components/ui/Modal";
import PersonImagePreview from "@/components/ui/PersonImagePreview";

const ExamReportCardReport = ({ locale }) => {
    useCheckViewPermit("student-report-card-view");

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: null,
            grade_id: null,
            semester_class_id: null,
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [filter, setFilter] = useState<any>(DEFAULT_FILTER_PARAMS);
    const [pagination, setPagination] = useState<any>();
    const [targetImage, setTargetImage] = useState<any>(null);

    const columns = [
        {
            key: "student_photo",
            modify: (data) => (
                <div className="flex h-[80px] items-center justify-center">
                    {data?.url ? (
                        <Image
                            src={data.url}
                            alt=""
                            width={50}
                            height={50}
                            className="mx-auto max-h-full w-auto cursor-pointer rounded-sm"
                            onClick={() => {
                                setTargetImage({
                                    student_name: data.name,
                                    url: data?.url,
                                });
                            }}
                        />
                    ) : (
                        <Image
                            src={IconProfilePhotoPlaceholder}
                            alt=""
                            width={50}
                            height={50}
                            className="mx-auto h-[50px] w-[50px] opacity-10"
                            unoptimized
                        />
                    )}
                </div>
            ),
        },
        {
            key: "student_number",
        },
        { key: "student_name" },
        {
            key: "file_url",
            displayAs: " ",
            modify: (url) => {
                return (
                    <div className="mt-1 flex flex-wrap items-center justify-center gap-x-3 gap-y-1.5">
                        <div
                            className="cursor-pointer text-center text-[14px] font-semibold leading-none text-themeGreen underline"
                            onClick={() => preview(url)}
                        >
                            Preview PDF
                        </div>
                        <div
                            className="cursor-pointer text-center text-[14px] font-semibold leading-none text-themeGreen underline"
                            onClick={() => download(url)}
                        >
                            Download PDF
                        </div>
                    </div>
                );
            },
        },
    ];

    function definedData() {
        return reports?.map((item) => ({
            id: item?.id,
            student_photo: {
                url: item?.student?.photo,
                name: optionUserLabel(
                    item?.student?.student_number,
                    item?.student?.translations?.name
                ),
            },
            student_number: item?.student?.student_number,
            student_name: combinedNames(item?.student?.translations?.name),
            file_url: item?.file_url,
        }));
    }

    const { data: reports, axiosQuery: getReports } = useAxios({
        api: examReportCardAPI,
        locale,
        onSuccess: (result) => {
            setPagination(result?.pagination);
        },
    });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
    });

    const { data: postingHeaderOptions, axiosQuery: getPostingHeaders } =
        useAxios({
            api: resultsPostingHeaderAPI + "/results-posting-header-by-grade",
            locale,
        });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    function fetchPostingHeaders() {
        getPostingHeaders({
            params: {
                ...GET_ALL_PARAMS,
                grade_id: form.getValues("grade_id"),
                semester_setting_id: form.getValues("semester_setting_id"),
            },
        });
    }

    function fetchSemesterClassOptions() {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                grade_id: form.watch("grade_id"),
                class_type: PRIMARY,
            },
        });
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    name: {
                        [locale]: "asc",
                    },
                },
            },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id") && form.watch("grade_id")) {
            fetchPostingHeaders();
        }
    }, [form.watch("semester_setting_id"), form.watch("grade_id"), locale]);

    useEffect(() => {
        if (filter?.results_posting_header_id) {
            getReports({
                params: filter,
            });
        }
    }, [filter, locale]);

    useEffect(() => {
        fetchSemesterClassOptions();
    }, [locale]);

    function download(url) {
        downloadFile(url);
    }

    function preview(url) {
        window.open(url, "_blank");
    }

    return (
        <Layout path="reports/academic/student-report-card" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Student Report Card</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-2">
                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isClearable={false}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("results_posting_header_id", "");
                                form.setValue("semester_class_id", "");
                                fetchSemesterClassOptions();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="Grade*"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={gradeOptions}
                            onChange={(gradeId) => {
                                form.setValue("semester_class_id", "");
                                form.setValue("results_posting_header_id", "");
                                fetchSemesterClassOptions();
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"results_posting_header_id"}
                            label="Exam*"
                            isDisabled={
                                !form.watch("semester_setting_id") ||
                                !form.watch("grade_id")
                            }
                            options={postingHeaderOptions?.map((item) => ({
                                id: item?.id,
                                name: `${item?.report_card_output_code} (${item?.id})`,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="semester class"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="lg:col-span-2">
                            <Button
                                className="mb-4"
                                disabled={
                                    !form.watch("results_posting_header_id")
                                }
                                onClick={() =>
                                    setFilter({
                                        ...filter,
                                        page: 1,
                                        results_posting_header_id:
                                            form.getValues(
                                                "results_posting_header_id"
                                            ),
                                        semester_class_id:
                                            form.getValues("semester_class_id"),
                                    })
                                }
                                variant={"outline"}
                            >
                                Filter
                            </Button>

                            {reports?.length > 0 && (
                                <DataTable
                                    columns={columns}
                                    data={definedData()}
                                    pagination={pagination}
                                    setPagination={setPagination}
                                    changePage={(arg) =>
                                        setFilter({ ...filter, ...arg })
                                    }
                                />
                            )}
                        </div>
                    </form>
                </Form>
            </Card>
            {/* student photo */}
            <Modal
                open={targetImage != null}
                onOpenChange={(value) => {
                    if (value === false) {
                        setTargetImage(null);
                    }
                }}
            >
                <PersonImagePreview
                    studentName={targetImage?.student_name}
                    url={targetImage?.url}
                />
            </Modal>
        </Layout>
    );
};

export default ExamReportCardReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
