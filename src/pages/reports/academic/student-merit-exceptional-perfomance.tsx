import { useEffect, useState } from "react";
import clsx from "clsx";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    bookstoreReportsByStudentAPI,
    firstDayOfCurrentMonth,
    GET_ALL_PARAMS,
    lastDayOfCurrentMonth,
    PDF,
    semesterClassesAPI,
    semesterClassPrimaryDropdownFilter,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";

const StudentMeritExceptionalReport = ({ locale }) => {
    useCheckViewPermit("academy-student-merit-exceptional-performance-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            semester_class_id: "",
            student_ids: [],
        },
    });

    const [reports, setReports] = useState<any[]>([]);
    const [targetId, setTargetId] = useState(null);
    const [openSearch, setOpenSearch] = useState(false);
    const [hasSearched, setHasSearched] = useState(false);

    const { data: semesterOptions, axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: bookstoreReportsByStudentAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: bookstoreReportsByStudentAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function download() {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout
            path="reports/academic/student-merit-exceptional-performance"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Student Merit and Exceptional Performance
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="Semester Class*"
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div>
                            <div className="c-text-size mb-1.5 font-medium capitalize text-themeLabel">
                                Students*
                            </div>
                            <div
                                className={clsx(
                                    "c-text-size min-h-[42px] rounded-md border border-input py-2.5 pl-3.5 pr-2",
                                    form.watch("student_ids")?.length > 0
                                        ? ""
                                        : "text-themeGray3"
                                )}
                                onClick={() => setOpenSearch(true)}
                            >
                                {form.watch("student_ids")?.length > 0
                                    ? `${form.watch("student_ids").length} student${form.watch("student_ids").length > 1 ? "s" : ""} selected`
                                    : "Select Student"}
                            </div>
                        </div>

                        <div className="flex items-start gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button onClick={download} variant={"outline"}>
                                Download Report PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>

            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(students) => {
                        form.setValue(
                            "student_ids",
                            students.map((student) => student?.id)
                        );
                    }}
                    otherFilterParams={{
                        semester_setting_id: form.getValues(
                            "semester_setting_id"
                        ),
                        semester_class_ids: form.getValues("semester_class_id"),
                        response: "FULL",
                        includes: [
                            "currentSemesterPrimaryClass.semesterClass.classModel",
                        ],
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </Layout>
    );
};

export default StudentMeritExceptionalReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
