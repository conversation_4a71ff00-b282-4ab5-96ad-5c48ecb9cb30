import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    studentAbsentReportAPI,
    semesterSettingAPI,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    gradeAPI,
    PRIMARY,
    ENGLISH,
    SOCIETY,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, optionUserLabel, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormSearchInput from "@/components/ui/FormSearchInput";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import Modal from "@/components/ui/Modal";
import FormInputInterger from "@/components/ui/FormInputInterger";

const StudentAbsentReport = ({ locale }) => {
    useCheckViewPermit("student-absent-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<any>(null);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            date_from: toYMD(firstDayOfCurrentMonth),
            date_to: toYMD(lastDayOfCurrentMonth),
            absent_count: "",
            semester_setting_id: null,
            semester_class_id: null,
            grade_id: null,
            class_type: null,
            student_number: "",
        },
    });

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getReportsUrl } = useAxios({
        api: studentAbsentReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: studentAbsentReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    name: {
                        [locale]: "asc",
                    },
                },
            },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                    grade_id: form.watch("grade_id"),
                    class_type: form.watch("class_type"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.date_from = toYMD(dateRange.startDate);
                    data.date_to = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/attendance/student-absent" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Student Absent Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />
                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                                error={
                                    form.formState.errors?.payment_date_from
                                        ?.message ||
                                    form.formState.errors?.payment_date_to
                                        ?.message
                                }
                            />
                        </div>

                        <FormInputInterger
                            control={form.control}
                            name={"absent_count"}
                            label="Absent Count (More Than)*"
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"class_type"}
                            isStringOptions={true}
                            options={[PRIMARY, ENGLISH, SOCIETY]}
                            isSortByName={false}
                            isDisabled={!form.watch("semester_setting_id")}
                            onChange={(type) => {
                                form.setValue("semester_class_id", "");
                                getSemesterClassOptions({
                                    params: {
                                        ...GET_ALL_PARAMS,
                                        ...semesterClassDropdownFilter(locale),
                                        semester_setting_id: form.watch(
                                            "semester_setting_id"
                                        ),
                                        grade_id: form.watch("grade_id"),
                                        class_type: type,
                                    },
                                });
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"grade_id"}
                            label="grade"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={gradeOptions}
                            onChange={(gradeId) => {
                                form.setValue("semester_class_id", "");
                                getSemesterClassOptions({
                                    params: {
                                        ...GET_ALL_PARAMS,
                                        ...semesterClassDropdownFilter(locale),
                                        semester_setting_id: form.watch(
                                            "semester_setting_id"
                                        ),
                                        grade_id: gradeId,
                                        class_type: form.watch("class_type"),
                                    },
                                });
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="semester class"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="lg:col-span-2">
                            <FormSearchInput
                                control={form.control}
                                name="student_number"
                                label="Student"
                                displayValue={optionUserLabel(
                                    selectedStudent?.student_number,
                                    selectedStudent?.translations?.name
                                )}
                                onClick={() => setOpenSearch(true)}
                                onClear={() => {
                                    setSelectedStudent(null);
                                    form.setValue("student_number", "");
                                }}
                            />
                        </div>
                        <div className="mt-2 w-full items-end gap-3 lg:col-span-3 lg:flex lg:flex-wrap">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    setSelection={(student) => {
                        form.setValue(
                            "student_number",
                            student?.student_number
                        );
                        setSelectedStudent(student);
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </Layout>
    );
};

export default StudentAbsentReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
