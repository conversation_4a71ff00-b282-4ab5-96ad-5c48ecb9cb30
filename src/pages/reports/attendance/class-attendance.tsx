import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    semesterClassesAPI,
    classAttendanceReportAPI,
    semesterSettingAPI,
    PRIMARY,
    ENGLISH,
    SOCIETY,
    semesterClassDropdownFilter,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";
import { DatePicker } from "@/components/ui/DatePicker";

const ClassAttendanceReport = ({ locale }) => {
    useCheckViewPermit("class-attendance-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            date: new Date(),
            semester_setting_id: "",
            semester_class_id: "",
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getReportsUrl } = useAxios({
        api: classAttendanceReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: classAttendanceReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    function fetchSemesterClassOptions(type?: string) {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                class_type: type ?? form.watch("class_type"),
            },
        });
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            fetchSemesterClassOptions();
        }
    }, [form.watch("semester_setting_id"), locale]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (data.date) {
                    data.date = toYMD(data.date);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/attendance/class-attendance" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Class Attendance Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />
                        <div className="lg:col-span-1">
                            <DatePicker
                                control={form.control}
                                name="date"
                                label="Date*"
                            />
                        </div>
                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"class_type"}
                            label={"Class Type"}
                            isSortByName={false}
                            isStringOptions={true}
                            options={[PRIMARY, ENGLISH, SOCIETY]}
                            isDisabled={!form.watch("semester_setting_id")}
                            onChange={(type) => {
                                if (type) {
                                    form.setValue("semester_class_id", "");
                                    fetchSemesterClassOptions(type);
                                }
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="semester class*"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="mt-1 flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default ClassAttendanceReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
