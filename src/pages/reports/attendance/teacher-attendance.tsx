import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    PDF,
    teacherAttendanceReportAPI,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    employeeAPI,
    EMPLOYEE,
    WORKING,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { downloadFile, getUserableType, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FormSelectAsync from "@/components/ui/FormSelectAsync";

const TeacherAttendanceReport = ({ locale }) => {
    useCheckViewPermit("class-attendance-taking-status-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const userProfile = useUserProfile((state) => state.userProfile);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            date_from: toYMD(firstDayOfCurrentMonth),
            date_to: toYMD(lastDayOfCurrentMonth),
            employee_id: null,
        },
    });

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: teacherAttendanceReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: teacherAttendanceReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { asyncOptions, setAsyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: employeeAPI,
        params: {
            status: WORKING,
            is_active: 1,
        },
    });

    function setDefaultTeacher() {
        const user = userProfile?.userables?.find(
            (item) => item?.userable_type === getUserableType(EMPLOYEE)
        );
        if (user) {
            loadAsyncOptions(user.name, setAsyncOptions);
            form.setValue("employee_id", user.userable_id);
        }
    }

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.date_from = toYMD(dateRange.startDate);
                    data.date_to = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        if (userProfile) {
            setDefaultTeacher();
        }
    }, [userProfile]);

    return (
        <Layout path="reports/attendance/teacher-attendance" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Teacher Attendance Report</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-2">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />
                        <div>
                            <DateRangePicker
                                label="Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                                error={
                                    form.formState.errors?.payment_date_from
                                        ?.message ||
                                    form.formState.errors?.payment_date_to
                                        ?.message
                                }
                            />
                        </div>

                        <div className="lg:col-span-2">
                            <FormSelectAsync
                                control={form.control}
                                name="employee_id"
                                label="Teacher*"
                                loadOptions={loadAsyncOptions}
                                value={asyncOptions.find(
                                    (option) =>
                                        option.value ===
                                        form.watch("employee_id")
                                )}
                            />
                        </div>
                        <div className="mt-1 flex flex-wrap items-end gap-3 lg:col-span-2">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default TeacherAttendanceReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
