import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterClassReportsStudentDetailAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const SemesterClassReportStudentDetail = ({ locale }) => {
    useCheckViewPermit("semester-class-student-details-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            semester_class_id: "",
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getReportsUrl } = useAxios({
        api: semesterClassReportsStudentDetailAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: semesterClassReportsStudentDetailAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/semester-class/student-details" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Semester Class Report - Student Details
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_id", "");
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_class_id"}
                            label="semester class*"
                            isDisabled={!form.watch("semester_setting_id")}
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default SemesterClassReportStudentDetail;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
