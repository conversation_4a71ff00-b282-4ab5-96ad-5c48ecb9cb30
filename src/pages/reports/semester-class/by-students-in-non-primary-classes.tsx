import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    semesterClassesAPI,
    semesterClassReportsByStudentsInClassAPI,
    semesterSettingAPI,
    ENGLISH,
    SOCIETY,
    semesterClassDropdownFilter,
    semesterClassReportByStudentInNonPrimaryClassAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const SemesterClassReportByStudentsInNonPrimaryClasses = ({ locale }) => {
    useCheckViewPermit(
        "non-primary-semester-class-by-students-in-class-report"
    );

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            semester_setting_id: "",
            class_type: null,
            semester_class_ids: [],
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getReportsUrl } = useAxios({
        api: semesterClassReportByStudentInNonPrimaryClassAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: semesterClassReportByStudentInNonPrimaryClassAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    function fetchSemesterClassOptions(type?: string) {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                class_type: type ?? form.watch("class_type"),
            },
        });
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            fetchSemesterClassOptions();
        }
    }, [form.watch("semester_setting_id")]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout
            path="reports/semester-class/by-students-in-non-primary-classes"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Semester Class Report - By Students in Non Primary Classes
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() => {
                                form.setValue("semester_class_ids", []);
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"class_type"}
                            label={"Class Type*"}
                            isStringOptions={true}
                            options={[ENGLISH, SOCIETY]}
                            isDisabled={!form.watch("semester_setting_id")}
                            onChange={(type) => {
                                if (type) {
                                    form.setValue("semester_class_ids", []);
                                    fetchSemesterClassOptions(type);
                                }
                            }}
                        />

                        <div className="lg:col-span-2">
                            <FormSelect
                                control={form.control}
                                isMulti={true}
                                name={"semester_class_ids"}
                                label="semester classes"
                                isDisabled={
                                    !form.watch("class_type") ||
                                    !form.watch("semester_setting_id")
                                }
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                            />
                        </div>

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default SemesterClassReportByStudentsInNonPrimaryClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
