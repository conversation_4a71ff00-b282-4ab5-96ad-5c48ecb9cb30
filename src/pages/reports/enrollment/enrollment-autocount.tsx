import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    firstDayOfCurrentMonth,
    PDF,
    lastDayOfCurrentMonth,
    INVOICE,
    ADVANCE_INVOICE,
    CREDIT_NOTE,
    DEBIT_NOTE,
    DRAFT,
    CONFIRMED,
    VOIDED,
    POSTED,
    enrollmentReportPostToAutocountAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { downloadFile, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormInput from "@/components/ui/FormInput";
import { useTranslations } from "next-intl";
import { isEmpty } from "lodash";

const PostToAutocount = ({ locale }) => {
    useCheckViewPermit("enrollment-post-to-autocount");
    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            payment_date_from: toYMD(firstDayOfCurrentMonth),
            payment_date_to: toYMD(lastDayOfCurrentMonth),
            type: INVOICE,
            status: CONFIRMED,
            reference_no: "",
        },
    });

    const [hasSearched, setHasSearched] = useState(false);
    const [reports, setReports] = useState<string | null>();
    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const { axiosQuery: postToAutocount } = useAxios({
        api: enrollmentReportPostToAutocountAPI,
        onSuccess(result) {
            downloadFile(result.data?.url);
            setReports(result.data?.url);
            setHasSearched(true);
        },
        onError() {
            setHasSearched(true);
            setReports(null);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.payment_date_from = toYMD(dateRange.startDate);
                    data.payment_date_to = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) => {
            postToAutocount({
                params: {
                    ...data,
                    export_type: type,
                },
            });
        });
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout path="reports/enrollment/enrollment-autocount" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">{t("Post to Autocount")}</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label={t("Payment Date Range") + "*"}
                                range={dateRange}
                                setRange={setDateRange}
                                error={
                                    form.formState.errors?.payment_date_from
                                        ?.message ||
                                    form.formState.errors?.payment_date_to
                                        ?.message
                                }
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name={"type"}
                            isStringOptions={true}
                            options={[
                                INVOICE,
                                ADVANCE_INVOICE,
                                CREDIT_NOTE,
                                DEBIT_NOTE,
                            ]}
                        />

                        <FormSelect
                            control={form.control}
                            name={"status"}
                            isStringOptions={true}
                            options={[DRAFT, CONFIRMED, VOIDED, POSTED]}
                        />

                        <FormInput
                            control={form.control}
                            name={"reference_no"}
                        />

                        <div className="flex flex-wrap gap-3 lg:col-span-3">
                            <Button onClick={() => download(EXCEL)}>
                                {t("Export Excel")}
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched && isEmpty(reports) ? (
                    <div className="h-20 text-center text-themeLabel">
                        {t("No Record Found")}
                    </div>
                ) : null}
            </Card>
        </Layout>
    );
};

export default PostToAutocount;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
