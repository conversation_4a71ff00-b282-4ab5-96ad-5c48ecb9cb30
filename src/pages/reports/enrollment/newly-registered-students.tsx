import { useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    newRegisteredStudentReportAPI,
    EXCEL,
    PDF,
    getLastDayOfCurrentWeek,
    getCurrentWeekStartDate,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { capitalize } from "lodash";
import { useTranslations } from "next-intl";

const NewRegisteredStudentReport = ({ locale }) => {
    useCheckViewPermit("student-registration-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [dateRange, setDateRange] = useState<any>({
        startDate: getCurrentWeekStartDate(),
        endDate: getLastDayOfCurrentWeek(),
        key: "selection",
    });

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            from_date: toYMD(dateRange.startDate),
            to_date: toYMD(dateRange.endDate),
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: newRegisteredStudentReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: newRegisteredStudentReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.from_date = toYMD(dateRange?.startDate);
                    data.to_date = toYMD(dateRange?.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    const t = useTranslations("common");

    return (
        <Layout
            path="reports/enrollment/newly-registered-students"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    {t("Newly Registered Students")} {capitalize(t("report"))}
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                            />
                        </div>

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default NewRegisteredStudentReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
