import { useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    registrationFeeDailyCollectionReportAPI,
    EXCEL,
    getLastDayOfCurrentWeek,
    getCurrentWeekStartDate,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";

const RegistrationFeeDailyCollectionReport = ({ locale }) => {
    useCheckViewPermit("enrollment-by-daily-collection-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [dateRange, setDateRange] = useState<any>({
        startDate: getCurrentWeekStartDate(),
        endDate: getLastDayOfCurrentWeek(),
        key: "selection",
    });

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            payment_date_from: toYMD(dateRange.startDate),
            payment_date_to: toYMD(dateRange.endDate),
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: registrationFeeDailyCollectionReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.payment_date_from = toYMD(dateRange?.startDate);
                    data.payment_date_to = toYMD(dateRange?.endDate);
                }
                query(data);
            })
        );
    }

    function download(type: typeof EXCEL) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    return (
        <Layout
            path="reports/enrollment/registration-fee-daily-collection"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Enrollment Registration Fee Daily Collection Report
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Payment Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                            />
                        </div>

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button onClick={() => download(EXCEL)}>
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default RegistrationFeeDailyCollectionReport;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
