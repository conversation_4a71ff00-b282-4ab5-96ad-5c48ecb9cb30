import { useEffect, useState } from "react";
import React from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    PDF,
    TableColumnType,
    BOOKSHOP,
    CANTEEN,
    terminalAPI,
    GET_ALL_PARAMS,
    posTerminalReportByDailySalesAPI,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    appCurrencySymbol,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, formatNumberForRead, toYMD } from "@/lib/utils";

const PosTerminalReportByDailySales = ({ locale }) => {
    useCheckViewPermit("wallet-report");
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [reports, setReports] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);
    const [total, setTotal] = useState<string | null>(null);

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            type: "",
            terminal_ids: "",
            start_date: "",
            end_date: "",
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "date",
        },
        {
            key: "day",
        },
        {
            key: "total_sales",
            displayAs: `Total Sales (${appCurrencySymbol})`,
        },
    ];

    const { data: terminalOptions, axiosQuery: getTerminalOptions } = useAxios({
        api: terminalAPI,
        locale,
    });

    const { axiosQuery: getReports } = useAxios({
        api: posTerminalReportByDailySalesAPI,
        locale,
        onSuccess(result) {
            setHasSearched(true);
            if (!result.data) {
                setReports([]);
                return;
            }

            const { transactions, grand_total } = result.data;
            const mappedData = transactions.map((transaction) => {
                return {
                    date: transaction.transaction_date,
                    day: transaction.transaction_day,
                    total_sales: formatNumberForRead(transaction.total_sales),
                };
            });

            setTotal(formatNumberForRead(grand_total));
            setReports(mappedData);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: posTerminalReportByDailySalesAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: posTerminalReportByDailySalesAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                if (dateRange) {
                    data.start_date = toYMD(dateRange.startDate);
                    data.end_date = toYMD(dateRange.endDate);
                }
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        if (form.watch("type")) {
            getTerminalOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    type: form.watch("type"),
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [form.watch("type"), locale]);

    return (
        <Layout path="reports/pos-terminal/by-daily-sales" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">POS Terminal Report by Daily Sales</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name={"report_language"}
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="type"
                            label="Type*"
                            isStringOptions={true}
                            options={[BOOKSHOP, CANTEEN]}
                        />

                        <FormSelect
                            control={form.control}
                            name="terminal_ids"
                            label="Terminals*"
                            options={terminalOptions}
                            isMulti={true}
                        />

                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                            />
                        </div>

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <DataTable
                            columns={columns}
                            data={reports}
                            extraRow={[
                                {
                                    value: (
                                        <span className="font-medium text-themeLabel">
                                            GRAND TOTAL
                                        </span>
                                    ),
                                    colSpan: 2,
                                },

                                { value: formatNumberForRead(total) },
                            ]}
                        />
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default PosTerminalReportByDailySales;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
