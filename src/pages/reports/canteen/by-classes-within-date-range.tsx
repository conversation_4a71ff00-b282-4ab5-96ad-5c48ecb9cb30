import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    canteenReportsByDateRangeClassesAPI,
    GET_ALL_PARAMS,
    getFirstDayOfNextWeek,
    getLastDayOfNextWeek,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";

const CanteenReportByClassesWithinDateRange = ({ locale }) => {
    useCheckViewPermit("ecommerce-canteens-classes-date-range-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            start_product_delivery_date: getFirstDayOfNextWeek(),
            end_product_delivery_date: getLastDayOfNextWeek(),
            semester_class_ids: [],
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByDateRangeClassesAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data?.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByDateRangeClassesAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.start_product_delivery_date = toYMD(
                    data.start_product_delivery_date
                );
                data.end_product_delivery_date = toYMD(
                    data.end_product_delivery_date
                );
                query(data);
            })
        );
    }

    function download() {
        submit((data) =>
            getReportsUrl({ params: { ...data, export_type: PDF } })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout
            path="reports/canteen/by-classes-within-date-range"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">Canteen Report by Classes (Date Range)</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="start_product_delivery_date"
                            label="Delivery Start Date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="end_product_delivery_date"
                            label="Delivery End date*"
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                            onChange={() =>
                                form.setValue("semester_class_ids", "")
                            }
                        />

                        <div className="lg:col-span-2">
                            <FormSelect
                                isMulti={true}
                                hasSelectAll={true}
                                control={form.control}
                                name={"semester_class_ids"}
                                label="semester classes*"
                                isDisabled={!form.watch("semester_setting_id")}
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                            />
                        </div>
                        <div className="flex flex-wrap gap-3 lg:col-span-3">
                            <Button onClick={preview}>Preview PDF</Button>

                            <Button onClick={download} variant={"outline"}>
                                Download Report PDF
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default CanteenReportByClassesWithinDateRange;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
