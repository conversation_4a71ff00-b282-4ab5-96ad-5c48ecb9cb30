import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSearchInput from "@/components/ui/FormSearchInput";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    canteenReportsByStudentAPI,
    DATE_FORMAT,
    EXCEL,
    getFirstDayOfNextWeek,
    getLastDayOfNextWeek,
    PDF,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    displayDateTime,
    downloadFile,
    optionUserLabel,
    toYMD,
} from "@/lib/utils";

const CanteenReportByStudent = ({ locale }) => {
    useCheckViewPermit("ecommerce-canteens-by-student-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            student_id: "",
            from_date: getFirstDayOfNextWeek(),
            to_date: getLastDayOfNextWeek(),
        },
    });

    const [reports, setReports] = useState<any[]>([]);
    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<any>(null);
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "receipt_number",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "meal_date",
            modify: (value) => (
                <span className="text-[14px]">{value ?? "-"}</span>
            ),
        },
        {
            key: "items",
        },
        {
            key: "quantity",
            modify: (value) => <span className="pl-2">{value ?? "-"}</span>,
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: canteenReportsByStudentAPI,
        locale,
        onSuccess(result) {
            const _reports =
                result.data?.orders?.map((report) => ({
                    id: report?.id,
                    receipt_number: report?.receipt_number,
                    meal_date: displayDateTime(
                        report?.meal_date,
                        DATE_FORMAT.DMY
                    ),
                    items: report?.items ?? "-",
                    quantity: report?.items_quantity,
                })) ?? [];

            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByStudentAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByStudentAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.from_date = toYMD(data.from_date);
                data.to_date = toYMD(data.to_date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({ params: { ...data, export_type: type } })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout path="reports/canteen/by-student" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">Canteen Report by Student</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <div className="lg:col-span-3">
                            <FormSearchInput
                                control={form.control}
                                name="student_id"
                                label="Student*"
                                displayValue={optionUserLabel(
                                    selectedStudent?.student_number,
                                    selectedStudent?.translations?.name
                                )}
                                onClick={() => setOpenSearch(true)}
                                styleClass="max-w-[400px]"
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="from_date"
                            label="Delivery Start Date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="to_date"
                            label="Delivery End date*"
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <div className="overflow-auto">
                            <div className="report-table-title">
                                {optionUserLabel(
                                    selectedStudent.student_number,
                                    selectedStudent.translations.name
                                )}
                            </div>
                            <DataTable columns={columns} data={reports} />
                        </div>
                    )
                ) : null}
            </Card>
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    setSelection={(student) => {
                        console.log(student);
                        if (student) {
                            form.setValue("student_id", student.id);
                            setSelectedStudent(student);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </Layout>
    );
};

export default CanteenReportByStudent;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
