import { useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import InfoCard from "@/components/ui/InfoCard";
import {
    appCurrencySymbol,
    CANTEEN,
    canteenReportsByDailySalesAPI,
    EXCEL,
    GET_ALL_PARAMS,
    getFirstDayOfNextWeek,
    getLastDayOfNextWeek,
    PDF,
    TableColumnType,
} from "@/lib/constant";
import {
    useAxios,
    useCheckViewPermit,
    useMerchants,
    useSubmit,
} from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    combinedNames,
    downloadFile,
    formatNumberForRead,
    toYMD,
} from "@/lib/utils";

const CanteenReportByMerchantDailySales = ({ locale }) => {
    useCheckViewPermit("ecommerce-canteens-by-merchant-daily-sales-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            from_date: getFirstDayOfNextWeek(),
            to_date: getLastDayOfNextWeek(),
            merchant_id: "",
        },
    });

    const [reports, setReports] = useState<any>();

    const columns: TableColumnType[] = [
        {
            key: "meal_date",
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">
                    {value ?? "-"}
                </span>
            ),
        },
        {
            key: "total_sales",
            displayAs: `Total Sales (${appCurrencySymbol})`,
        },
        {
            key: "total_item_sold",
        },
        {
            key: "total_receipt",
        },
        {
            key: "avg_per_item",
            displayAs: `Average per item (${appCurrencySymbol})`,
        },
        {
            key: "avg_per_receipt",
            displayAs: `Average per receipt (${appCurrencySymbol})`,
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: canteenReportsByDailySalesAPI,
        locale,
        onSuccess(result) {
            const data = result.data;
            const _reports = {
                info: {
                    [`merchant total sales (${appCurrencySymbol})`]:
                        formatNumberForRead(data?.merchant_total_sales),
                    merchant_total_item_sold: data?.merchant_total_item_sold,
                    merchant_total_receipt: data?.merchant_total_receipt,
                    [`merchant average per item (${appCurrencySymbol})`]:
                        formatNumberForRead(data?.merchant_avg_per_item),
                    [`merchant average per receipt (${appCurrencySymbol})`]:
                        formatNumberForRead(data?.merchant_avg_per_receipt),
                },
                orders: data.orders.map((order) => ({
                    meal_date: order?.meal_date ?? "-",
                    avg_per_item: order?.avg_per_item ?? "-",
                    avg_per_receipt: order?.avg_per_receipt ?? "-",
                    total_item_sold: order?.total_item_sold ?? "-",
                    total_receipt: order?.total_receipt ?? "-",
                    total_sales: order?.total_sales ?? "-",
                })),
            };
            setReports(_reports);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByDailySalesAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByDailySalesAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { merchantOptions, isLoadingMerchants } = useMerchants({
        params: { ...GET_ALL_PARAMS, type: CANTEEN, is_active: 1 },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.from_date = toYMD(data.from_date);
                data.to_date = toYMD(data.to_date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({ params: { ...data, export_type: type } })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/canteen/by-merchant-daily-sales" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Canteen Report by Daily Sales (Merchant)
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-4">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <FormSelect
                            control={form.control}
                            name="merchant_id"
                            label="merchant*"
                            options={merchantOptions}
                            isLoading={isLoadingMerchants}
                        />

                        <DatePicker
                            control={form.control}
                            name="from_date"
                            label="delivery start date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="to_date"
                            label="delivery end date*"
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-4">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>

                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {!isEmpty(reports) && (
                    <div className="overflow-auto">
                        <div className="report-table-title">
                            {combinedNames(
                                merchantOptions.find(
                                    (merchant) =>
                                        merchant.id ===
                                        form.watch("merchant_id")
                                )?.translations?.name
                            ) ?? ""}
                        </div>
                        <div className="my-2">
                            <InfoCard data={reports?.info} />
                        </div>
                        <DataTable columns={columns} data={reports.orders} />
                    </div>
                )}
            </Card>
        </Layout>
    );
};

export default CanteenReportByMerchantDailySales;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
