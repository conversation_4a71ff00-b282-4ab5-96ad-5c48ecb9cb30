import { useEffect, useState } from "react";
import { format } from "date-fns";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    canteenReportsByClassesAPI,
    DATE_FORMAT,
    EXCEL,
    GET_ALL_PARAMS,
    getLastDayOfNextWeek,
    getFirstDayOfNextWeek,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";

const CanteenReportByClasses = ({ locale }) => {
    useCheckViewPermit("ecommerce-canteens-classes-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            start_product_delivery_date: getFirstDayOfNextWeek(),
            end_product_delivery_date: getLastDayOfNextWeek(),
            semester_class_ids: [],
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [reports, setReports] = useState<Record<string, any>[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "students",
        },
        {
            key: "product_name",
        },
        {
            key: "quantity",
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: canteenReportsByClassesAPI,
        locale,
        onSuccess(result) {
            if (!result.data) {
                setReports([]);
                return;
            }
            setReports(result.data);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByClassesAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByClassesAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassPrimaryDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
            },
        });
    }, [form.watch("semester_setting_id"), locale]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.start_product_delivery_date = toYMD(
                    data.start_product_delivery_date
                );
                data.end_product_delivery_date = toYMD(
                    data.end_product_delivery_date
                );
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/canteen/by-classes" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">Canteen Report by Classes</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="start_product_delivery_date"
                            label="Delivery Start Date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="end_product_delivery_date"
                            label="Delivery End date*"
                        />

                        {/*<FormSelect*/}
                        {/*    control={form.control}*/}
                        {/*    name={"semester_setting_id"}*/}
                        {/*    label="Semester*"*/}
                        {/*    options={semesterOptions}*/}
                        {/* onChange={() =>
                                form.setValue("semester_class_ids", "")
                            } */}
                        {/*/>*/}

                        {/*<div className="lg:col-span-2">*/}
                        {/*    <FormSelect*/}
                        {/*        isMulti={true}*/}
                        {/*        control={form.control}*/}
                        {/*        name={"semester_class_ids"}*/}
                        {/*        label="semester classes*"*/}
                        {/*        isDisabled={!form.watch("semester_setting_id")}*/}
                        {/*        options={*/}
                        {/*            semesterClassOptions?.map((option) => ({*/}
                        {/*                id: option?.id,*/}
                        {/*                name: `${option?.class_model?.name}`,*/}
                        {/*            })) ?? []*/}
                        {/*        }*/}
                        {/*    />*/}
                        {/*</div>*/}
                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched && isEmpty(reports) ? (
                    <div className="h-20 text-center text-themeLabel">
                        No Record Found
                    </div>
                ) : (
                    <div className="grid gap-y-5 overflow-auto">
                        {reports.map((item) => (
                            <div
                                key={item?.date}
                                className="mb-4 overflow-hidden rounded-md border"
                            >
                                <div className="mr-auto bg-themeGreen4 px-2 py-2 font-semibold text-themeGreen2">
                                    {format(item?.date, DATE_FORMAT.DMY)}
                                </div>
                                {item?.classes?.map((classItem) => (
                                    <div key={classItem?.class_name}>
                                        <div className="c-text-size bg-themeGreen3 px-2 py-2 font-semibold text-gray-600">
                                            {classItem?.class_name}
                                            <span className="ml-2 inline-block font-medium">{`( ${classItem?.quantity} Units)`}</span>
                                        </div>

                                        <DataTable
                                            key={classItem?.class_name}
                                            isSmaller={true}
                                            styleClass="border-0 border-t  rounded-none"
                                            columns={columns}
                                            data={classItem?.order_items?.map(
                                                (item) => ({
                                                    ...item,
                                                    students:
                                                        item?.students
                                                            .map(
                                                                (student) =>
                                                                    student?.name
                                                            )
                                                            .join(", ") ?? "-",
                                                })
                                            )}
                                        />
                                    </div>
                                ))}
                            </div>
                        ))}
                    </div>
                )}
            </Card>
        </Layout>
    );
};

export default CanteenReportByClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
