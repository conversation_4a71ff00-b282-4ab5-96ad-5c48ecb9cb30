import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import InfoCard from "@/components/ui/InfoCard";
import {
    appCurrencySymbol,
    canteenReportsByDailyCollectionAPI,
    EXCEL,
    PDF,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, formatNumberForRead, toYMD } from "@/lib/utils";

const CanteenReportByDailyCollection = ({ locale }) => {
    useCheckViewPermit("ecommerce-canteens-by-daily-collection-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            date: "",
        },
    });

    const [reports, setReports] = useState<Record<string, any>[]>([]);
    const [info, setInfo] = useState<Record<string, any>>();
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "product_name",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "unit_price",
            displayAs: `Unit Price (${appCurrencySymbol})`,
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "quantity",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "total_price",
            displayAs: `Total Price (${appCurrencySymbol})`,
            modify: (value) => <span>{value ?? "-"}</span>,
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: canteenReportsByDailyCollectionAPI,
        locale,
        onSuccess(result) {
            const _reports = Object.entries(result.data?.orders)?.map(
                ([merchant, data]: [merchant: string, data: any]) => {
                    return {
                        merchant,
                        total_quantity: data?.total_group_quantity,
                        total_price: formatNumberForRead(
                            data?.total_group_price
                        ),
                        orders: data?.orders?.map((order: any) => ({
                            product_name: order?.product_name,
                            unit_price: order?.unit_price,
                            quantity: order?.quantity,
                            total_price: order?.total_price,
                        })),
                    };
                }
            );
            setInfo({
                date: result.data?.date,
                total_collection:
                    appCurrencySymbol +
                    " " +
                    formatNumberForRead(result.data?.total_collection),
            });
            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByDailyCollectionAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByDailyCollectionAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.date = toYMD(data.date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => {
            getReports({ params: data });
        });
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) => {
            getReportsUrl({
                params: { ...data, export_type: type },
            });
        });
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout path="reports/canteen/by-daily-collection" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">Canteen Report by Daily Collection</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="date"
                            label="Delivery Date*"
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <div className="border-t border-dashed pt-5">
                            {info && (
                                <div className="mb-4">
                                    <InfoCard data={info} />
                                </div>
                            )}
                            <div className="grid gap-y-5 overflow-auto">
                                {reports.map((reportGroup, index) => (
                                    <div key={reportGroup?.merchant + index}>
                                        <div className="report-table-title">
                                            {reportGroup?.merchant}
                                        </div>
                                        <DataTable
                                            columns={columns}
                                            data={reportGroup.orders}
                                            extraRow={[
                                                {
                                                    value: (
                                                        <span className="font-medium text-themeLabel">
                                                            TOTAL
                                                        </span>
                                                    ),
                                                    colSpan: 2,
                                                },
                                                {
                                                    value: reportGroup.total_quantity,
                                                },
                                                {
                                                    value: reportGroup.total_price,
                                                },
                                            ]}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default CanteenReportByDailyCollection;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
