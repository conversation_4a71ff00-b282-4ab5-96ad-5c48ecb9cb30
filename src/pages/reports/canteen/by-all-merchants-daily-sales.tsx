import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import InfoCard from "@/components/ui/InfoCard";
import {
    appCurrencySymbol,
    canteenReportsByDailySalesGroupAPI,
    EXCEL,
    getFirstDayOfNextWeek,
    getLastDayOfNextWeek,
    PDF,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    downloadFile,
    formatNumberForRead,
    getDayName,
    toYMD,
} from "@/lib/utils";

const CanteenReportByAllMerchantsDailySales = ({ locale }) => {
    useCheckViewPermit(
        "ecommerce-canteens-by-daily-sales-group-by-merchant-report"
    );
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            from_date: getFirstDayOfNextWeek(),
            to_date: getLastDayOfNextWeek(),
        },
    });

    const [reports, setReports] = useState<Record<string, any>[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const columns: TableColumnType[] = [
        {
            key: "meal_date",
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">
                    {value ?? "-"}
                </span>
            ),
        },
        {
            key: "day",
        },
        {
            key: "avg_per_item",
            displayAs: `Average per item (${appCurrencySymbol})`,
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "avg_per_receipt",
            displayAs: `Average per receipt (${appCurrencySymbol})`,
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "total_item_sold",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "total_receipt",
            modify: (value) => <span>{value ?? "-"}</span>,
        },
        {
            key: "total_sales",
            displayAs: `Total Sales (${appCurrencySymbol})`,
            modify: (value) => <span>{value ?? "-"}</span>,
        },
    ];

    const { axiosQuery: getReports } = useAxios({
        api: canteenReportsByDailySalesGroupAPI,
        locale,
        onSuccess(result) {
            const _reports = Object.entries(result.data?.orders)?.map(
                ([merchant, data]: [merchant: string, data: any]) => {
                    return {
                        merchant,
                        info: {
                            [`merchant average per item (${appCurrencySymbol})`]:
                                formatNumberForRead(
                                    data?.merchant_avg_per_item
                                ),
                            [`merchant average per receipt (${appCurrencySymbol})`]:
                                formatNumberForRead(
                                    data?.merchant_avg_per_receipt
                                ),
                            merchant_total_item_sold:
                                data?.merchant_total_item_sold,
                            merchant_total_receipt:
                                data?.merchant_total_receipt,
                            [`merchant total sales (${appCurrencySymbol})`]:
                                formatNumberForRead(data?.merchant_total_sales),
                        },
                        orders: data?.orders?.map((order: any) => ({
                            meal_date: order?.meal_date,
                            day: getDayName(order?.meal_date),
                            avg_per_item: order?.avg_per_item,
                            avg_per_receipt: order?.avg_per_receipt,
                            total_item_sold: order?.total_item_sold,
                            total_receipt: order?.total_receipt,
                            total_sales: order?.total_sales,
                        })),
                    };
                }
            );
            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByDailySalesGroupAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByDailySalesGroupAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.from_date = toYMD(data.from_date);
                data.to_date = toYMD(data.to_date);
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({ params: { ...data, export_type: type } })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    return (
        <Layout
            path="reports/canteen/by-all-merchants-daily-sales"
            locale={locale}
        >
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">
                    Canteen Report by Daily Sales (All Merchants)
                </h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="from_date"
                            label="delivery start date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="to_date"
                            label="delivery end date*"
                        />

                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>

                            <Button
                                variant={"outline"}
                                size={"smaller"}
                                onClick={() => download(EXCEL)}
                            >
                                Download Excel
                            </Button>
                            <Button
                                variant={"outline"}
                                size={"smaller"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched ? (
                    isEmpty(reports) ? (
                        <div className="h-20 text-center text-themeLabel">
                            No Record Found
                        </div>
                    ) : (
                        <div className="overflow-auto">
                            <div className="grid gap-y-5">
                                {reports.map((reportGroup, index) => (
                                    <div key={reportGroup?.merchant + index}>
                                        <div className="report-table-title">
                                            {reportGroup?.merchant}
                                        </div>
                                        <div className="my-2">
                                            <InfoCard
                                                data={reportGroup?.info}
                                            />
                                        </div>
                                        <DataTable
                                            columns={columns}
                                            data={reportGroup.orders}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    )
                ) : null}
            </Card>
        </Layout>
    );
};

export default CanteenReportByAllMerchantsDailySales;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
