import { Fragment, useEffect, useState } from "react";
import { format } from "date-fns";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    canteenReportsByWeeklyClassesAPI,
    DATE_FORMAT,
    EXCEL,
    GET_ALL_PARAMS,
    getLastDayOfNextWeek,
    getFirstDayOfNextWeek,
    PDF,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    TableColumnType,
    PRIMARY,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { toYMD } from "@/lib/utils";

const CanteenReportByWeeklyClasses = ({ locale }) => {
    useCheckViewPermit("ecommerce-canteens-classes-weekly-report");

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            start_product_delivery_date: getFirstDayOfNextWeek(),
            end_product_delivery_date: getLastDayOfNextWeek(),
            semester_class_ids: [],
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [reports, setReports] = useState<Record<string, any>[]>([]);
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const { axiosQuery: getReports } = useAxios({
        api: canteenReportsByWeeklyClassesAPI,
        locale,
        onSuccess(result) {
            if (!result.data) {
                setReports([]);
                setColumns([]);
                return;
            }
            const _dates = result.data?.dates.map((date) =>
                format(date, DATE_FORMAT.EDMY)
            );
            setColumns([
                {
                    key: "student_name",
                },
                ..._dates.map((date) => ({
                    key: date,
                    modifyHeader: (value) => (
                        <div className="whitespace-nowrap text-[14px] leading-tight">
                            {value}
                        </div>
                    ),
                    modify: (value) => {
                        if (value.length === 0) {
                            return "-";
                        }
                        return value.map((item, index) => (
                            <Fragment key={index}>
                                <span className="text-[13px] leading-tight">
                                    {item?.product_name + " x" + item?.quantity}
                                </span>
                                <br />
                            </Fragment>
                        ));
                    },
                })),
            ]);

            const _reports = result.data?.classes?.map((classData) => ({
                class: classData?.class_name,
                studentOrders: classData?.students.map((student) => {
                    const datedOrderItems = student?.dates.reduce(
                        (acc, data) => {
                            const formattedDate = data?.date
                                ? format(data.date, DATE_FORMAT.EDMY)
                                : "";
                            acc[formattedDate] = data?.order_items;
                            return acc;
                        },
                        {}
                    );
                    return {
                        student_name: student?.name,
                        ...datedOrderItems,
                    };
                }),
            }));

            setReports(_reports);
            setHasSearched(true);
        },
    });

    const { axiosQuery: getReportsUrl } = useAxios({
        api: canteenReportsByWeeklyClassesAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: canteenReportsByWeeklyClassesAPI,
        locale,
        onSuccess(result) {
            window.open(result.data?.url, "_blank");
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        form.setValue("semester_class_ids", []);
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    useEffect(() => {
        const subscription = form.watch(() => {
            setHasSearched(false);
        });
        return () => subscription.unsubscribe();
    }, [form]);

    const { initLoader } = useSubmit();

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                data.start_product_delivery_date = toYMD(
                    data.start_product_delivery_date
                );
                data.end_product_delivery_date = toYMD(
                    data.end_product_delivery_date
                );
                query(data);
            })
        );
    }

    function view() {
        submit((data) => getReports({ params: data }));
    }

    function download(type: typeof EXCEL | typeof PDF) {
        submit((data) =>
            getReportsUrl({
                params: { ...data, export_type: type },
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    return (
        <Layout path="reports/canteen/by-weekly-classes" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-4">Canteen Report by Classes (Weekly)</h2>
                <Form {...form}>
                    <form className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="report_language"
                            label="report language*"
                            options={activeLanguages?.map((language) => ({
                                id: language?.code,
                                name: language?.name,
                            }))}
                        />

                        <DatePicker
                            control={form.control}
                            name="start_product_delivery_date"
                            label="Delivery Start Date*"
                        />

                        <DatePicker
                            control={form.control}
                            name="end_product_delivery_date"
                            label="Delivery End date*"
                        />

                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label="Semester*"
                            options={semesterOptions}
                            isSortByName={false}
                        />

                        <div className="lg:col-span-2">
                            <FormSelect
                                isMulti={true}
                                hasSelectAll={true}
                                control={form.control}
                                name={"semester_class_ids"}
                                label="semester classes*"
                                isDisabled={!form.watch("semester_setting_id")}
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                            />
                        </div>
                        <div className="flex flex-wrap items-center gap-3 lg:col-span-3">
                            <Button onClick={view}>View Report</Button>
                            <Button onClick={preview}>Preview PDF</Button>
                            <Button
                                variant={"outline"}
                                onClick={() => download(PDF)}
                            >
                                Download PDF
                            </Button>
                        </div>
                    </form>
                </Form>

                {hasSearched && isEmpty(reports) ? (
                    <div className="h-20 text-center text-themeLabel">
                        No Record Found
                    </div>
                ) : (
                    <div className="grid gap-y-5 overflow-auto">
                        {reports.map((report, index) => (
                            <div key={report.class + index}>
                                <div className="report-table-title">
                                    {report.class}
                                </div>
                                <DataTable
                                    key={index}
                                    columns={columns}
                                    data={report.studentOrders}
                                />
                            </div>
                        ))}
                    </div>
                )}
            </Card>
        </Layout>
    );
};

export default CanteenReportByWeeklyClasses;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
