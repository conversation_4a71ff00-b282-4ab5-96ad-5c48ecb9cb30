import { useEffect, useState } from "react";
import clsx from "clsx";
import { capitalize, isArray } from "lodash";
import { useRouter } from "next/router";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterFeeForm from "@/components/forms/accounting/FilterFeeForm";
import ViewFee from "@/components/forms/accounting/ViewFee";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    TableColumnType,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    accountingFeeAPI,
    appCurrencySymbol,
    COMPLETED,
    APPLYING,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    convertDateTime,
    displayDateTime,
    formatNumberForRead,
} from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const FeeManagement = ({ locale }) => {
    useCheckViewPermit("fees-create-and-assign");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const router = useRouter();
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);

    const [viewId, setViewId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getFeeRecords } = useAxios({
        api: accountingFeeAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "name",
            },
            {
                key: "product",
            },
            {
                key: "unit_price",
                displayAs: `Unit Price (${appCurrencySymbol})`,
            },
            {
                key: "apply_at",
                modify: (value) => {
                    return <span className="text-sm">{value}</span>;
                },
                hasSort: true,
            },
            {
                key: "applied_at",
                modify: (value) => {
                    return <span className="text-sm">{value}</span>;
                },
                hasSort: true,
            },
            {
                key: "status",
                hasSort: true,
                modify: (value) => (
                    <span
                        className={clsx(
                            value === COMPLETED && "text-green-600",
                            value === APPLYING && "text-yellow-500"
                        )}
                    >
                        {capitalize(value)}
                    </span>
                ),
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  name: item?.name,
                  product: item?.product?.translations?.name?.[locale],
                  unit_price: formatNumberForRead(item?.unit_price),
                  apply_at: item.apply_at
                      ? convertDateTime(item.apply_at)
                      : "-",
                  applied_at: item.applied_at
                      ? displayDateTime(item.applied_at, DATE_FORMAT.forDisplay)
                      : "-",
                  status: item?.status,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function fetchFeeRecords() {
        getFeeRecords({
            params: {
                includes: ["product", "studentAssignments.student"],
                order_by: {
                    apply_at: "desc",
                    created_at: "desc",
                },
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchFeeRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <>
            <Layout locale={locale} path="accounting/fee-management">
                <Card styleClass="table-card">
                    <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                        <h2>Fee Management</h2>
                        <div className="flex items-center gap-x-3">
                            {hasPermit("fees-create-and-assign") && (
                                <Button
                                    onClick={() =>
                                        router.push("/accounting/create-fee")
                                    }
                                >
                                    Add Fee
                                </Button>
                            )}
                            {/* <TableFilterBtn
                                filter={filter}
                                onClick={() => setOpenFilter(true)}
                            /> */}
                            <FilterChevronButton />
                        </div>
                    </div>

                    <FilterFormWrapper>
                        <FilterFeeForm
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                        />
                    </FilterFormWrapper>

                    <DataTable
                        columns={columns}
                        data={definedData(data)}
                        pagination={pagination}
                        setPagination={setPagination}
                        changePage={(arg) => setFilter({ ...filter, ...arg })}
                        sorted={filter?.order_by}
                        sort={onSort}
                        actionMenu={({ cell }) => {
                            const id = cell.row.original.id;
                            const status = capitalize(cell.row.original.status);

                            return (
                                <ActionDropdown>
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setViewId(cell.row.original.id)
                                        }
                                    >
                                        View
                                    </DropdownMenuItem>

                                    {hasPermit(
                                        "unpaid-item-assignment-admin-delete"
                                    ) &&
                                        status == "New" && (
                                            <>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    className="c-text-size text-red-600"
                                                    onClick={() =>
                                                        setTargetDeleteId(id)
                                                    }
                                                >
                                                    Delete
                                                </DropdownMenuItem>
                                            </>
                                        )}
                                </ActionDropdown>
                            );
                        }}
                    />
                </Card>

                {/* view */}
                <Modal open={viewId} onOpenChange={setViewId} size="large">
                    <ViewFee id={viewId} close={() => setViewId(null)} />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={accountingFeeAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={fetchFeeRecords}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterFeeForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </Layout>
        </>
    );
};

export default FeeManagement;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
