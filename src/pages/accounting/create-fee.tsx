import { useEffect, useState } from "react";
import { format } from "date-fns";
import { chunk, indexOf, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useRouter } from "next/router";
import { useFieldArray, useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import CreateFeeForm from "@/components/forms/accounting/CreateFeeForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import FormDivider from "@/components/ui/FormDivider";
import Modal from "@/components/ui/Modal";
import RaisedButton from "@/components/ui/RaisedButton";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    accountingFeeCreateAPI,
    appCurrencySymbol,
    CHUNKED_FILTER_PARAMS,
    DATE_FORMAT,
    FULL,
    STUDENT,
} from "@/lib/constant";
import { useAxios, usePaginatedTable, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    combineSemesterClass,
    displayDateTime,
    formatNumberForRead,
    showBackendFormError,
    toUTC,
} from "@/lib/utils";

const CreateFee = ({ locale }) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const router = useRouter();

    const form = useForm<any>({
        defaultValues: {
            studentRecords: [],
            feeRecords: [],
        },
    });

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        chunk(form.getValues().studentRecords, CHUNKED_FILTER_PARAMS.per_page)
    );
    const [openCreate, setOpenCreate] = useState(false);
    const [openEdit, setOpenEdit] = useState(false);
    const [editFeeData, setEditFeeData] = useState<any>(null);

    function closeForm() {
        setOpenCreate(false);
        setOpenEdit(false);
    }

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    const { append: appendStudent, remove: removeStudent } = useFieldArray({
        control: form.control,
        name: "studentRecords",
    });

    const studentColumns = [
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
            modify: (value, cell) => {
                const index = indexOf(
                    form.watch("studentRecords").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="grid min-w-[200px] gap-y-1">
                        <span>{value}</span>
                        {form.formState.errors?.studentRecords?.[index]
                            ?.student_id && (
                            <span className="text-xs text-destructive">
                                Invalid student
                            </span>
                        )}
                    </div>
                );
            },
        },
        {
            key: "class",
            hasSort: true,
        },
        {
            key: "_",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("studentRecords").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            removeStudent(index);
                            onRemove(cell.row.original?.id);
                        }}
                    />
                );
            },
        },
    ];

    const {
        append: appendFee,
        update: updateFee,
        remove: removeFee,
    } = useFieldArray({
        control: form.control,
        name: "feeRecords",
    });

    const feeColumns = [
        {
            key: "name",
            displayAs: "Fee Name",
        },

        {
            key: "unit_price",
            modifyHeader: () => (
                <span className="whitespace-nowrap">{`Unit Price (${appCurrencySymbol})`}</span>
            ),
        },
        {
            key: "quantity",
        },

        {
            key: "no_of_occurrences",
            displayAs: "no. of occurrences",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("feeRecords").map((fee) => fee.id),
                    cell.row.id
                );
                const no_of_occurrences =
                    form.watch("feeRecords")[index]?.recurring_settings?.length;

                return <span className="text-[14px]">{no_of_occurrences}</span>;
            },
        },
        {
            key: "price",
            modifyHeader: () => (
                <span className="whitespace-nowrap">{`Occurrence x Price (${appCurrencySymbol})`}</span>
            ),
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("feeRecords").map((fee) => fee.id),
                    cell.row.id
                );
                const no_of_occurrences =
                    form.watch("feeRecords")[index]?.recurring_settings
                        ?.length ?? 1;
                const price =
                    form.watch("feeRecords")[index]?.unit_price *
                    no_of_occurrences;
                return (
                    <span className="text-[14px]">
                        {formatNumberForRead(price)}
                    </span>
                );
            },
        },
        {
            key: "apply_at",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("feeRecords").map((fee) => fee.id),
                    cell.row.id
                );
                const apply_at = form.watch("feeRecords")[index]?.apply_at;
                const applyAt = format(apply_at, DATE_FORMAT.YMD_HMS);
                return (
                    <span className="text-[14px]">
                        {displayDateTime(applyAt, DATE_FORMAT.forDisplay)}
                    </span>
                );
            },
        },
    ];

    function onAddStudent(selection) {
        const currentIds = form
            .getValues()
            .studentRecords.map((student) => student.id.toString());

        const newStudents = selection
            .filter((student) => !currentIds.includes(student.id.toString()))
            .map((student) => ({
                id: student?.id,
                student_number: student?.student_number,
                name: combinedNames(student?.translations?.name),
                class: combineSemesterClass(student?.current_primary_class),
            }));

        const updatedRecords = form.getValues("studentRecords");
        setTargetsChunk(chunk(updatedRecords, CHUNKED_FILTER_PARAMS.per_page));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        onAdd(selection);
    }

    function onAddFee(feeData) {
        appendFee(feeData);
        closeForm();
    }

    function onEditFee(feeData) {
        const feeRecords = form.getValues("feeRecords");
        const index = feeRecords.findIndex(
            (record) => record.id === feeData?.id
        );

        if (index !== -1) {
            updateFee(index, feeData);
        }

        closeForm();
    }

    const { axiosPost: createAssignPaymentFee, error: postError } = useAxios({
        api: accountingFeeCreateAPI,
        onSuccess: () => {
            form.reset();
            router.push("/accounting/fee-management");
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                const { studentRecords, feeRecords } = data;
                const student_ids = studentRecords.map((student) => student.id);

                feeRecords.forEach((fee) => {
                    fee.apply_at = toUTC(fee.apply_at);
                });

                const payload = {
                    student_ids,
                    fees: feeRecords,
                };

                createAssignPaymentFee(payload, { showErrorInToast: true });
            })
        );
    }

    useEffect(() => {
        setTargetsChunk(
            chunk(form.watch("studentRecords"), CHUNKED_FILTER_PARAMS.per_page)
        );
    }, [form.watch("studentRecords")]);

    return (
        <Layout path="accounting/create-fee" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5 pt-2">Create Fee</h2>
                <Form {...form}>
                    <form className="pb-7" onSubmit={onSubmit}>
                        <div className="pb-4">
                            <RaisedButton
                                name="Select Students"
                                onClick={() => setOpenStudentSearch(true)}
                            />
                        </div>

                        {targetsChunk?.length > 0 && pagination && (
                            <DataTable
                                isSmaller
                                columns={studentColumns}
                                data={
                                    (clonedFilteredChunk ?? targetsChunk)[
                                        pagination?.current_page - 1
                                    ]
                                }
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                hasPerPage={false}
                                sorted={filter?.order_by}
                                sort={onSort}
                                styleClass="overflow-visible"
                            />
                        )}

                        {form.getValues("studentRecords")?.length > 0 && (
                            <>
                                <FormDivider />
                                <div className="pt-4">
                                    <RaisedButton
                                        name="Add Fees"
                                        onClick={() => setOpenCreate(true)}
                                    />
                                </div>
                            </>
                        )}

                        {form.getValues("feeRecords")?.length > 0 && (
                            <>
                                <div className="pt-5">
                                    <DataTable
                                        isSmaller
                                        columns={feeColumns}
                                        data={form.getValues("feeRecords")}
                                        changePage={(arg) =>
                                            setFilter({ ...filter, ...arg })
                                        }
                                        styleClass="overflow-visible"
                                        actionMenu={({ cell }) => (
                                            <>
                                                <ActionDropdown>
                                                    <DropdownMenuItem
                                                        className="c-text-size"
                                                        onClick={() => {
                                                            console.log(
                                                                "editdata",
                                                                cell.row
                                                                    .original
                                                            );
                                                            setEditFeeData(
                                                                cell.row
                                                                    .original
                                                            );
                                                            setOpenEdit(true);
                                                        }}
                                                    >
                                                        Edit / View
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        className="c-text-size text-red-600"
                                                        onClick={() => {
                                                            removeFee(
                                                                cell.row.index
                                                            );
                                                            onRemove(
                                                                cell.row
                                                                    .original
                                                                    ?.id
                                                            );
                                                        }}
                                                    >
                                                        Delete
                                                    </DropdownMenuItem>
                                                </ActionDropdown>
                                            </>
                                        )}
                                    />
                                </div>
                                {hasPermit("fees-create-and-assign") && (
                                    <div className="mt-6 flex justify-end gap-x-3">
                                        <Button
                                            type="submit"
                                            disabled={
                                                isEmpty(
                                                    form.getValues(
                                                        "studentRecords"
                                                    )
                                                ) ||
                                                isEmpty(
                                                    form.getValues("feeRecords")
                                                )
                                            }
                                        >
                                            Submit
                                        </Button>
                                    </div>
                                )}
                            </>
                        )}
                    </form>
                </Form>
            </Card>

            {/* add students */}
            <Modal
                open={openStudentSearch}
                onOpenChange={setOpenStudentSearch}
                size="large"
            >
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(selection) => onAddStudent(selection)}
                    close={() => setOpenStudentSearch(false)}
                    otherFilterParams={{
                        response: FULL,
                        includes: [
                            "currentSemesterPrimaryClass.semesterSetting",
                            "currentSemesterPrimaryClass.semesterClass.classModel",
                        ],
                    }}
                />
            </Modal>

            {/* add fee */}
            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <CreateFeeForm
                    isCreate={true}
                    close={closeForm}
                    submitFeeForm={onAddFee}
                />
            </Modal>

            {/* update fee */}
            <Modal open={openEdit} onOpenChange={setOpenEdit} size="medium">
                <CreateFeeForm
                    feeData={editFeeData}
                    close={closeForm}
                    submitFeeForm={onEditFee}
                />
            </Modal>
        </Layout>
    );
};

export default CreateFee;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
