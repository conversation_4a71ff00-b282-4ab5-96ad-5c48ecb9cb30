import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { capitalize, isArray } from "lodash";
import Layout from "@/components/Layout";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import BillingDocumentChangeStatusForm from "@/components/forms/accounting/BillingDocumentChangeStatusForm";
import FilterBillingDocumentForm from "@/components/forms/accounting/FilterBillingDocumentForm";
import ViewBillingDocument from "@/components/forms/accounting/ViewBillingDocument";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    appCurrencySymbol,
    billingDocumentAPI,
    CONFIRMED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    PAID,
    PARTIAL,
    TableColumnType,
    UNPAID,
    VOIDED,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    displayDateTime,
    formatNumberForRead,
    replaceAll,
    strStartCase,
} from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const BillingDocuments = ({ locale }) => {
    useCheckViewPermit("billing-document-admin-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);

    const [viewId, setViewId] = useState(null);
    const [targetId, setTargetId] = useState(null);

    const { data, axiosQuery: getBillingDocuments } = useAxios({
        api: billingDocumentAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchBillingDocuments() {
        getBillingDocuments({
            params: {
                includes: ["billTo", "payments"],
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchBillingDocuments();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            const _columns: TableColumnType[] = [
                {
                    key: "reference_no",
                    displayAs: "Reference No",
                    hasSort: true,
                },
                {
                    key: "payment_reference_no",
                    displayAs: "Payment Reference No",
                },
                {
                    key: "document_date",
                    hasSort: true,
                },
                {
                    key: "paid_at",
                    hasSort: true,
                    modify: (value) => {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {displayDateTime(value, DATE_FORMAT.forDisplay)}
                            </span>
                        );
                    },
                },
                {
                    key: "type",
                    hasSort: true,
                    modify: (value) => {
                        return (
                            <span className="text-sm">
                                {replaceAll(value, "_", " ")}
                            </span>
                        );
                    },
                },
                {
                    key: "sub_type",
                    hasSort: true,
                    modify: (value) => (
                        <div className="leading-none">
                            {strStartCase(value ?? "-")}
                        </div>
                    ),
                },
                {
                    key: "payment_status",
                    hasSort: true,
                    modify: (value) => (
                        <span
                            className={clsx(
                                value === PARTIAL && "text-yellow-500",
                                value === PAID && "text-green-600",
                                value === UNPAID && "text-red-500"
                            )}
                        >
                            {capitalize(value)}
                        </span>
                    ),
                },
                {
                    key: "bill_to_name",
                    hasSort: true,
                },

                {
                    key: "bill_to_reference_number",
                    hasSort: true,
                },
                {
                    key: "payment_due_date",
                    hasSort: true,
                },
                {
                    key: "amount_after_tax",
                    displayAs: `Amount (${appCurrencySymbol})`,
                    hasSort: true,
                },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value) => (
                        <span
                            className={clsx(
                                value === CONFIRMED && "text-green-600",
                                value === DRAFT && "text-yellow-500",
                                value === VOIDED && "text-red-500"
                            )}
                        >
                            {capitalize(value)}
                        </span>
                    ),
                },
            ];
            setColumns(_columns);
        }
    }, [data]);

    function definedData() {
        return isArray(data)
            ? data.map((document) => ({
                  id: document?.id,
                  reference_no: document?.reference_number ?? "-",
                  document_date: document?.document_date ?? "-",
                  paid_at:
                      displayDateTime(document?.paid_at, DATE_FORMAT.YMD_HMS) ??
                      "-",
                  type: document?.type ?? "-",
                  sub_type: document?.sub_type?.replaceAll("_", " "),
                  status: document?.status ?? "-",
                  payment_status: document?.payment_status ?? "-",
                  bill_to_name: document?.bill_to_name,
                  bill_to_reference_number:
                      document?.bill_to_reference_number ?? "-",
                  payment_due_date: document?.payment_due_date ?? "-",
                  amount_after_tax: formatNumberForRead(
                      document?.amount_after_tax
                  ),
                  payment_reference_no:
                      document?.payments?.find((p) => p?.paid_at !== null)
                          ?.payment_reference_no ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    return (
        <Layout locale={locale} path="accounting/billing-documents">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">Billing Documents</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["includes"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterBillingDocumentForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setViewId(cell.row.original.id)
                                    }
                                >
                                    View
                                </DropdownMenuItem>
                                {hasPermit("billing-document-status-update") &&
                                    cell.row.original.status !== VOIDED && (
                                        <>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() =>
                                                    setTargetId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                Change Status
                                            </DropdownMenuItem>
                                        </>
                                    )}
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* view */}
            <Modal open={viewId} onOpenChange={setViewId} size="large">
                <ViewBillingDocument
                    id={viewId}
                    close={() => setViewId(null)}
                />
            </Modal>

            {/* change status */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <BillingDocumentChangeStatusForm
                    id={targetId}
                    currentStatus={
                        data?.find((item) => item.id === targetId)?.status
                    }
                    close={() => setTargetId(null)}
                    refresh={fetchBillingDocuments}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter} size="medium">
                <FilterBillingDocumentForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default BillingDocuments;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
