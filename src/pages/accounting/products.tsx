import { isArray, lowerCase } from "lodash";
import FilterAccountingProductForm from "@/components/forms/accounting/FilterAccountingProductForm";
import AccountingProductForm from "@/components/forms/accounting/ProductForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import {
    accountingProductAPI,
    ACTIVE,
    appCurrencySymbol,
    INACTIVE,
    TableColumnType,
} from "@/lib/constant";
import { getNameColumns } from "@/lib/utils";

const AccountingProducts = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="accounting/products"
        api={accountingProductAPI}
        definedColumn={(activeLanguages) => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                },
                ...getNameColumns(activeLanguages),
                {
                    key: "category",
                },
                {
                    key: "sub_category_1",
                },
                {
                    key: "sub_category_2",
                },
                {
                    key: "uom_code",
                    displayAs: "UOM Code",
                },
                {
                    key: "gl_account_code",
                    displayAs: "GL Account Code",
                },
                {
                    key: "unit_price",
                    displayAs: `Unit Price (${appCurrencySymbol})`,
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      code: item.code,
                      category: item?.category,
                      sub_category_1: item?.sub_category_1 ?? "-",
                      sub_category_2: item?.sub_category_2 ?? "-",
                      uom_code: item?.uom_code,
                      gl_account_code: item?.gl_account_code,
                      unit_price: item?.unit_price,
                      is_active: lowerCase(item?.is_active ? ACTIVE : INACTIVE),
                      ...item?.translations?.name,
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return name === "is_active"
                ? { [name]: direction }
                : { name: { [name]: direction } };
        }}
        form={(params) => <AccountingProductForm {...params} />}
        filterForm={(params) => <FilterAccountingProductForm {...params} />}
        formSize="medium"
        viewPermit="master-product-view"
        createPermit="master-product-create"
        updatePermit="master-product-update"
        deletePermit="master-product-delete"
    />
);

export default AccountingProducts;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
