import { useEffect, useState } from "react";
import { isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterScholarshipForm from "@/components/forms/accounting/FilterScholarshipForm";
import ScholarshipForm from "@/components/forms/accounting/ScholarshipForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    TableColumnType,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    scholarshipAPI,
    ACTIVE,
    INACTIVE,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    convertDateTime,
    displayDateTime,
    refreshForUpdate,
} from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const ScholarshipManagement = ({ locale }) => {
    useCheckViewPermit("scholarship-admin-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getFeeRecords } = useAxios({
        api: scholarshipAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "name",
                hasSort: true,
            },
            {
                key: "award_body",
            },
            {
                key: "application_open_at",
                modify: (value) => {
                    return <span className="text-sm">{value}</span>;
                },
                hasSort: true,
            },
            {
                key: "application_close_at",
                modify: (value) => {
                    return <span className="text-sm">{value}</span>;
                },
                hasSort: true,
            },
            {
                key: "is_active",
                displayAs: "Status",
                hasSort: true,
                modify: (value) => (
                    <div className={`cell-status ${value}`}>{value}</div>
                ),
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  name: item?.name,
                  award_body: item?.award_body,
                  application_open_at: item?.application_open_at
                      ? convertDateTime(item?.application_open_at)
                      : "-",
                  application_close_at: item?.application_close_at
                      ? convertDateTime(item?.application_close_at)
                      : "-",
                  is_active: item
                      ? lowerCase(item?.is_active ? ACTIVE : INACTIVE)
                      : "",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    function fetchScholarshipRecords() {
        getFeeRecords({
            params: {
                order_by: {
                    apply_at: "desc",
                    created_at: "desc",
                },
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchScholarshipRecords();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <>
            <Layout locale={locale} path="accounting/scholarship">
                <Card styleClass="table-card">
                    <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                        <h2>Scholarship</h2>
                        <div className="flex gap-x-3">
                            {hasPermit("scholarship-admin-create") && (
                                <Button
                                    size="smallerOnMobile"
                                    onClick={() => setOpenCreate(true)}
                                >
                                    Add Scholarship
                                </Button>
                            )}
                            {/* <TableFilterBtn
                                filter={filter}
                                onClick={() => setOpenFilter(true)}
                            /> */}
                            <FilterChevronButton />
                        </div>
                    </div>

                    <FilterFormWrapper>
                        <FilterScholarshipForm
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                        />
                    </FilterFormWrapper>

                    <DataTable
                        columns={columns}
                        data={definedData(data)}
                        pagination={pagination}
                        setPagination={setPagination}
                        changePage={(arg) => setFilter({ ...filter, ...arg })}
                        sorted={filter?.order_by}
                        sort={onSort}
                        actionMenu={({ cell }) => {
                            const id = cell.row.original.id;
                            return (
                                <ActionDropdown>
                                    {hasPermit("scholarship-admin-update") && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Edit / View
                                        </DropdownMenuItem>
                                    )}
                                    {hasPermit("scholarship-admin-delete") && (
                                        <>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(id)
                                                }
                                            >
                                                Delete
                                            </DropdownMenuItem>
                                        </>
                                    )}
                                </ActionDropdown>
                            );
                        }}
                    />
                </Card>

                {/* create */}
                <Modal
                    open={openCreate}
                    onOpenChange={setOpenCreate}
                    size="medium"
                >
                    <ScholarshipForm
                        isCreate={true}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                    <ScholarshipForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={scholarshipAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={fetchScholarshipRecords}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterScholarshipForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </Layout>
        </>
    );
};

export default ScholarshipManagement;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
