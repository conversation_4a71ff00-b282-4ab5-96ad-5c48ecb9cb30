import { Fragment, useEffect, useState } from "react";
import clsx from "clsx";
import { format } from "date-fns";
import { capitalize, isArray, isEmpty, lowerCase } from "lodash";
import { Loader2, RotateCcw } from "lucide-react";
import Image from "next/image";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import BillingDocumentChangeStatusForm from "@/components/forms/accounting/BillingDocumentChangeStatusForm";
import FilterBillingDocumentForm from "@/components/forms/accounting/FilterBillingDocumentForm";
import FilterUnpaidItemForm from "@/components/forms/accounting/FilterUnpaidItemForm";
import ProcessPaymentForm from "@/components/forms/accounting/ProcessPaymentForm";
import ViewBillingDocument from "@/components/forms/accounting/ViewBillingDocument";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import RaisedButton from "@/components/ui/RaisedButton";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import Tabs from "@/components/ui/Tabs";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    accountingUnpaidFeeAPI,
    ACTIVE,
    appCurrencySymbol,
    billingDocumentAPI,
    billingDocumentCreateAPI,
    CONFIRMED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    FULL,
    hostelSavingAccountGetBalanceAPI,
    INACTIVE,
    PAID,
    PARTIAL,
    STUDENT,
    TableColumnType,
    UNPAID,
    VOIDED,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    combineSemesterClass,
    displayDateTime,
    formatNumberForRead,
    getTableSelection,
    replaceAll,
    strStartCase,
} from "@/lib/utils";
import IconProfilePhotoPlaceholder from "/public/icons/icon-profile-photo-placeholder.svg";
import toast from "react-hot-toast";

const _unpaidFees = "Fees";
const _invoices = "Invoices";

const feeDefaultFilterParams = {
    page: 1,
    per_page: 10,
    period_from: "",
    period_to: "",
    status: UNPAID,
};

const Payment = ({ locale }) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<any>(null);

    const [filter, setFilter] = useState<Record<string, any>>(
        feeDefaultFilterParams
    );
    const [pagination, setPagination] = useState({});
    const [openProcessPayment, setOpenProcessPayment] = useState(false);
    const [selectedFees, setSelectedFees] = useState<any[]>([]);
    const [clearSelectionCount, setClearSelectionCount] = useState(0);

    const [totalBeforeDiscount, setTotalBeforeDiscount] = useState(0);
    const [totalAfterDiscount, setTotalAfterDiscount] = useState(0);
    const [openFilter, setOpenFilter] = useState(false);
    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);
    const [displayType, setDisplayType] = useState(_unpaidFees);
    const [billingDocument, setBillingDocument] = useState<any>(null);

    const [isLoadingPhoto, setIsLoadingPhoto] = useState(false);

    const { data: unpaidItems, axiosQuery: getUnpaidItems } = useAxios({
        api: accountingUnpaidFeeAPI,
        locale,
        onSuccess: (result) => {
            if (result?.data) {
                const totalAmountBeforeDiscount = result.data.reduce(
                    (sum, item) => {
                        return sum + (item.amount_before_tax ?? 0);
                    },
                    0
                );

                const totalAmountAfterDiscount = result.data.reduce(
                    (sum, item) => {
                        return (
                            sum + (item.amount_before_tax_after_discount ?? 0)
                        );
                    },
                    0
                );

                const pagination = {
                    total: result.data.length,
                    hasPerPage: false,
                    hideChangePage: true,
                };

                setNavigatedResults([...navigatedResults, ...result.data]);
                setPagination(pagination);
                setTotalBeforeDiscount(totalAmountBeforeDiscount);
                setTotalAfterDiscount(totalAmountAfterDiscount);
            }
        },
    });

    const { axiosPost: createBillingDocument } = useAxios({
        api: billingDocumentCreateAPI,
        toastMsg: "Invoice created successfully",
        onSuccess: (response) => {
            refresh();
            setSelectedFees([]);
            setNavigatedResults([]);

            const { amount_before_tax_after_less_advance } = response.data;
            if (amount_before_tax_after_less_advance > 0) {
                setBillingDocument(response.data);
                setOpenProcessPayment(true);
            } else {
                setDisplayType(_invoices);
                setSelectedFees([]);
                toast("No payment is required for this invoice");
            }
        },
    });

    const {
        data: availableBalance,
        axiosQuery: getStudentHostelSavingAccountBalance,
    } = useAxios({
        api: hostelSavingAccountGetBalanceAPI,
        locale,
    });

    const columns: TableColumnType[] = [
        {
            key: "description",
            displayAs: "Fee",
            hasSort: true,
        },
        {
            key: "period",
        },
        {
            key: "product",
        },
        {
            key: "status",
            modify: (value) => (
                <span
                    className={clsx(
                        value === PAID && "text-green-600",
                        value === UNPAID && "text-red-500"
                    )}
                >
                    {capitalize(value)}
                </span>
            ),
        },
        {
            key: "amount_before_tax",
            displayAs: `Amount (${appCurrencySymbol})`,
        },
        {
            key: "amount_before_tax_after_discount",
            displayAs: `Amount with Discount (${appCurrencySymbol})`,
        },
    ];

    function definedData() {
        return isArray(unpaidItems)
            ? unpaidItems.map((item: any) => {
                  return {
                      id: item?.id,
                      product: item?.product?.translations?.name?.[locale],
                      period: format(item?.period, DATE_FORMAT.MY),
                      description: item?.description ?? "-",
                      status: item?.status ?? "-",
                      amount_before_tax: formatNumberForRead(
                          item?.amount_before_tax
                      ),
                      amount_before_tax_after_discount: formatNumberForRead(
                          item?.amount_before_tax_after_discount
                      ),
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function formattedStudent(student) {
        return {
            name: combinedNames(student?.translations?.name),
            student_number: student?.student_number,
            nric: student?.nric,
            status: lowerCase(student?.is_active ? ACTIVE : INACTIVE),
            class: combineSemesterClass(student?.current_primary_class),
            hostel_student: student?.is_hostel ? "YES" : "NO",
            ...(student?.is_hostel && availableBalance
                ? {
                      hostel_saving_account_balance:
                          appCurrencySymbol + " " + availableBalance?.balance,
                  }
                : {}),
        };
    }

    function closeForm() {
        setOpenProcessPayment(false);
        setDisplayType(_invoices);
    }

    function fetchStudentHostelSavingAccountBalance() {
        getStudentHostelSavingAccountBalance({
            params: { student_id: selectedStudent?.id },
        });
    }

    function fetchFees() {
        getUnpaidItems({
            params: {
                ...filter,
                status: filter.status ?? UNPAID,
                bill_to_type: STUDENT,
                bill_to_id: selectedStudent?.id,
                includes: ["product"],
            },
        });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, navigatedResults);
        setSelectedFees(_selection);
    }

    const handleProcessPayment = () => {
        setBillingDocument(null);
        createBillingDocument({
            unpaid_item_ids: selectedFees.map((item) => item?.id),
        });
    };

    function refresh() {
        fetchFees();
        if (selectedStudent?.is_hostel) {
            fetchStudentHostelSavingAccountBalance();
        }
    }

    useEffect(() => {
        if (selectedStudent?.is_hostel) {
            fetchStudentHostelSavingAccountBalance();
        }
    }, [selectedStudent]);

    useEffect(() => {
        if (selectedStudent?.id) {
            setSelectedFees([]);
            setNavigatedResults([]);

            if (displayType === _unpaidFees) {
                fetchFees();
            }
            if (displayType === _invoices) {
                fetchBillingDocuments();
            }
        }
    }, [selectedStudent, filter, displayType]);

    // invoices
    const [invoiceFilter, setInvoiceFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
    });
    const [viewInvoiceId, setViewInvoiceId] = useState(null);
    const [invoices, setInvoices] = useState<any[]>([]);
    const [invoicePagination, setInvoicePagination] = useState();
    const [openInvoiceFilter, setOpenInvoiceFilter] = useState(false);
    const [invoiceChangeStatusId, setInvoiceChangeStatusId] = useState(null);

    const { axiosQuery: getBillingDocuments } = useAxios({
        api: billingDocumentAPI,
        locale,
        onSuccess: (result) => {
            setInvoices(result.data);
            setInvoicePagination(result?.pagination);
        },
    });

    const invoiceColumns: TableColumnType[] = [
        {
            key: "reference_no",
            displayAs: "Reference No",
            hasSort: true,
        },
        {
            key: "payment_reference_no",
            displayAs: "Payment Reference No",
        },
        {
            key: "document_date",
            hasSort: true,
            displayAs: "Purchase Date",
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
        {
            key: "paid_at",
            hasSort: true,
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
        {
            key: "type",
            hasSort: true,
            modify: (value) => {
                return (
                    <span className="text-sm">
                        {replaceAll(value, "_", " ")}
                    </span>
                );
            },
        },
        {
            key: "sub_type",
            hasSort: true,
        },
        {
            key: "payment_status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === PARTIAL && "text-yellow-500",
                        value === PAID && "text-green-600",
                        value === UNPAID && "text-red-500"
                    )}
                >
                    {capitalize(value)}
                </span>
            ),
        },
        {
            key: "amount_before_tax_after_less_advance",
            displayAs: `Amount (${appCurrencySymbol})`,
            hasSort: true,
        },
        {
            key: "status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === CONFIRMED && "text-green-600",
                        value === DRAFT && "text-yellow-500",
                        value === VOIDED && "text-red-500"
                    )}
                >
                    {capitalize(value)}
                </span>
            ),
        },
    ];

    function definedInvoiceData() {
        return isArray(invoices)
            ? invoices.map((item: any) => {
                  return {
                      id: item?.id,
                      reference_no: item?.reference_number,
                      document_date: item?.document_date ?? "-",
                      paid_at: item.paid_at
                          ? displayDateTime(
                                item.paid_at,
                                DATE_FORMAT.forDisplay
                            )
                          : "-",
                      type: item?.type ?? "-",
                      sub_type: strStartCase(item?.sub_type ?? "-").replaceAll(
                          "_",
                          " "
                      ),
                      status: item?.status ?? "-",
                      payment_status: item?.payment_status ?? "-",
                      amount_before_tax_after_less_advance:
                          formatNumberForRead(
                              item?.amount_before_tax_after_less_advance
                          ) ?? "-",
                      payment_due_date: item?.payment_due_date ?? "-",
                      payment_reference_no:
                          item?.payments?.find((p) => p?.paid_at !== null)
                              ?.payment_reference_no ?? "-",
                  };
              })
            : [];
    }

    function fetchBillingDocuments() {
        getBillingDocuments({
            params: {
                ...invoiceFilter,
                includes: ["billTo", "payments"],
                bill_to_type: STUDENT,
                bill_to_id: selectedStudent?.id,
            },
        });
    }

    const handleInvoicePayment = (billingDocumentId) => {
        const billingDocument = invoices.find(
            (item) => item?.id === billingDocumentId
        );
        setBillingDocument(billingDocument);
        setOpenProcessPayment(true);
    };

    function refreshInvoices() {
        fetchBillingDocuments();

        if (selectedStudent?.is_hostel) {
            fetchStudentHostelSavingAccountBalance();
        }
    }

    function onSortInvoices(name: string, direction: string) {
        setInvoiceFilter({
            ...invoiceFilter,
            order_by: { [name]: direction },
        });
    }

    useEffect(() => {
        if (displayType === _invoices) {
            fetchBillingDocuments();
        }
    }, [selectedStudent, invoiceFilter, displayType]);

    return (
        <Layout path="accounting/payment" locale={locale}>
            <Card styleClass="max-w-screen-2xl mx-auto">
                <div className="pb-7 lg:px-2">
                    <h2 className="mb-5">Payment</h2>

                    <RaisedButton
                        name="Select Student"
                        onClick={() => setOpenSearch(true)}
                    />

                    {selectedStudent && (
                        <div className="pt-5">
                            <div className="flex flex-col gap-3 lg:flex-row lg:items-center">
                                <div className="relative">
                                    {isLoadingPhoto && (
                                        <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                                            <Loader2
                                                className="animate-spin text-themeGray"
                                                size={30}
                                            />
                                        </div>
                                    )}
                                    <Image
                                        src={
                                            selectedStudent?.photo ??
                                            IconProfilePhotoPlaceholder
                                        }
                                        alt="Profile Photo"
                                        width={126}
                                        height={126}
                                        className={clsx(
                                            "h-auto w-auto max-w-[126px] rounded-sm",
                                            isLoadingPhoto && "opacity-0"
                                        )}
                                        onLoad={() => {
                                            setIsLoadingPhoto(false);
                                        }}
                                        onError={() => setIsLoadingPhoto(false)}
                                    />
                                </div>

                                <div className="grid flex-grow gap-3 lg:grid-cols-2 lg:pl-4">
                                    {Object.entries(
                                        formattedStudent(selectedStudent)
                                    ).map(([key, value], index) => (
                                        <Fragment key={key}>
                                            <div className="flex flex-col">
                                                <p className="mb-.5 font-medium capitalize text-themeLabel">
                                                    {replaceAll(
                                                        key === "nric"
                                                            ? "NRIC"
                                                            : key,
                                                        "_",
                                                        " "
                                                    )}
                                                </p>
                                                <p
                                                    className={clsx(
                                                        "text-themeText text-[16px] font-medium",
                                                        key === "status" &&
                                                            `cell-status ${value}`
                                                    )}
                                                >
                                                    {isEmpty(value?.toString())
                                                        ? "-"
                                                        : value}
                                                </p>
                                            </div>
                                        </Fragment>
                                    ))}
                                </div>
                            </div>

                            <Tabs
                                list={[_unpaidFees, _invoices]}
                                selected={displayType}
                                setSelected={setDisplayType}
                                styleClass="mt-8 mb-3"
                            />

                            {displayType === _unpaidFees && (
                                <div>
                                    <div className="flex justify-end gap-x-3 pb-3">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            className="flex items-center gap-x-1"
                                            onClick={refresh}
                                        >
                                            <RotateCcw size={16} />
                                            <span>Refresh</span>
                                        </Button>

                                        <TableFilterBtn
                                            filter={filter}
                                            onClick={() => setOpenFilter(true)}
                                        />
                                    </div>

                                    <DataTable
                                        {...(filter?.status !== UNPAID
                                            ? {}
                                            : { onMultiSelect })}
                                        columns={columns}
                                        data={definedData()}
                                        pagination={pagination}
                                        setPagination={setPagination}
                                        changePage={(arg) =>
                                            setFilter({ ...filter, ...arg })
                                        }
                                        sorted={filter?.order_by}
                                        sort={onSort}
                                        clearCount={clearSelectionCount}
                                        extraRow={[
                                            {
                                                value: (
                                                    <span className="font-medium text-themeLabel">
                                                        Total Amount (
                                                        {appCurrencySymbol})
                                                    </span>
                                                ),
                                                colSpan:
                                                    filter?.status !== UNPAID
                                                        ? 4
                                                        : 5,
                                            },
                                            {
                                                value: (
                                                    <span className="text-[16px] font-medium">
                                                        {formatNumberForRead(
                                                            totalBeforeDiscount
                                                        )}
                                                    </span>
                                                ),
                                            },
                                            {
                                                value: (
                                                    <span className="text-[16px] font-medium">
                                                        {formatNumberForRead(
                                                            totalAfterDiscount
                                                        )}
                                                    </span>
                                                ),
                                            },
                                        ]}
                                    />

                                    {hasPermit(
                                        "billing-document-manual-payment"
                                    ) && (
                                        <div className="mt-3 flex justify-end gap-x-3">
                                            <Button
                                                type="submit"
                                                disabled={isEmpty(selectedFees)}
                                                onClick={() =>
                                                    handleProcessPayment()
                                                }
                                            >
                                                Next
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            )}

                            {displayType === _invoices && (
                                <div>
                                    <div className="flex justify-end gap-x-3 pb-3">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            className="flex items-center gap-x-1"
                                            onClick={refreshInvoices}
                                        >
                                            <RotateCcw size={16} />
                                            <span>Refresh</span>
                                        </Button>

                                        <TableFilterBtn
                                            filter={invoiceFilter}
                                            onClick={() =>
                                                setOpenInvoiceFilter(true)
                                            }
                                        />
                                    </div>

                                    <DataTable
                                        columns={invoiceColumns}
                                        data={definedInvoiceData()}
                                        pagination={invoicePagination}
                                        setPagination={setInvoicePagination}
                                        changePage={(arg) =>
                                            setInvoiceFilter({
                                                ...invoiceFilter,
                                                ...arg,
                                            })
                                        }
                                        sorted={invoiceFilter?.order_by}
                                        sort={onSortInvoices}
                                        actionMenu={({ cell }) => (
                                            <>
                                                <ActionDropdown>
                                                    <DropdownMenuItem
                                                        className="c-text-size"
                                                        onClick={() =>
                                                            setViewInvoiceId(
                                                                cell.row
                                                                    .original.id
                                                            )
                                                        }
                                                    >
                                                        View
                                                    </DropdownMenuItem>
                                                    {hasPermit(
                                                        "billing-document-manual-payment"
                                                    ) &&
                                                        cell.row.original
                                                            .payment_status !==
                                                            PAID &&
                                                        cell.row.original
                                                            .status ==
                                                            CONFIRMED && (
                                                            <>
                                                                <DropdownMenuSeparator />
                                                                <DropdownMenuItem
                                                                    className="c-text-size"
                                                                    onClick={() =>
                                                                        handleInvoicePayment(
                                                                            cell
                                                                                .row
                                                                                .original
                                                                                .id
                                                                        )
                                                                    }
                                                                >
                                                                    Make Payment
                                                                </DropdownMenuItem>
                                                            </>
                                                        )}
                                                    {hasPermit(
                                                        "billing-document-status-update"
                                                    ) &&
                                                        cell.row.original
                                                            .status !==
                                                            VOIDED && (
                                                            <>
                                                                <DropdownMenuSeparator />
                                                                <DropdownMenuItem
                                                                    className="c-text-size"
                                                                    onClick={() =>
                                                                        setInvoiceChangeStatusId(
                                                                            cell
                                                                                .row
                                                                                .original
                                                                                .id
                                                                        )
                                                                    }
                                                                >
                                                                    Change
                                                                    Status
                                                                </DropdownMenuItem>
                                                            </>
                                                        )}
                                                </ActionDropdown>
                                            </>
                                        )}
                                    />
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </Card>

            {/* search student */}
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    setSelection={(student) => {
                        if (student) {
                            setSelectedFees([]);
                            setNavigatedResults([]);
                            setSelectedStudent(student);
                            setIsLoadingPhoto(true);
                            setClearSelectionCount((prev) => prev + 1);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                    otherFilterParams={{
                        response: FULL,
                        includes: [
                            "currentSemesterPrimaryClass.semesterSetting",
                            "currentSemesterPrimaryClass.semesterClass.classModel",
                        ],
                    }}
                />
            </Modal>

            {/* Process payment */}
            <Modal
                open={billingDocument && openProcessPayment}
                onOpenChange={setOpenProcessPayment}
                size="large"
            >
                <ProcessPaymentForm
                    billingDocument={billingDocument}
                    refresh={refresh}
                    close={closeForm}
                />
            </Modal>

            {/* unpaid fees - filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterUnpaidItemForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* invoice -view */}
            <Modal
                open={viewInvoiceId}
                onOpenChange={setViewInvoiceId}
                size="large"
            >
                <ViewBillingDocument
                    id={viewInvoiceId}
                    close={() => setViewInvoiceId(null)}
                />
            </Modal>

            {/* invoice - change status */}
            <Modal
                open={invoiceChangeStatusId}
                onOpenChange={setInvoiceChangeStatusId}
            >
                <BillingDocumentChangeStatusForm
                    id={invoiceChangeStatusId}
                    currentStatus={
                        invoices?.find(
                            (item: any) => item?.id === invoiceChangeStatusId
                        )?.status
                    }
                    close={() => setInvoiceChangeStatusId(null)}
                    refresh={refreshInvoices}
                />
            </Modal>

            {/* invoice - filter */}
            <Modal
                open={openInvoiceFilter}
                onOpenChange={setOpenInvoiceFilter}
                size="medium"
            >
                <FilterBillingDocumentForm
                    filter={invoiceFilter}
                    setFilter={setInvoiceFilter}
                    close={() => setOpenInvoiceFilter(false)}
                    hideBillTo={true}
                />
            </Modal>
        </Layout>
    );
};

export default Payment;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
