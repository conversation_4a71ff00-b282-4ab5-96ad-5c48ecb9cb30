import { useEffect, useState } from "react";
import { capitalize, isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import DiscountBulkCreateForm from "@/components/forms/accounting/DiscountBulkCreateForm";
import DiscountChangeStatusPrompt from "@/components/forms/accounting/DiscountChangeStatusPrompt";
import DiscountForm from "@/components/forms/accounting/DiscountForm";
import FilterDiscountForm from "@/components/forms/accounting/FilterDiscountForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import Tabs from "@/components/ui/Tabs";
import {
    TableColumnType,
    counsellingCaseRecordAPI,
    DEFAULT_FILTER_PARAMS,
    discountAPI,
    ACTIVE,
    INACTIVE,
    appCurrencySymbol,
    DATE_FORMAT,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    displayDateTime,
    formatNumberForRead,
    getTableSelection,
    refreshForUpdate,
} from "@/lib/utils";
import { format } from "date-fns";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const _confirmed = "confirmed";
const _unconfirmed = "unconfirmed";

const Discount = ({ locale }) => {
    useCheckViewPermit("discount-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const tabList = [
        hasPermit("discount-view") ? _confirmed : null,
        hasPermit("discount-view") ? _unconfirmed : null,
    ].filter((tab) => tab);

    const [discountType, setDiscountType] = useState<
        typeof _confirmed | typeof _unconfirmed
    >(_confirmed);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();
    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const [selectedDiscounts, setSelectedDiscounts] = useState<any[]>([]);
    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);
    const [openBulkChangeStatus, setOpenBulkChangeStatus] = useState(false);

    const { data, axiosQuery: getDiscountRecords } = useAxios({
        api: discountAPI,
        locale,
        onSuccess: (result) => {
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
        },
    });

    const confirmedColumns: TableColumnType[] = [
        // {
        //     key: "created_by",
        // },
        {
            key: "effective_from",
            displayAs: `Effective (From)`,
        },
        {
            key: "effective_to",
            displayAs: `Effective (To)`,
            modify: (value) => <div className="min-w-[100px]">{value}</div>,
        },
        // {
        //     key: "created_date",
        //     modify: (value) => (
        //         <span className="whitespace-nowrap text-[14px]">{value}</span>
        //     ),
        // },
        {
            key: "user_number",
            displayAs: `Student Number`,
        },
        {
            key: "grade",
            modify: (value) => <div className="min-w-[114px]">{value}</div>,
        },
        {
            key: "user_name",
            displayAs: `Student Name`,
            modify: (value) => <div className="min-w-[200px]">{value}</div>,
        },
        {
            key: "basis_type",
        },
        {
            key: "gl_account_codes",
            displayAs: `GL Codes`,
            modify: (value) => (value.length === 0 ? "-" : value),
        },
        {
            key: "basis_amount",
            displayAs: `Basis Amount (${appCurrencySymbol})`,
        },
        {
            key: "max_amount",
            displayAs: `Max Amount (${appCurrencySymbol})`,
        },
        {
            key: "used_amount",
            displayAs: `Used Amount (${appCurrencySymbol})`,
        },
        {
            key: "source",
        },
        {
            key: "description",
            modify: (value) => (
                <div className="min-w-[100px] text-[14px]">{value}</div>
            ),
        },
        {
            key: "is_active",
            displayAs: "Status",
            hasSort: true,
            modify: (value) => (
                <div className={`cell-status ${value}`}>{value}</div>
            ),
        },
    ];

    const unconfirmedColumns: TableColumnType[] = [
        {
            key: "effective_from",
            displayAs: `Effective (From)`,
        },
        {
            key: "effective_to",
            displayAs: `Effective (To)`,
            modify: (value) => <div className="min-w-[100px]">{value}</div>,
        },
        {
            key: "user_number",
            displayAs: `Student Number`,
        },
        {
            key: "grade",
            modify: (value) => <div className="min-w-[114px]">{value}</div>,
        },
        {
            key: "user_name",
            displayAs: `Student Name`,
            modify: (value) => <div className="min-w-[200px]">{value}</div>,
        },
        {
            key: "basis_type",
        },
        {
            key: "gl_account_codes",
            displayAs: `GL Codes`,
            modify: (value) => (value.length === 0 ? "-" : value),
        },
        {
            key: "basis_amount",
            displayAs: `Basis Amount (${appCurrencySymbol})`,
        },
        {
            key: "max_amount",
            displayAs: `Max Amount (${appCurrencySymbol})`,
        },
        {
            key: "used_amount",
            displayAs: `Used Amount (${appCurrencySymbol})`,
        },
        {
            key: "source",
        },
        {
            key: "description",
            modify: (value) => (
                <div className="min-w-[100px] text-[14px]">{value}</div>
            ),
        },
        {
            key: "is_active",
            displayAs: "Status",
            hasSort: true,
            modify: (value) => (
                <div className={`cell-status ${value}`}>{value}</div>
            ),
        },
    ];

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  created_by:
                      combinedNames(item?.created_by?.translations.name) ?? "-",
                  created_date: displayDateTime(
                      item?.created_at,
                      DATE_FORMAT.forDisplay
                  ),
                  grade: combinedNames(
                      item?.userable?.current_primary_class?.grade
                  ),
                  user_type: item?.userable?.user_type_description,
                  user_number: item?.userable?.number,
                  user_name: combinedNames(item?.userable?.translations?.name),
                  basis_type: capitalize(item?.basis),
                  basis_amount: formatNumberForRead(item?.basis_amount),
                  max_amount: formatNumberForRead(item?.max_amount),
                  used_amount: formatNumberForRead(item?.used_amount),
                  is_active: lowerCase(item?.is_active ? ACTIVE : INACTIVE),
                  gl_account_codes: item?.gl_account_codes?.join(", ") ?? "",
                  effective_from: format(item?.effective_from, DATE_FORMAT.MY),
                  effective_to: format(item?.effective_to, DATE_FORMAT.MY),
                  source:
                      item?.source?.scholarship?.translations?.name?.[locale] ??
                      "-",
                  description: item?.description ?? "-",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, navigatedResults);
        setSelectedDiscounts(_selection);
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
        setOpenBulkChangeStatus(false);
    }

    function fetchDiscountRecords() {
        getDiscountRecords({
            params: {
                includes: [
                    "source.student",
                    "userable",
                    "source",
                    "userable.latestPrimaryClassAndGrade",
                    // "createdByEmployee",
                ],
                order_by: {
                    apply_at: "desc",
                    created_at: "desc",
                },
                is_active: discountType == _confirmed ? 1 : 0,
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchDiscountRecords();
    }, [filter, discountType, locale]);

    return (
        <>
            <Layout locale={locale} path="accounting/discount">
                <Card styleClass="table-card">
                    <div className="mb-2 flex w-full flex-wrap items-center justify-between gap-3 lg:gap-x-3.5 lg:px-1">
                        <h2>Discount</h2>
                        <div className="flex items-center gap-x-3">
                            {hasPermit("discount-create") && (
                                <Button
                                    size="smallerOnMobile"
                                    onClick={() => setOpenCreate(true)}
                                >
                                    Add Discount
                                </Button>
                            )}
                            {hasPermit("discount-confirm") &&
                                discountType == _unconfirmed && (
                                    <Button
                                        size="smallerOnMobile"
                                        disabled={selectedDiscounts?.length < 1}
                                        onClick={() =>
                                            setOpenBulkChangeStatus(true)
                                        }
                                    >
                                        Bulk Change Status
                                    </Button>
                                )}

                            {/* <TableFilterBtn
                                filter={filter}
                                onClick={() => setOpenFilter(true)}
                            /> */}
                            <FilterChevronButton />
                        </div>
                    </div>

                    <FilterFormWrapper>
                        <FilterDiscountForm
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                        />
                    </FilterFormWrapper>

                    <div className="h-3"></div>
                    <Tabs
                        list={tabList}
                        selected={discountType}
                        setSelected={(type) => {
                            setDiscountType(type);
                            setFilter({
                                ...filter,
                                page: 1,
                            });
                        }}
                    />

                    <div className="flex flex-col gap-y-3 pb-3">
                        {discountType === _confirmed && (
                            <DataTable
                                columns={confirmedColumns}
                                data={definedData(data)}
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                sorted={filter?.order_by}
                                sort={onSort}
                                actionMenu={({ cell }) => {
                                    const id = cell.row.original.id;
                                    const status = cell.row.original.status;
                                    return (
                                        <ActionDropdown>
                                            {hasPermit("discount-update") && (
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() =>
                                                        setTargetId(
                                                            cell.row.original.id
                                                        )
                                                    }
                                                >
                                                    Edit / View
                                                </DropdownMenuItem>
                                            )}

                                            {/* {hasPermit("hostel-in-out-record-delete") && ( */}
                                            {/* {status == NEW && (
                                         <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(id)
                                                }
                                            >
                                                Delete
                                            </DropdownMenuItem>
                                        )} */}

                                            {/* )} */}
                                        </ActionDropdown>
                                    );
                                }}
                            />
                        )}
                        {discountType === _unconfirmed && (
                            <DataTable
                                onMultiSelect={onMultiSelect}
                                columns={unconfirmedColumns}
                                data={definedData(data)}
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                sorted={filter?.order_by}
                                sort={onSort}
                                actionMenu={({ cell }) => {
                                    const id = cell.row.original.id;
                                    const status = cell.row.original.status;
                                    return (
                                        <ActionDropdown>
                                            {hasPermit("discount-update") && (
                                                <DropdownMenuItem
                                                    className="c-text-size"
                                                    onClick={() =>
                                                        setTargetId(
                                                            cell.row.original.id
                                                        )
                                                    }
                                                >
                                                    Edit / View
                                                </DropdownMenuItem>
                                            )}

                                            {/* {hasPermit("hostel-in-out-record-delete") && ( */}
                                            {/* {status == NEW && (
                                        <DropdownMenuSeparator />
                                           <DropdownMenuItem
                                               className="c-text-size text-red-600"
                                               onClick={() =>
                                                   setTargetDeleteId(id)
                                               }
                                           >
                                               Delete
                                           </DropdownMenuItem>
                                       )} */}

                                            {/* )} */}
                                        </ActionDropdown>
                                    );
                                }}
                            />
                        )}
                    </div>
                </Card>

                {/* create */}
                <Modal
                    open={openCreate}
                    onOpenChange={setOpenCreate}
                    size="medium"
                >
                    <DiscountBulkCreateForm
                        isCreate={true}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                    <DiscountForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* bulk change status */}
                <Modal
                    open={openBulkChangeStatus}
                    onOpenChange={setOpenBulkChangeStatus}
                >
                    <DiscountChangeStatusPrompt
                        selectedDiscounts={selectedDiscounts?.map(
                            (discount) => discount.id
                        )}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={() => {
                            closeForm();
                            setDiscountType(_confirmed);
                            setFilter({
                                ...filter,
                                page: 1,
                            });
                        }}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={counsellingCaseRecordAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={fetchDiscountRecords}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterDiscountForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </Layout>
        </>
    );
};

export default Discount;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
