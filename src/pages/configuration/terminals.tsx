import FilterTerminalForm from "@/components/forms/configuration/FilterTerminalForm";
import TerminalForm from "@/components/forms/configuration/TerminalForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { TableColumnType, terminalAPI } from "@/lib/constant";

const Terminals = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="configuration/terminals"
        api={terminalAPI}
        otherFilterParams={{
            includes: ["merchant"],
        }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "code",
                    hasSort: true,
                },
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "type",
                    hasSort: true,
                },
                {
                    key: "merchant",
                },
            ];
            return _columns;
        }}
        definedData={(data, t) => {
            console.log("data", data);
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      name: item?.name,
                      code: item?.code,
                      type: t(item?.type),
                      merchant:
                          item?.merchant?.translations?.name?.[locale] ?? "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <TerminalForm {...params} />}
        filterForm={(params) => <FilterTerminalForm {...params} />}
        viewPermit="terminal-view"
        createPermit="terminal-create"
        updatePermit="terminal-update"
        deletePermit="terminal-delete"
    />
);

export default Terminals;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
