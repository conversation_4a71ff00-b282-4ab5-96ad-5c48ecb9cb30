import { useEffect, useState } from "react";
import QRCode from "qrcode";
import toast from "react-hot-toast";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterTerminalKeyForm from "@/components/forms/configuration/FilterTerminalKeyForm";
import TerminalKeyForm from "@/components/forms/configuration/TerminalKeyForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import { axiosInstance } from "@/lib/api";
import {
    TableColumnType,
    DEFAULT_FILTER_PARAMS,
    terminalKeyAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLoading, useUserProfile } from "@/lib/store";
import {
    convertDateTime,
    isUnauthenticated,
    refreshForUpdate,
    strStartCase,
} from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const TerminalKeys = ({ locale }) => {
    useCheckViewPermit("pos-terminal-key-view");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState<any>(null);

    const [openFilter, setOpenFilter] = useState(false);
    const [openCreate, setOpenCreate] = useState(false);
    const [targetId, setTargetId] = useState(null);
    const [targetRegenerateId, setTargetRegenerateId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);
    const [showQRCode, setShowQRCode] = useState(null);
    const setLoading = useLoading((state) => state.setLoading);

    function definedColumns() {
        const _columns: TableColumnType[] = [
            {
                key: "name",
                hasSort: true,
            },
            {
                key: "secret",
                hasSort: true,
            },
            {
                key: "terminal",
            },
            {
                key: "expires_at",
                hasSort: true,
                modify: (value, cell) => (
                    <span className="whitespace-nowrap text-sm">{value}</span>
                ),
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return Array.isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  name: item?.name,
                  secret: item?.secret,
                  terminal: item?.terminal?.name,
                  expires_at: item.expires_at
                      ? convertDateTime(item.expires_at)
                      : "-",
              }))
            : [];
    }

    const { data, axiosQuery: getTerminalKeys } = useAxios({
        api: terminalKeyAPI,
        locale,
        onSuccess(result) {
            setPagination(result?.pagination);
        },
    });

    function fetchKeys() {
        getTerminalKeys({
            params: {
                includes: ["terminal"],
                ...filter,
            },
        });
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    function regenerateTerminalKey() {
        setLoading(true);
        axiosInstance
            .put(`/pos-terminal-keys/${targetRegenerateId}/regenerate`)
            .then((res) => {
                toast(`Terminal updated successfully`);
                fetchKeys();
            })
            .catch((error) => {
                if (isUnauthenticated(error)) {
                    return;
                }
                console.log(error);
                toast.error("Failed to regenerate terminal key");
            })
            .finally(() => setLoading(false));
    }

    useEffect(() => {
        definedColumns();
    }, []);

    useEffect(() => {
        fetchKeys();
    }, [filter, locale]);

    return (
        <Layout locale={locale} path="configuration/terminal-keys">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>{strStartCase(t("terminal keys"))}</h2>
                    <div className="flex items-center gap-x-3">
                        {hasPermit("pos-terminal-key-create") && (
                            <Button
                                size="smallerOnMobile"
                                className="ml-auto capitalize"
                                onClick={() => setOpenCreate(true)}
                            >
                                {t("Add ")}
                                {t("terminal key")}
                            </Button>
                        )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                <FilterFormWrapper>
                    <FilterTerminalKeyForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit("pos-terminal-key-update") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setTargetId(cell.row.original.id)
                                    }
                                >
                                    {t("Edit / View")}
                                </DropdownMenuItem>
                            )}
                            {hasPermit("pos-terminal-key-update") && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setTargetRegenerateId(
                                            cell.row.original.id
                                        )
                                    }
                                >
                                    {t("Regenerate Secret")}
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                                className="c-text-size"
                                onClick={() =>
                                    setShowQRCode(cell.row.original.secret)
                                }
                            >
                                {t("Generate QR Code")}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {hasPermit("pos-terminal-key-delete") && (
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    {t("Delete")}
                                </DropdownMenuItem>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            <Modal open={openCreate} onOpenChange={setOpenCreate}>
                <TerminalKeyForm
                    isCreate={true}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <TerminalKeyForm
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={terminalKeyAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchKeys}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterTerminalKeyForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            <Modal
                open={targetRegenerateId}
                onOpenChange={setTargetRegenerateId}
            >
                <>
                    <p className="mt-3 font-medium">
                        {t("Are you sure you want to proceed?")}
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setTargetRegenerateId(null)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button onClick={regenerateTerminalKey}>
                            {t("Confirm")}
                        </Button>
                    </DialogFooter>
                </>
            </Modal>

            {/* QR code */}
            <Modal open={showQRCode} onOpenChange={setShowQRCode}>
                <QRCodeDisplay secret={showQRCode} />
            </Modal>
        </Layout>
    );
};

const QRCodeDisplay = ({ secret }) => {
    const t = useTranslations("common");
    const [img, setImg] = useState(null);

    useEffect(() => {
        if (!secret) return;
        QRCode.toDataURL(secret)
            .then((url) => {
                console.log(url);
                setImg(url);
            })
            .catch((err) => {
                console.error(err);
                toast.error(t("Failed to generate QR code"));
            });
    }, [secret]);

    return (
        <div className="flex flex-col items-center">
            <h3>{t("QR Code")}</h3>
            {img ? (
                <img className="mb-3 mt-4" src={img} alt="QR Code" />
            ) : (
                <div className="h-[150px]"></div>
            )}
        </div>
    );
};

export default TerminalKeys;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
