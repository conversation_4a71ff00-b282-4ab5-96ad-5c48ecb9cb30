import { useEffect } from "react";
import { useForm } from "react-hook-form";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    copySemesterSettingsAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit, useSubmit } from "@/lib/hook";
import { useTranslations } from "next-intl";

const CopySemesterSettings = ({ locale }) => {
    useCheckViewPermit("master-semester-setting-copy");
    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            from_semester_setting_id: null,
            to_semester_setting_id: null,
        },
    });

    const { axiosPost: copySemesterSettings } = useAxios({
        api: copySemesterSettingsAPI,
        toastMsg: t(
            "Submitted successfully. Request to copy data to semester is being processed"
        ),
    });

    const { data: semesterOptions, axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
    });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                copySemesterSettings(data, { showErrorInToast: true });
            })
        );
    }

    return (
        <Layout path="configuration/copy-semester-settings" locale={locale}>
            <Card styleClass="report-card-max-width">
                <h2 className="mb-5">{t("Copy Semester Settings")}</h2>
                <Form {...form}>
                    <form
                        className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-2"
                        onSubmit={onSubmit}
                    >
                        <FormSelect
                            control={form.control}
                            name={"from_semester_setting_id"}
                            label={t("from semester") + "*"}
                            options={semesterOptions}
                            isSortByName={false}
                        />
                        <FormSelect
                            control={form.control}
                            name={"to_semester_setting_id"}
                            label={t("to semester") + "*"}
                            options={semesterOptions}
                            isSortByName={false}
                        />

                        <div className="flex flex-wrap items-center gap-3 pt-1 lg:col-span-2">
                            <Button className="ml-auto" type="submit">
                                {t("Submit")}
                            </Button>
                        </div>
                    </form>
                </Form>
            </Card>
        </Layout>
    );
};

export default CopySemesterSettings;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
