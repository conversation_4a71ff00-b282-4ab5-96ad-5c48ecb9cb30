import FilterRoleForm from "@/components/forms/configuration/FilterRoleForm";
import RoleForm from "@/components/forms/configuration/RoleForm";
import CommonTablePageWrap from "@/components/ui/CommonTablePageWrap";
import { roleAPI, TableColumnType } from "@/lib/constant";

const Roles = ({ locale }) => (
    <CommonTablePageWrap
        locale={locale}
        path="configuration/roles"
        api={roleAPI}
        otherFilterParams={{
            includes: ["models"],
        }}
        definedColumn={() => {
            const _columns: TableColumnType[] = [
                {
                    key: "name",
                    hasSort: true,
                },
                {
                    key: "models",
                    displayAs: "system default role",
                    hasSort: true,
                },
            ];
            return _columns;
        }}
        definedData={(data) => {
            return Array.isArray(data)
                ? data.map((item) => ({
                      id: item?.id,
                      name: item?.name,
                      models:
                          item?.default_models?.length > 0
                              ? item.default_models.join(", ")
                              : "-",
                  }))
                : [];
        }}
        orderBy={(name, direction) => {
            return { [name]: direction };
        }}
        form={(params) => <RoleForm {...params} />}
        filterForm={(params) => <FilterRoleForm {...params} />}
        formSize="medium"
        viewPermit="role-view"
        createPermit="role-create"
        updatePermit="role-update"
        deletePermit="role-delete"
    />
);

export default Roles;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
