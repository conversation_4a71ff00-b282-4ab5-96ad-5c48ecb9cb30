import { useEffect, useState } from "react";
import React from "react";
import { isArray, lowerCase } from "lodash";
import Layout from "@/components/Layout";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import FilterUserForm from "@/components/forms/configuration/FilterUserForm";
import UserForm from "@/components/forms/configuration/UserForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DEFAULT_FILTER_PARAMS,
    INACTIVE,
    TableColumnType,
    userAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { optionUserLabel, refreshForUpdate } from "@/lib/utils";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import { useTranslations } from "next-intl";

const Users = ({ locale }) => {
    useCheckViewPermit("user-update"); // TODO: update permission
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);

    const { data, axiosQuery: getUsers } = useAxios({
        api: userAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    useEffect(() => {
        fetchUsers();
    }, [filter, locale]);

    useEffect(() => {
        if (data) {
            const _columns: TableColumnType[] = [
                {
                    key: "email",
                    hasSort: true,
                },
                {
                    key: "phone_number",
                    hasSort: true,
                },
                {
                    key: "roles",
                    rowSpan: true,
                    modify(list) {
                        return (
                            <div className="table-chips-wrap">
                                {list.length > 0
                                    ? list.map((role, index) => (
                                          <span
                                              key={index}
                                              className="table-chip"
                                          >
                                              {role}
                                          </span>
                                      ))
                                    : "-"}
                            </div>
                        );
                    },
                },
                {
                    key: "direct_userables",
                    displayAs: "user",
                    modify(list) {
                        return (
                            <ul>
                                {list.length > 0
                                    ? list.map((user, index) => (
                                          <li
                                              key={index}
                                              className="table-item-li"
                                          >
                                              <span>{user}</span>
                                          </li>
                                      ))
                                    : "-"}
                            </ul>
                        );
                    },
                },
                {
                    key: "accessible_student_userables",
                    displayAs: "access students",
                    modify(list) {
                        return (
                            <ul>
                                {list.length > 0
                                    ? list.map((user, index) => (
                                          <li
                                              key={index}
                                              className="table-item-li"
                                          >
                                              <span>{user}</span>
                                          </li>
                                      ))
                                    : "-"}
                            </ul>
                        );
                    },
                },
                {
                    key: "is_active",
                    displayAs: "Status",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{t(value)}</div>
                    ),
                },
            ];
            setColumns(_columns);
        }
    }, [data]);

    function definedData() {
        return isArray(data)
            ? data.map((user) => ({
                  id: user?.id,
                  email: user?.email || "-",
                  phone_number: user?.phone_number || "-",
                  is_active: lowerCase(user?.is_active ? ACTIVE : INACTIVE),
                  direct_userables: isArray(user?.direct_userables)
                      ? user?.direct_userables.map(
                            (userable) =>
                                optionUserLabel(
                                    userable?.number,
                                    userable?.translations?.name
                                ) + ` (${userable?.user_type_description})`
                        )
                      : [],
                  accessible_student_userables: isArray(
                      user?.accessible_student_userables
                  )
                      ? user?.accessible_student_userables.map(
                            (userable) =>
                                optionUserLabel(
                                    userable?.number,
                                    userable?.translations?.name
                                ) + ` (${userable?.user_type_description})`
                        )
                      : [],
                  roles: isArray(user?.roles)
                      ? user?.roles.map((role) => role.name)
                      : [],
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function fetchUsers() {
        getUsers({
            params: {
                includes: ["userables", "roles"],
                order_by: { email: "asc" },
                ...filter,
            },
        });
    }

    const t = useTranslations("common");

    return (
        <Layout locale={locale} path="configuration/users">
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2 className="capitalize">{t("users")}</h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        excludeFields={["includes"]}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>

                <FilterFormWrapper>
                    <FilterUserForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={columns}
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                {hasPermit("user-update") && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetId(cell.row.original.id)
                                        }
                                    >
                                        {t("Edit / View")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        </>
                    )}
                />
            </Card>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <UserForm
                    id={targetId}
                    close={() => setTargetId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterUserForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </Layout>
    );
};

export default Users;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
