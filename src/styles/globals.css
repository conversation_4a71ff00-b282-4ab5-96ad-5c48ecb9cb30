@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 224 71.4% 4.1%;

        --card: 0 0% 100%;
        --card-foreground: 224 71.4% 4.1%;

        --popover: 0 0% 100%;
        --popover-foreground: 224 71.4% 4.1%;

        --primary: 220.9 39.3% 11%;
        --primary-foreground: 210 20% 98%;

        --secondary: 220 14.3% 95.9%;
        --secondary-foreground: 220.9 39.3% 11%;

        --muted: 220 14.3% 95.9%;
        --muted-foreground: 220 8.9% 46.1%;

        --accent: 220 14.3% 95.9%;
        --accent-foreground: 220.9 39.3% 11%;

        --destructive: 357 84.25% 50.95%;
        --destructive-foreground: 210 20% 98%;

        --border: 220 13% 91%;
        --input: 220 13% 91%;
        --ring: 224 71.4% 4.1%;

        --radius: 0.5rem;
    }

    .dark {
        --background: 224 71.4% 4.1%;
        --foreground: 210 20% 98%;

        --card: 224 71.4% 4.1%;
        --card-foreground: 210 20% 98%;

        --popover: 224 71.4% 4.1%;
        --popover-foreground: 210 20% 98%;

        --primary: 210 20% 98%;
        --primary-foreground: 220.9 39.3% 11%;

        --secondary: 215 27.9% 16.9%;
        --secondary-foreground: 210 20% 98%;

        --muted: 215 27.9% 16.9%;
        --muted-foreground: 217.9 10.6% 64.9%;

        --accent: 215 27.9% 16.9%;
        --accent-foreground: 210 20% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 20% 98%;

        --border: 215 27.9% 16.9%;
        --input: 215 27.9% 16.9%;
        --ring: 216 12.2% 83.9%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    /* width */
    ::-webkit-scrollbar {
        width: 14px;
        height: 14px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: rgb(248, 248, 248);
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #c7c7c7;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #b4b4b4;
    }
    body {
        @apply font-pjs text-themeBlack;
    }
    h2 {
        @apply text-lg font-bold text-themeGreenDark lg:text-xl;
        line-height: 1.25;
    }
    h3 {
        @apply text-base font-bold text-themeGreenDark;
        line-height: 1.25;
    }
    p {
        @apply text-[14px] leading-tight lg:text-[15px];
    }
    .nav-btn {
        @apply nav-text-size flex cursor-pointer items-center gap-x-3 rounded-lg text-left capitalize text-white transition hover:bg-themeGreen5 hover:bg-opacity-20;
        padding: 8px 12px !important;
    }
    .nav-dot {
        @apply h-1 w-1 rounded-full bg-themeGreen;
    }
    .nav-text-size {
        @apply text-[12px] leading-tight md:text-[14px];
    }
    .table-card {
        @apply mx-auto;
    }
    .c-text-size {
        @apply text-[14px] leading-tight lg:text-[15px];
    }
    .c-btn-border {
        @apply rounded-sm border border-themeBlack;
    }
    .cell-status {
        @apply text-[12px] font-medium uppercase lg:text-[13px];
    }
    .cell-status.active {
        @apply bg-opacity-10 text-themeGreen;
    }
    .cell-status.inactive {
        @apply bg-opacity-10 text-themeOrange;
    }
    .thead-item {
        @apply font-medium leading-none text-themeLabel;
    }
    .grid-form {
        @apply grid gap-x-3 gap-y-3 lg:grid-cols-2 lg:gap-y-4;
    }
    .disabled-style {
        @apply disabled:border-themeGray2 disabled:text-gray-500;
    }
    .info-label {
        @apply inline-block pr-2 text-[13px] font-bold capitalize text-themeGreenDark;
    }
    .info-table td,
    .info-table th {
        @apply px-2.5;
    }
    .search-input {
        background: #f4f4f6 !important;
        border: none !important;
        border-radius: 8px !important;
    }
    .textarea-h-sm {
        height: 43px;
    }
    .label {
        @apply mb-2.5 ml-0.5 block w-fit capitalize;
    }
    /* fix shadcn checkbox */
    input[type="checkbox"] {
        bottom: 0;
    }
    .c-table-wrap {
        @apply c-text-size w-full overflow-auto rounded-md border text-left;
    }
    .c-table.smaller {
        @apply text-[14px] leading-none;
    }
    .c-table {
        @apply w-fit;
    }
    .c-table th {
        @apply font-medium text-themeLabel;
    }
    .c-table th {
        @apply p-2;
    }
    .c-table td {
        @apply px-2 py-1;
    }
    .c-table tr {
        @apply border-b;
    }
    .c-table tbody tr:last-of-type {
        @apply border-b-0;
    }
    .c-table tbody {
        @apply py-2;
    }
    .warning-text {
        @apply mt-1 text-[13px] font-medium leading-tight text-destructive;
    }
    .timetable-col {
        @apply min-w-[120px] max-w-[120px];
    }
    .timetable-first-col {
        @apply min-w-[50px] max-w-[50px];
    }
    .draggable-product-group-item {
        @apply cursor-pointer rounded-md border px-3 py-1.5 text-center text-sm font-medium shadow-sm lg:min-w-[80px];
    }
    .table-chips-wrap {
        @apply flex flex-wrap gap-1;
    }
    .table-chip {
        @apply whitespace-nowrap rounded-sm border border-gray-300 px-1.5 py-1 text-[13px] font-medium;
    }
    ul:has(> li) {
        @apply pl-[18px];
    }
    .table-item-li {
        @apply list-disc whitespace-nowrap py-0.5 text-[14px] leading-tight;
    }
    .table-item-li::marker {
        @apply text-[12px] leading-none text-gray-300;
    }
    .table-item-li span {
        position: relative;
        left: -4px;
    }
    .filter-form {
        @apply flex w-full flex-wrap items-end gap-3 border-t border-dashed pb-3 pt-3 lg:grid lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5;
    }
    .filter-duo-buttons {
        @apply flex items-center gap-x-2;
    }
    .report-card-max-width {
        @apply mx-auto max-w-[1200px];
        padding-bottom: 20px !important;
    }
    .report-table-title {
        @apply c-text-size bg-themeGreen3 px-2 py-3 font-semibold text-themeGreen2;
    }
    .c-text-ellipsis {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* Number of lines to show */
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 4.5em; /* Adjust based on line height and number of lines */
    }
    .table-page-top {
        @apply mb-2 flex w-full flex-wrap items-center gap-3 lg:mb-3 lg:gap-x-3.5 lg:px-1;
    }
    .formula-button {
        @apply w-fit cursor-pointer rounded-md border border-themeGreen2 border-opacity-10 bg-themeGreen3 px-3 py-2 text-[14px] font-medium leading-none text-themeGreenDark shadow-sm transition hover:border-opacity-40 hover:bg-themeGreen hover:bg-opacity-15;
    }
    .timeslot-override .first-col {
        @apply flex w-[100px] items-center justify-center border-r p-1 text-center font-medium;
    }
    .timeslot-override .col {
        @apply flex w-[80px] flex-col items-center justify-center border-r p-1 text-center text-[13px] last:border-r-0;
    }
}

.rdp-vhidden {
    @apply hidden;
}
.rdp-caption_dropdowns {
    @apply ml-1 flex justify-start gap-x-2;
}
.rdp-dropdown {
    @apply cursor-pointer rounded-md border px-2 pb-1.5 pt-1 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-themeGray;
}

.fc .fc-toolbar-title {
    @apply ml-0.5 text-base font-semibold leading-none text-gray-500;
}
.fc .fc-toolbar.fc-header-toolbar {
    @apply mb-1.5;
}
.fc .fc-button:focus {
    outline: none !important;
    box-shadow: none !important;
}
/* nav arrow button */
.fc .fc-button-primary {
    @apply h-8 border-themeGreen bg-white py-0 text-themeGreen hover:border-themeGreen hover:bg-themeGreen4 hover:text-themeGreen focus:shadow-none;
}
.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
    @apply focus-within:border-themeGreen hover:border-themeGreen hover:bg-themeGreen4 hover:text-themeGreen active:bg-themeGreen4 active:text-themeGreen;
}
.fc .fc-button .fc-icon {
    @apply block text-xl leading-none;
}

.fc .fc-button-primary.fc-today-button {
    @apply border-themeGreen bg-white text-[14px] font-medium capitalize text-themeGreen hover:bg-themeGreen4 disabled:opacity-0;
}
.fc .fc-daygrid-day-events {
    @apply cursor-default;
}
.fc .fc-daygrid-day.fc-day-today {
    @apply bg-white;
}
.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
    @apply rounded-full bg-themeGreen bg-opacity-10 font-semibold text-themeGreen;
}
.fc .fc-col-header-cell-cushion {
    @apply text-[14px] font-medium text-gray-500;
}
.fc .fc-daygrid-event {
    @apply whitespace-normal;
}
.fc .fc-h-event {
    @apply hidden;
}
.fc .fc-h-event .fc-event-main {
    @apply hidden;
}
.fc .fc-daygrid-day-number {
    @apply m-1 px-2 py-0.5 text-[14px] text-gray-500;
}
.fc .fc-daygrid-dot-event.fc-event-mirror,
.fc-daygrid-dot-event:hover {
    @apply bg-transparent;
}

/* show more button */
.fc .fc-daygrid-day-bottom {
    @apply mb-1.5 ml-2 text-[12px] font-medium text-themeGreen;
    margin-top: 2px !important;
}
.fc .fc-popover-header {
    @apply border-themeGray2 bg-white pl-4 pt-1 text-[14px] text-gray-500;
}
.fc .fc-daygrid-more-link:hover {
    @apply bg-transparent opacity-80 transition;
}
.sr-only {
    top: 0 !important;
}
select[aria-hidden="true"] {
    bottom: 0 !important;
}

.rdrDefinedRangesWrapper {
    display: none;
}
.rdrDayToday .rdrDayNumber {
    @apply rounded-md bg-themeGreen bg-opacity-10;
}
.rdrDayToday .rdrDayNumber span:after {
    background: #44b656 !important;
}

.calendar .fc-daygrid-day-frame,
.calendar .fc-daygrid-day-events,
.calendar .fc-daygrid-event-harness,
.calendar .fc-event {
    @apply flex flex-grow flex-col;
}
.calendar .fc-initiateCalendar-button {
    @apply h-10 bg-themeGreen px-4 font-semibold text-white;
}
.calendar .fc-close-button {
    @apply border-gray-300 px-1.5 text-gray-500;
}
.calendar .fc .fc-daygrid-body-natural .fc-daygrid-day-events,
.calendar .fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
    @apply mb-0 min-h-0;
}

.calendar .fc-daygrid-day-bottom {
    margin-top: 0 !important;
}
.calendar .fc-day {
    @apply relative;
}
.calendar .fc .fc-daygrid-day-number {
    @apply m-0;
}
.calendar .fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
    @apply bg-transparent text-gray-500;
}

.print-table {
    width: 100%;
    border-collapse: collapse;
}
.print-table th,
.print-table td {
    border: 1px solid #ddd;
    padding: 8px;
}
.print-table th {
    background-color: #f2f2f2;
}

.print-timetable-table {
    width: 100%;
    border-collapse: collapse;
}
.print-timetable-table th,
.print-timetable-table td {
    border: 1px solid #ddd;
    padding: 4px;
}

@media screen {
    #print-container {
        min-width: 600px !important;
    }

    #print-container.bigger {
        min-width: 800px !important;
    }
}

.printable {
    @apply hidden;
}

@media print {
    body {
        visibility: hidden;
    }
    .print-show {
        height: auto !important;
        overflow: visible !important;
        max-height: none !important;
        max-width: none !important;
        position: absolute !important;
        top: 0;
        left: 0;
        width: 100%;
        visibility: visible !important;
        display: block !important;
    }
    .print-hide {
        visibility: hidden !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    #print-container,
    #print-container * {
        visibility: visible;
    }

    #print-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        background: white;
    }

    .print-two-chart-in-one {
        height: 355px !important;
        max-height: 355px !important;
    }

    .print-two-chart-in-one canvas {
        max-height: 100% !important;
        width: 70% !important;
    }

    @page {
        size: A4 landscape;
    }

    html,
    body {
        margin: 0;
        padding: 0;
        height: auto;
        overflow: hidden;
    }
}
