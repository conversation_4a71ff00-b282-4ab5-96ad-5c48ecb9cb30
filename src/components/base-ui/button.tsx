import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { useLoading } from "@/lib/store";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
    "c-text-size w-fit flex items-center justify-center whitespace-nowrap rounded-lg font-semibold ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-200 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-40",
    {
        variants: {
            variant: {
                primary: "bg-themeGreen text-white hover:bg-themeGreen/80",
                secondary: "bg-themeGray2 text-gray-600 hover:bg-themeGray/30",
                outline:
                    "border border-themeGreen text-themeGreen bg-white hover:bg-themeGreen/10",
                outlineBlack:
                    "border border-themeGray text-themeBlack bg-white hover:bg-themeGray/5",
                outlineGray:
                    "border border-input text-themeBlack bg-white hover:bg-themeGray/10",
                ghost: "hover:bg-themeGray/10 hover:text-opacity-100",
                none: "",
                link: "text-primary underline-offset-4 hover:underline",
                destructive:
                    "bg-destructive text-destructive-foreground hover:bg-destructive/90",
            },
            size: {
                primary: "px-5 min-h-[42px]",
                smallerOnMobile: "px-3 md:px-5 min-h-[38px] lg:min-h-[42px]",
                icon: "h-8 w-8",
                ghost: "px-1",
                smaller: "h-9 px-3",
            },
        },
        defaultVariants: {
            variant: "primary",
            size: "primary",
        },
    }
);

export interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : "button";

        const isLoading = useLoading((state) => state.isLoading);

        return (
            <Comp
                className={cn(
                    buttonVariants({
                        variant,
                        size: variant === "ghost" ? "ghost" : size,
                        className,
                    })
                )}
                ref={ref}
                type={props.type || "button"}
                disabled={
                    props.disabled || (props.type === "submit" && isLoading)
                }
                {...props}
            />
        );
    }
);
Button.displayName = "Button";

export { Button, buttonVariants };
