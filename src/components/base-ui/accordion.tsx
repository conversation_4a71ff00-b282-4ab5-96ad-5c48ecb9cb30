import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import clsx from "clsx";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

const Accordion = AccordionPrimitive.Root;

type AccordionItemProps = React.ComponentPropsWithoutRef<
    typeof AccordionPrimitive.Item
> & {
    hasLine?: boolean;
};

type AccordionTriggerProps = React.ComponentPropsWithoutRef<
    typeof AccordionPrimitive.Trigger
> & {
    isWhite?: boolean;
    hasChevron?: boolean;
};

const AccordionItem = React.forwardRef<
    React.ElementRef<typeof AccordionPrimitive.Item>,
    AccordionItemProps
>(({ className, hasLine = true, ...props }, ref) => (
    <AccordionPrimitive.Item
        ref={ref}
        className={cn(hasLine && "border-b", className)}
        {...props}
    />
));
AccordionItem.displayName = "AccordionItem";

const AccordionTrigger = React.forwardRef<
    React.ElementRef<typeof AccordionPrimitive.Trigger>,
    AccordionTriggerProps
>(
    (
        { className, children, isWhite = false, hasChevron = true, ...props },
        ref
    ) => (
        <AccordionPrimitive.Header className="flex">
            <AccordionPrimitive.Trigger
                ref={ref}
                className={cn(
                    "flex flex-1 items-center justify-between py-4 font-medium transition-all [&[data-state=open]>svg]:rotate-180",
                    className
                )}
                {...props}
            >
                {children}
                {hasChevron && (
                    <ChevronDown
                        className={clsx(
                            "h-4 w-4 shrink-0 transition-transform duration-200",
                            isWhite
                                ? "text-white text-opacity-80"
                                : "text-themeBlack"
                        )}
                    />
                )}
            </AccordionPrimitive.Trigger>
        </AccordionPrimitive.Header>
    )
);
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef<
    React.ElementRef<typeof AccordionPrimitive.Content>,
    React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
    <AccordionPrimitive.Content
        ref={ref}
        className="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
        {...props}
    >
        <div className={cn("pb-4 pt-0", className)}>{children}</div>
    </AccordionPrimitive.Content>
));

AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
