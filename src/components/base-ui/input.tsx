import * as React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {
    suffixIcon?: string;
    onClickIcon?: () => void;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
    ({ className, type, suffixIcon, onClickIcon, ...props }, ref) => {
        return (
            <div className="relative flex items-center">
                <input
                    type={type}
                    className={cn(
                        "c-text-size disabled-style flex h-[42px] w-full rounded-sm border border-input bg-background px-4 py-2.5 font-pjs file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-themeGray3",
                        !props.disabled &&
                            "hover:border-themeGreen2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-themeGreen2",
                        className
                    )}
                    ref={ref}
                    autoFocus={false}
                    {...props}
                />
                {suffixIcon && (
                    <div
                        className="absolute right-2 flex cursor-pointer items-center"
                        onClick={onClickIcon}
                    >
                        <Image
                            src={suffixIcon}
                            alt="postfix-icon"
                            width={24}
                            height={24}
                            className="m-2"
                            unoptimized
                        />
                    </div>
                )}
            </div>
        );
    }
);
Input.displayName = "Input";

export { Input };
