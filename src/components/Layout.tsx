import { useTranslations } from "next-intl";
import { ReactNode, useEffect, useState } from "react";
import React from "react";
import Head from "next/head";
import { internationalizationAPI, TOKEN } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages, useLoading } from "@/lib/store";
import { getPageTitle } from "@/lib/utils";
import NavWrap from "./NavWrap";
import LoaderOverlay from "./ui/LoaderOverlay";
import { parseCookies } from "nookies";
import { useRouter } from "next/router";

type LayoutProps = {
    path: string;
    children: ReactNode;
    locale: string;
};

const Layout = ({ path, children, locale }: LayoutProps) => {
    const t = useTranslations("Nav");
    const title = getPageTitle(path, t);
    const router = useRouter();

    const isLoading = useLoading((state) => state.isLoading);
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const setLanguages = useLanguages((state) => state.setLanguages);

    const [hasMounted, setHasMounted] = useState(false);
    const [hasToken, setHasToken] = useState(false);

    const { data: languageData, axiosQuery: getLanguages } = useAxios({
        api: internationalizationAPI,
        locale,
        noLoading: true,
    });

    useEffect(() => {
        setHasMounted(true);
    }, []);

    useEffect(() => {
        if (!hasMounted) return;

        const token = parseCookies()[TOKEN];

        if (!token) {
            router.replace("/login");
            return;
        }

        setHasToken(true);

        if (!activeLanguages) {
            getLanguages();
        }
    }, [hasMounted, activeLanguages]);

    useEffect(() => {
        if (languageData?.length > 0) {
            setLanguages(languageData);
        }
    }, [languageData]);

    if (!hasMounted) {
        return (
            <Head>
                <title>{title}</title>
            </Head>
        );
    }

    return (
        <>
            <Head>
                <title>{title}</title>
            </Head>
            {hasToken && (
                <NavWrap path={path} locale={locale}>
                    {children}
                </NavWrap>
            )}
            {(!hasToken || isLoading) && <LoaderOverlay />}
        </>
    );
};

export default Layout;
