import React from "react";
import { Upload, X } from "lucide-react";
import { Controller } from "react-hook-form";
import toast from "react-hot-toast";
import { Input } from "@/components/base-ui/input";
import { getInputErrorMessage, replaceAll } from "@/lib/utils";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

const MAX_FILE_SIZE = 2 * 1024 * 1024;

type FreeFileInputProps = {
    control: any;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    showPreview?: boolean;
    onChange?: (file: File | null) => void;
    error?: any;
};

const FreeFileInput = ({
    control,
    name,
    label,
    placeholder = "Choose File",
    disabled = false,
    onChange,
    error,
    showPreview = true,
}: FreeFileInputProps) => {
    const t = useTranslations("common");

    return control ? (
        <div>
            {label && (
                <Label className={"label"}>
                    {t(replaceAll(label || name, "_", " "))}
                </Label>
            )}
            <Controller
                control={control}
                name={name}
                render={({ field }) => {
                    const file = field.value;
                    let preview = file && URL.createObjectURL(file);

                    return (
                        <div>
                            {!file ? (
                                <div className="relative">
                                    <Input
                                        type="file"
                                        disabled={disabled}
                                        className="pl-2 pt-2"
                                        accept="image/*"
                                        ref={field.ref}
                                        onChange={(e) => {
                                            const selectedFile =
                                                e.target.files?.[0] || null;

                                            if (selectedFile) {
                                                if (
                                                    selectedFile.size >
                                                    MAX_FILE_SIZE
                                                ) {
                                                    toast.error(
                                                        `File size should not exceed ${MAX_FILE_SIZE / (1024 * 1024)} MB`
                                                    );
                                                    return;
                                                }

                                                preview =
                                                    URL.createObjectURL(
                                                        selectedFile
                                                    );

                                                field.onChange(selectedFile);
                                                onChange &&
                                                    onChange(selectedFile);
                                            }
                                        }}
                                    />
                                    <div className="pointer-events-none absolute left-0 top-0 flex h-full w-full items-center gap-x-2 rounded-sm border border-input bg-white px-4 text-gray-500">
                                        <Upload size={16} />
                                        <span className="font-medium text-themeLabel">
                                            {placeholder}
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <div className="ml-1 mt-2 flex gap-x-2">
                                    <X
                                        size={20}
                                        className="cursor-pointer rounded-full border border-gray-500 bg-white p-0.5 text-gray-500"
                                        onClick={() => {
                                            field.onChange(null);
                                        }}
                                    />
                                    <div className="w-[calc(100%-40px)] overflow-hidden text-ellipsis text-[13px] font-medium text-gray-600">
                                        {file?.name}
                                    </div>
                                </div>
                            )}

                            {preview && showPreview && (
                                <div className="relative mt-2.5 w-fit">
                                    <img src={preview} className="w-[200px]" />
                                </div>
                            )}
                            {error && (
                                <div className="warning-text">
                                    {`${getInputErrorMessage(error)}`}
                                </div>
                            )}
                        </div>
                    );
                }}
            />
        </div>
    ) : (
        <></>
    );
};
export default FreeFileInput;
