import React from "react";
import AsyncCreatableSelect from "react-select/async-creatable";
import {
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { selectStyles } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

type FormSelectAsyncCreatableProps = {
    control: any;
    value: any;
    name: string;
    label?: string;
    isDisabled?: boolean;
    isClearable?: boolean;
    hideLabel?: boolean;
    loadOptions?: (inputValue: any, callback: any) => void;
    onChange?: (value: any) => void;
};

const FormSelectAsyncCreatable = ({
    control,
    value,
    name,
    label,
    isDisabled,
    isClearable = true,
    hideLabel = false,
    loadOptions,
    onChange = () => {},
}: FormSelectAsyncCreatableProps) => {
    const t = useTranslations("common");
    const selectName = t(replaceAll(label || name, "_", " "));

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hideLabel == false && (
                        <FormLabel className="capitalize">
                            {selectName}
                        </FormLabel>
                    )}

                    <AsyncCreatableSelect
                        isClearable={isClearable}
                        cacheOptions
                        defaultOptions
                        loadOptions={loadOptions}
                        {...field}
                        placeholder={t("Type to search")}
                        isDisabled={isDisabled}
                        value={value}
                        onChange={(selectedOption) => {
                            field.onChange(selectedOption?.value || null);
                            onChange(selectedOption);
                        }}
                        styles={selectStyles}
                    />
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormSelectAsyncCreatable;
