import React, { useState } from "react";
import { format, parseISO } from "date-fns";
import { isString } from "lodash";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/base-ui/calendar";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/base-ui/popover";
import { DATE_FORMAT } from "@/lib/constant";
import { cn, getYesterday, replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { enUS, zhCN } from "date-fns/locale";
import { useLocale, useTranslations } from "next-intl";

export function DatePicker({
    control,
    name,
    label,
    onChange,
    hasLabel = true,
    isDisabled = false,
    disableDateBeforeToday = false,
    disableDateAfterToday = false,
    placeholder,
    isSmaller = false,
    showSelection = true,
    rules,
}: {
    control: any;
    name: string;
    label?: string;
    onChange?: (value) => void;
    hasLabel?: boolean;
    isDisabled?: boolean;
    disableDateBeforeToday?: boolean;
    disableDateAfterToday?: boolean;
    placeholder?: string;
    isSmaller?: boolean;
    showSelection?: boolean;
    rules?: any;
}) {
    const t = useTranslations("common");
    const [isOpen, setIsOpen] = useState(false);

    return control ? (
        <FormField
            control={control}
            rules={rules}
            name={name}
            render={({ field }) => (
                <FormItem className="flex flex-col">
                    {hasLabel && (
                        <FormLabel className="min-w-[150px] capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </FormLabel>
                    )}
                    {isDisabled ? (
                        <div
                            className={cn(
                                "c-text-size flex min-h-[42px] w-full items-center rounded-md border border-themeGray2 px-4 py-2 pl-4 text-left font-normal text-themeLabel",
                                isDisabled && "bg-gray-50 text-black"
                            )}
                        >
                            {field.value &&
                                format(
                                    field.value,
                                    DATE_FORMAT.DMY
                                    // locale === "zh" ? "PPP" : DATE_FORMAT.DMY,
                                    // {
                                    //     locale: locale === "zh" ? zhCN : enUS,
                                    // }
                                )}
                        </div>
                    ) : (
                        <Popover open={isOpen} onOpenChange={setIsOpen}>
                            <PopoverTrigger asChild>
                                <FormControl>
                                    <Button
                                        size={isSmaller ? "smaller" : "primary"}
                                        variant={"outlineGray"}
                                        className={
                                            "w-full rounded-md border-input pl-4 text-left font-normal"
                                        }
                                    >
                                        {" "}
                                        <div className="pr-2">
                                            {showSelection && field.value ? (
                                                format(
                                                    field.value,
                                                    DATE_FORMAT.DMY
                                                    // locale === "zh"
                                                    //     ? DATE_FORMAT.PPP
                                                    //     : DATE_FORMAT.DMY,
                                                    // {
                                                    //     locale:
                                                    //         locale === "zh"
                                                    //             ? zhCN
                                                    //             : enUS,
                                                    // }
                                                )
                                            ) : (
                                                <span className="text-themeLabel">
                                                    {t(placeholder ?? "")}
                                                </span>
                                            )}
                                        </div>
                                        <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                                    </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                                className="w-auto bg-white p-0"
                                align="start"
                            >
                                <Calendar
                                    mode="single"
                                    defaultMonth={field.value}
                                    selected={
                                        isString(field.value)
                                            ? parseISO(field.value)
                                            : field.value
                                    }
                                    onSelect={(date) => {
                                        field.onChange(date);
                                        onChange && onChange(date);
                                        setIsOpen(false);
                                    }}
                                    disabled={(date) =>
                                        (disableDateBeforeToday &&
                                            date < getYesterday()) ||
                                        (disableDateAfterToday &&
                                            date > new Date())
                                    }
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                    )}
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
}
