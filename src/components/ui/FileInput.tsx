import { useState } from "react";
import { Upload, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { mediaUploadAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Input } from "../base-ui/input";

const FileInput = ({ setId }) => {
    const form = useForm();
    const [fileName, setFileName] = useState<string | null>(null);

    const { axiosMultipartPost: uploadFiles } = useAxios({
        api: mediaUploadAPI,
        onSuccess: (result) => setId(result.data?.[0]?.id),
    });

    function submitFiles() {
        form.handleSubmit((data) => {
            if (data.files) {
                delete data.files.length;

                const _files: any[] = Object.values(data.files);

                setFileName(_files[0].name);

                const newData = {
                    files: _files.map((file: any) => ({
                        name: file?.name,
                        file,
                    })),
                };

                console.log("newData", newData);
                uploadFiles(newData, false);
            }
        })();
    }

    function onClear() {
        setId(null);
        setFileName(null);
        form.reset({ files: null });
    }

    return (
        <div className="w-full">
            <div className="relative w-full">
                <div className="c-text-size flex h-[42px] w-full items-center gap-x-2 rounded-md border border-input pl-4 font-medium text-gray-500">
                    <Upload size={16} />
                    <span>Choose File</span>
                </div>
                <div className="absolute left-0 top-0 z-10 h-full w-full opacity-0">
                    <Input
                        className="pl-2 pt-2"
                        type="file"
                        accept=".jpg, .jpeg, .png, .doc, .docx, .pdf"
                        onAbort={() => {}}
                        {...form.register("files", {
                            onChange: (e) => {
                                if (e.target.files?.length > 0) {
                                    submitFiles();
                                } else {
                                    onClear();
                                }
                            },
                        })}
                    />
                </div>
            </div>
            {fileName && (
                <div className="ml-1 mt-2 flex gap-x-3">
                    <div className="text-[13px]">{fileName}</div>
                    <X
                        size={20}
                        className="cursor-pointer rounded-full border border-gray-500 bg-white p-0.5 text-gray-500"
                        onClick={onClear}
                    />
                </div>
            )}
        </div>
    );
};

export default FileInput;
