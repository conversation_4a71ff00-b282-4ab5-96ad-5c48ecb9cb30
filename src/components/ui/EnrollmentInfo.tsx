import { replaceAll } from "@/lib/utils";

const EnrollmentInfo = ({ data }) => {
    function formattedData() {
        return data
            ? {
                  enrollment_status: data?.status,
                  student_name: data?.student_name,
                  email: data?.email,
                  nric_no: data?.nric_no,
                  passport_no: data?.passport_no,
                  gender: data?.gender,
                  nationality: data?.nationality?.name,
                  race: data?.race?.name,
                  religion: data?.religion?.name,
                  address: data?.address,
                  state: data?.state?.name,
                  city: data?.city,
              }
            : {};
    }
    return data ? (
        <div className="pb-5">
            <h2 className="mb-5">Enrollment Info</h2>
            {Object.entries(formattedData())?.map(([key, value]) => {
                return (
                    <div
                        key={key}
                        className="mb-1.5 flex gap-x-2 border-b pb-1.5"
                    >
                        <p className="text-[14px] font-medium capitalize text-themeLabel">
                            {replaceAll(key, "_", " ")}
                        </p>
                        <p className="">{`${value}`}</p>
                    </div>
                );
            })}
        </div>
    ) : (
        <></>
    );
};

export default EnrollmentInfo;
