import React, { useState } from "react";
import {
    flexRender,
    getCoreRowModel,
    useReactTable,
} from "@tanstack/react-table";
import clsx from "clsx";
import { Check } from "lucide-react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/base-ui/table";
import { TableColumnType } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import TableHeaderSorter from "./TableHeaderSorter";
import { useTranslations } from "next-intl";

type DataTableDraggableProps = {
    columns: Array<TableColumnType>;
    data: Array<any>;
    actionMenu?: ({ cell }: { cell: any }) => JSX.Element;
    sort?: (name: string, direction: string) => void;
    sorted?: Record<string, any>;
    hasPerPage?: boolean;
    onSelect?: (id) => void;
    onMultiSelect?: (list: any[]) => void;
    isForm?: boolean;
    clearCount?: number;
    onDragEnd: (result: any) => void;
};

const DataTableDraggable = ({
    columns,
    data,
    actionMenu,
    sort = () => {},
    sorted = {},
    hasPerPage,
    onSelect,
    onMultiSelect,
    isForm = false,
    clearCount = 0,
    onDragEnd,
}: DataTableDraggableProps) => {
    const t = useTranslations("common");

    const [selected, setSelected] = useState<any>(null);
    const [rowSelection, setRowSelection] = useState<any>({});

    const tableColumns = columns.map((item) => ({
        accessorKey: item.key,
        header: () => {
            return item.hasSort ? (
                <TableHeaderSorter
                    head={item.key}
                    label={item.displayAs}
                    sorted={sorted}
                    sort={sort}
                />
            ) : (
                <div className="c-text-size thead-item capitalize">
                    {t(replaceAll(item.displayAs || item.key, "_", " "))}
                </div>
            );
        },
        cell: ({ cell }) => {
            const value = cell.getValue(item);
            if (item.modify) {
                return item.modify(value, cell);
            } else {
                return value;
            }
        },
    }));

    if (onSelect) {
        tableColumns.unshift({
            accessorKey: "select",
            header: () => <></>,
            cell: ({ cell }) => {
                const _id = cell.row.original?.id;
                return (
                    <div
                        className={clsx(
                            "flex h-5 w-5 cursor-pointer items-center justify-center rounded-full border",
                            _id && selected == _id
                                ? "border-themeGreen bg-themeGreen"
                                : "border-gray-400"
                        )}
                        onClick={() => onSelect(_id)}
                    >
                        {_id && selected == _id && (
                            <Check size={14} className="text-white" />
                        )}
                    </div>
                );
            },
        });
    }

    tableColumns.unshift({
        accessorKey: "drag",
        header: () => <span className="sr-only">Drag</span>,
        cell: () => (
            <div className="cursor-move">
                <span>::</span>
            </div>
        ),
    });

    const table = useReactTable({
        data: data ?? [],
        columns: tableColumns,
        getCoreRowModel: getCoreRowModel(),
        state: { rowSelection },
    });

    return (
        <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="table-body">
                {(provided) => (
                    <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="overflow-auto rounded-md border border-gray-200"
                    >
                        <Table>
                            <TableHeader>
                                {table?.getHeaderGroups().map((headerGroup) => (
                                    <TableRow key={headerGroup.id}>
                                        {headerGroup.headers.map((header) => (
                                            <TableHead key={header.id}>
                                                {header.isPlaceholder
                                                    ? null
                                                    : flexRender(
                                                          header.column
                                                              .columnDef.header,
                                                          header.getContext()
                                                      )}
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                ))}
                            </TableHeader>
                            <TableBody>
                                {table.getRowModel().rows.map((row, index) => (
                                    <Draggable
                                        key={row.id}
                                        draggableId={row.id}
                                        index={index}
                                    >
                                        {(provided) => (
                                            <TableRow
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                className={clsx({
                                                    "opacity-50":
                                                        row.original
                                                            .is_active == false,
                                                })}
                                            >
                                                {row
                                                    .getVisibleCells()
                                                    .map((cell) => (
                                                        <TableCell
                                                            key={cell.id}
                                                            className={
                                                                cell.column
                                                                    .columnDef
                                                                    .id ===
                                                                "action"
                                                                    ? "w-14"
                                                                    : ""
                                                            }
                                                        >
                                                            {flexRender(
                                                                cell.column
                                                                    .columnDef
                                                                    .cell,
                                                                cell.getContext()
                                                            )}
                                                        </TableCell>
                                                    ))}
                                            </TableRow>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </TableBody>
                        </Table>
                    </div>
                )}
            </Droppable>
        </DragDropContext>
    );
};

export default DataTableDraggable;
