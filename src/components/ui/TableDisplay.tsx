import { X } from "lucide-react";
import { formatUnderscores } from "@/lib/utils";

const TableDisplay = ({
    title,
    data,
    hiddenFields,
    onRemove,
}: {
    title?: string;
    data: Record<any, string>[] | null;
    hiddenFields?: string[];
    onRemove?: (id) => void;
}) => {
    return (
        <div className="w-full">
            {title && (
                <h3 className="mb-2 ml-0.5 font-bold capitalize text-themeGreenDark lg:col-span-2">
                    {title}
                </h3>
            )}
            <div className="w-full overflow-auto">
                {data && (
                    <table className="w-full">
                        <thead>
                            <tr className="border-y border-gray-400 hover:bg-gray-50">
                                {onRemove && <th></th>}
                                {data[0] &&
                                    Object.keys(data[0])?.map((header) =>
                                        !hiddenFields?.includes(header) ? (
                                            <th
                                                key={header}
                                                className="border-r px-1.5 py-2 text-left text-[14px] font-medium capitalize leading-tight text-themeLabel last:border-r-0"
                                            >
                                                {formatUnderscores(header)}
                                            </th>
                                        ) : null
                                    )}
                            </tr>
                        </thead>
                        <tbody>
                            {data?.map((row, index) => (
                                <tr
                                    key={index}
                                    className="rounded-md border-b transition hover:bg-gray-50"
                                >
                                    {onRemove && (
                                        <td className="">
                                            <div
                                                className="my-1 ml-1 cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80 lg:mr-2"
                                                onClick={() => onRemove(row)}
                                            >
                                                <X
                                                    size={14}
                                                    className="text-themeGreenDark"
                                                />
                                            </div>
                                        </td>
                                    )}
                                    {row &&
                                        Object.entries(row)?.map(
                                            ([key, val]) =>
                                                !hiddenFields?.includes(key) ? (
                                                    <td
                                                        key={`${key + val}`}
                                                        className="c-text-size border-r px-1.5 py-2 last:border-r-0"
                                                    >{`${val}`}</td>
                                                ) : null
                                        )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
        </div>
    );
};

export default TableDisplay;
