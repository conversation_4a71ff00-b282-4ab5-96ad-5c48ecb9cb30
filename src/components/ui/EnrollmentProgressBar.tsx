import { Fragment } from "react";
import React from "react";
import clsx from "clsx";

const EnrollmentProgressBar = ({ step }: { step: number }) => {
    function indicator(order: number) {
        return (
            <div className="relative flex flex-col items-center">
                <div
                    className={clsx(
                        "flex h-6 w-6 items-center justify-center rounded-full border-2 p-0.5 text-[12px] font-semibold text-gray-300 lg:h-7 lg:w-7 lg:text-[14px]",
                        step > order &&
                            "border-themeGreen bg-themeGreen text-white",
                        step === order &&
                            "border-themeGreen font-bold text-themeGreen",
                        step < order && "border-gray-300"
                    )}
                >
                    {order}
                </div>
            </div>
        );
    }

    function line(order: number) {
        return (
            <div
                className={clsx(
                    "h-[2px] flex-grow",
                    step > order ? "bg-themeGreen" : "bg-gray-300"
                )}
            ></div>
        );
    }

    const steps = 5;

    return (
        <div className="mx-auto mb-1 mt-4 flex w-[90%] max-w-[320px] items-center justify-center lg:mt-5">
            {Array(steps)
                .fill(null)
                .map((_, index) => (
                    <Fragment key={"step-" + index}>
                        {indicator(index + 1)}
                        {index !== steps - 1 && line(index + 1)}
                    </Fragment>
                ))}
        </div>
    );
};

export default EnrollmentProgressBar;
