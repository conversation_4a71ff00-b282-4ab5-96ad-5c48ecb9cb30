import React from "react";
import { useAxios } from "@/lib/hook";
import { Button } from "../base-ui/button";
import { DialogFooter } from "../base-ui/dialog";

type CancelPromptProps = {
    api: string;
    id: number | string | null;
    refresh: () => void;
    close: () => void;
};

const CancelPrompt = ({ api, id, close, refresh }: CancelPromptProps) => {
    const { axiosPut } = useAxios({
        api: api,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit() {
        if (id) {
            axiosPut({ data: {} });
        }
    }
    return (
        <>
            <p className="mt-3 font-medium">
                Are you sure you want to cancel this?
            </p>
            <DialogFooter className={"mt-2"}>
                <Button variant="outline" onClick={close}>
                    Cancel
                </Button>
                <Button onClick={onSubmit}>Confirm</Button>
            </DialogFooter>
        </>
    );
};

export default CancelPrompt;
