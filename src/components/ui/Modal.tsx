import React, { ReactNode } from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import clsx from "clsx";
import { X } from "lucide-react";
import { Dialog, DialogContent } from "../base-ui/dialog";

type ModalProps = {
    open: any;
    onOpenChange: any;
    children: ReactNode;
    canOverflow?: boolean;
    noCloseButton?: boolean;
    size?: "small" | "medium" | "large" | "fit";
};

const Modal = ({
    open,
    onOpenChange,
    children,
    canOverflow = false,
    noCloseButton = false,
    size = "small",
}: ModalProps) => {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent
                size={size}
                className={clsx(
                    canOverflow && "lg:overflow-visible"
                    // "flex h-full flex-col items-start justify-start"
                )}
                onOpenAutoFocus={(e) => e.preventDefault()}
                // onInteractOutside={(e) => e.preventDefault()}
            >
                {children}
                {!noCloseButton && (
                    <DialogPrimitive.Close className="absolute right-4 top-4 z-10 rounded-sm bg-white p-0.5 opacity-60 transition-opacity hover:bg-gray-100 hover:opacity-100 focus:outline-none disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                        <X className="h-5 w-5" />
                        <span className="sr-only">Close</span>
                    </DialogPrimitive.Close>
                )}
            </DialogContent>
        </Dialog>
    );
};

export default Modal;
