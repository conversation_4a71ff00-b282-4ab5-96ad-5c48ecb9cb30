import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { X } from "lucide-react";
import Select from "react-select";
import { Skeleton } from "@/components/base-ui/skeleton";
import { GET_ALL_PARAMS, selectStyles } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { formatForSelect, removeLastWordWith } from "@/lib/utils";
import { Button } from "../base-ui/button";

const SelectToAdd = ({
    api,
    params = {},
    name,
    form,
    append,
    remove,
    errorMessage,
    hasLabel = false,
}: {
    api: string;
    params?: Record<string, any>;
    name: string;
    form: any;
    append: any;
    remove: any;
    errorMessage?: string;
    hasLabel?: boolean;
}) => {
    const locale = useLocale();
    const [selectedItem, setSelectedItem] = useState<any>();

    const {
        data: options,
        axiosQuery: getOptions,
        isLoading,
    } = useAxios({
        api,
        locale,
    });

    useEffect(() => {
        getOptions({ params: { ...GET_ALL_PARAMS, ...params } });
    }, []);

    return (
        <div>
            <p className="mb-1.5 font-medium capitalize text-themeLabel">
                {name}
            </p>
            {errorMessage && (
                <p className="warning-text mb-2">{errorMessage}</p>
            )}

            <div className="flex items-center gap-x-2">
                <div className="flex-grow">
                    {isLoading ? (
                        <Skeleton className="h-[42px] w-full border border-transparent" />
                    ) : (
                        <Select
                            isClearable
                            options={formatForSelect(options)}
                            value={
                                formatForSelect(options)?.find(
                                    (option) =>
                                        option.value === selectedItem?.value
                                ) ?? null
                            }
                            placeholder={
                                hasLabel
                                    ? ""
                                    : `Select ${removeLastWordWith(name, "s")}`
                            }
                            onChange={(selectedOption: any) => {
                                setSelectedItem(selectedOption);
                            }}
                            styles={selectStyles}
                        />
                    )}
                </div>

                <Button
                    variant={"outline"}
                    size={"smaller"}
                    onClick={() => {
                        if (!selectedItem) return;
                        if (
                            form
                                .watch(name)
                                .find(
                                    (item) => item.value === selectedItem.value
                                )
                        )
                            return;
                        append(selectedItem);
                        setSelectedItem(null);
                    }}
                >
                    Add
                </Button>
            </div>

            {form.watch(name).length > 0 && (
                <div className="ml-0.5 mt-3 grid gap-y-2.5">
                    {form.watch(name).map((item, index) => (
                        <div key={index} className="flex items-center gap-x-3">
                            <X
                                size={20}
                                className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                onClick={() => remove(index)}
                            />
                            <div className="c-text-size">{item?.label}</div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default SelectToAdd;
