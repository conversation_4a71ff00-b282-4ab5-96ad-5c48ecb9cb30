import { useLocale, useTranslations } from "next-intl";
import React, { useState } from "react";
import clsx from "clsx";
import { Plus, X } from "lucide-react";
import { Control, useFieldArray } from "react-hook-form";
import toast from "react-hot-toast";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { axiosInstance } from "@/lib/api";
import { duplicateEntryErrorMessage } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import FormSelectAsyncCreatable from "./FormSelectAsyncCreatable";

type FormSelectAsyncCreatableMultiProps = {
    control: Control<any>;
    label?: string;
    name: string;
    placeholder?: string;
    disabled?: boolean;
    api: string;
    idField: string;
    labelField: string;
};

type OptionType = {
    value: string | number;
    label: string;
};

const FormSelectAsyncCreatableMulti = ({
    control,
    name,
    label,
    api,
    idField,
    labelField,
}: FormSelectAsyncCreatableMultiProps) => {
    const { fields, append, remove } = useFieldArray({
        control,
        name,
    });

    const { handleError } = useAxios({ api: api });
    const [selectedOption, setSelectedOption] = useState<OptionType | null>(
        null
    );

    const handleAddMore = () => {
        if (selectedOption) {
            const duplicate = fields.some(
                (item) =>
                    item[idField] === selectedOption.value ||
                    item[labelField].toLowerCase() ===
                        selectedOption.label.toLowerCase()
            );

            if (duplicate) {
                toast(`${duplicateEntryErrorMessage} ${selectedOption.label}`);
                return;
            }

            append({
                [idField]: selectedOption.value,
                [labelField]: selectedOption.label,
            });
            setSelectedOption(null);
        }
    };

    const locale = useLocale();
    const t = useTranslations("common");

    const loadOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(api, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: item[labelField],
                        value: item[idField],
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleError(error);
                });
        } else {
            callback([]);
        }
    };

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => {
                return (
                    <FormItem>
                        <FormLabel className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </FormLabel>
                        <FormControl>
                            <>
                                <div
                                    className={clsx(
                                        fields.length > 0 && "mb-2.5 ml-0.5"
                                    )}
                                >
                                    {fields?.map((item, index) => {
                                        return (
                                            <div
                                                key={`${item[idField]}-${index}`}
                                                className="mb-1.5 items-center space-x-2"
                                            >
                                                {fields.length > 0 &&
                                                    item[labelField] && (
                                                        <div className="flex items-center gap-x-3">
                                                            <X
                                                                size={20}
                                                                className="cursor-pointer rounded-full border border-gray-500 bg-white p-0.5 text-gray-500"
                                                                onClick={() => {
                                                                    return remove(
                                                                        index
                                                                    );
                                                                }}
                                                            />
                                                            <p className="flex-grow content-center">
                                                                {
                                                                    item[
                                                                        labelField
                                                                    ]
                                                                }
                                                            </p>
                                                        </div>
                                                    )}
                                            </div>
                                        );
                                    })}
                                </div>
                                <div className="flex w-full items-center gap-x-3">
                                    <div className="w-full">
                                        <FormSelectAsyncCreatable
                                            isClearable={false}
                                            hideLabel={true}
                                            control={control}
                                            name={`${name}_temp`}
                                            loadOptions={loadOptions}
                                            value={selectedOption}
                                            onChange={setSelectedOption}
                                        />
                                    </div>
                                    <Button
                                        type="button"
                                        variant={"outline"}
                                        disabled={
                                            !selectedOption ||
                                            selectedOption?.label?.trim()
                                                ?.length < 1
                                        }
                                        onClick={handleAddMore}
                                        className="flex items-center gap-x-1"
                                    >
                                        <Plus size={20} className="-ml-2" />
                                        <span className="">{t("Add")}</span>
                                    </Button>
                                </div>
                            </>
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                );
            }}
        />
    ) : (
        <></>
    );
};

export default FormSelectAsyncCreatableMulti;
