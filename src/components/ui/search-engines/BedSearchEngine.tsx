import { CommonSearchEngineProps, hostelBedsAPI } from "@/lib/constant";
import BedSearchForm from "./BedSearchForm";
import SearchEngine from "./SearchEngine";
import { useTranslations } from "next-intl";

const BedSearchEngine = (props: CommonSearchEngineProps) => {
    const t = useTranslations("common");

    return (
        <SearchEngine
            {...props}
            name={"Bed"}
            api={hostelBedsAPI}
            otherFilterParams={{
                is_active: 1,
                status: !props.hasActiveBed ? "AVAILABLE" : null,
                includes: [
                    "hostelRoom",
                    "currentHostelBedAssignment.assignable",
                ],
                hostel_block_type: props.hostelType,
            }}
            definedColumn={[
                {
                    key: "block",
                    hasSort: false,
                },
                {
                    key: "room",
                    hasSort: true,
                },
                {
                    key: "bed",
                    hasSort: true,
                },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value) => t(value),
                },
                {
                    key: "assigned_to",
                    modify(value, cell) {
                        return <span className="text-[14px]">{value}</span>;
                    },
                },
            ]}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => {
                          const assignable =
                              item?.current_hostel_bed_assignment?.assignable;
                          return {
                              id: item?.id,
                              block: item?.hostel_room?.hostel_block?.name,
                              room: item?.hostel_room?.name,
                              bed: item?.name,
                              status: item?.status,
                              assigned_to: `${assignable?.number ?? "-"} ${assignable?.name ?? ""}`,
                          };
                      })
                    : [];
            }}
            orderBy={(name, direction) => {
                return name === "hostel_room"
                    ? { name: { [name]: direction } }
                    : { [name]: direction };
            }}
            searchForm={(params) => (
                <BedSearchForm {...params} hostelType={props.hostelType} />
            )}
        />
    );
};

export default BedSearchEngine;
