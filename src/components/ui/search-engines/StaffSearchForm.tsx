import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import { Switch } from "@/components/base-ui/switch";
import { axiosInstance } from "@/lib/api";
import {
    countryAPI,
    employeeJobTitlesAPI,
    employeeStatusOptions,
    genderOptions,
    raceAPI,
    religionAPI,
    SearchEngineFormProps,
    stateAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { configForGetAll } from "@/lib/utils";
import FormInput from "../FormInput";
import FormSelect from "../FormSelect";
import { Loader2 } from "lucide-react";
import { capitalize } from "lodash";

const StaffSearchForm = ({
    onSubmit,
    onReset,
    isSelectAll,
    setIsSelectAll,
    isMultiSelect,
    otherFilterParams,
}: SearchEngineFormProps & {
    isMultiSelect?: boolean;
}) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const staffForm = useForm({
        defaultValues: {
            name: otherFilterParams?.name ?? "",
            job_title_id: otherFilterParams?.job_title_id ?? "",
            email: otherFilterParams?.email ?? "",
            employee_number: otherFilterParams?.employee_number ?? "",
            race_id: otherFilterParams?.race_id ?? "",
            religion_id: otherFilterParams?.religion_id ?? "",
            country_id: otherFilterParams?.country_id ?? "",
            state_id: otherFilterParams?.state_id ?? "",
            status: otherFilterParams?.status ?? "",
            is_teacher:
                getStringForNumber(otherFilterParams?.is_teacher) ?? undefined,
            is_hostel:
                getStringForNumber(otherFilterParams?.is_hostel) ?? undefined,
        },
    });

    function getStringForNumber(value) {
        return typeof value === "number" ? value.toString() : value;
    }

    const [options, setOptions] = useState<Record<string, any> | null>(null);
    const [loadingState, setLoadingState] = useState(false);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.jobTitles = res[0].data.data;
            _options.religions = res[1].data.data;
            _options.races = res[2].data.data;
            _options.countries = res[3].data.data;
            setOptions(_options);
        },
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(employeeJobTitlesAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(countryAPI, _config),
        ]);
    }, []);

    const { data: stateOptions, axiosQuery: getStateOptions } = useAxios({
        api: stateAPI,
        locale,
        noLoading: true,
        onSuccess: () => setLoadingState(false),
        onError: () => setLoadingState(false),
    });

    useEffect(() => {
        const countryId = staffForm.watch("country_id");
        if (countryId) {
            setLoadingState(true);
            getStateOptions({ params: { country_id: countryId } });
        }
    }, [staffForm.watch("country_id")]);

    function submit() {
        staffForm.handleSubmit((data) => {
            Object.keys(data).forEach((key) => {
                if (!data[key]) {
                    data[key] = undefined;
                }
            });
            onSubmit(data);
        })();
    }

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    return (
        <Form {...staffForm}>
            <form
                onSubmit={submit}
                className="grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6"
            >
                <FormInput control={staffForm.control} name={"name"} />

                <FormInput
                    control={staffForm.control}
                    name={"email"}
                    type="email"
                />

                <FormSelect
                    name="job_title_id"
                    label="job title"
                    control={staffForm.control}
                    options={options?.jobTitles}
                />

                <FormInput
                    control={staffForm.control}
                    name={"employee_number"}
                />

                <FormSelect
                    control={staffForm.control}
                    name="gender"
                    isSortByName={false}
                    options={genderOptions.map((option) => ({
                        id: option.id,
                        name: capitalize(t(option.name)),
                    }))}
                />

                <FormSelect
                    control={staffForm.control}
                    name="race_id"
                    label="race"
                    options={options?.races}
                />

                <FormSelect
                    control={staffForm.control}
                    name="religion_id"
                    label="religion"
                    options={options?.religions}
                />

                <FormSelect
                    control={staffForm.control}
                    name="country_id"
                    label="country"
                    options={options?.countries}
                    onChange={(val) => {
                        getStateOptions({ params: { country_id: val } });
                        staffForm.setValue("state_id", "");
                    }}
                />

                <div className="relative">
                    <FormSelect
                        control={staffForm.control}
                        name="state_id"
                        label="state"
                        options={stateOptions}
                        isDisabled={!staffForm.watch("country_id")}
                    />
                    {loadingState && (
                        <div className="absolute left-0 top-0 flex h-full w-full justify-center bg-white bg-opacity-50">
                            <Loader2
                                className="mt-3 animate-spin text-themeGreenDark"
                                size={26}
                            />
                        </div>
                    )}
                </div>
                <FormSelect
                    control={staffForm.control}
                    name="status"
                    isStringOptions={true}
                    options={employeeStatusOptions}
                />

                <FormSelect
                    control={staffForm.control}
                    isSortByName={false}
                    name={"is_teacher"}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <FormSelect
                    control={staffForm.control}
                    isSortByName={false}
                    name={"is_hostel"}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />
            </form>
            <div className="mb-3 mt-1.5 flex w-full items-center justify-end gap-x-5">
                {isMultiSelect && (
                    <div className="ml-1 mr-auto flex items-center gap-x-2">
                        <Switch
                            checked={isSelectAll}
                            onCheckedChange={(checked: boolean) => {
                                setIsSelectAll(checked);
                            }}
                        />
                        <Label className="c-text-size font-medium text-gray-500">
                            {t("Select All")}
                        </Label>
                    </div>
                )}

                <div
                    className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                    onClick={() => {
                        onReset();
                        staffForm.reset();
                    }}
                >
                    {t("Reset")}
                </div>
                <Button variant={"outline"} onClick={submit}>
                    {t("Search")}
                </Button>
            </div>
        </Form>
    );
};

export default StaffSearchForm;
