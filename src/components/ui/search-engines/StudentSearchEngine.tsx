import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { capitalize, isArray } from "lodash";
import {
    CommonSearchEngineProps,
    GET_ALL_PARAMS,
    hostelStudentAPIFilter,
    semesterSettingAPI,
    STUDENT,
    studentAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { combinedNames, combinedNamesCell } from "@/lib/utils";
import SearchEngine from "./SearchEngine";
import StudentSearchForm from "./StudentSearchForm";
import { useTranslations } from "next-intl";

const StudentSearchEngine = (props: CommonSearchEngineProps) => {
    const locale = useLocale();
    const t = useTranslations("common");
    const [currentSemester, setCurrentSemester] = useState<any>(null);

    const _columns: TableColumnType[] = [
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "student_name",
            modify(value) {
                return combinedNamesCell(value);
            },
        },
        {
            key: "class",
            displayAs: "Current Class",
        },
        {
            key: "gender",
            hasSort: false,
            modify: (value) => t(value),
        },
        ...(props.isHostel
            ? [
                  {
                      key: "block",
                      hasSort: false,
                  },
                  {
                      key: "room",
                      hasSort: false,
                  },
                  {
                      key: "bed",
                      hasSort: false,
                  },
              ]
            : []),
    ];

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                setCurrentSemester(currentSemester.id);
            } else {
                setCurrentSemester("");
            }
        },
    });

    useEffect(() => {
        getSemesterOptions({ params: GET_ALL_PARAMS });
    }, []);

    if (currentSemester == null) {
        return <div></div>;
    } else
        return (
            <SearchEngine
                {...props}
                name={"Student"}
                api={studentAPI}
                otherFilterParams={{
                    ...(props.isHostel
                        ? {
                              ...hostelStudentAPIFilter,
                              has_active_bed:
                                  props.hasActiveBed == undefined
                                      ? null
                                      : props.hasActiveBed
                                        ? 1
                                        : 0,
                          }
                        : {
                              ...props.otherFilterParams,
                          }),
                    response: "FULL",
                    includes: [
                        "currentSemesterPrimaryClass.semesterClass.classModel",
                        ...(props.otherFilterParams?.includes ?? []),
                        ...(props.isHostel
                            ? hostelStudentAPIFilter.includes
                            : []),
                    ],
                }}
                definedColumn={_columns}
                definedData={(data) => {
                    return isArray(data)
                        ? data.map((item) => ({
                              id: item?.id,
                              student_number: item?.student_number,
                              gender: capitalize(item?.gender),
                              student_name: Object.values(
                                  item?.translations?.name ?? {}
                              ),
                              class: combinedNames(
                                  item?.current_primary_class?.semester_class
                                      ?.class_model?.translations?.name
                              ),
                              block:
                                  item?.active_hostel_bed_assignment?.bed
                                      ?.hostel_room?.hostel_block?.name ?? "-",
                              room:
                                  item?.active_hostel_bed_assignment?.bed
                                      ?.hostel_room?.name ?? "-",
                              bed:
                                  item?.active_hostel_bed_assignment?.bed
                                      ?.name ?? "-",
                          }))
                        : [];
                }}
                fetchViewType={STUDENT}
                orderBy={(name, direction) => ({
                    [name]: direction,
                })}
                searchForm={(params) => (
                    <StudentSearchForm
                        currentSemester={currentSemester}
                        isMultiSelect={props.isMultiSelect}
                        {...params}
                    />
                )}
            />
        );
};

export default StudentSearchEngine;
