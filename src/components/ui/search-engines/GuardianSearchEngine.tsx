import { isArray } from "lodash";
import {
    CommonSearchEngineProps,
    guardianAPI,
    TableColumnType,
} from "@/lib/constant";
import { combinedNames, combinedNamesCell } from "@/lib/utils";
import GuardianSearchForm from "./GuardianSearchForm";
import SearchEngine from "./SearchEngine";

const GuardianSearchEngine = (props: CommonSearchEngineProps) => {
    const _columns: TableColumnType[] = [
        {
            key: "name",
            modify(value) {
                return combinedNamesCell(value);
            },
        },
        {
            key: "email",
            hasSort: true,
        },
        {
            key: "phone_number",
            hasSort: true,
        },
        {
            key: "nric",
            hasSort: true,
        },
        {
            key: "race",
            hasSort: false,
        },
    ];

    return (
        <SearchEngine
            {...props}
            name={"Guardian"}
            api={guardianAPI}
            definedColumn={_columns}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: Object.values(item?.translations?.name ?? {}),
                          email: item?.email ?? "-",
                          phone_number: item?.phone_number ?? "-",
                          race: item?.race?.name ?? "-",
                          nric: item?.nric ?? "-",
                      }))
                    : [];
            }}
            viewDetails={(data: any) => ({
                name: combinedNames(data?.translations?.name),
                email: data?.email ?? "-",
                phone_number: data?.phone_number ?? "-",
                nric: data?.nric ?? "-",
                passport_number: data?.passport_number ?? "-",
                married_status: data?.married_status ?? "-",
                nationality: data?.nationality?.name ?? "-",
                race: data?.race?.name ?? "-",
                religion: data?.religion?.name ?? "-",
                education: data?.education?.name ?? "-",
                occupation: data?.occupation ?? "-",
                occupation_description: data?.occupation_description ?? "-",
            })}
            orderBy={(name, direction) => ({
                [name]: direction,
            })}
            searchForm={(params) => <GuardianSearchForm {...params} />}
        />
    );
};

export default GuardianSearchEngine;
