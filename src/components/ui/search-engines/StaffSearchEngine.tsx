import { capitalize, isArray } from "lodash";
import {
    CommonSearchEngineProps,
    EMPLOYEE,
    employeeAPI,
    WORKING,
} from "@/lib/constant";
import { combinedNamesCell } from "@/lib/utils";
import SearchEngine from "./SearchEngine";
import StaffSearchForm from "./StaffSearchForm";
import { useTranslations } from "next-intl";

const StaffSearchEngine = (
    props: CommonSearchEngineProps & { api?: string }
) => {
    const t = useTranslations("common");
    const _columns = [
        // {
        //     key: "employee_number",
        //     displayAs: "Employee No",
        //     hasSort: true,
        // },
        {
            key: "employee_name",
            modify(value, cell) {
                return combinedNamesCell(value);
            },
        },
        ...(props.isHostel
            ? [
                  {
                      key: "gender",
                      hasSort: false,
                  },
                  {
                      key: "block",
                      hasSort: false,
                  },
                  {
                      key: "room",
                      hasSort: false,
                  },
                  {
                      key: "bed",
                      hasSort: false,
                  },
              ]
            : [
                  {
                      key: "job_title",
                      modify: (value) => (
                          <div className="min-w-[110px]">{t(value)}</div>
                      ),
                  },
                  //   {
                  //       key: "nric",
                  //       displayAs: "NRIC",
                  //       hasSort: true,
                  //   },
                  //   {
                  //       key: "badge_no",
                  //       hasSort: true,
                  //   },
                  {
                      key: "status",
                      hasSort: true,
                      modify: (value) => (
                          <div className="min-w-[110px]">{t(value)}</div>
                      ),
                  },
              ]),
    ];

    return (
        <SearchEngine
            {...props}
            name={"Employee"}
            api={props.api ?? employeeAPI}
            otherFilterParams={{
                status: WORKING,
                ...(props.isHostel
                    ? {
                          is_hostel: 1,
                          has_active_bed:
                              props.hasActiveBed == undefined
                                  ? null
                                  : props.hasActiveBed
                                    ? 1
                                    : 0,
                          includes: [
                              "activeHostelBedAssignments.bed.hostelRoom.hostelBlock",
                          ],
                      }
                    : props.otherFilterParams),
            }}
            definedColumn={_columns}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          employee_number: item?.employee_number,
                          job_title: item?.job_title?.name || "-",
                          nric: item?.nric ?? "-",
                          badge_no: item?.badge_no ?? "-",
                          status: item?.status ?? "-",
                          gender: capitalize(item?.gender),
                          employee_name: Object.values(
                              item?.translations?.name ?? {}
                          ),
                          block:
                              item?.active_hostel_bed_assignment?.bed
                                  ?.hostel_room?.hostel_block?.name ?? "-",
                          room:
                              item?.active_hostel_bed_assignment?.bed
                                  ?.hostel_room?.name ?? "-",
                          bed:
                              item?.active_hostel_bed_assignment?.bed?.name ??
                              "-",
                      }))
                    : [];
            }}
            fetchViewType={EMPLOYEE}
            orderBy={(name, direction) => ({
                [name]: direction,
            })}
            searchForm={(params) => (
                <StaffSearchForm
                    otherFilterParams={props.otherFilterParams}
                    isMultiSelect={props.isMultiSelect}
                    {...params}
                />
            )}
        />
    );
};

export default StaffSearchEngine;
