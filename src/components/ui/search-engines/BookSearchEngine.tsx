import { capitalize, isArray } from "lodash";
import {
    CommonSearchEngineProps,
    libraryBooksAPI,
    TableColumnType,
} from "@/lib/constant";
import BookSearchForm from "./BookSearchForm";
import SearchEngine from "./SearchEngine";

const BookSearchEngine = (props: CommonSearchEngineProps) => {
    const _columns: TableColumnType[] = [
        {
            key: "isbn",
            displayAs: "ISBN",
            hasSort: true,
            modify(value, cell) {
                return <span className="text-[14px]">{value}</span>;
            },
        },
        {
            key: "book_no",
            displayAs: "Barcode Number (条码)",
            hasSort: true,
        },
        {
            key: "call_no",
            displayAs: "Book Number (书号)",
            hasSort: true,
        },
        {
            key: "title",
            hasSort: true,
        },
        {
            key: "authors",
            modify(value, cell) {
                return <span className="text-[14px]">{value}</span>;
            },
        },
        {
            key: "book_sub_classification",
        },
        {
            key: "status",
        },
        {
            key: "due_date",
            modify(value, cell) {
                return (
                    <div className="whitespace-nowrap px-1 text-[14px]">
                        {value}
                    </div>
                );
            },
        },
    ];

    return (
        <SearchEngine
            {...props}
            name={"Book"}
            api={libraryBooksAPI}
            otherFilterParams={props.otherFilterParams}
            definedColumn={_columns}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          isbn: item?.isbn ?? "-",
                          book_no: item?.book_no,
                          call_no: item?.call_no,
                          title: item?.title,
                          authors: item?.authors
                              ?.map((author) => author?.name)
                              .join(", "),
                          book_sub_classification:
                              item?.book_sub_classification?.name ?? "-",
                          status: capitalize(item?.status),
                          due_date: item?.active_book_loan?.due_date ?? "-",
                      }))
                    : [];
            }}
            orderBy={(name, direction) => {
                return { [name]: direction };
            }}
            searchForm={(params) => <BookSearchForm {...params} />}
        />
    );
};

export default BookSearchEngine;
