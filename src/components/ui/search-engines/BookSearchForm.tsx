import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { axiosInstance } from "@/lib/api";
import {
    authorAPI,
    bookSubClassficationAPI,
    GET_ALL_PARAMS,
    SearchEngineFormProps,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import FormInput from "../FormInput";
import FormSelect from "../FormSelect";
import FormSelectAsync from "../FormSelectAsync";

const BookSearchForm = ({ onSubmit, onReset }: SearchEngineFormProps) => {
    const locale = useLocale();
    const [authorOptions, setAuthorOptions] = useState<any[]>([]);

    const bookForm = useForm<any>({
        defaultValues: {
            isbn: "",
            book_no: "",
            call_no: "",
            title: "",
            author_ids: [],
            book_sub_classification_id: "",
        },
    });

    const {
        data: bookSubclassificationOptions,
        axiosQuery: getBookSubclassificationOptions,
    } = useAxios({
        api: bookSubClassficationAPI,
        locale,
    });

    const { handleError } = useAxios({ api: authorAPI });

    const loadAuthorOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(authorAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: item.name,
                        value: item.id,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleError(error);
                });
        } else {
            callback([]);
        }
    };

    const fetchAuthorsByIds = async (ids) => {
        if (ids.length > 0) {
            try {
                const fetchedAuthors = await Promise.all(
                    ids.map(async (id) => {
                        const response = await axiosInstance.get(
                            `${authorAPI}/${id}`
                        );
                        return {
                            label: response.data.data.name,
                            value: response.data.data.id,
                        };
                    })
                );
                setAuthorOptions(fetchedAuthors);
            } catch (error) {
                handleError(error);
            }
        }
    };

    const handleAuthorChange = async (selectedAuthors) => {
        if (selectedAuthors && selectedAuthors.length > 0) {
            fetchAuthorsByIds(selectedAuthors);
        } else {
            setAuthorOptions([]);
            bookForm.setValue("author_ids", []);
        }
    };

    const { initLoader } = useSubmit();

    function submit() {
        initLoader(
            bookForm.handleSubmit((data) => {
                data.is_active = data.is_active ? 1 : 0;
                onSubmit(data);
            })
        );
    }

    useEffect(() => {
        getBookSubclassificationOptions({
            params: { ...GET_ALL_PARAMS },
        });
    }, []);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    return (
        <Form {...bookForm}>
            <form className="grid grid-cols-2 gap-x-3 gap-y-4 lg:min-w-[800px] lg:grid-cols-3">
                <FormInput control={bookForm.control} name={"title"} />

                <FormInput
                    control={bookForm.control}
                    name={"book_no"}
                    label="Barcode Number (条码)"
                />

                <FormInput
                    control={bookForm.control}
                    name={"call_no"}
                    label="Book Number (书号)"
                />

                <FormSelectAsync
                    control={bookForm.control}
                    name="author_ids"
                    label="Authors"
                    isMulti={true}
                    loadOptions={loadAuthorOptions}
                    value={authorOptions}
                    onChange={handleAuthorChange}
                />

                <FormSelect
                    control={bookForm.control}
                    name={"book_sub_classification_id"}
                    label="book subclassification"
                    options={bookSubclassificationOptions}
                />

                <FormInput
                    control={bookForm.control}
                    name={"isbn"}
                    label={"ISBN"}
                />

                <div className="col-span-2 -mt-2 flex items-center justify-end gap-x-5 lg:col-span-3">
                    <div
                        className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                        onClick={() => {
                            onReset();
                            bookForm.reset();
                        }}
                    >
                        Reset
                    </div>
                    <Button onClick={submit} variant={"outline"}>
                        Search
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default BookSearchForm;
