import { useLocale, useTranslations } from "next-intl";
import { ReactNode, useEffect, useState } from "react";
import clsx from "clsx";
import { Eye } from "lucide-react";
import { Button } from "@/components/base-ui/button";
import {
    EMPLOYEE,
    employeeAPI,
    FULL,
    SearchEngineFormProps,
    STUDENT,
    studentAPI,
    studentProfileParams,
    TableColumnType,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { getTableSelection } from "@/lib/utils";
import DataTable from "../DataTable";
import InfoCard from "../InfoCard";
import Modal from "../Modal";
import ProfileCard from "../ProfileCard";

const _filterParams = {
    page: 1,
    per_page: 10,
    is_active: 1,
    response: "SIMPLE",
};

type SearchEngineProps = {
    isMultiSelect?: boolean;
    hasButtons?: boolean;

    name: string;
    api: string;
    otherFilterParams?: Record<string, any>;

    definedColumn: TableColumnType[];
    definedData: (data: unknown) => any[];
    viewDetails?: (data: unknown) => any;
    fetchViewType?: typeof STUDENT | typeof EMPLOYEE;

    selection?: any;
    setSelection: (val: any) => void;

    searchForm: (formParams: SearchEngineFormProps) => ReactNode;

    orderBy: (name: string, direction: string) => Record<string, any>;
    reset?: () => void;
    close?: () => void;
    onConfirm?: () => void;

    styleClass?: string;
    isInitFetch?: boolean;

    extraDescription?: string;
};

const SearchEngine = ({
    isMultiSelect = false,
    hasButtons = true,

    name,
    api,
    otherFilterParams = {},

    definedColumn,
    definedData,
    viewDetails,
    fetchViewType,

    selection,
    setSelection,

    searchForm,

    orderBy,
    reset,
    close,
    onConfirm,

    styleClass = "",
    isInitFetch = true,

    extraDescription,
}: SearchEngineProps) => {
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>({
        ..._filterParams,
        ...otherFilterParams,
    });
    const [pagination, setPagination] = useState<Record<string, any>>();
    const [result, setResult] = useState<Record<string, any> | null>(null);
    const [navigatedResults, setNavigatedResults] = useState<any[]>(
        selection ?? []
    );
    const [isInit, setIsInit] = useState<boolean>(isInitFetch);
    const [localSelection, setLocalSelection] = useState<any>(
        isMultiSelect ? [] : null
    );
    const [selectedID, setSelectedID] = useState<string | number | null>(null);
    const [clearCount, setClearCount] = useState(0);
    const [defaultSelection, setDefaultSelection] = useState<any>(
        selection ?? (isMultiSelect ? [] : null)
    );
    const [isSelectAll, setIsSelectAll] = useState(false);

    const locale = useLocale();
    const t = useTranslations("common");
    const { axiosQuery } = useAxios({
        api,
        locale,
        onSuccess: (result) => {
            setResult(result.data);
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
            const _columns = definedColumn;
            if (
                (viewDetails || fetchViewType) &&
                _columns.map((col) => col.key).indexOf("_") === -1
            ) {
                _columns.push({
                    key: "_",
                    modify(value, cell) {
                        return (
                            <Eye
                                size={16}
                                className="mx-auto cursor-pointer text-gray-500"
                                onClick={() =>
                                    setSelectedID(cell.row.original.id)
                                }
                            />
                        );
                    },
                });
            }
            setColumns(_columns);
        },
    });

    const { axiosQuery: getAll } = useAxios({
        api,
        locale,
        onSuccess: (result) => {
            setDefaultSelection(result.data);
        },
    });

    useEffect(() => {
        if (isInit) {
            axiosQuery({ params: filter });
        }
    }, [filter]);

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: orderBy(name, direction),
        });
    }

    function onSelect(id) {
        if (!id) {
            setSelection(isMultiSelect ? [] : null);
            setLocalSelection(isMultiSelect ? [] : null);
            return;
        }
        const _selection = result?.find((result) => result.id == id);
        setLocalSelection(_selection);
        if (!hasButtons) {
            setSelection(_selection);
        }
    }

    function onMultiSelect(selectedIds: any[]) {
        const _selection = getTableSelection(selectedIds, [
            ...navigatedResults,
            ...defaultSelection,
        ]);
        console.log("onMultiSelect_selection", _selection);

        setLocalSelection(_selection);
        if (!hasButtons) {
            setSelection(_selection);
        }
    }

    function onReset() {
        // setClearCount(clearCount + 1);
        if (reset) reset();
        setIsSelectAll(false);
        if (isInitFetch) {
            setFilter({
                ..._filterParams,
                ...otherFilterParams,
            });
        } else {
            setResult(null);
        }
    }

    function onSubmit(data) {
        console.log(data);
        setIsInit(true);
        setIsSelectAll(false);
        setFilter({ ...filter, ...data, page: 1 });
    }

    useEffect(() => {
        if (isSelectAll) {
            getAll({
                params: {
                    ...filter,
                    per_page: -1,
                },
            });
        } else {
            setDefaultSelection([]);
        }
    }, [isSelectAll]);

    const { data: studentProfile, axiosQuery: getStudentData } = useAxios({
        api: studentAPI,
        locale,
    });

    const { data: employeeProfile, axiosQuery: getEmployeeData } = useAxios({
        api: employeeAPI,
        locale,
    });

    useEffect(() => {
        if (selectedID && fetchViewType) {
            if (fetchViewType === STUDENT) {
                getStudentData({
                    id: selectedID,
                    params: studentProfileParams,
                });
            }
            if (fetchViewType === EMPLOYEE) {
                getEmployeeData({ id: selectedID });
            }
        }
    }, [selectedID, fetchViewType]);

    return (
        <>
            <div
                className={clsx(
                    "min-h-[300px] w-[86vw] max-w-[2600px]",
                    styleClass
                )}
            >
                <h3 className="mb-3 font-bold text-themeGreenDark">
                    {t(name)}
                    {t("Search ")}
                    {t("Engine")}
                </h3>

                {extraDescription && (
                    <p className="-mt-1 mb-4 font-medium text-gray-500">
                        {extraDescription}
                    </p>
                )}

                {searchForm({
                    onSubmit,
                    onReset: onReset,
                    otherFilterParams,
                    isSelectAll: isSelectAll,
                    setIsSelectAll: setIsSelectAll,
                })}

                {result && (
                    <>
                        <div className="mt-3 flex gap-x-3 lg:mt-0">
                            <h3 className="font-bold text-themeGreenDark">
                                {t("Search ")}
                                {t("Results")}
                            </h3>
                            <div className="mb-3 mt-1 text-xs text-gray-500">
                                <span className="font-semibold">
                                    {t("page_total", {
                                        number: pagination?.total,
                                    })}
                                </span>
                            </div>
                        </div>
                        <DataTable
                            columns={columns}
                            data={definedData(result)}
                            pagination={pagination}
                            setPagination={setPagination}
                            changePage={(arg) =>
                                setFilter({ ...filter, ...arg })
                            }
                            sorted={filter?.order_by}
                            sort={onSort}
                            hasPerPage={false}
                            onSelect={!isMultiSelect ? onSelect : undefined}
                            onMultiSelect={
                                isMultiSelect ? onMultiSelect : undefined
                            }
                            clearCount={clearCount}
                            selectedRows={defaultSelection}
                        />
                    </>
                )}

                {selectedID && viewDetails && (
                    <Modal open={selectedID} onOpenChange={setSelectedID}>
                        <InfoCard
                            title={`${name} Details`}
                            data={viewDetails(
                                result?.find((item) => item?.id == selectedID)
                            )}
                            noBorder={true}
                        />
                    </Modal>
                )}
                {selectedID && fetchViewType === STUDENT && (
                    <Modal
                        open={selectedID}
                        onOpenChange={setSelectedID}
                        size="large"
                    >
                        <ProfileCard
                            title="Student Profile"
                            data={studentProfile}
                            type={STUDENT}
                        />
                    </Modal>
                )}
                {selectedID && fetchViewType === EMPLOYEE && (
                    <Modal
                        open={selectedID}
                        onOpenChange={setSelectedID}
                        size="medium"
                    >
                        <ProfileCard
                            title="Employee Profile"
                            data={employeeProfile}
                            type={EMPLOYEE}
                        />
                    </Modal>
                )}
            </div>
            {hasButtons && (
                <div className="sticky -bottom-5 left-0 right-0 flex w-full justify-end">
                    <div className="-mr-3 flex w-fit justify-end gap-x-4 rounded-xl border bg-white p-2.5 shadow-lg">
                        <Button
                            disabled={
                                isMultiSelect
                                    ? localSelection?.length === 0
                                    : localSelection === null
                            }
                            className="w-[120px]"
                            onClick={() => {
                                console.log("localSelection", localSelection);
                                setSelection(localSelection);
                                if (onConfirm) {
                                    onConfirm();
                                }
                                if (close) close();
                            }}
                        >
                            {t("Confirm")}
                        </Button>
                    </div>
                </div>
            )}
        </>
    );
};

export default SearchEngine;
