import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { ACTIVE, INACTIVE, SearchEngineFormProps } from "@/lib/constant";
import FormInput from "../FormInput";
import FormInputInterger from "../FormInputInterger";
import FormPhoneInput from "../FormPhoneInput";
import FormSelect from "../FormSelect";
import { useEffect } from "react";
import { useTranslations } from "next-intl";

const TrainerSearchForm = ({ onSubmit, onReset }: SearchEngineFormProps) => {
    const t = useTranslations("common");
    const staffForm = useForm({
        defaultValues: {
            name: "",
            email: "",
            phone_number: "",
            nric: "",
            passport_number: "",
            status: "",
        },
    });

    function submit() {
        staffForm.handleSubmit((data) => {
            Object.keys(data).forEach((key) => {
                if (!data[key]) {
                    data[key] = undefined;
                }
            });
            onSubmit(data);
        })();
    }

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    return (
        <Form {...staffForm}>
            <form
                onSubmit={submit}
                className="grid grid-cols-2 gap-x-3 gap-y-4 lg:min-w-[800px] lg:grid-cols-3"
            >
                <FormInput control={staffForm.control} name={"name"} />

                <FormInput
                    control={staffForm.control}
                    name={"email"}
                    type="email"
                />

                <FormPhoneInput form={staffForm} name="phone_number" />

                <FormInputInterger
                    control={staffForm.control}
                    name="nric"
                    label="NRIC"
                />

                <FormInput
                    control={staffForm.control}
                    name={"passport_number"}
                />

                <FormSelect
                    control={staffForm.control}
                    name="status"
                    options={[
                        {
                            name: t("Active"),
                            id: ACTIVE,
                        },
                        {
                            name: t("Inactive"),
                            id: INACTIVE,
                        },
                    ]}
                />

                <div className="col-span-2 -mt-2 flex items-center justify-end gap-x-5 lg:col-span-3">
                    <div
                        className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                        onClick={() => {
                            onReset();
                            staffForm.reset();
                        }}
                    >
                        {t("Reset")}
                    </div>
                    <Button variant={"outline"} onClick={submit}>
                        {t("Search")}
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default TrainerSearchForm;
