import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import { Switch } from "@/components/base-ui/switch";
import { axiosInstance } from "@/lib/api";
import {
    classStreamOptions,
    genderOptions,
    GET_ALL_PARAMS,
    gradeAPI,
    SearchEngineFormProps,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    configForGetAll,
    getNumBooleanValue,
    groupedSemesterClassOptions,
    NO,
    YES,
} from "@/lib/utils";
import FormCheckbox from "../FormCheckbox";
import FormInput from "../FormInput";
import FormSelect from "../FormSelect";
import { Skeleton } from "@/components/base-ui/skeleton";
import FormGroupedSelect from "../FormGroupedSelect";
import { capitalize } from "lodash";

const StudentSearchForm = ({
    onSubmit,
    onReset,
    otherFilterParams,
    isMultiSelect,
    currentSemester,
    isSelectAll,
    setIsSelectAll,
}: SearchEngineFormProps & {
    isMultiSelect?: boolean;
    currentSemester?: string | null;
}) => {
    const locale = useLocale();
    const t = useTranslations("common");
    const studentForm = useForm<any>({
        defaultValues: {
            name: "",
            student_number_wildcard: "",
            grade_ids: [],
            semester_setting_id:
                otherFilterParams?.semester_setting_id ?? currentSemester ?? "",
            semester_class_ids: otherFilterParams?.semester_class_ids ?? [],
            admission_type: otherFilterParams?.admission_type ?? "",
            class_stream: otherFilterParams?.class_stream ?? "",
            is_hostel: getNumBooleanValue(otherFilterParams?.is_hostel),
            is_active: true,
            gender: otherFilterParams?.gender,
        },
    });

    const [options, setOptions] = useState<Record<string, any> | null>(null);
    const [loadingClass, setLoadingClass] = useState(false);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.grades = res[0].data.data;
            _options.semesters = res[1].data.data;
            setOptions(_options);
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
            noLoading: true,
            onSuccess: () => setLoadingClass(false),
            onError: () => setLoadingClass(false),
        });

    useEffect(() => {
        if (studentForm.watch("semester_setting_id")) {
            setLoadingClass(true);
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: studentForm.watch(
                        "semester_setting_id"
                    ),
                },
            });
        }
    }, [studentForm.watch("semester_setting_id")]);

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(gradeAPI, _config),
            axiosInstance.get(semesterSettingAPI, _config),
        ]);
    }, []);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    function submit() {
        studentForm.handleSubmit((data) => {
            data.is_active = data.is_active ? 1 : 0;
            data.is_hostel =
                data.is_hostel === YES
                    ? 1
                    : data.is_hostel == NO
                      ? 0
                      : undefined;
            onSubmit(data);
        })();
    }

    return (
        <>
            <Form {...studentForm}>
                <form className="grid grid-cols-2 gap-2 md:grid-cols-3">
                    <FormInput control={studentForm.control} name={"name"} />
                    <FormInput
                        control={studentForm.control}
                        name={"student_number_wildcard"}
                        label="student number"
                    />
                    <FormSelect
                        isMulti={true}
                        control={studentForm.control}
                        name={"grade_ids"}
                        label={"grades"}
                        options={options?.grades}
                    />

                    <FormSelect
                        control={studentForm.control}
                        name={"semester_setting_id"}
                        label={"semester"}
                        options={options?.semesters}
                        onChange={() => {
                            studentForm.setValue("semester_class_ids", "");
                        }}
                    />

                    <div className="relative">
                        <FormGroupedSelect
                            isMulti={true}
                            control={studentForm.control}
                            name={"semester_class_ids"}
                            label={"classes"}
                            isDisabled={
                                !studentForm.watch("semester_setting_id")
                            }
                            options={groupedSemesterClassOptions(
                                semesterClassOptions
                            )}
                        />

                        {loadingClass && (
                            <div className="absolute left-0 top-0 flex h-full w-full justify-center bg-white bg-opacity-50">
                                <Skeleton className="mt-auto h-[42px] w-full border border-transparent" />
                            </div>
                        )}
                    </div>

                    <FormSelect
                        control={studentForm.control}
                        name="admission_type"
                        isSortByName={false}
                        options={["NEW", "TRANSFERRED"].map((type) => ({
                            id: type,
                            name: t(type === "NEW" ? "new_student" : type),
                        }))}
                    />

                    <FormSelect
                        control={studentForm.control}
                        name="class_stream"
                        options={classStreamOptions.map((option) => ({
                            id: option?.id,
                            name: t(option?.name),
                        }))}
                    />

                    <FormSelect
                        control={studentForm.control}
                        name="gender"
                        isSortByName={false}
                        options={genderOptions.map((gender) => ({
                            id: gender.id,
                            name: t(gender.name),
                        }))}
                    />

                    <FormSelect
                        control={studentForm.control}
                        name="is_hostel"
                        options={[
                            {
                                name: t("Yes"),
                                id: YES,
                            },
                            {
                                name: t("No"),
                                id: NO,
                            },
                        ]}
                    />

                    <FormCheckbox
                        control={studentForm.control}
                        name={"is_active"}
                        label={"Active"}
                        styleClass="ml-1 mt-2"
                    />
                </form>
            </Form>
            <div className="mb-3 mt-1.5 flex items-center justify-between gap-x-5">
                {isMultiSelect && (
                    <div className="mr-auto flex items-center gap-x-2">
                        <Switch
                            checked={isSelectAll}
                            onCheckedChange={(checked: boolean) => {
                                setIsSelectAll(checked);
                            }}
                        />
                        <Label className="c-text-size font-medium text-gray-500">
                            {t("Select All")}
                        </Label>
                    </div>
                )}
                <div
                    className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                    onClick={() => {
                        onReset();
                        studentForm.reset();
                    }}
                >
                    {t("Reset")}
                </div>
                <Button onClick={submit} variant={"outline"}>
                    {t("Search")}
                </Button>
            </div>
        </>
    );
};

export default StudentSearchForm;
