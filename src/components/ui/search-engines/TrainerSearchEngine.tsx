import { isArray } from "lodash";
import {
    COCURRICULUM,
    CommonSearchEngineProps,
    trainerAPI,
} from "@/lib/constant";
import { combinedNamesCell } from "@/lib/utils";
import SearchEngine from "./SearchEngine";
import TrainerSearchForm from "./TrainerSearchForm";
import { useTranslations } from "next-intl";

const TrainerSearchEngine = (props: CommonSearchEngineProps) => {
    const t = useTranslations("common");
    const _columns = [
        {
            key: "name",
            modify(value, cell) {
                return combinedNamesCell(value);
            },
            hasSort: true,
        },
        {
            key: "email",
            hasSort: true,
        },
        {
            key: "phone_number",
            hasSort: true,
        },
        {
            key: "nric",
            displayAs: "NRIC",
            hasSort: true,
        },
        {
            key: "status",
            hasSort: true,
            modify: (value) => t(value),
        },
    ];

    return (
        <SearchEngine
            {...props}
            name={"Coach"}
            api={trainerAP<PERSON>}
            otherFilterParams={{
                includes: ["user"],
                department: COCURRICULUM,
            }}
            definedColumn={_columns}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: Object.values(item?.translations?.name ?? {}),
                          email: item?.email ?? "-",
                          phone_number: item?.phone_number ?? "-",
                          nric: item?.nric ?? "-",
                          passport_number: item?.passport_number ?? "-",
                          status: item?.status ?? "-",
                          contractor_number: item?.contractor_number,
                      }))
                    : [];
            }}
            orderBy={(name, direction) => ({
                [name]: direction,
            })}
            searchForm={(params) => <TrainerSearchForm {...params} />}
        />
    );
};

export default TrainerSearchEngine;
