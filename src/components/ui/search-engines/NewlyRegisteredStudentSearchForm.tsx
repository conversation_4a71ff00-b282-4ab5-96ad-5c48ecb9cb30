import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import {
    APPROVED,
    GET_ALL_PARAMS,
    PAID,
    PENDING,
    REJECTED,
    SHORTLISTED,
    SearchEngineFormProps,
    UNPAID,
    genderOptions,
    gradeAPI,
} from "@/lib/constant";
import FormInput from "../FormInput";
import FormInputInterger from "../FormInputInterger";
import FormSelect from "../FormSelect";
import { useLocale, useTranslations } from "next-intl";
import { useAxios } from "@/lib/hook";
import { useEffect } from "react";
import { capitalize } from "lodash";

const NewlyRegisteredStudentSearchForm = ({
    onSubmit,
    onReset,
}: SearchEngineFormProps) => {
    const locale = useLocale();

    const newStudentForm = useForm({
        defaultValues: {
            name: "",
            admission_grade_id: "",
            nric: "",
            passport_number: "",
            enrollment_status: "",
            gender: "",
            payment_status: "",
            is_hostel: "",
            is_foreigner: "",
        },
    });

    const { data: gradeOptions, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onError: close,
    });

    function submit() {
        newStudentForm.handleSubmit((data) => {
            Object.keys(data).forEach((key) => {
                if (!data[key]) {
                    data[key] = undefined;
                }
            });
            onSubmit(data);
        })();
    }

    useEffect(() => {
        getGrades({ params: GET_ALL_PARAMS });
    }, []);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    const t = useTranslations("common");

    return (
        <Form {...newStudentForm}>
            <form
                onSubmit={submit}
                className="grid grid-cols-2 gap-x-3 gap-y-4 lg:min-w-[800px] lg:grid-cols-3"
            >
                <FormInput control={newStudentForm.control} name={"name"} />

                <FormInputInterger
                    control={newStudentForm.control}
                    name="nric"
                    label={"NRIC"}
                    max={12}
                />

                <FormInput
                    control={newStudentForm.control}
                    name="passport_number"
                />

                <FormSelect
                    control={newStudentForm.control}
                    name="admission_grade_id"
                    label="admission grade"
                    options={gradeOptions}
                />

                <FormSelect
                    control={newStudentForm.control}
                    name="enrollment_status"
                    isStringOptions={true}
                    options={[APPROVED, SHORTLISTED, REJECTED]}
                />

                <FormSelect
                    control={newStudentForm.control}
                    name="payment_status"
                    isStringOptions={true}
                    options={[PAID, UNPAID, PENDING]}
                />

                <FormSelect
                    control={newStudentForm.control}
                    name="gender"
                    isSortByName={false}
                    options={genderOptions.map((option) => ({
                        id: option.id,
                        name: capitalize(t(option.name)),
                    }))}
                />

                <FormSelect
                    control={newStudentForm.control}
                    name="is_hostel"
                    isSortByName={false}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <FormSelect
                    control={newStudentForm.control}
                    name="is_foreigner"
                    isSortByName={false}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <div className="col-span-2 flex items-center justify-end gap-x-5 lg:col-span-3">
                    <div
                        className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                        onClick={() => {
                            onReset();
                            newStudentForm.reset();
                        }}
                    >
                        {t("Reset")}
                    </div>
                    <Button variant={"outline"} onClick={submit}>
                        {t("Search")}
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default NewlyRegisteredStudentSearchForm;
