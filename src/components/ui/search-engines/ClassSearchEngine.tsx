import { useEffect, useState } from "react";
import {
    classesAPI,
    CommonSearchEngineProps,
    TableColumnType,
} from "@/lib/constant";
import { useLanguages } from "@/lib/store";
import ClassSearchForm from "./ClassSearchForm";
import SearchEngine from "./SearchEngine";

const ClassSearchEngine = (props: CommonSearchEngineProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    function getDefinedColumn() {
        const _columns: TableColumnType[] = [
            ...activeLanguages!.map(
                (lang): TableColumnType => ({
                    key: lang.code,
                    displayAs: `Class ( ${lang?.name} )`,
                    hasSort: true,
                })
            ),
            { key: "code", hasSort: true },
            { key: "type", hasSort: true },
            { key: "grade" },
        ];
        setColumns(_columns);
    }

    useEffect(() => {
        if (activeLanguages) {
            getDefinedColumn();
        }
    }, [activeLanguages]);

    return columns.length > 0 ? (
        <SearchEngine
            {...props}
            name={"Class"}
            api={classesAPI}
            otherFilterParams={props.otherFilterParams}
            definedColumn={columns}
            definedData={(data) => {
                return Array.isArray(data)
                    ? data.map((item) => {
                          return {
                              id: item?.id,
                              ...item?.translations?.name,
                              code: item?.code,
                              type: item?.type,
                              grade: item?.grade?.name,
                          };
                      })
                    : [];
            }}
            orderBy={(name, direction) => {
                return name === "hostel_room"
                    ? { name: { [name]: direction } }
                    : { [name]: direction };
            }}
            searchForm={(params) => <ClassSearchForm {...params} />}
        />
    ) : (
        <></>
    );
};

export default ClassSearchEngine;
