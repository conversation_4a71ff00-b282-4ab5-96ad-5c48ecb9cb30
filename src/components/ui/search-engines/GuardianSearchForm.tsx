import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { SearchEngineFormProps } from "@/lib/constant";
import FormInput from "../FormInput";
import { useEffect } from "react";

const GuardianSearchForm = ({ onSubmit, onReset }: SearchEngineFormProps) => {
    const guardianForm = useForm<any>({
        defaultValues: {
            name: "",
            email: "",
            phone_number: "",
            nric: "",
        },
    });

    function submit() {
        guardianForm.handleSubmit((data) => {
            onSubmit(data);
        })();
    }

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    return (
        <Form {...guardianForm}>
            <form className="grid grid-cols-2 gap-x-3 gap-y-4 lg:min-w-[800px] lg:grid-cols-3">
                <FormInput control={guardianForm.control} name={"name"} />

                <FormInput
                    control={guardianForm.control}
                    name="nric"
                    label="NRIC"
                />
                <FormInput control={guardianForm.control} name="email" />
                <FormInput control={guardianForm.control} name="phone_number" />

                <div className="col-span-2 -mt-2 flex items-center justify-end gap-x-5 lg:col-span-3">
                    <div
                        className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                        onClick={() => {
                            onReset();
                            guardianForm.reset();
                        }}
                    >
                        Reset
                    </div>
                    <Button onClick={submit} variant={"outline"}>
                        Search
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default GuardianSearchForm;
