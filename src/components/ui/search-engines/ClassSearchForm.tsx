import { useLocale } from "next-intl";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import {
    CLUB,
    ENGLISH,
    GET_ALL_PARAMS,
    gradeAPI,
    PRIMARY,
    SearchEngineFormProps,
    SOCIETY,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import FormCheckbox from "../FormCheckbox";
import FormInput from "../FormInput";
import FormSelect from "../FormSelect";

const ClassSearchForm = ({
    onSubmit,
    onReset,
    otherFilterParams,
}: SearchEngineFormProps) => {
    const locale = useLocale();

    const classForm = useForm<any>({
        defaultValues: {
            name: "",
            code: "",
            type: otherFilterParams?.type ?? "",
            grade_id: "",
            is_active: true,
        },
    });

    const { data: gradeOptions, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
    });

    useEffect(() => {
        getGrades({ params: GET_ALL_PARAMS });
    }, []);

    const { initLoader } = useSubmit();

    function submit() {
        initLoader(
            classForm.handleSubmit((data) => {
                data.is_active = data.is_active ? 1 : 0;
                onSubmit(data);
            })
        );
    }

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    return (
        <Form {...classForm}>
            <form className="grid grid-cols-2 gap-x-3 gap-y-4 lg:min-w-[800px] lg:grid-cols-3">
                <FormInput control={classForm.control} name={"name"} />
                <FormInput control={classForm.control} name={"code"} />

                {!otherFilterParams?.type && (
                    <FormSelect
                        control={classForm.control}
                        isSortByName={false}
                        name={"type"}
                        options={[PRIMARY, ENGLISH, SOCIETY]}
                    />
                )}

                <FormSelect
                    control={classForm.control}
                    name={"grade_id"}
                    label="grade"
                    options={gradeOptions}
                />

                <FormCheckbox
                    control={classForm.control}
                    name={"is_active"}
                    label={"Active"}
                    styleClass="ml-1"
                />

                <div className="col-span-2 -mt-2 flex items-center justify-end gap-x-5 lg:col-span-3">
                    <div
                        className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                        onClick={() => {
                            onReset();
                            classForm.reset();
                        }}
                    >
                        Reset
                    </div>
                    <Button onClick={submit} variant={"outline"}>
                        Search
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default ClassSearchForm;
