import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import {
    GET_ALL_PARAMS,
    hostelBedsAPI,
    hostelBlocksAPI,
    hostelRoomsAPI,
    SearchEngineFormProps,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import FormInput from "../FormInput";
import FormSelect from "../FormSelect";

const BedSearchForm = ({
    onSubmit,
    onReset,
    hostelType,
}: SearchEngineFormProps & { hostelType?: string }) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const bedForm = useForm({
        defaultValues: {
            name: "",
            hostel_block_id: "",
            hostel_room_id: "",
            hostel_bed_id: "",
        },
    });

    const { data: hostelBlocks, axiosQuery: getHostelBlocks } = useAxios({
        api: hostelBlocksAPI,
        locale,
    });

    const { data: hostelRoomOptions, axiosQuery: getHostelRoomOptions } =
        useAxios({
            api: hostelRoomsAPI,
            locale,
        });

    const { data: hostelBedOptions, axiosQuery: getHostelBedOptions } =
        useAxios({
            api: hostelBedsAPI,
            locale,
        });

    useEffect(() => {
        bedForm.setValue("hostel_bed_id", "");

        if (bedForm.watch("hostel_room_id")) {
            getHostelBedOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    hostel_room_id: bedForm.watch("hostel_room_id"),
                    hostel_block_type: hostelType,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [bedForm.watch("hostel_room_id")]);

    useEffect(() => {
        bedForm.setValue("hostel_room_id", "");
        bedForm.setValue("hostel_bed_id", "");

        if (bedForm.watch("hostel_block_id")) {
            getHostelRoomOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    hostel_block_id: bedForm.watch("hostel_block_id"),
                    hostel_block_type: hostelType,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [bedForm.watch("hostel_block_id")]);

    useEffect(() => {
        getHostelBlocks({
            params: {
                ...GET_ALL_PARAMS,
                type: hostelType,
            },
        });
    }, []);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                submit();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    function reset() {
        onReset();
        bedForm.reset();
    }

    function submit() {
        bedForm.handleSubmit(onSubmit)();
    }

    return (
        <Form {...bedForm}>
            <form
                onSubmit={submit}
                className="grid grid-cols-2 gap-x-3 gap-y-4 lg:min-w-[800px] lg:grid-cols-3"
            >
                <FormSelect
                    control={bedForm.control}
                    name={"hostel_block_id"}
                    label="block"
                    options={hostelBlocks}
                />

                <FormSelect
                    control={bedForm.control}
                    name={"hostel_room_id"}
                    label="room"
                    isDisabled={!bedForm.watch("hostel_block_id")}
                    options={hostelRoomOptions}
                />

                <FormSelect
                    control={bedForm.control}
                    name={"hostel_bed_id"}
                    label="bed"
                    isDisabled={!bedForm.watch("hostel_room_id")}
                    options={hostelBedOptions}
                />

                <FormInput control={bedForm.control} name={"name"} />

                <div className="col-span-2 -mt-2 flex items-center justify-end gap-x-5 lg:col-span-3">
                    <div
                        className="c-text-size w-fit cursor-pointer font-medium text-gray-500 underline"
                        onClick={reset}
                    >
                        {t("Reset")}
                    </div>
                    <Button variant={"outline"} onClick={submit}>
                        {t("Search")}
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default BedSearchForm;
