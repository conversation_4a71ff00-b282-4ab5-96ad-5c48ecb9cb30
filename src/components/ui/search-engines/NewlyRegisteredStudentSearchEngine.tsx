import { capitalize, isArray } from "lodash";
import {
    CommonSearchEngineProps,
    enrollmentAPI,
    PAID,
    PENDING,
    UNPAID,
} from "@/lib/constant";
import { isValueTrue } from "@/lib/utils";
import SearchEngine from "./SearchEngine";
import NewlyRegisteredStudentSearchForm from "./NewlyRegisteredStudentSearchForm";
import clsx from "clsx";
import { useTranslations } from "next-intl";

const NewlyRegisteredStudentSearchEngine = (props: CommonSearchEngineProps) => {
    const t = useTranslations("common");

    const _columns = [
        {
            key: "name",
            hasSort: true,
        },
        {
            key: "nric",
            displayAs: "NRIC",
            hasSort: true,
        },
        {
            key: "passport_number",
            hasSort: true,
        },
        {
            key: "enrollment_status",
            hasSort: true,
        },
        {
            key: "payment_status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === UNPAID
                            ? "text-red-600"
                            : value === PAID
                              ? "text-themeGreen2"
                              : value === PENDING
                                ? "text-orange-500"
                                : ""
                    )}
                >
                    {t(value)}
                </span>
            ),
        },
        {
            key: "is_hostel",
            hasSort: true,
            modify: (value) =>
                t(value === "-" ? value : isValueTrue(value) ? "Yes" : "No"),
        },
        {
            key: "is_foreigner",
            hasSort: true,
            modify: (value) =>
                t(value === "-" ? value : isValueTrue(value) ? "Yes" : "No"),
        },
    ];

    return (
        <SearchEngine
            {...props}
            name={"Newly Registered Student"}
            api={enrollmentAPI}
            otherFilterParams={{
                order_by: {
                    created_at: "desc",
                },
            }}
            definedColumn={_columns}
            definedData={(data) => {
                return isArray(data)
                    ? data.map((item) => ({
                          id: item?.id,
                          name: item?.name ?? "-",
                          nric: item?.nric ?? "-",
                          passport_number: item?.passport_number ?? "-",
                          enrollment_status: t(item?.enrollment_status ?? "-"),
                          is_hostel: item?.is_hostel ?? "-",
                          is_foreigner: item?.is_foreigner ?? "-",
                          payment_status: item?.payment_status ?? "-",
                      }))
                    : [];
            }}
            orderBy={(name, direction) => ({
                [name]: direction,
            })}
            searchForm={(params) => (
                <NewlyRegisteredStudentSearchForm {...params} />
            )}
        />
    );
};

export default NewlyRegisteredStudentSearchEngine;
