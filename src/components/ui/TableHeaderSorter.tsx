import React from "react";
import { isObject, isString } from "lodash";
import { ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react";
import { ASC, DESC } from "@/lib/constant";
import { replaceAll, strStartCase } from "@/lib/utils";
import clsx from "clsx";
import { useTranslations } from "next-intl";

type TableHeaderSorterProps = {
    head: string;
    label?: string;
    sort: (name: string, direction: string) => void;
    sorted: Record<string, any>;
};

const TableHeaderSorter = ({
    head,
    label,
    sorted,
    sort,
}: TableHeaderSorterProps) => {
    const t = useTranslations("common");

    const sortedValue = Object.values(sorted)[0];
    const order = isString(sortedValue)
        ? sortedValue
        : sortedValue
          ? Object.values(sortedValue)[0]
          : null;

    const target = isString(sortedValue)
        ? Object.keys(sorted)[0]
        : sorted.name
          ? Object.keys(sorted.name)[0]
          : sorted.question
            ? Object.keys(sorted.question)[0]
            : sorted.userable
              ? Object.keys(sorted.userable)[0]
              : isObject(sortedValue)
                ? Object.keys(sorted)[0]
                : null;

    const isTargeted = target === head;

    return (
        <div
            className={clsx(
                "thead-item flex cursor-pointer items-center px-0 font-medium",
                (label ?? head)?.length < 24 && "whitespace-nowrap"
            )}
            onClick={() =>
                sort(head, !isTargeted ? ASC : order === ASC ? DESC : ASC)
            }
        >
            <span className="max-w-[calc(100%-24px)] capitalize leading-none">
                {label
                    ? t(label.replaceAll("_", " "))
                    : t(head.replaceAll("_", " "))}
            </span>
            {!isTargeted ? (
                <ChevronsUpDown className="ml-2 h-4 w-4" />
            ) : order === ASC ? (
                <ArrowUp className="ml-2 h-4 w-4" />
            ) : order === DESC ? (
                <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
                <></>
            )}
        </div>
    );
};

export default TableHeaderSorter;
