import React from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import { Control, Controller } from "react-hook-form";
import { Input } from "@/components/base-ui/input";
import { getInputErrorMessage, replaceAll } from "@/lib/utils";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FreeInputIntergerProps = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    type?: string;
    min?: any;
    max?: any;
    isSearch?: boolean;
    hasLabel?: boolean;
    suffixIcon?: string;
    onClickIcon?: () => void;
    error?: any;
    rules?: any;
    onChange?: (val: any) => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
    onMouseDown?: (e: React.MouseEvent<HTMLInputElement>) => void;
    refs?: any;
};

const FreeInputInterger = ({
    control,
    name,
    label,
    placeholder,
    disabled = false,
    type = "text",
    min = null,
    max = null,
    isSearch,
    hasLabel = true,
    suffixIcon,
    onClickIcon,
    error,
    rules,
    onChange = (val) => {},
    onKeyDown,
    onBlur,
    onMouseDown,
    refs,
}: FreeInputIntergerProps) => {
    const t = useTranslations("common");

    return control ? (
        <Controller
            control={control}
            name={name}
            rules={rules}
            render={({ field }) => (
                <div className="relative">
                    {hasLabel && (
                        <Label className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </Label>
                    )}
                    <Input
                        placeholder={capitalize(placeholder ?? "")}
                        disabled={disabled}
                        type={type}
                        minLength={min}
                        maxLength={max}
                        suffixIcon={suffixIcon}
                        onClickIcon={onClickIcon}
                        {...field}
                        ref={(el) => {
                            field.ref(el);
                            if (refs && refs.current) {
                                refs.current[name] = el;
                            }
                        }}
                        className={clsx(isSearch && "search-input")}
                        onChange={(e) => {
                            const sanitizedValue = e.target.value.replace(
                                /[^0-9]/g,
                                ""
                            );
                            field.onChange(sanitizedValue);
                            onChange(sanitizedValue);
                        }}
                        onKeyDown={onKeyDown}
                        onBlur={onBlur}
                        onMouseDown={onMouseDown}
                    />
                    {error && (
                        <div className="warning-text">
                            {`${getInputErrorMessage(error)}`}
                        </div>
                    )}
                </div>
            )}
        />
    ) : (
        <></>
    );
};

export default FreeInputInterger;
