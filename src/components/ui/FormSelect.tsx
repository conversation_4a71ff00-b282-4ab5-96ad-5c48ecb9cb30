import React, { useEffect, useState } from "react";
import clsx from "clsx";
import { isArray, sortBy } from "lodash";
import { useFormContext } from "react-hook-form";
import Select, { components } from "react-select";
import {
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import {
    formatForSelect,
    formatStringsForSelect,
    replaceAll,
    selectStyles,
} from "@/lib/utils";
import { Checkbox } from "../base-ui/checkbox";
import { Skeleton } from "../base-ui/skeleton";
import { useTranslations } from "next-intl";

type FormSelectProps = {
    control: any;
    name: string;
    options: any;
    label?: string;
    placeholder?: string;
    isDisabled?: boolean;
    onChange?: (value: any) => void;
    isLoading?: boolean;
    isStringOptions?: boolean;
    isMulti?: boolean;
    hasLabel?: boolean;
    isClearable?: boolean;
    isSmaller?: boolean;
    isSortByName?: boolean;
    hasSelectAll?: boolean;
    noChevron?: boolean;
    displayItemSelected?: boolean;
    hideSelectedOptions?: boolean;
    maxMenuHeight?: number;
};

// Custom ValueContainer to show selected count or "All items"
const CustomValueContainer = (props: any) => {
    const { children, getValue, selectProps } = props;
    const selectedValues = getValue();
    const totalOptions = selectProps.options.length;
    const allSelected = selectedValues.length === totalOptions;

    const displayText =
        selectedValues.length === 0
            ? selectProps.placeholder
            : allSelected
              ? "All items selected"
              : `${selectedValues.length} items selected`;

    return (
        <>
            {(!selectProps.inputValue || selectedValues.length === 0) &&
                displayText && (
                    <div
                        className={clsx(
                            "pl-2",
                            selectedValues.length === 0 && "text-gray-400"
                        )}
                    >
                        {displayText}
                    </div>
                )}
            <components.ValueContainer {...props}>
                {React.Children.map(children, (child) =>
                    child?.type === components.Input ? child : null
                )}
            </components.ValueContainer>
        </>
    );
};

const FormSelect = ({
    control,
    name,
    options,
    label,
    placeholder = "",
    isDisabled,
    onChange = (value: any) => {},
    isLoading = false,
    isStringOptions = false,
    isMulti = false,
    hasLabel = true,
    isClearable = true,
    isSmaller = false,
    isSortByName = true,
    hasSelectAll = false,
    noChevron = false,
    displayItemSelected = false,
    hideSelectedOptions = true,
    maxMenuHeight = 160,
}: FormSelectProps) => {
    const t = useTranslations("common");

    const { setValue } = useFormContext();

    const selectName = t(replaceAll(label || name, "_", " "));

    const formattedOptions = isArray(options)
        ? isStringOptions
            ? formatStringsForSelect(options, t)
            : formatForSelect(options)
        : [];

    const sortedOptions = isSortByName
        ? sortBy(formattedOptions, "label")
        : formattedOptions;

    const [isSelectAll, seIsSelectAll] = useState(false);

    useEffect(() => {
        if (hasSelectAll && isSelectAll) {
            setValue(name, sortedOptions?.map((option) => option.value) ?? []);
        }
    }, [sortedOptions, isSelectAll]);

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hasLabel && (
                        <FormLabel
                            className={clsx(
                                "min-w-[180px] capitalize",
                                isSmaller && "mb-1 text-[12px]"
                            )}
                        >
                            {selectName}
                        </FormLabel>
                    )}
                    {isLoading ? (
                        <Skeleton className="h-[42px] w-full border border-transparent" />
                    ) : (
                        !isSelectAll && (
                            <Select
                                maxMenuHeight={maxMenuHeight}
                                id={name}
                                instanceId={name}
                                {...field}
                                isClearable={isClearable}
                                isMulti={isMulti}
                                closeMenuOnSelect={isMulti ? false : true}
                                options={sortedOptions}
                                placeholder={t(placeholder)}
                                isDisabled={isDisabled}
                                hideSelectedOptions={hideSelectedOptions}
                                components={{
                                    ...(noChevron && {
                                        DropdownIndicator: () => null,
                                        IndicatorSeparator: () => null,
                                    }),
                                    ...(displayItemSelected && {
                                        ValueContainer: CustomValueContainer,
                                    }),
                                }}
                                value={
                                    isMulti
                                        ? sortedOptions?.filter((option) =>
                                              isArray(field.value)
                                                  ? field.value?.includes(
                                                        option.value
                                                    )
                                                  : field.value === option.value
                                          )
                                        : sortedOptions?.find(
                                              (option) =>
                                                  option.value === field.value
                                          ) ?? null
                                }
                                onChange={(selectedOption: any) => {
                                    const value = isMulti
                                        ? selectedOption?.map(
                                              (item: any) => item.value
                                          ) || []
                                        : selectedOption?.value || null;

                                    field.onChange(value);
                                    onChange(value);
                                }}
                                styles={selectStyles(isSmaller)}
                            />
                        )
                    )}
                    {isMulti && hasSelectAll && (
                        <div
                            className={clsx(
                                "ml-0.5 flex gap-2",
                                isSelectAll ? "mt-3.5" : "mt-1.5"
                            )}
                        >
                            <Checkbox
                                checked={isSelectAll}
                                onCheckedChange={(checked: boolean) => {
                                    seIsSelectAll(checked);
                                    if (!checked) {
                                        field.onChange([]);
                                    }
                                }}
                            />
                            <div
                                className={clsx(
                                    "c-text-size font-medium",
                                    isSelectAll
                                        ? "text-gray-600"
                                        : "text-themeLabel"
                                )}
                            >
                                Select All
                            </div>
                        </div>
                    )}
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormSelect;
