import { useLocale, useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin, { Draggable } from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/react";
import clsx from "clsx";
import { eachDayOfInterval } from "date-fns";
import { capitalize, groupBy, isEmpty } from "lodash";
import { X } from "lucide-react";
import toast from "react-hot-toast";
import Select from "react-select";
import { Button } from "@/components/base-ui/button";
import Card from "@/components/ui/Card";
import {
    BOOKSHOP,
    CANTEEN,
    GET_ALL_PARAMS,
    productAPI,
    productGroupAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    formatStringsForSelect,
    getAxiosErrorMessage,
    replaceAll,
    selectStyles,
    toYMD,
} from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "../base-ui/popover";

const _PRODUCT_GROUP = "product_group";
const _PRODUCT = "product";

function getGroupNames(groups) {
    return groups?.map((group) => group?.name).join(", ");
}

const AssignProducts = ({
    merchantType,
    api,
}: {
    merchantType: typeof BOOKSHOP | typeof CANTEEN;
    api: string;
}) => {
    const locale = useLocale();

    const merchantTypeDate =
        merchantType === BOOKSHOP
            ? "available_date"
            : merchantType === CANTEEN
              ? "delivery_date"
              : "";

    const calendarRef = useRef<any>(null);

    const [assignedItems, setAssignedItems] = useState<Record<string, any>[]>(
        []
    );

    const [type, setType] = useState<typeof _PRODUCT_GROUP | typeof _PRODUCT>(
        _PRODUCT_GROUP
    );

    const [productGroupOptions, setProductGroupOptions] = useState<
        Record<string, any>[]
    >([]);

    const [dateRange, setDateRange] = useState<Date[]>([]);

    const [isUpdated, setIsUpdated] = useState(false);

    const [popupId, setPopupId] = useState<any>();

    const { data: currentProducts, axiosQuery: getCurrentProducts } = useAxios({
        api: `${productAPI}/get-by-${merchantTypeDate.replaceAll("_", "-")}`,
        locale,
    });

    const { axiosQuery: getProductGroups } = useAxios({
        api: productGroupAPI,
        locale,
        onSuccess: (res) => {
            // Filter out product groups that have no products
            const _options = res?.data?.filter(
                (group: { products: [] }) => group?.products?.length > 0
            );
            setProductGroupOptions(_options);
        },
    });

    const { data: productOptions, axiosQuery: getProductOptions } = useAxios({
        api: productAPI,
        locale,
    });

    const { axiosPost: saveAssignment, error: postError } = useAxios({
        api,
        toastMsg: "Products assigned successfully",
        onSuccess: () => {
            close();
            getPeriodicAssignedProducts();
            setIsUpdated(false);
        },
    });

    function getPeriodicAssignedProducts() {
        const params: Record<string, any> = {
            merchant_type: merchantType,
            per_page: -1,
            [`${merchantTypeDate}_from`]: toYMD(dateRange[0]),
            [`${merchantTypeDate}_to`]: toYMD(dateRange[1]),
        };
        getCurrentProducts({ params });
    }

    useEffect(() => {
        console.log("assignedItems", assignedItems);
    }, [assignedItems]);

    useEffect(() => {
        if (isEmpty(dateRange)) return;
        getPeriodicAssignedProducts();
    }, [dateRange]);

    useEffect(() => {
        if (!currentProducts) return;

        const items = currentProducts.flatMap((productObj) => [
            ...(productObj?.product_groups?.map((group) => ({
                product_id: group?.id?.toString(),
                product_group: null,
                title: group?.name,
                start: new Date(productObj[merchantTypeDate]),
                type: _PRODUCT_GROUP,
                products: [...group?.products],
            })) || []),
            ...(productObj?.products?.map((product) => ({
                product_id: product?.id?.toString(),
                product_group: null,
                title: product?.name,
                start: new Date(productObj[merchantTypeDate]),
                type: _PRODUCT,
                products: [],
            })) || []),
        ]);

        setAssignedItems(items);
    }, [currentProducts]);

    useEffect(() => {
        if (type === _PRODUCT_GROUP) {
            getProductGroups({
                params: {
                    ...GET_ALL_PARAMS,
                    type: merchantType,
                    includes: ["products"],
                    order_by: { name: "asc" },
                },
            });
        } else if (type === _PRODUCT) {
            getProductOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    merchant_type: merchantType,
                    is_active: 1,
                    order_by: { sequence: "desc" },
                },
            });
        }
    }, [type]);

    useEffect(() => {
        const draggableEl = document.getElementById(
            "draggable-product-group-list"
        );
        if (!draggableEl) return;

        new Draggable(draggableEl, {
            itemSelector: ".draggable-product-group-item",
            longPressDelay: 0,
            eventData: function (eventEl) {
                return {
                    product_id: eventEl.getAttribute("data-product-id"),
                    product_group: eventEl.getAttribute("data-product-group"),
                    title: eventEl.innerText,
                    start: new Date(),
                    products: JSON.parse(
                        eventEl.getAttribute("data-products") || "[]"
                    ),
                };
            },
        });
    }, []);

    useEffect(() => {
        if (postError) {
            toast.error(getAxiosErrorMessage(postError));
        }
    }, [postError]);

    function renderEventContent({ event }) {
        const { type, products } = event.extendedProps;
        const isProductGroup = type === _PRODUCT_GROUP;

        const productIdentifier = event.extendedProps.product_id + event.start;

        return (
            <Popover
                open={popupId === productIdentifier}
                onOpenChange={(isOpen) =>
                    isOpen ? setPopupId(productIdentifier) : setPopupId(null)
                }
            >
                <PopoverTrigger
                    className={clsx(
                        "relative mx-1 flex w-full touch-auto items-start justify-between gap-x-3 rounded-sm border border-themeGreen border-opacity-40 p-1.5 text-[12px] font-medium leading-tight text-themeGreenDark transition hover:bg-gray-50",
                        isProductGroup &&
                            "product-group bg-themeGreen3 hover:bg-themeGreen hover:bg-opacity-10"
                    )}
                >
                    <div className="popover-title-container flex w-[calc(100%-28px)] flex-wrap gap-x-1">
                        <span className="popover-title font-semibold">
                            {event.title}
                        </span>
                    </div>
                    <X
                        className="h-[16px] w-[16px] cursor-pointer text-themeGreenDark text-opacity-50"
                        onClick={() => {
                            removeEvent(
                                event.extendedProps.product_id,
                                event.extendedProps.type,
                                event.start
                            );
                        }}
                    />
                </PopoverTrigger>
                {isProductGroup && products && (
                    <PopoverContent className="fc-popover-body space-y-1">
                        {products?.map((product) => (
                            <div
                                key={product?.id}
                                className="relative mx-1 flex rounded-sm border border-themeGreen border-opacity-40 p-1.5 text-[12px] font-medium leading-tight text-themeGreenDark"
                            >
                                <span className={"font-semibold"}>
                                    {product?.name}
                                </span>
                            </div>
                        ))}
                    </PopoverContent>
                )}
            </Popover>
        );
    }

    function removeEvent(id, type, start) {
        // remove item that has same product_id, type and date
        setAssignedItems((prevAssignedItems) =>
            prevAssignedItems.filter(
                (item) =>
                    item.product_id?.toString() != id?.toString() ||
                    toYMD(item.start) != toYMD(start) ||
                    item.type != type
            )
        );
        setIsUpdated(true);
    }

    function isDuplicateEvent(info) {
        const productId = info.event.extendedProps.product_id?.toString();
        const isDuplicate = assignedItems.some((event) => {
            return (
                event.product_id?.toString() == productId &&
                event.type == (info.event.extendedProps.type ?? type) &&
                toYMD(event.start) == toYMD(info.event.start)
            );
        });
        if (isDuplicate) {
            info.revert();
            return true;
        }
        return false;
    }

    function handleEventReceive(info) {
        const productId = info.event.extendedProps.product_id;
        if (isDuplicateEvent(info)) {
            return;
        }
        const newEvent = {
            product_id: productId,
            title: info.event.title,
            start: info.event.start,
            type: type,
            product_group: info.event.extendedProps.product_group,
            products: info?.event?.extendedProps?.products,
        };
        setAssignedItems((prevAssignedItems) => [
            ...prevAssignedItems,
            newEvent,
        ]);
        setIsUpdated(true);
    }

    function handleEventDrop(info) {
        const { event, oldEvent } = info;
        if (isDuplicateEvent(info)) {
            return;
        }
        const updatedItems = assignedItems.map((item) => {
            if (
                item.product_id?.toString() ===
                    event.extendedProps.product_id?.toString() &&
                item.type === event.extendedProps.type &&
                toYMD(item.start) === toYMD(oldEvent.start)
            ) {
                return {
                    ...item,
                    start: event.start,
                };
            }
            return item;
        });
        setAssignedItems(updatedItems);
        setIsUpdated(true);
    }

    function handleDatesSet(data) {
        setDateRange([data.start, data.end]);
    }

    function handleButtonClick(action: "prev" | "next" | "today") {
        if (isUpdated) {
            toast("Please save the changes before moving to another dates");
            return;
        }
        const calendarApi = calendarRef?.current?.getApi();
        if (action === "prev") {
            calendarApi.prev();
        } else if (action === "next") {
            calendarApi.next();
        } else if (action === "today") {
            calendarApi.today();
        }
    }

    function onSave() {
        const formattedAssignedItems: Record<string, any>[] = assignedItems.map(
            (item) => ({
                ...item,
                start: toYMD(item.start),
            })
        );
        const groupByDate = groupBy(formattedAssignedItems, "start");

        const dates: Record<string, any>[] = [];

        Object.entries(groupByDate).forEach(([date, items]) => {
            dates.push({
                [merchantTypeDate]: date,
                product_ids: items
                    .filter((item) => item.type === _PRODUCT)
                    .map((item) => item.product_id),
                product_group_ids: items
                    .filter((item) => item.type === _PRODUCT_GROUP)
                    .map((item) => item.product_id),
            });
        });

        const currentDates = eachDayOfInterval({
            start: dateRange[0],
            end: dateRange[1],
        })?.map((date) => toYMD(date));

        currentDates.pop();

        const unassignedDates = currentDates.filter(
            (currentDate) =>
                !dates
                    .map((date) => date[merchantTypeDate])
                    .includes(currentDate)
        );

        unassignedDates.forEach((unassignedDate) => {
            dates.push({
                [merchantTypeDate]: unassignedDate,
                product_ids: [],
                product_group_ids: [],
            });
        });

        console.log("groupByDate", groupByDate);
        console.log("dates", dates);

        saveAssignment({ dates });
    }

    const t = useTranslations("common");

    return (
        <Card styleClass="pb-10">
            <h2 className="capitalize">
                Assign{" "}
                {merchantType === BOOKSHOP
                    ? "Bookstore"
                    : capitalize(merchantType)}{" "}
                Products with {replaceAll(merchantTypeDate, "_", " ")}
            </h2>
            <div className="mt-4 flex w-full flex-col-reverse gap-5 lg:flex-row">
                <div className="flex-grow overflow-auto border-r border-dashed lg:border-none">
                    <div className="min-w-[800px] lg:min-w-0">
                        <FullCalendar
                            ref={calendarRef}
                            plugins={[dayGridPlugin, interactionPlugin]}
                            initialView="dayGridMonth"
                            droppable={true}
                            editable={true}
                            events={assignedItems}
                            eventOrder={"-sequence"}
                            eventContent={renderEventContent}
                            eventReceive={handleEventReceive}
                            eventDrop={handleEventDrop}
                            datesSet={handleDatesSet}
                            eventLongPressDelay={0}
                            dayMaxEventRows={5}
                            contentHeight={700}
                            views={{
                                dayGridMonth: {
                                    moreLinkText: "Show more",
                                },
                            }}
                            customButtons={{
                                prev: {
                                    text: "Prev",
                                    click: () => handleButtonClick("prev"),
                                },
                                next: {
                                    text: "Next",
                                    click: () => handleButtonClick("next"),
                                },
                                today: {
                                    text: "Today",
                                    click: () => handleButtonClick("today"),
                                },
                            }}
                        />
                    </div>
                </div>
                <div className="w-[150px]">
                    <div className="mb-1.5 ml-0.5 text-[14px] font-medium text-themeLabel">
                        Select Type
                    </div>
                    <Select
                        id={"select-type"}
                        instanceId={"select-type"}
                        options={formatStringsForSelect(
                            [_PRODUCT_GROUP, _PRODUCT],
                            t
                        )}
                        value={
                            formatStringsForSelect(
                                [_PRODUCT_GROUP, _PRODUCT],
                                t
                            )?.find((option) => option.value === type) ?? null
                        }
                        placeholder={"Select Type"}
                        onChange={(selectedOption: any) => {
                            setType(selectedOption?.value);
                        }}
                        styles={selectStyles(true)}
                    />

                    <div className="relative mt-1">
                        <div
                            id="draggable-product-group-list"
                            className="grid h-full max-h-[600px] gap-y-2 overflow-auto py-3"
                        >
                            {(type === _PRODUCT_GROUP
                                ? productGroupOptions
                                : type === _PRODUCT
                                  ? productOptions
                                  : []
                            )?.map((productGroup) => (
                                <div
                                    className="draggable-product-group-item"
                                    key={productGroup?.id}
                                    data-product-id={productGroup?.id}
                                    data-product-group={getGroupNames(
                                        productGroup?.groups
                                    )}
                                    data-products={JSON.stringify(
                                        productGroup?.products
                                    )}
                                    // data-product-type={type}
                                >
                                    {productGroup?.name}
                                </div>
                            ))}
                        </div>
                        <div className="absolute left-0 top-0 h-3 w-full bg-gradient-to-b from-white"></div>
                        <div className="absolute bottom-0 left-0 h-3 w-full bg-gradient-to-t from-white"></div>
                    </div>
                </div>
            </div>
            <Button
                className="mr-[170px] mt-5 lg:ml-auto"
                onClick={onSave}
                disabled={!isUpdated}
            >
                Save
            </Button>
        </Card>
    );
};

export default AssignProducts;
