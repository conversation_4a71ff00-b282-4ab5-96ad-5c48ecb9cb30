import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTableDraggable from "@/components/ui/DataTableDraggable";
import FormSelect from "@/components/ui/FormSelect";
import FreeInput from "@/components/ui/FreeInput";
import Tabs from "@/components/ui/Tabs";
import {
    GET_ALL_PARAMS,
    seatSettingsAPI,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { combinedNames, showBackendFormError } from "@/lib/utils";
import { useLocale, useTranslations } from "next-intl";

const _bySemester = "By Semester";
const _byClass = "By Class";

const _STUDENT_NUMBER = "STUDENT_NUMBER";
const _ENGLISH_NAME = "ENGLISH_NAME";

const SeatSettingsEditor = () => {
    const t = useTranslations("common");

    const [type, setType] = useState(_bySemester);

    return (
        <Card styleClass="table-card">
            <h2 className="mb-4 capitalize">{t("Seat Settings")}</h2>
            <Tabs
                list={[_bySemester, _byClass]}
                selected={type}
                setSelected={setType}
            />
            {type === _bySemester && <SeatSettingBySemester />}
            {type === _byClass && <SeatSettingByClass />}
        </Card>
    );
};

const SeatSettingBySemester = () => {
    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { axiosPost: assignSeats, error: postError } = useAxios({
        api: `${seatSettingsAPI}/${form.getValues("semester_setting_id")}`,
        onSuccess: () => {
            close();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                assignSeats({ data });
            })
        );
    }

    useEffect(() => {
        getSemesterOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: { name: "desc" },
            },
        });
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const t = useTranslations("common");

    return (
        <Form {...form}>
            <form onSubmit={onSubmit}>
                <div className="mb-5 max-w-[500px]">
                    <FormSelect
                        control={form.control}
                        name={"semester_setting_id"}
                        label={t("semester") + "*"}
                        options={semesterOptions}
                        isSortByName={false}
                    />
                </div>

                {hasPermit("class-seat-assignment-update") && (
                    <div className="flex flex-wrap items-center justify-between gap-3">
                        <Button
                            type="submit"
                            disabled={!form.watch("semester_setting_id")}
                        >
                            {t("Apply")}
                        </Button>
                    </div>
                )}
            </form>
        </Form>
    );
};

const SeatSettingByClass = () => {
    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const form = useForm<any>({
        defaultValues: {
            semester_class_id: "",
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const { fields } = useFieldArray({
        control: form.control,
        name: "seats",
    });

    const { initLoader } = useSubmit();

    function onFilter(e) {
        if (e) {
            e.preventDefault();
            form.setValue("sort_by", "");
        }
        initLoader(
            form.handleSubmit((data) => {
                getStudentSeatSettings({ id: data.semester_class_id });
            })
        );
    }

    const handleDragEnd = (result) => {
        if (!result.destination) return;

        const seats = form.getValues("seats");
        const [movedItem] = seats.splice(result.source.index, 1);
        seats.splice(result.destination.index, 0, movedItem);

        seats.forEach((item, index) => {
            item.seat_no = (index + 1).toString();
        });

        form.setValue("seats", seats);
    };

    const handleAssignSeats = () => {
        const data = form.getValues();
        const formattedSeats = data.seats.map((seat) => ({
            student_class_id: seat.student_class_id,
            seat_no: seat.seat_no,
        }));

        assignSeats({
            id: data.semester_class_id,
            data: { seats: formattedSeats },
        });
    };

    const handleFillInEmptySeats = () => {
        const seats = form.getValues("seats");
        let largestSeatNo = 0;
        let nextSeatNo = 1;

        const updatedSeats = seats.map((seat) => {
            if (!isEmpty(seat.seat_no)) {
                const seatNo = parseInt(seat.seat_no, 10);
                largestSeatNo = Math.max(largestSeatNo, seatNo);
                return seat;
            } else {
                return {
                    ...seat,
                    seat_no: largestSeatNo + nextSeatNo++,
                };
            }
        });

        form.setValue("seats", updatedSeats);
    };

    const { axiosQuery: getStudentSeatSettings } = useAxios({
        api: seatSettingsAPI,
        locale,
        onSuccess(result) {
            if (!result.data) {
                form.setValue("seats", []);
                return;
            } else {
                const sort_by = form.watch("sort_by");
                if (sort_by) {
                    if (sort_by === _STUDENT_NUMBER) {
                        result.data.sort((a, b) => {
                            const numA = a.student_number.replace(
                                /^[A-Za-z]/,
                                ""
                            );
                            const numB = b.student_number.replace(
                                /^[A-Za-z]/,
                                ""
                            );
                            return numA.localeCompare(numB, undefined, {
                                numeric: true,
                            });
                        });
                    } else if (sort_by === _ENGLISH_NAME) {
                        result.data.sort((a, b) =>
                            a.student_name.en.localeCompare(b.student_name.en)
                        );
                    }
                    result.data.forEach((item, index) => {
                        item.seat_no = (index + 1).toString();
                    });
                }

                form.setValue(
                    "seats",
                    result.data.map((item) => ({
                        student_class_id: item.student_class_id,
                        seat_no: item.seat_no ?? "",
                        student_number: item.student_number,
                        student_name: `${combinedNames(item?.student_name)} ${
                            !item.is_active ? " (Inactive)" : ""
                        }`,
                        is_active: item.is_active ?? false,
                    }))
                );
            }
            setHasSearched(true);
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { axiosPut: assignSeats, error: putError } = useAxios({
        api: seatSettingsAPI,
        onSuccess: () => {
            close();
            // refresh();
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const t = useTranslations("common");

    return (
        <Form {...form}>
            <form
                className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3"
                onSubmit={onFilter}
            >
                <FormSelect
                    control={form.control}
                    name={"semester_setting_id"}
                    label={t("semester") + "*"}
                    options={semesterOptions}
                    isSortByName={false}
                    onChange={() => {
                        form.setValue("semester_class_id", "");
                    }}
                />

                <FormSelect
                    control={form.control}
                    name={"semester_class_id"}
                    label={t("semester class") + "*"}
                    isDisabled={!form.watch("semester_setting_id")}
                    options={
                        semesterClassOptions?.map((option) => ({
                            id: option?.id,
                            name: `${option?.class_model?.name}`,
                        })) ?? []
                    }
                />

                <Button
                    type="submit"
                    variant={"outline"}
                    disabled={
                        !form.watch("semester_setting_id") ||
                        !form.watch("semester_class_id")
                    }
                >
                    {t("Filter")}
                </Button>

                {hasSearched && !isEmpty(form.getValues("seats")) && (
                    <>
                        <FormSelect
                            control={form.control}
                            name="sort_by"
                            isStringOptions={true}
                            options={[_ENGLISH_NAME, _STUDENT_NUMBER]}
                        />
                        <Button
                            variant={"outline"}
                            onClick={() => onFilter(null)}
                        >
                            {t("Sort")}
                        </Button>
                    </>
                )}
            </form>

            {hasSearched ? (
                isEmpty(form.getValues("seats")) ? (
                    <div className="h-20 text-center text-themeLabel">
                        {t("No Record Found")}
                    </div>
                ) : (
                    <div className="grid gap-y-5 overflow-auto">
                        <DataTableDraggable
                            columns={[
                                {
                                    key: "seat_no",
                                    modify: (_, cell) => (
                                        <FreeInput
                                            type="number"
                                            hasLabel={false}
                                            control={form.control}
                                            name={`seats[${cell.row.index}].seat_no`}
                                            error={
                                                form.formState.errors?.seats?.[
                                                    cell.row.index
                                                ]?.seat_no
                                            }
                                            styleClass="max-w-[120px]"
                                            onChange={() =>
                                                setTimeout(() => {
                                                    form.setFocus(
                                                        `seats[${cell.row.index}].seat_no`
                                                    );
                                                }, 0)
                                            }
                                        />
                                    ),
                                },
                                { key: "student_number" },
                                {
                                    key: "student_name",
                                    modify: (_, cell) => (
                                        <div className="w-[550px]">
                                            {cell.row.original.student_name}
                                        </div>
                                    ),
                                },
                            ]}
                            data={fields}
                            onDragEnd={handleDragEnd}
                        />

                        {hasPermit("class-seat-assignment-update") && (
                            <div className="mb-2 flex justify-end gap-x-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleFillInEmptySeats}
                                >
                                    {t("Fill In Empty Seats")}
                                </Button>

                                <Button onClick={handleAssignSeats}>
                                    {t("Apply")}
                                </Button>
                            </div>
                        )}
                    </div>
                )
            ) : null}
        </Form>
    );
};

export default SeatSettingsEditor;
