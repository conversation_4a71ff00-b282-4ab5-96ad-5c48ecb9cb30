import { isArray } from "lodash";
import { Controller } from "react-hook-form";
import Select from "react-select";
import { selectStyles } from "@/lib/constant";
import {
    formatForSelect,
    formatStringsForSelect,
    getInputErrorMessage,
    replaceAll,
} from "@/lib/utils";
import { Label } from "../base-ui/label";
import { Skeleton } from "../base-ui/skeleton";
import { useTranslations } from "next-intl";

type FormSelectProps = {
    control: any;
    name: string;
    options: any;
    placeholder?: string;
    label?: string;
    hasLabel?: boolean;
    isStringOptions?: boolean;
    isStartCaseForStringOptions?: boolean;
    isMulti?: boolean;
    isLoading?: boolean;
    onChange?: (value: any) => void;
    isDisabled?: boolean;
    isClearable?: boolean;
    error?: any;
    rules?: any;
};

const FreeSelect = ({
    control,
    name,
    label,
    options,
    placeholder = "",
    hasLabel = true,
    isStringOptions = false,
    isStartCaseForStringOptions = true,
    isMulti = false,
    isLoading,
    onChange = (value) => {},
    isDisabled = false,
    isClearable = true,
    error,
    rules,
}: FormSelectProps) => {
    const t = useTranslations("common");

    const selectName = t(replaceAll(label || name, "_", " "));
    const formattedOptions = isArray(options)
        ? isStringOptions
            ? formatStringsForSelect(options, t, isStartCaseForStringOptions)
            : formatForSelect(options)
        : [];
    return control ? (
        <div>
            {hasLabel && <Label className={"label"}>{selectName}</Label>}
            {isLoading ? (
                <Skeleton className="h-[40px] w-full" />
            ) : (
                <Controller
                    control={control}
                    name={name}
                    rules={rules}
                    render={({ field }) => {
                        return (
                            <Select
                                maxMenuHeight={160}
                                id={name}
                                instanceId={name}
                                {...field}
                                isMulti={isMulti}
                                isClearable={isClearable}
                                options={formattedOptions}
                                placeholder={t(placeholder)}
                                isDisabled={isDisabled}
                                value={
                                    isMulti
                                        ? formattedOptions?.filter((option) =>
                                              isArray(field.value)
                                                  ? field.value?.includes(
                                                        option.value
                                                    )
                                                  : field.value === option.value
                                          )
                                        : formattedOptions?.find(
                                              (option) =>
                                                  option.value === field.value
                                          ) ?? null
                                }
                                onChange={(selectedOption: any) => {
                                    const value = isMulti
                                        ? selectedOption?.map(
                                              (item: any) => item.value
                                          ) || []
                                        : selectedOption?.value || null;

                                    field.onChange(value);
                                    onChange(value);
                                }}
                                styles={selectStyles}
                            />
                        );
                    }}
                />
            )}
            {error && (
                <div className="warning-text">
                    {`${getInputErrorMessage(error)}`}
                </div>
            )}
        </div>
    ) : (
        <></>
    );
};

export default FreeSelect;
