import React, { useEffect, useState } from "react";
import {
    flexRender,
    getCoreRowModel,
    useReactTable,
    getSortedRowModel,
    RowSelectionState,
    Table as TableType,
} from "@tanstack/react-table";
import clsx from "clsx";
import { isArray, isEmpty } from "lodash";
import {
    Check,
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
} from "lucide-react";
import { Button } from "@/components/base-ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/base-ui/select";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/base-ui/table";
import { TableColumnType } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import TableCheckbox from "./TableCheckbox";
import TableHeaderSorter from "./TableHeaderSorter";
import { useTranslations } from "next-intl";

type DataTableProps = {
    columns: Array<TableColumnType>;
    data: Array<any>;
    actionMenu?: ({ cell }: { cell: any }) => JSX.Element | undefined;
    changePage?: (arg: Record<string, any>) => void;
    pagination?: any;
    setPagination?: any;
    sort?: (name: string, direction: string) => void;
    sorted?: Record<string, any>;
    hasPerPage?: boolean;
    selectedRows?: any[];
    onSelect?: (id) => void;
    onMultiSelect?: (list: any[]) => void;
    clearCount?: number;
    isSmaller?: boolean;
    extraRow?: ExtraCellProps[];
    styleClass?: string;
    hidePagination?: boolean;
    canOverflow?: boolean;
    hasPicker?: boolean;
    noCellMinWidth?: boolean;
};

type ExtraCellProps = {
    value: any;
    colSpan?: number;
};

const DataTable = ({
    columns,
    data,
    actionMenu,
    changePage,
    pagination = {},
    setPagination,
    sort = () => {},
    sorted = {},
    hasPerPage,
    selectedRows,
    onSelect,
    onMultiSelect,
    clearCount = 0,
    isSmaller,
    extraRow,
    styleClass,
    hidePagination = false,
    canOverflow = false,
    hasPicker = false,
    noCellMinWidth = false,
}: DataTableProps) => {
    const t = useTranslations("common");
    // single selection
    const [selected, setSelected] = useState<any>(null);
    // multiple selection
    const [rowSelection, setRowSelection] = useState<any>({});

    const tableColumns = columns.map((item) => ({
        accessorKey: item.key,
        header: () => {
            return item.hasSort ? (
                <TableHeaderSorter
                    head={item.key}
                    label={item.displayAs}
                    sorted={sorted}
                    sort={sort}
                />
            ) : item.modifyHeader ? (
                <div className="thead-item">{item.modifyHeader(item.key)}</div>
            ) : (
                <div className="c-text-size thead-item capitalize">
                    {t(replaceAll(item.displayAs || item.key, "_", " "))}
                </div>
            );
        },
        cell: ({ cell }) => {
            const value = cell.getValue(item);
            if (item.modify) {
                return item.modify(value, cell);
            } else {
                return value;
            }
        },
    }));

    useEffect(() => {
        if (selectedRows) {
            console.log("selectedRows", selectedRows);
            setRowSelection(
                selectedRows
                    .map((i) => i?.id)
                    .reduce((acc, id) => {
                        acc[id] = true;
                        return acc;
                    }, {})
            );
        }
    }, [selectedRows]);

    useEffect(() => {
        if (onMultiSelect) {
            if (rowSelection) {
                console.log("rowSelection", rowSelection);
                onMultiSelect(Object.keys(rowSelection));
            }
        }
    }, [rowSelection]);

    useEffect(() => {
        if (clearCount) {
            setSelected(null);
            setRowSelection({});
        }
    }, [clearCount]);

    function _onSelect(id) {
        if (onSelect) {
            onSelect(id);
            setSelected(id);
        }
    }

    if (onSelect) {
        tableColumns.unshift({
            accessorKey: "select",
            header: () => <></>,
            cell: ({ cell }) => {
                const _id = cell.row.original?.id;
                return (
                    <div
                        className={clsx(
                            "mx-auto flex h-5 w-5 cursor-pointer items-center justify-center rounded-full border",
                            _id && selected == _id
                                ? "border-themeGreen bg-themeGreen"
                                : "border-gray-400"
                        )}
                        onClick={() => _onSelect(_id)}
                    >
                        {_id && selected == _id && (
                            <Check size={14} className="text-white" />
                        )}
                    </div>
                );
            },
        });
    }
    if (onMultiSelect) {
        tableColumns.unshift({
            accessorKey: "checkbox",
            header: () => <TableCheckbox table={table} row={null} />,
            cell: ({ cell }) => <TableCheckbox row={cell.row} table={null} />,
        });
    }

    const hasActions =
        isArray(data) &&
        data.length > 0 &&
        data.some((row) => {
            const cell = { row: { original: row } };
            return actionMenu?.({ cell }) !== undefined;
        });

    // Only add the action column if at least one row has actions
    if (hasActions) {
        tableColumns.push({
            accessorKey: "action",
            header: () => <></>,
            cell: ({ cell }) => actionMenu?.({ cell }) ?? null,
        });
    }

    const table = useReactTable({
        data: data ?? [],
        columns: tableColumns,
        manualPagination: true,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        pageCount: pagination?.last_page,
        getRowId: (row) => row.id,
        state: {
            rowSelection,
            pagination: {
                pageIndex: pagination?.current_page - 1,
                pageSize: pagination?.per_page,
            },
        },
    });

    if (isEmpty(columns)) return <></>;

    return (
        <>
            <div
                className={clsx(
                    canOverflow ? "overflow-visible" : "overflow-x-auto",
                    "c-scrollbar rounded-md border border-gray-200",
                    styleClass
                )}
            >
                <Table className={clsx(hasPicker && "relative z-10")}>
                    <TableHeader>
                        {table?.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead
                                            key={header.id}
                                            className={clsx(
                                                isSmaller && "h-10"
                                            )}
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext()
                                                  )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table?.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row, rowIndex) => (
                                <TableRow
                                    key={row.id}
                                    data-state={
                                        rowSelection &&
                                        row.getIsSelected() &&
                                        "selected"
                                    }
                                    className={clsx({
                                        "bg-red-100 hover:bg-red-200":
                                            row.original.error,
                                        "border-b-2 border-white bg-green-100 hover:bg-green-200":
                                            row.original.hasAbsentPeriod,
                                        "border-b-2 border-white bg-yellow-200 hover:bg-yellow-300":
                                            row.original.check_in_datetime ===
                                                null ||
                                            row.original
                                                .hasLeaveApplicationInfo,
                                    })}
                                >
                                    {row.getVisibleCells().map((cell) => {
                                        return (
                                            <TableCell
                                                key={cell.id}
                                                className={clsx(
                                                    extraRow &&
                                                        rowIndex ==
                                                            table?.getRowModel()
                                                                .rows?.length -
                                                                1 &&
                                                        "border-b border-b-gray-200",
                                                    isSmaller && "h-10",
                                                    cell.column.columnDef.id ===
                                                        "action"
                                                        ? "w-14"
                                                        : ""
                                                )}
                                            >
                                                <div
                                                    className={clsx(
                                                        !noCellMinWidth &&
                                                            "min-w-[40px]"
                                                    )}
                                                >
                                                    {flexRender(
                                                        cell.column.columnDef
                                                            .cell,
                                                        cell.getContext()
                                                    )}
                                                </div>
                                            </TableCell>
                                        );
                                    })}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={tableColumns.length}
                                    className="h-20 text-center text-themeLabel"
                                >
                                    {t("No data")}
                                </TableCell>
                            </TableRow>
                        )}
                        {extraRow && (
                            <>
                                <TableRow>
                                    {extraRow.map((cellData, index) => (
                                        <TableCell
                                            key={
                                                "extraRow" +
                                                cellData.value.toString() +
                                                index
                                            }
                                            colSpan={cellData.colSpan ?? 1}
                                        >
                                            {cellData.value}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            </>
                        )}
                    </TableBody>
                </Table>
            </div>

            {!hidePagination && !isEmpty(pagination) && (
                <DataTablePagination
                    table={table}
                    rowSelection={rowSelection}
                    changePage={changePage}
                    pagination={pagination}
                    hasPerPage={hasPerPage}
                    hidePagination={hidePagination}
                />
            )}
        </>
    );
};

export default DataTable;

type DataTablePaginationProps = {
    table: TableType<any>;
    rowSelection?: RowSelectionState;
    changePage?: (arg: Record<string, any>) => void;
    pagination: Record<string, any>;
    hasPerPage?: boolean;
    hidePagination?: boolean;
};

const DataTablePagination = ({
    table,
    rowSelection,
    changePage = () => {},
    pagination,
    hasPerPage = true,
    hidePagination,
}: DataTablePaginationProps) => {
    const t = useTranslations("common");

    return (
        <div className="mt-3 flex flex-col items-center justify-between gap-x-3 gap-y-4 px-2 lg:flex-row">
            <div>
                {/* checkbox selection count */}
                {rowSelection &&
                table?.getFilteredSelectedRowModel().rows.length > 0 ? (
                    <div className="flex-1 text-[13px] text-muted-foreground">
                        {table.getFilteredSelectedRowModel().rows.length} of{" "}
                        {table.getFilteredRowModel().rows.length} row(s)
                        selected
                    </div>
                ) : (
                    <div></div>
                )}
                <div className="text-[13px] text-gray-500">
                    {t("page_total", { number: pagination?.total })}
                </div>
            </div>
            {!pagination?.hideChangePage && (
                <div className="flex flex-col items-center justify-center gap-x-5 gap-y-4 lg:flex-row">
                    {/* per page dropdown */}
                    {hasPerPage && (
                        <div className="order-2 flex items-center gap-x-2 lg:order-1">
                            <p className="text-sm font-medium">
                                {t("rows_per_page")}
                            </p>
                            <Select
                                value={`${table.getState().pagination.pageSize}`}
                                onValueChange={(value) => {
                                    table.setPageSize(Number(value));
                                    changePage({
                                        per_page: Number(value),
                                        page: 1,
                                    });
                                }}
                            >
                                <SelectTrigger className="bottom-0 w-fit text-[14px]">
                                    <SelectValue
                                        placeholder={pagination?.per_page}
                                    />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((pageSize) => (
                                        <SelectItem
                                            className="text-[14px]"
                                            key={pageSize}
                                            value={`${pageSize}`}
                                        >
                                            {pageSize}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    )}

                    {/* page */}
                    <div className="flex items-center justify-center text-xs font-medium capitalize text-gray-500 lg:order-2">
                        {t("pagination", {
                            number: table.getState().pagination.pageIndex + 1,
                            total:
                                pagination?.current_page > pagination?.last_page
                                    ? pagination?.current_page
                                    : table.getPageCount(),
                        })}
                        {}
                    </div>

                    {/* nav */}
                    <div className="flex items-center gap-x-2 lg:order-2">
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() => {
                                changePage({ page: 1 });
                            }}
                            disabled={!table.getCanPreviousPage()}
                        >
                            <span className="sr-only">Go to first page</span>
                            <ChevronsLeft className="h-4 w-4 text-themeBlack" />
                        </Button>
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() => {
                                changePage({
                                    page: pagination.current_page - 1,
                                });
                            }}
                            disabled={!table.getCanPreviousPage()}
                        >
                            <span className="sr-only">Go to previous page</span>
                            <ChevronLeft className="h-4 w-4 text-themeBlack" />
                        </Button>
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() => {
                                changePage({
                                    page: pagination.current_page + 1,
                                });
                            }}
                            disabled={!table.getCanNextPage()}
                        >
                            <span className="sr-only">Go to next page</span>
                            <ChevronRight className="h-4 w-4 text-themeBlack" />
                        </Button>
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() =>
                                changePage({ page: pagination.last_page })
                            }
                            disabled={!table.getCanNextPage()}
                        >
                            <span className="sr-only">Go to last page</span>
                            <ChevronsRight className="h-4 w-4 text-themeBlack" />
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
};
