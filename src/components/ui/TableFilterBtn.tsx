import React from "react";
import { FilterIcon } from "lucide-react";
import { Button } from "../base-ui/button";
import { useTranslations } from "next-intl";

type TableFilterBtnProps = {
    filter: Record<string, any>;
    onClick: () => void;
    excludeFields?: string[];
};

const TableFilterBtn = ({
    filter,
    onClick = () => {},
    excludeFields,
}: TableFilterBtnProps) => {
    const t = useTranslations("common");

    function isFiltered(): boolean {
        const _filter = { ...filter };
        delete _filter.per_page;
        delete _filter.page;
        delete _filter.order_by;
        excludeFields?.forEach((field) => delete _filter[field]);
        return Object.values(_filter).some(
            (v) => v !== "" && v !== null && v !== undefined
        );
    }
    return (
        <Button
            variant={isFiltered() ? "outline" : "outlineBlack"}
            size="smallerOnMobile"
            onClick={onClick}
        >
            <div className="mr-1.5 -translate-x-0.5">
                <FilterIcon
                    className={
                        isFiltered() ? "text-themeGreen" : "text-themeBlack"
                    }
                    size={16}
                />
            </div>
            <div>{t(`${isFiltered() ? "Filtered" : "Filter"}`)}</div>
        </Button>
    );
};

export default TableFilterBtn;
