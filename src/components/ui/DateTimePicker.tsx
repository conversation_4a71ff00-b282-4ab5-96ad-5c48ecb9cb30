import React, { useState } from "react";
import { format, parseISO } from "date-fns";
import { isString } from "lodash";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/base-ui/calendar";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/base-ui/popover";
import { DATE_FORMAT } from "@/lib/constant";
import { cn, getYesterday, replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { TimePicker } from "./TimePicker";
import { useTranslations } from "next-intl";

export function DateTimePicker({
    control,
    name,
    label,
    hasLabel = true,
    disableDateBeforeToday = false,
}: {
    control: any;
    name: string;
    label?: string;
    hasLabel?: boolean;
    disableDateBeforeToday?: boolean;
}) {
    const t = useTranslations("common");
    const [isOpen, setIsOpen] = useState(false);

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => {
                const dateTimeValue = isString(field.value)
                    ? parseISO(field.value)
                    : field.value;

                return (
                    <FormItem className="flex flex-col">
                        {hasLabel && (
                            <FormLabel className="capitalize">
                                {t(replaceAll(label || name, "_", " "))}
                            </FormLabel>
                        )}
                        <Popover open={isOpen} onOpenChange={setIsOpen}>
                            <PopoverTrigger asChild>
                                <FormControl>
                                    <Button
                                        onClick={() => setIsOpen(true)}
                                        variant={"outlineGray"}
                                        className={cn(
                                            "w-full rounded-md border-input pl-4 text-left font-normal"
                                        )}
                                    >
                                        {dateTimeValue ? (
                                            format(
                                                dateTimeValue,
                                                DATE_FORMAT.forDisplay
                                            )
                                        ) : (
                                            <span className="text-themeLabel">
                                                {t("Pick a date and time")}
                                            </span>
                                        )}
                                        <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                                    </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                                className="w-auto bg-white p-0"
                                align="start"
                            >
                                <Calendar
                                    mode="single"
                                    defaultMonth={dateTimeValue}
                                    selected={dateTimeValue}
                                    onSelect={(date) => {
                                        field.onChange(date);
                                    }}
                                    disabled={(date) =>
                                        date <
                                        (disableDateBeforeToday
                                            ? getYesterday()
                                            : new Date("1900-01-01"))
                                    }
                                    initialFocus
                                />
                                <div className="border-t border-border p-3 pt-2">
                                    <TimePicker
                                        setDate={field.onChange}
                                        date={dateTimeValue ?? undefined}
                                    />
                                </div>
                            </PopoverContent>
                        </Popover>
                        <FormMessage />
                    </FormItem>
                );
            }}
        />
    ) : (
        <></>
    );
}
