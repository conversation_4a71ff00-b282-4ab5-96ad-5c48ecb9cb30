import { useEffect, useState } from "react";
import React from "react";
import { Upload } from "lucide-react";
import toast from "react-hot-toast";
import { FormItem, FormLabel } from "@/components/base-ui/form";
import { Input } from "@/components/base-ui/input";
import { replaceAll, strStartCase } from "@/lib/utils";
import { useTranslations } from "next-intl";

const MAX_FILE_SIZE = 2 * 1024 * 1024;

type FormFileInputProps = {
    name: string;
    label?: string;
    placeholder?: string;
    currentFileUrl: string;
    disabled?: boolean;
    register: any;
    errors: any;
    clear?: () => void;
};

const FormFileInput = ({
    name,
    label,
    placeholder = "Choose File",
    currentFileUrl,
    disabled = false,
    register,
    clear = () => {},
    errors,
}: FormFileInputProps) => {
    const t = useTranslations("common");
    const [fileName, setFileName] = useState<string | null>(null);
    const [preview, setPreview] = useState<string | null>(
        currentFileUrl || null
    );

    useEffect(() => {
        if (!currentFileUrl) {
            setPreview(null);
            setFileName(null);
        }
    }, [currentFileUrl]);

    return (
        <FormItem>
            <FormLabel className="capitalize">
                {t(replaceAll(label || name, "_", " "))}
            </FormLabel>

            <div className="relative">
                <Input
                    disabled={disabled}
                    type="file"
                    className="pl-2 pt-2"
                    accept="image/*"
                    {...register(name, {
                        onChange: (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                                if (file.size > MAX_FILE_SIZE) {
                                    toast.error(
                                        `${t("File size should not exceed")} ${MAX_FILE_SIZE / (1024 * 1024)} MB`
                                    );
                                    return;
                                }
                                setPreview(URL.createObjectURL(file));
                                setFileName(file.name);
                            }
                        },
                    })}
                />
                <div className="pointer-events-none absolute left-0 top-0 flex h-full w-full items-center gap-x-2 rounded-sm border border-input bg-white px-4 text-gray-500">
                    <Upload size={16} />
                    <span className="c-text-size font-medium text-themeLabel">
                        {strStartCase(t(placeholder))}
                    </span>
                </div>
            </div>
            {fileName && <div className="mt-3 text-[13px]">{fileName}</div>}
            {preview && (
                <div className="relative mt-2.5 w-fit">
                    <img src={preview} className="w-[200px]" />
                    {/* <XCircle
                        size={22}
                        className="absolute -right-3 -top-2.5 cursor-pointer rounded-full bg-white text-gray-600"
                        onClick={() => {
                            clear();
                            setPreview(null);
                        }}
                    /> */}
                </div>
            )}
            {errors[name] && (
                <div className="mt-2 text-[13px] font-medium text-destructive">
                    {errors[name]?.message.toString()}
                    {console.log(errors[name])}
                </div>
            )}
        </FormItem>
    );
};

export default FormFileInput;
