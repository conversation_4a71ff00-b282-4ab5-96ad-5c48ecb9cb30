import clsx from "clsx";
import { formatUnderscores } from "@/lib/utils";

type RaisedButtonProps = {
    name: string;
    isSelected?: boolean;
    onClick: () => void;
};

const RaisedButton = ({ name, isSelected, onClick }: RaisedButtonProps) => {
    return (
        <div
            className={clsx(
                "c-text-size flex w-fit min-w-[80px] cursor-pointer items-center justify-center rounded-md border px-1.5 py-2.5 text-center font-semibold text-gray-600 shadow-sm transition hover:border-themeGreenDark lg:px-3",
                isSelected &&
                    "border-themeGreenDark bg-themeGreenDark text-white"
            )}
            onClick={onClick}
        >
            {formatUnderscores(name)}
        </div>
    );
};

export default RaisedButton;
