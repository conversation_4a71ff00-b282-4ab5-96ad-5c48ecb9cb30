import { useState } from "react";
import { Form, useForm } from "react-hook-form";
import { mediaUploadAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Input } from "../base-ui/input";

const MultiFilesInput = ({ setIds }) => {
    const form = useForm();
    const [filesNames, setFilesNames] = useState<any[]>([]);

    const { axiosMultipartPost: uploadFiles } = useAxios({
        api: mediaUploadAPI,
        onSuccess: (result) => setIds(result.data.map((item) => item?.id)),
    });

    function submitFiles() {
        form.handleSubmit((data) => {
            if (data.files) {
                delete data.files.length;

                const _files = Object.values(data.files);

                setFilesNames(_files.map((file: any) => file.name));

                const newData = {
                    files: _files.map((file: any) => ({
                        name: file?.name,
                        file,
                    })),
                };

                console.log("newData", newData);
                uploadFiles(newData, false);
            }
        })();
    }
    return (
        <Form {...form}>
            <form>
                <Input
                    className="pl-2 pt-2"
                    type="file"
                    accept=".jpg, .jpeg, .png, .doc, .docx, .pdf"
                    multiple
                    onAbort={() => {}}
                    {...form.register("files", {
                        onChange: (e) => {
                            if (e.target.files?.length > 0) {
                                submitFiles();
                            } else {
                                setIds([]);
                                setFilesNames([]);
                            }
                        },
                    })}
                />
                {filesNames.length > 0 && (
                    <div className="ml-1 mt-2 flex flex-col">
                        {filesNames.map((name, index) => (
                            <div className="text-[13px]" key={name + index}>
                                {name}
                            </div>
                        ))}
                    </div>
                )}
            </form>
        </Form>
    );
};

export default MultiFilesInput;
