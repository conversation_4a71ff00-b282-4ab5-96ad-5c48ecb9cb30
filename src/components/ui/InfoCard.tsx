import { Fragment, useState } from "react";
import clsx from "clsx";
import { isEmpty, isNumber } from "lodash";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { isObjectType, replaceAll } from "@/lib/utils";
import { useTranslations } from "use-intl";

const InfoCard = ({
    title,
    data,
    noBorder = false,
    cardStyleClass = "",
}: {
    title?: string;
    data: object;
    noBorder?: boolean;
    cardStyleClass?: string;
}) => {
    const t = useTranslations("common");
    const [isLoadingPhoto, setIsLoadingPhoto] = useState(true);

    function hasBorder(index: number) {
        const lastIndex = Object.keys(data)?.length - 1;
        return index !== lastIndex;
    }

    return isObjectType(data) ? (
        <div>
            {title && <h3 className="text-themeGreenDark">{title}</h3>}
            <div
                className={clsx(
                    "grid w-fit grid-cols-3",
                    !noBorder && "rounded-md border border-themeGreen p-3",
                    title && "mt-2.5",
                    cardStyleClass
                )}
            >
                {Object.entries(data)?.map(([key, value], index) => {
                    return (
                        <Fragment key={key}>
                            {key !== "photo" && (
                                <p
                                    className={clsx(
                                        "text-[14px] font-medium capitalize leading-none text-themeLabel",
                                        hasBorder(index) &&
                                            "mb-1.5 border-b pb-1.5"
                                    )}
                                >
                                    <span className="mt-px inline-block">
                                        {t(
                                            replaceAll(
                                                key === "nric" ? "NRIC" : key,
                                                "_",
                                                " "
                                            )
                                        )}
                                    </span>
                                </p>
                            )}
                            {key === "photo" ? (
                                value ? (
                                    <div className="relative col-span-3 mb-2 min-h-10">
                                        {isLoadingPhoto && (
                                            <Loader2 className="absolute left-3 top-3 animate-spin text-themeGreenDark" />
                                        )}
                                        <Image
                                            src={value}
                                            alt=""
                                            width={200}
                                            height={200}
                                            className="mb-2 h-auto w-auto max-w-[200px] rounded-sm"
                                            onLoad={() => {
                                                setIsLoadingPhoto(false);
                                            }}
                                            onError={() =>
                                                setIsLoadingPhoto(false)
                                            }
                                        />
                                    </div>
                                ) : (
                                    <></>
                                )
                            ) : (
                                <p
                                    className={clsx(
                                        "col-span-2 pl-2 font-medium",
                                        hasBorder(index) &&
                                            "mb-1.5 border-b pb-1.5"
                                    )}
                                >
                                    {isNumber(value)
                                        ? value
                                        : isEmpty(value)
                                          ? "-"
                                          : value}
                                </p>
                            )}
                        </Fragment>
                    );
                })}
            </div>
        </div>
    ) : (
        <></>
    );
};

export default InfoCard;
