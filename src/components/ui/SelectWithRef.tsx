import React from "react";
import * as SelectPrimitive from "@radix-ui/react-select";
import clsx from "clsx";
import { capitalize } from "lodash";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/base-ui/select";

type SelectWithRefProps = {
    label: string;
    options: any;
    hasEmptyOption?: boolean;
    isForm?: boolean;
};

const SelectWithRef = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.Trigger>,
    SelectWithRefProps
>(({ label, options = [], hasEmptyOption = false, isForm = false }, ref) => {
    return (
        <Select>
            <SelectTrigger
                ref={ref || null}
                className={clsx(
                    !isForm && "bg-gray-50 font-bold text-themeGreenDark",
                    "w-fit"
                )}
                isForm={isForm}
            >
                <SelectValue placeholder={label} />
            </SelectTrigger>
            <SelectContent>
                {hasEmptyOption && (
                    <SelectItem value={""} className="opacity-60">
                        {label}
                    </SelectItem>
                )}
                {options?.map((opt) => (
                    <SelectItem
                        key={opt?.value?.toString() || opt}
                        value={opt?.value?.toString() || opt}
                    >
                        {capitalize(opt?.name || opt)}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    );
});

SelectWithRef.displayName = "SelectWithRef";

export default SelectWithRef;
