import clsx from "clsx";
import { Controller } from "react-hook-form";
import { getInputErrorMessage } from "@/lib/utils";
import { Textarea } from "../base-ui/textarea";

const FreeTextArea = ({
    control,
    name,
    isSmaller = true,
    error,
    onChange = (val) => {},
}: {
    control: any;
    name: string;
    isSmaller?: boolean;
    error?: any;
    onChange?: (val: any) => void;
}) => {
    return control ? (
        <div>
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <Textarea
                        {...field}
                        onChange={(e) => {
                            e.preventDefault();
                            const value = e.target.value;
                            field.onChange(value);
                            onChange && onChange(value);
                        }}
                        className={clsx(isSmaller && "textarea-h-sm")}
                    />
                )}
            />
            {error && (
                <div className="warning-text">
                    {`${getInputErrorMessage(error)}`}
                </div>
            )}
        </div>
    ) : (
        <></>
    );
};

export default FreeTextArea;
