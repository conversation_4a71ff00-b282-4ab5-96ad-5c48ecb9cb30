import React, { useCallback, useState } from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import { Eye, EyeOff } from "lucide-react";
import { Control, Controller } from "react-hook-form";
import { Input } from "@/components/base-ui/input";
import { password } from "@/lib/constant";
import {
    getDecimalValues,
    getInputErrorMessage,
    replaceAll,
} from "@/lib/utils";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FreeInputProps = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    type?: string;
    min?: any;
    max?: any;
    isSearch?: boolean;
    hasLabel?: boolean;
    suffixIcon?: string;
    onClickIcon?: () => void;
    error?: any;
    onChange?: (val: any) => void;
    isDecimal?: boolean;
    isSmallerX?: boolean;
    isSmallerY?: boolean;
    styleClass?: string;
    rules?: any;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
    onMouseDown?: (e: React.MouseEvent<HTMLInputElement>) => void;
};

const FreeInput = ({
    control,
    name,
    label,
    placeholder,
    disabled = false,
    type = "text",
    min = null,
    max = null,
    isSearch,
    hasLabel = true,
    suffixIcon,
    onClickIcon,
    error,
    onChange = (val) => {},
    isDecimal,
    isSmallerX = false,
    isSmallerY = false,
    styleClass = "",
    rules,
    onKeyDown,
    onBlur,
    onMouseDown,
}: FreeInputProps) => {
    const t = useTranslations("common");

    const [InputType, setInputType] = useState(type);
    const isPassword = type === password;

    return control ? (
        <Controller
            control={control}
            name={name}
            rules={rules}
            render={({ field }) => (
                <div className={clsx("relative", styleClass)}>
                    {hasLabel && (
                        <div className="mb-1.5">
                            <Label className="capitalize">
                                {t(replaceAll(label || name, "_", " "))}
                            </Label>
                        </div>
                    )}

                    <Input
                        placeholder={
                            placeholder?.includes("OTP")
                                ? placeholder
                                : capitalize(placeholder ?? "")
                        }
                        disabled={disabled}
                        type={InputType}
                        minLength={min}
                        maxLength={max}
                        suffixIcon={suffixIcon}
                        onClickIcon={onClickIcon}
                        {...field}
                        onChange={(e) => {
                            e.preventDefault();
                            let value = e.target.value;
                            if (isDecimal) {
                                value = getDecimalValues(e.target.value);
                            }
                            field.onChange(value);
                            onChange(value);
                        }}
                        onKeyDown={onKeyDown}
                        onBlur={onBlur}
                        onMouseDown={onMouseDown}
                        className={clsx(
                            isSearch && "search-input",
                            isPassword && "pr-9",
                            isSmallerX && "px-2",
                            isSmallerY && "h-[38px] py-1"
                        )}
                    />
                    {error && (
                        <div className="warning-text">
                            {`${getInputErrorMessage(error)}`}
                        </div>
                    )}
                    {isPassword &&
                        (InputType === password ? (
                            <Eye
                                size={20}
                                className="absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-600"
                                onClick={() => setInputType("text")}
                            />
                        ) : (
                            <EyeOff
                                size={20}
                                className="absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-600"
                                onClick={() => setInputType(password)}
                            />
                        ))}
                </div>
            )}
        />
    ) : (
        <></>
    );
};

export default FreeInput;
