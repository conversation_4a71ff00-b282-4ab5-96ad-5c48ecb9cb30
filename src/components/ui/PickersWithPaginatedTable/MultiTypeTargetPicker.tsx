import { useState } from "react";
import { flatten } from "lodash";
import { X } from "lucide-react";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import TrainerSearchEngine from "@/components/ui/search-engines/TrainerSearchEngine";
import {
    STUDENT,
    EMPLOYEE,
    CONTRACTOR,
    TableColumnType,
    MULTI,
} from "@/lib/constant";
import { usePaginatedTable } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import { Label } from "../../base-ui/label";
import PaginatedTableFilter from "../PaginatedTableFilter";

const MultiTypeTargetPicker = ({
    title,
    targetsChunk,
    setTargetsChunk,
    targetTypes,
    columns,
}: {
    title: string;
    targetsChunk: any[];
    setTargetsChunk: any;
    targetTypes: any[];
    columns?: TableColumnType[];
}) => {
    const [openSearch, setOpenSearch] = useState<
        typeof STUDENT | typeof EMPLOYEE | typeof CONTRACTOR | null
    >(null);

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
        resetClonedFilter,
    } = usePaginatedTable({
        targetsChunk,
        setTargetsChunk,
        targetType: MULTI,
    });

    const _columns: TableColumnType[] = [
        {
            key: "_",
            modify: (value, cell) => {
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            onRemove(cell.row.original?.id);
                        }}
                    />
                );
            },
        },
        ...(columns ?? [
            { key: "type" },
            { key: "number", hasSort: true },
            { key: "name", hasSort: true },
        ]),
    ];

    return (
        <div>
            <div className="mb-1 flex gap-x-2">
                <Label className="label">{title}</Label>
                {targetsChunk?.length > 0 && (
                    <div className="mt-0.5 text-xs text-gray-500">
                        <span className="font-semibold">
                            {flatten(targetsChunk)?.length}
                        </span>{" "}
                        in total
                    </div>
                )}
            </div>
            <div className="mb-4 flex flex-wrap gap-3 lg:col-span-2">
                {targetTypes.includes(STUDENT) && (
                    <Button
                        variant={"outline"}
                        size={"smaller"}
                        onClick={() => setOpenSearch(STUDENT)}
                    >
                        Select Students
                    </Button>
                )}
                {targetTypes.includes(EMPLOYEE) && (
                    <Button
                        variant={"outline"}
                        size={"smaller"}
                        onClick={() => setOpenSearch(EMPLOYEE)}
                    >
                        Select Employees
                    </Button>
                )}
                {targetTypes.includes(CONTRACTOR) && (
                    <Button
                        variant={"outline"}
                        size={"smaller"}
                        onClick={() => setOpenSearch(CONTRACTOR)}
                    >
                        Select Contractors
                    </Button>
                )}
                {targetsChunk?.length > 0 && (
                    <PaginatedTableFilter
                        type={MULTI}
                        filter={filter}
                        setFilter={setFilter}
                        reset={resetClonedFilter}
                    />
                )}
            </div>

            {targetsChunk?.length > 0 && pagination && (
                <DataTable
                    isSmaller
                    columns={_columns}
                    data={
                        (clonedFilteredChunk ?? targetsChunk)[
                            pagination?.current_page - 1
                        ]
                    }
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    hasPerPage={false}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            )}

            {targetTypes.includes(STUDENT) && (
                <Modal
                    open={openSearch === STUDENT}
                    onOpenChange={setOpenSearch}
                    size="large"
                >
                    <StudentSearchEngine
                        isMultiSelect={true}
                        setSelection={(value) => onAdd(value, STUDENT)}
                        close={() => setOpenSearch(null)}
                    />
                </Modal>
            )}

            {targetTypes.includes(EMPLOYEE) && (
                <Modal
                    open={openSearch === EMPLOYEE}
                    onOpenChange={setOpenSearch}
                    size="large"
                >
                    <StaffSearchEngine
                        isMultiSelect={true}
                        setSelection={(value) => onAdd(value, EMPLOYEE)}
                        close={() => setOpenSearch(null)}
                    />
                </Modal>
            )}

            {targetTypes.includes(CONTRACTOR) && (
                <Modal
                    open={openSearch === CONTRACTOR}
                    onOpenChange={setOpenSearch}
                    size="large"
                >
                    <TrainerSearchEngine
                        isMultiSelect={true}
                        setSelection={(value) => onAdd(value, CONTRACTOR)}
                        close={() => setOpenSearch(null)}
                    />
                </Modal>
            )}
        </div>
    );
};

export default MultiTypeTargetPicker;
