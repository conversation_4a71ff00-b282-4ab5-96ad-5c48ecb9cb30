import { useState } from "react";
import { flatten } from "lodash";
import { X } from "lucide-react";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import { STUDENT, TableColumnType } from "@/lib/constant";
import { usePaginatedTable } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import { Label } from "../../base-ui/label";
import PaginatedTableFilter from "../PaginatedTableFilter";
import { useTranslations } from "next-intl";

const StudentsPicker = ({
    targetsChunk,
    setTargetsChunk,
    columns,
    errorMessage,
    otherFilterParams,
    showPrimaryClass,
    label,
}: {
    targetsChunk: any[];
    setTargetsChunk: any;
    columns?: TableColumnType[];
    errorMessage?: any;
    otherFilterParams?: Record<string, any>;
    showPrimaryClass?: boolean;
    label?: string;
}) => {
    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
        resetClonedFilter,
    } = usePaginatedTable({
        targetsChunk,
        setTargetsChunk,
        targetType: STUDENT,
        showPrimaryClass: showPrimaryClass,
    });

    const _columns: TableColumnType[] = [
        {
            key: "_",
            modify: (_, cell) => {
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => onRemove(cell.row.original?.id)}
                    />
                );
            },
        },
        ...(columns ?? [
            {
                key: "student_number",
                hasSort: true,
            },
            {
                key: "name",
                hasSort: true,
            },
            { key: "primary_class" },
        ]),
    ];

    const [open, setOpen] = useState(false);

    const t = useTranslations("common");

    return (
        <div>
            <div className="mb-0.5 flex gap-x-2">
                <Label className="label">{t(label ?? "Students")}</Label>
                {targetsChunk?.length > 0 && (
                    <div className="mt-0.5 text-xs text-gray-500">
                        <span className="font-semibold">
                            {flatten(targetsChunk)?.length}
                        </span>{" "}
                        {t("in total")}
                    </div>
                )}
            </div>
            <div className="mb-4 flex justify-between gap-5">
                <Button
                    variant={"outline"}
                    size={"smaller"}
                    onClick={() => setOpen(true)}
                >
                    {t("Select Students")}
                </Button>

                {targetsChunk?.length > 0 && (
                    <PaginatedTableFilter
                        type={STUDENT}
                        filter={filter}
                        setFilter={setFilter}
                        reset={resetClonedFilter}
                    />
                )}
            </div>

            {errorMessage && (
                <p className="warning-text mb-2">{`${errorMessage}`}</p>
            )}

            {targetsChunk?.length > 0 && pagination && (
                <DataTable
                    isSmaller
                    columns={
                        showPrimaryClass
                            ? _columns
                            : _columns.filter(
                                  (column) => column.key !== "primary_class"
                              )
                    }
                    data={
                        (clonedFilteredChunk ?? targetsChunk)[
                            pagination?.current_page - 1
                        ]
                    }
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    hasPerPage={false}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            )}

            <Modal open={open} onOpenChange={setOpen} size="large">
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(value) => onAdd(value, STUDENT)}
                    close={() => setOpen(false)}
                    otherFilterParams={otherFilterParams}
                />
            </Modal>
        </div>
    );
};

export default StudentsPicker;
