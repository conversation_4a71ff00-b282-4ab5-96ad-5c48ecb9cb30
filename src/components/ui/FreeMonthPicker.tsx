import React, { useState } from "react";
import {
    CalendarIcon,
    ChevronLeftCircleIcon,
    ChevronRightCircleIcon,
} from "lucide-react";
import { Controller } from "react-hook-form";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/base-ui/popover";
import { monthPickerLang } from "@/lib/constant";
import { cn, getInputErrorMessage, replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

const FreeMonthPicker = ({
    control,
    name,
    label,
    onChange,
    placeholder = "",
    hasLabel = true,
    showSelection = true,
    isDisabled = false,
    isSmaller = false,
    error,
    disableDateBefore,
}: {
    control: any;
    name: string;
    label?: string;
    onChange?: (value: { year: number; month: number }) => void;
    placeholder?: string;
    hasLabel?: boolean;
    showSelection?: boolean;
    isDisabled?: boolean;
    isSmaller?: boolean;
    error?: any;
    disableDateBefore?: Date;
}) => {
    const t = useTranslations("common");
    const tMonth = useTranslations("months");

    const [isOpen, setIsOpen] = useState(false);

    const [selectedYearMonth, setSelectedYearMonth] = useState<{
        year: number;
        month: number;
    }>({ year: new Date().getFullYear(), month: new Date().getMonth() + 1 });

    const handleSelect = (value: { year: number; month: number }) => {
        setSelectedYearMonth(value);
        onChange && onChange(value);
        setIsOpen(false);
    };

    const formatMonth = (value: { year: number; month: number } | null) =>
        value
            ? `${tMonth(monthPickerLang.months[value.month - 1])} ${value.year}`
            : placeholder;

    const changeYear = (direction: "prev" | "next", field) => {
        let newYearMonth = selectedYearMonth;

        setSelectedYearMonth((prev) => {
            const newYear =
                direction === "prev" ? prev.year - 1 : prev.year + 1;

            newYearMonth = { ...prev, year: newYear };

            return newYearMonth;
        });

        field.onChange(newYearMonth);
        onChange && onChange(newYearMonth);
    };

    const isBeforeDisabledDate = (year: number, month: number) => {
        if (!disableDateBefore) return false;
        const current = new Date(year, month - 1);
        return current < disableDateBefore;
    };

    return control ? (
        <div className="flex flex-col">
            {hasLabel && (
                <Label className={"label"}>
                    {t(replaceAll(label || name, "_", " "))}
                </Label>
            )}

            <Controller
                control={control}
                name={name}
                render={({ field }) => {
                    if (
                        field.value &&
                        (field.value.year !== selectedYearMonth.year ||
                            field.value.month !== selectedYearMonth.month)
                    ) {
                        setTimeout(() => {
                            setSelectedYearMonth(field.value);
                        }, 0);
                    }
                    return (
                        <Popover open={isOpen} onOpenChange={setIsOpen}>
                            <PopoverTrigger asChild>
                                <Button
                                    onClick={() => setIsOpen(true)}
                                    variant={"outlineGray"}
                                    size={isSmaller ? "smaller" : "primary"}
                                    className={
                                        "w-full gap-x-2 rounded-md border-input px-4 text-left font-normal"
                                    }
                                    disabled={isDisabled}
                                >
                                    {showSelection && field.value ? (
                                        tMonth(formatMonth(field.value))
                                    ) : (
                                        <span className="text-themeLabel">
                                            {placeholder ?? ""}
                                        </span>
                                    )}

                                    <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent
                                className="w-auto rounded-sm border bg-white shadow-md"
                                align="start"
                            >
                                {/* Year Navigation */}
                                <div className="relative mb-2 flex items-center justify-center gap-6 text-sm leading-none">
                                    <ChevronLeftCircleIcon
                                        className="cursor-pointer text-themeGreen transition hover:text-themeGreen2"
                                        onClick={() =>
                                            changeYear("prev", field)
                                        }
                                    />
                                    <span>{selectedYearMonth.year}</span>

                                    <ChevronRightCircleIcon
                                        className="cursor-pointer text-themeGreen transition hover:text-themeGreen2"
                                        onClick={() =>
                                            changeYear("next", field)
                                        }
                                    />
                                </div>

                                {/* Month Grid */}
                                <div className="grid grid-cols-3 justify-items-center gap-1">
                                    {monthPickerLang.months.map(
                                        (month, index) => {
                                            const monthValue = index + 1;
                                            const isSelected =
                                                selectedYearMonth.month ===
                                                monthValue;
                                            const isDisabledMonth =
                                                isDisabled ||
                                                isBeforeDisabledDate(
                                                    selectedYearMonth.year,
                                                    monthValue
                                                );

                                            return (
                                                <div
                                                    key={index}
                                                    className={cn(
                                                        "relative flex h-8 w-10 cursor-pointer items-center justify-center whitespace-nowrap rounded-md text-[13px] leading-none text-gray-700",
                                                        {
                                                            "w-12 border border-themeGreen font-medium text-themeGreen":
                                                                isSelected,
                                                            "text-muted-foreground opacity-50":
                                                                isDisabledMonth,
                                                            "hover:bg-themeGreen hover:bg-opacity-10":
                                                                !isSelected &&
                                                                !isDisabledMonth,
                                                        }
                                                    )}
                                                    onClick={() => {
                                                        if (isDisabledMonth)
                                                            return;
                                                        const newSelectedYearMonth =
                                                            {
                                                                year: selectedYearMonth.year,
                                                                month: monthValue,
                                                            };
                                                        handleSelect(
                                                            newSelectedYearMonth
                                                        );
                                                        field.onChange(
                                                            newSelectedYearMonth
                                                        );
                                                    }}
                                                >
                                                    {tMonth(month)}
                                                </div>
                                            );
                                        }
                                    )}
                                </div>
                            </PopoverContent>
                        </Popover>
                    );
                }}
            />

            {error && (
                <div className="warning-text">
                    {`${getInputErrorMessage(error)}`}
                </div>
            )}
        </div>
    ) : null;
};

export default FreeMonthPicker;
