import { useEffect, useState } from "react";
import { capitalize, isArray, isEmpty } from "lodash";
import { ArrowDown, Plus, X } from "lucide-react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import {
    BED,
    EMPLOYEE,
    hostelBedAssignmentAPI,
    hostelBedChangeAPI,
    hostelBedUnassignmentAPI,
    hostelBulkBedAssignmentAPI,
    hostelBulkBedAssignmentImportAPI,
    hostelBulkBedAssignmentTemplateAPI,
    OCCUPIED,
    STUDENT,
    TableColumnType,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    bedInfoDisplay,
    combinedNames,
    downloadFile,
    employeeInfoDisplay,
    studentInfoDisplay,
    toYMD,
} from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Form } from "../base-ui/form";
import Card from "./Card";
import DataTable from "./DataTable";
import { DatePicker } from "./DatePicker";
import FileInputExcel from "./FileInputExcel";
import FormDivider from "./FormDivider";
import FormTextarea from "./FormTextarea";
import FreeTextArea from "./FreeTextArea";
import InfoDisplay from "./InfoDisplay";
import Modal from "./Modal";
import RaisedButton from "./RaisedButton";
import Tabs from "./Tabs";
import BedSearchEngine from "./search-engines/BedSearchEngine";
import StaffSearchEngine from "./search-engines/StaffSearchEngine";
import StudentSearchEngine from "./search-engines/StudentSearchEngine";
import OutlinedX from "./OutlinedX";
import { useLocale } from "use-intl";
import { useTranslations } from "next-intl";

const _assign_beds = "assign beds";
const _bulk_bed_assignment = "bulk bed assignment";
const _unassign_beds = "unassign beds";
const _change_beds = "change beds";

const BedAssignmentForms = ({
    type,
}: {
    type: typeof STUDENT | typeof EMPLOYEE;
}) => {
    const userProfile = useUserProfile((state) => state.userProfile);

    return userProfile ? <BedAssignmentWrap type={type} /> : <></>;
};

const BedAssignmentWrap = ({
    type,
}: {
    type: typeof STUDENT | typeof EMPLOYEE;
}) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const tabList = [
        hasPermit("hostel-bed-assignment-assign") ? _assign_beds : null,
        hasPermit("hostel-bed-assignment-bulk-assignment")
            ? _bulk_bed_assignment
            : null,
        hasPermit("hostel-bed-assignment-unassign") ? _unassign_beds : null,
        hasPermit("hostel-bed-assignment-change") ? _change_beds : null,
    ].filter((tab) => tab);

    const displayTabs = tabList.map((key) => t(key));

    const [assignType, setAssignType] = useState(tabList[0]);

    return (
        <Card styleClass="table-card">
            <h2 className="mb-5 capitalize">
                {t(capitalize(type) + " ")}
                {t("bed assignment")}
            </h2>

            <Tabs
                list={displayTabs}
                selected={t(assignType)}
                setSelected={(label) => {
                    const selectedKey = tabList.find((key) => t(key) === label);
                    if (selectedKey) setAssignType(selectedKey);
                }}
            />

            <div className="flex flex-col gap-y-3 pb-3">
                {assignType === _assign_beds && <AssignForm type={type} />}
                {assignType === _bulk_bed_assignment && (
                    <BulkBedAssignForm type={type} />
                )}
                {assignType === _unassign_beds && <UnassignForm type={type} />}
                {assignType === _change_beds && <ChangeForm type={type} />}
            </div>
        </Card>
    );
};

const AssignForm = ({ type }) => {
    const t = useTranslations("common");
    const [popUp, setPopUp] = useState<string | null>(null);
    const [assignedItems, setAssignedItems] = useState<any[]>([]);

    const { axiosPost: assignBeds } = useAxios({
        api: hostelBedAssignmentAPI,
        onSuccess: () => onClear(),
        toastMsg: t("Assigned successfully"),
    });

    function onClear() {
        setAssignedItems([]);
    }

    function onAddItem(data: any[]) {
        setPopUp(null);
        setAssignedItems([...assignedItems, data]);
    }

    function onRemoveItem(data) {
        const newList = [...assignedItems].filter(
            (item) => item?.person?.id !== data?.id
        );
        setAssignedItems(newList);
    }

    function getFormattedData() {
        return assignedItems?.map((item) => {
            const newItem: Record<string, any> = {
                id: item?.person?.id,
                name: item?.person?.name,
                assigned_bed: `${item?.bed?.name} - ${item?.bed?.hostel_room?.hostel_block?.name} - ${item?.bed?.hostel_room?.name}`,
                start_date: item?.start_date,
            };
            if (type === STUDENT) {
                newItem.student_number = item?.person?.student_number;
            }
            if (type === EMPLOYEE) {
                newItem.employee_number = item?.person?.employee_number;
            }

            return newItem;
        });
    }

    function onAssignBeds() {
        const assignedData = assignedItems.map((item) => {
            const newItem: Record<string, any> = {
                id: item.person?.id,
            };
            newItem.hostel_room_bed_id = item.bed?.id;
            newItem.start_date = item?.start_date;
            return newItem;
        });

        const data: Record<string, any> = {};
        if (type === STUDENT) {
            data.students = assignedData;
        }
        if (type === EMPLOYEE) {
            data.employees = assignedData;
        }

        assignBeds(data);
    }

    return (
        <div>
            <Button
                variant={"outline"}
                className="mb-4 flex gap-x-1"
                onClick={() => setPopUp(type)}
            >
                <Plus size={18} className="-ml-2 text-themeGreen" />{" "}
                <span>{t("Add")}</span>
            </Button>

            {assignedItems?.length > 0 && (
                <InfoDisplay
                    title={t("Selected ") + t("Assignment")}
                    data={getFormattedData()}
                    onRemove={onRemoveItem}
                    hiddenFields={["id"]}
                    smallerFields={["start_date"]}
                />
            )}

            <FormDivider />

            <div className="mt-4 flex justify-end gap-x-4">
                <Button variant="ghost" onClick={onClear}>
                    {t("Clear")}
                </Button>
                <Button
                    onClick={onAssignBeds}
                    className="min-w-[120px]"
                    disabled={!(assignedItems?.length > 0)}
                >
                    {t("Submit")}
                </Button>
            </div>

            <Modal open={popUp} onOpenChange={setPopUp} size="medium">
                <AddAssignedItemForm
                    type={type}
                    onAddItem={onAddItem}
                    assignedItems={assignedItems}
                />
            </Modal>
        </div>
    );
};

const AddAssignedItemForm = ({
    type,
    onAddItem,
    assignedItems,
}: {
    type: typeof STUDENT | typeof EMPLOYEE;
    onAddItem: (data: Record<string, any>) => void;
    assignedItems: any[];
}) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            start_date: new Date(),
        },
    });

    const [selectedPerson, setSelectedPerson] = useState<any | null>();
    const [selectedBed, setSelectedBed] = useState<any | null>();
    const [popUp, setPopUp] = useState<string | null>();
    const [warning, setWarning] = useState<string | null>(null);

    function onClear() {
        setSelectedPerson(null);
        setSelectedBed(null);
        setWarning(null);
    }

    function onSubmit() {
        setWarning(null);
        form.handleSubmit((data) => {
            if (selectedPerson && data.start_date && selectedBed) {
                const assignedPersonIds = assignedItems.map(
                    (item) => item?.person?.id
                );
                const assignedBedIds = assignedItems.map(
                    (item) => item?.bed?.id
                );

                if (assignedPersonIds.includes(selectedPerson?.id)) {
                    setWarning(
                        t("selected_already_added", {
                            item: type.toLowerCase(),
                        })
                    );
                    return;
                }
                if (assignedBedIds.includes(selectedBed?.id)) {
                    setWarning(
                        t("selected_already_added", {
                            item: "bed",
                        })
                    );
                    return;
                }
                if (selectedBed.status === OCCUPIED) {
                    setWarning(t("selected bed is occupied"));
                    return;
                }

                const newData = {
                    person: selectedPerson,
                    bed: selectedBed,
                    start_date: toYMD(data.start_date),
                };
                onAddItem(newData);
            } else {
                setWarning(
                    t("Please make sure all required selection are made")
                );
            }
        })();
    }

    return (
        <div className="flex flex-col gap-y-3">
            <div>
                <div className="c-text-size mb-3 mt-2 font-medium normal-case text-themeLabel">
                    {t("Select a ")}
                    {t(type.toLowerCase() + " ")}
                    {t("and the bed to be assigned")}
                </div>
                <div className="mb-1 flex gap-x-3">
                    <RaisedButton
                        name={t("Select ") + capitalize(t(type))}
                        onClick={() => {
                            setPopUp(type);
                            setWarning(null);
                        }}
                    />
                    <RaisedButton
                        name={t("Select ") + t("Bed")}
                        onClick={() => {
                            setPopUp(BED);
                            setWarning(null);
                        }}
                    />
                </div>
            </div>

            {selectedPerson &&
                (type === STUDENT ? (
                    <InfoDisplay
                        title={t("Selected ") + t(`Student`)}
                        data={[studentInfoDisplay(selectedPerson)]}
                    />
                ) : (
                    <InfoDisplay
                        title={t("Selected ") + t(`Employee`)}
                        data={[employeeInfoDisplay(selectedPerson)]}
                    />
                ))}

            {selectedBed && (
                <InfoDisplay
                    title={t("Selected ") + t(`Bed`)}
                    data={[bedInfoDisplay(selectedBed)]}
                />
            )}

            <FormDivider />

            <Form {...form}>
                <form className="w-[200px]">
                    <DatePicker
                        control={form.control}
                        name="start_date"
                        onChange={() => setWarning(null)}
                    />
                </form>
            </Form>

            <Modal
                open={popUp === STUDENT}
                onOpenChange={setPopUp}
                size="large"
            >
                <StudentSearchEngine
                    isHostel={true}
                    hasActiveBed={false}
                    setSelection={setSelectedPerson}
                    close={() => setPopUp(null)}
                    reset={() => setSelectedPerson(null)}
                />
            </Modal>

            <Modal
                open={popUp === EMPLOYEE}
                onOpenChange={setPopUp}
                size="large"
            >
                <StaffSearchEngine
                    isHostel={true}
                    hasActiveBed={false}
                    setSelection={setSelectedPerson}
                    close={() => setPopUp(null)}
                    reset={() => setSelectedPerson(null)}
                />
            </Modal>

            <Modal open={popUp === BED} onOpenChange={setPopUp} size="large">
                <BedSearchEngine
                    setSelection={setSelectedBed}
                    close={() => setPopUp(null)}
                    hostelType={type}
                />
            </Modal>

            {warning && (
                <div className="mt-1 text-right text-xs text-destructive lg:text-sm">
                    {warning}
                </div>
            )}

            <div className="mt-1 flex justify-end gap-x-4">
                <Button variant="ghost" onClick={onClear}>
                    {t("Clear")}
                </Button>
                <Button onClick={onSubmit} className="min-w-[120px]">
                    {t("Add")}
                </Button>
            </div>
        </div>
    );
};

const UnassignForm = ({ type }) => {
    const t = useTranslations("common");
    const [popUp, setPopUp] = useState<string | null>(null);
    const [unassignedItems, setUnassignedItems] = useState<any[]>([]);

    const { axiosPost: unassignBeds } = useAxios({
        api: hostelBedUnassignmentAPI,
        onSuccess: () => onClear(),
        toastMsg: t("Unassigned successfully"),
    });

    function onClear() {
        setUnassignedItems([]);
    }

    function onAddItem(data: any[]) {
        setPopUp(null);
        setUnassignedItems([...unassignedItems, ...data]);
    }

    function onRemoveItem(id) {
        const newList = [...unassignedItems].filter(
            (item) => item?.person?.id !== id
        );
        setUnassignedItems(newList);
    }

    function getFormattedData() {
        return unassignedItems?.map((item) => {
            const newItem: Record<string, any> = {
                id: item?.person?.id,
                name: combinedNames(item?.person?.translations?.name),
                number:
                    item?.person?.student_number ??
                    item?.person?.employee_number ??
                    "-",
                end_date: item?.end_date,
                block: item?.person?.active_hostel_bed_assignment?.bed
                    ?.hostel_room?.hostel_block?.name,
                room: item?.person?.active_hostel_bed_assignment?.bed
                    ?.hostel_room?.name,
                bed: item?.person?.active_hostel_bed_assignment?.bed?.name,
                remarks: item?.remarks ?? "-",
            };
            return newItem;
        });
    }

    function onUnassignBeds() {
        const assignedData = unassignedItems.map((item) => {
            const newItem: Record<string, any> = {
                id: item.person?.id,
            };
            newItem.end_date = item?.end_date;
            newItem.remarks = item?.remarks;
            return newItem;
        });

        const data: Record<string, any> = {};
        if (type === STUDENT) {
            data.students = assignedData;
        }
        if (type === EMPLOYEE) {
            data.employees = assignedData;
        }
        unassignBeds(data);
    }

    return (
        <div>
            <Button
                variant={"outline"}
                className="mb-4 flex gap-x-1"
                onClick={() => setPopUp(type)}
            >
                <Plus size={18} className="-ml-2 text-themeGreen" />{" "}
                <span>{t("Add")}</span>
            </Button>

            {unassignedItems?.length > 0 && (
                <>
                    <h3 className="mb-2">
                        {t("Selected ") + t("Unassignment")}
                    </h3>
                    <DataTable
                        columns={[
                            {
                                key: "id",
                                displayAs: " ",
                                modify: (value) => (
                                    <div className="flex justify-center">
                                        <OutlinedX
                                            onClick={() => onRemoveItem(value)}
                                        />
                                    </div>
                                ),
                            },
                            { key: "name" },
                            {
                                key: "number",
                                displayAs:
                                    type === STUDENT
                                        ? "Student Number"
                                        : "Employee Number",
                            },
                            { key: "end_date" },
                            { key: "block" },
                            { key: "room" },
                            { key: "bed" },
                            {
                                key: "remarks",
                                modify: (value) =>
                                    isEmpty(value) ? "-" : value,
                            },
                        ]}
                        data={getFormattedData()}
                    />
                </>
            )}

            <div className="mt-5 flex justify-end gap-x-4">
                <Button variant="ghost" onClick={onClear}>
                    {t("Clear")}
                </Button>
                <Button
                    onClick={onUnassignBeds}
                    className="min-w-[120px]"
                    disabled={!(unassignedItems?.length > 0)}
                >
                    {t("Submit")}
                </Button>
            </div>

            <Modal open={popUp} onOpenChange={setPopUp} size="medium">
                <AddUnassignedItemForm
                    type={type}
                    onAddItem={onAddItem}
                    unassignedItems={unassignedItems}
                />
            </Modal>
        </div>
    );
};

const AddUnassignedItemForm = ({
    type,
    onAddItem,
    unassignedItems,
}: {
    type: typeof STUDENT | typeof EMPLOYEE;
    onAddItem: (data: any[]) => void;
    unassignedItems: any[];
}) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            end_date: new Date(),
            remarks: "",
        },
    });

    const [selectedPersons, setSelectedPersons] = useState<any[]>([]);
    const [popUp, setPopUp] = useState<string | null>();
    const [warning, setWarning] = useState<string | null>(null);

    function onClear() {
        setSelectedPersons([]);
        setWarning(null);
    }

    function remove(id) {
        setSelectedPersons(
            [...selectedPersons].filter((person) => person.id != id)
        );
    }

    function onSubmit() {
        setWarning(null);
        form.handleSubmit((data) => {
            if (selectedPersons?.length > 0 && data.end_date) {
                const unassignedItemsIds = unassignedItems.map(
                    (item) => item.person.id
                );
                const isAdded: boolean =
                    selectedPersons.filter((person) =>
                        unassignedItemsIds.includes(person.id)
                    )?.length > 0;
                if (isAdded) {
                    setWarning(
                        t("one_or_more_selected_already_added", {
                            item: type.toLowerCase(),
                        })
                    );
                    return;
                }
                const newData = selectedPersons.map((person) => ({
                    person: person,
                    end_date: toYMD(data.end_date),
                    remarks: data.remarks,
                }));
                onAddItem(newData);
            } else {
                setWarning(
                    t("Please make sure all required selection are made")
                );
            }
        })();
    }

    return (
        <div className="flex flex-col gap-y-3">
            <div>
                <div className="c-text-size mb-3 mt-2 font-medium text-themeLabel">
                    {t("Select ") +
                        t(type.toLowerCase()) +
                        t("to be unassigned")}
                </div>
                <div className="mb-1 flex gap-x-3">
                    <RaisedButton
                        name={t("Select ") + capitalize(t(type))}
                        onClick={() => {
                            setPopUp(type);
                            setWarning(null);
                        }}
                    />
                </div>
            </div>

            {selectedPersons?.length > 0 &&
                (type === STUDENT ? (
                    <DataTable
                        columns={[
                            {
                                key: "id",
                                displayAs: " ",
                                modify: (value) => (
                                    <OutlinedX
                                        onClick={(id) => remove(value)}
                                    />
                                ),
                            },
                            { key: "name" },
                            { key: "student_number" },
                            { key: "gender" },
                            { key: "bed" },
                            { key: "room" },
                            { key: "block" },
                        ]}
                        data={selectedPersons.map((person) => ({
                            id: person.id,
                            ...studentInfoDisplay(person, true),
                        }))}
                    />
                ) : (
                    <DataTable
                        columns={[
                            {
                                key: "id",
                                displayAs: " ",
                                modify: (value) => (
                                    <OutlinedX
                                        onClick={(id) => remove(value)}
                                    />
                                ),
                            },
                            { key: "name" },
                            { key: "employee_number" },
                            { key: "gender" },
                            { key: "bed" },
                            { key: "room" },
                            { key: "block" },
                        ]}
                        data={selectedPersons.map((person) => ({
                            id: person.id,
                            ...employeeInfoDisplay(person, true),
                        }))}
                    />
                ))}

            <Form {...form}>
                <form className="mt-1 grid gap-y-3">
                    <div className="w-[200px]">
                        <DatePicker
                            control={form.control}
                            name="end_date"
                            onChange={() => setWarning(null)}
                        />
                    </div>
                    <FormTextarea control={form.control} name="remarks" />
                </form>
            </Form>

            <Modal
                open={popUp === STUDENT}
                onOpenChange={setPopUp}
                size="large"
            >
                <StudentSearchEngine
                    isHostel={true}
                    isMultiSelect={true}
                    hasActiveBed={true}
                    setSelection={setSelectedPersons}
                    close={() => setPopUp(null)}
                    reset={() => setSelectedPersons([])}
                />
            </Modal>

            <Modal
                open={popUp === EMPLOYEE}
                onOpenChange={setPopUp}
                size="large"
            >
                <StaffSearchEngine
                    isHostel={true}
                    isMultiSelect={true}
                    hasActiveBed={true}
                    setSelection={setSelectedPersons}
                    close={() => setPopUp(null)}
                    reset={() => setSelectedPersons([])}
                />
            </Modal>

            {warning && (
                <div className="mt-1 text-right text-xs text-destructive lg:text-sm">
                    {warning}
                </div>
            )}

            <div className="mt-2 flex justify-end gap-x-4">
                <Button variant="ghost" onClick={onClear}>
                    {t("Clear")}
                </Button>
                <Button onClick={onSubmit} className="min-w-[120px]">
                    {t("Add")}
                </Button>
            </div>
        </div>
    );
};

const ChangeForm = ({ type }) => {
    const t = useTranslations("common");
    const [popUp, setPopUp] = useState<string | null>(null);
    const [changedItems, setChangedItems] = useState<any[]>([]);

    const { axiosPost: assignBeds } = useAxios({
        api: hostelBedChangeAPI,
        onSuccess: () => onClear(),
        toastMsg: t("Changed successfully"),
    });

    function onClear() {
        setChangedItems([]);
    }

    function onAddItem(data: any[]) {
        setPopUp(null);
        setChangedItems([...changedItems, data]);
    }

    function onRemoveItem(data) {
        const newList = [...changedItems].filter(
            (item) => item?.person?.id !== data?.id
        );
        setChangedItems(newList);
    }

    function getFormattedData() {
        return changedItems?.map((item) => {
            const bedName =
                item?.person?.active_hostel_bed_assignment?.bed?.name;
            const roomName =
                item?.person?.active_hostel_bed_assignment?.bed?.hostel_room
                    ?.name;
            const blockName =
                item?.person?.active_hostel_bed_assignment?.bed?.hostel_room
                    ?.hostel_block?.name;

            const newItem: Record<string, any> = {
                id: item?.person?.id,
                name: item?.person?.name,
                current_bed: `${bedName}-${blockName}-${roomName}`,
                bed_to_change: `${item?.bed?.name} - ${item?.bed?.hostel_room?.hostel_block?.name} - ${item?.bed?.hostel_room?.name}`,
                start_date: item?.start_date,
                remarks: item?.remarks ?? "-",
            };
            if (type === STUDENT) {
                newItem.student_number = item?.person?.student_number;
            }
            if (type === EMPLOYEE) {
                newItem.employee_number = item?.person?.employee_number;
            }

            return newItem;
        });
    }

    function onChangeBed() {
        const assignedData = changedItems.map((item) => {
            const newItem: Record<string, any> = {
                id: item.person?.id,
            };
            newItem.hostel_room_bed_id = item.bed?.id;
            newItem.start_date = item?.start_date;
            newItem.remarks = item?.remarks;
            return newItem;
        });

        const data: Record<string, any> = {};
        if (type === STUDENT) {
            data.students = assignedData;
        }
        if (type === EMPLOYEE) {
            data.employees = assignedData;
        }
        assignBeds(data);
    }

    return (
        <div>
            <Button
                variant={"outline"}
                className="mb-4 flex gap-x-1"
                onClick={() => setPopUp(type)}
            >
                <Plus size={18} className="-ml-2 text-themeGreen" />{" "}
                <span>{t("Add")}</span>
            </Button>

            {changedItems?.length > 0 && (
                <InfoDisplay
                    title={t("Selected ") + t("Bed") + " " + t("Changes")}
                    data={getFormattedData()}
                    onRemove={onRemoveItem}
                    hiddenFields={["id"]}
                    smallerFields={[
                        "start_date",
                        "current_bed",
                        "bed_to_change",
                    ]}
                />
            )}

            <FormDivider />
            <div className="mt-4 flex justify-end gap-x-4">
                <Button variant="ghost" onClick={onClear}>
                    {t("Clear")}
                </Button>
                <Button
                    onClick={onChangeBed}
                    className="min-w-[120px]"
                    disabled={!(changedItems?.length > 0)}
                >
                    {t("Submit")}
                </Button>
            </div>

            <Modal open={popUp} onOpenChange={setPopUp} size="medium">
                <AddChangedItemForm
                    type={type}
                    onAddItem={onAddItem}
                    changedItems={changedItems}
                />
            </Modal>
        </div>
    );
};

const AddChangedItemForm = ({
    type,
    onAddItem,
    changedItems,
}: {
    type: typeof STUDENT | typeof EMPLOYEE;
    onAddItem: (data: Record<string, any>) => void;
    changedItems: any[];
}) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            start_date: new Date(),
            remarks: "",
        },
    });

    const [selectedPerson, setSelectedPerson] = useState<any | null>();
    const [selectedBed, setSelectedBed] = useState<any | null>();
    const [popUp, setPopUp] = useState<string | null>();
    const [warning, setWarning] = useState<string | null>(null);

    function onClear() {
        setSelectedPerson(null);
        setSelectedBed(null);
        setWarning(null);
    }

    function onSubmit() {
        setWarning(null);
        form.handleSubmit((data) => {
            if (selectedPerson && data.start_date && selectedBed) {
                if (
                    selectedPerson?.active_hostel_bed_assignment?.bed?.id ===
                    selectedBed?.id
                ) {
                    setWarning(
                        t(
                            "Selected bed is the same as the selected student's bed"
                        )
                    );
                    return;
                }
                const listedPersonIds = changedItems.map(
                    (item) => item?.person?.id
                );
                const assignedBedIds = changedItems.map(
                    (item) => item?.bed?.id
                );

                if (listedPersonIds.includes(selectedPerson?.id)) {
                    setWarning(
                        t("selected_already_added", {
                            item: type.toLowerCase(),
                        })
                    );
                    return;
                }
                if (assignedBedIds.includes(selectedBed?.id)) {
                    setWarning(
                        t("selected_already_added", {
                            item: "bed",
                        })
                    );
                    return;
                }

                const newData = {
                    person: selectedPerson,
                    bed: selectedBed,
                    start_date: toYMD(data.start_date),
                    remarks: data.remarks,
                };
                onAddItem(newData);
            } else {
                setWarning(
                    t("Please make sure all required selection are made")
                );
            }
        })();
    }

    return (
        <div className="flex flex-col gap-y-3">
            <div>
                <div className="c-text-size mb-3 mt-2 font-medium normal-case text-themeLabel">
                    {t("Select a ")}
                    {t(type.toLowerCase() + " ")}
                    {t("and the bed to be assigned")}
                </div>
                <div className="mb-1 flex gap-x-3">
                    <RaisedButton
                        name={t("Select ") + capitalize(t(type))}
                        onClick={() => {
                            setPopUp(type);
                            setWarning(null);
                        }}
                    />
                    <RaisedButton
                        name={t("Select the Bed To Change")}
                        onClick={() => {
                            setPopUp(BED);
                            setWarning(null);
                        }}
                    />
                </div>
            </div>

            {selectedPerson &&
                (type === STUDENT ? (
                    <InfoDisplay
                        title={t("Selected ") + t(`Student`)}
                        data={[studentInfoDisplay(selectedPerson, true)]}
                    />
                ) : (
                    <InfoDisplay
                        title={t("Selected ") + t(`Employee`)}
                        data={[employeeInfoDisplay(selectedPerson, true)]}
                    />
                ))}

            {selectedBed && (
                <div>
                    {selectedPerson && (
                        <ArrowDown
                            className="mx-auto text-themeGreen"
                            size={24}
                        />
                    )}
                    <InfoDisplay
                        title={t("Selected ") + t("Bed")}
                        data={[bedInfoDisplay(selectedBed)]}
                    />
                </div>
            )}

            <FormDivider />

            <Form {...form}>
                <form className="grid gap-y-3">
                    <div className="w-[200px]">
                        <DatePicker
                            control={form.control}
                            name="start_date"
                            onChange={() => setWarning(null)}
                        />
                    </div>
                    <FormTextarea control={form.control} name="remarks" />
                </form>
            </Form>

            <Modal
                open={popUp === STUDENT}
                onOpenChange={setPopUp}
                size="large"
            >
                <StudentSearchEngine
                    isHostel={true}
                    hasActiveBed={true}
                    setSelection={setSelectedPerson}
                    close={() => setPopUp(null)}
                    reset={() => setSelectedPerson(null)}
                />
            </Modal>

            <Modal
                open={popUp === EMPLOYEE}
                onOpenChange={setPopUp}
                size="large"
            >
                <StaffSearchEngine
                    isHostel={true}
                    hasActiveBed={true}
                    setSelection={setSelectedPerson}
                    close={() => setPopUp(null)}
                    reset={() => setSelectedPerson(null)}
                />
            </Modal>

            <Modal open={popUp === BED} onOpenChange={setPopUp} size="large">
                <BedSearchEngine
                    setSelection={setSelectedBed}
                    reset={() => setSelectedBed(null)}
                    close={() => setPopUp(null)}
                    hasActiveBed={true}
                    hostelType={type}
                />
            </Modal>

            {warning && (
                <div className="mt-1 text-right text-xs text-destructive lg:text-sm">
                    {warning}
                </div>
            )}

            <div className="mt-1 flex justify-end gap-x-4">
                <Button variant="ghost" onClick={onClear}>
                    {t("Clear")}
                </Button>
                <Button onClick={onSubmit} className="min-w-[120px]">
                    {t("Add")}
                </Button>
            </div>
        </div>
    );
};

const BulkBedAssignForm = ({ type }) => {
    const t = useTranslations("common");
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [showTable, setShowTable] = useState<boolean>(false);
    const [data, setData] = useState<any[]>([]);
    const [fileCleared, setFileCleared] = useState(0);

    const locale = useLocale();

    // Step 1
    function onDownloadTemplate() {
        getTemplate({ params: { type: type } });
    }

    const { axiosQuery: getTemplate } = useAxios({
        api: hostelBulkBedAssignmentTemplateAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    // Step 2
    const form = useForm();

    const handleImportData = (response) => {
        const successData = response.success ?? [];
        const errorData = response.errors ?? [];
        const combinedData = [...successData, ...errorData];

        if (combinedData.length > 0) {
            setData(combinedData);
            setShowTable(true);
        } else {
            setData([]);
            setShowTable(false);
        }
    };

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "block_code",
            },
            {
                key: "room",
            },
            {
                key: "bed",
            },
            {
                key: "bed_assign_to",
            },
            {
                key: "start_date",
                displayAs: "Start Date (Y-m-d)",
            },
            {
                key: "remarks",
                displayAs: "Remarks (optional)",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    return (
                        <FreeTextArea
                            control={form.control}
                            name={`students[${index}].remarks`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.remarks
                            }
                        />
                    );
                },
            },
            {
                key: "_",
                hasSort: false,
                modify: (_, cell) => {
                    const index = cell.row.index;
                    return (
                        <div className="flex h-full items-center">
                            <div
                                className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                                onClick={() => handleRemoveRow(index)}
                            >
                                <X size={14} className="text-themeGreenDark" />
                            </div>
                        </div>
                    );
                },
            },
            {
                key: "error",
                displayAs: "Error Message",
            },
        ];
        setColumns(_columns);
    }

    function definedData() {
        return isArray(data)
            ? data.map((item) => ({
                  block_code: item.block_code,
                  room: item.room,
                  bed: item.bed,
                  bed_assign_to: item?.student_name
                      ? `${item.number} - ${combinedNames(item.student_name)}`
                      : `${item.number}`,
                  start_date: item.start_date,
                  remarks: item.remarks || "",
                  error: item.error,
              }))
            : [];
    }

    const handleRemoveRow = (index) => {
        const updatedData = data.filter((_, i) => i !== index);
        setData(updatedData);

        if (updatedData.length === 0) {
            setShowTable(false);
        }
    };

    function onClear() {
        setData([]);
        setShowTable(false);
        setFileCleared(fileCleared + 1);
    }

    const { axiosPost: assignBedsInBulk } = useAxios({
        api: hostelBulkBedAssignmentAPI,
        onSuccess: () => onClear(),
        toastMsg: "Assigned successfully",
    });

    function onAssignBedsInBulk(data) {
        const payload =
            type === STUDENT ? { students: data } : { employees: data };
        assignBedsInBulk(payload);
    }

    function onSubmit() {
        form.clearErrors();

        const hasErrors = data.some((row) => row.error);

        if (hasErrors) {
            toast.error(
                t(
                    "Please revise the data to resolve all errors before submitting"
                )
            );
            return;
        } else {
            onAssignBedsInBulk(data);
        }
    }

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <>
            <div className="flex items-start gap-3">
                <Button variant={"outline"} onClick={onDownloadTemplate}>
                    {t("Download Template")}
                </Button>

                <FileInputExcel
                    api={hostelBulkBedAssignmentImportAPI}
                    extraImportParams={{ type: type }}
                    handleImportData={handleImportData}
                    clearTrigger={fileCleared}
                />
            </div>

            {showTable && (
                <>
                    <div className="overflow-x-auto pt-4">
                        <DataTable columns={columns} data={definedData()} />
                    </div>

                    <div className="mt-4 flex justify-end gap-2">
                        <Button
                            type="reset"
                            variant="outline"
                            className="ml-auto"
                        >
                            {t("Cancel")}
                        </Button>
                        <Button type="submit" onClick={onSubmit}>
                            {t("Submit")}
                        </Button>
                    </div>
                </>
            )}
        </>
    );
};

export default BedAssignmentForms;
