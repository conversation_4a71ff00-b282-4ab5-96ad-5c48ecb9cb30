import React, { forwardRef } from "react";
import { GroupBase } from "react-select";
import AsyncSelect from "react-select/async";
import Select from "react-select/dist/declarations/src/Select";
import {
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { selectStyles } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

type FormSelectAsyncProps = {
    control: any;
    value?: any;
    name: string;
    label?: string;
    isDisabled?: boolean;
    isMulti?: boolean;
    loadOptions?: (inputValue: any, callback: any) => void;
    onChange?: (value: any) => void;
    hasLabel?: boolean;
};

const FormSelectAsync = forwardRef<
    Select<any, false, GroupBase<any>>,
    FormSelectAsyncProps
>(
    (
        {
            control,
            value,
            name,
            label,
            isDisabled,
            isMulti = false,
            loadOptions,
            onChange = () => {},
            hasLabel = true,
        }: FormSelectAsyncProps,
        ref
    ) => {
        const t = useTranslations("common");
        const selectName = t(replaceAll(label || name, "_", " "));

        return control ? (
            <FormField
                control={control}
                name={name}
                render={({ field }) => (
                    <FormItem>
                        {hasLabel && (
                            <FormLabel className="capitalize">
                                {selectName}
                            </FormLabel>
                        )}
                        <AsyncSelect
                            key={name}
                            isClearable
                            isMulti={isMulti}
                            closeMenuOnSelect={isMulti ? false : true}
                            cacheOptions
                            defaultOptions
                            loadOptions={loadOptions}
                            {...field}
                            ref={ref}
                            placeholder={t("Type to search")}
                            isDisabled={isDisabled}
                            value={value}
                            onChange={(selectedOption: any) => {
                                const value = isMulti
                                    ? selectedOption?.map(
                                          (item: any) => item.value
                                      ) || []
                                    : selectedOption?.value || null;
                                field.onChange(value);
                                onChange(isMulti ? value : selectedOption);
                            }}
                            styles={selectStyles}
                        />
                        <FormMessage />
                    </FormItem>
                )}
            />
        ) : (
            <></>
        );
    }
);

FormSelectAsync.displayName = "FormSelectAsync";

export default FormSelectAsync;
