import { ASC, DESC, TableColumnType } from "@/lib/constant";
import { ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react";
import React, { useState, useMemo, useEffect } from "react";
import { Checkbox } from "../base-ui/checkbox";
import { useTranslations } from "next-intl";

type SortDirection = "asc" | "desc";

type FreeTableProps<T> = {
    columns: TableColumnType[];
    data: T[];
    rowsPerPage?: number;
    clearSelectionCount?: number;
    selection?: any[];
    setSelection?: React.Dispatch<React.SetStateAction<any[]>>;
};

function FreeTable<T extends Record<string, any>>({
    columns,
    data,
    rowsPerPage,
    clearSelectionCount = 0,
    selection = [],
    setSelection,
}: FreeTableProps<T>) {
    const t = useTranslations("common");
    const [sortConfig, setSortConfig] = useState<{
        key: keyof T | null;
        direction: SortDirection;
    }>({
        key: null,
        direction: "asc",
    });
    const [currentPage, setCurrentPage] = useState(1);

    const pageCount = rowsPerPage ? Math.ceil(data.length / rowsPerPage) : 1;

    const sortedData = useMemo(() => {
        if (!sortConfig.key) return data;

        return [...data].sort((a, b) => {
            const aVal = a[sortConfig.key!];
            const bVal = b[sortConfig.key!];

            if (aVal < bVal) return sortConfig.direction === "asc" ? -1 : 1;
            if (aVal > bVal) return sortConfig.direction === "asc" ? 1 : -1;
            return 0;
        });
    }, [data, sortConfig]);

    const paginatedData = useMemo(() => {
        if (!rowsPerPage) return sortedData;

        const start = (currentPage - 1) * rowsPerPage;
        return sortedData.slice(start, start + rowsPerPage);
    }, [sortedData, currentPage, rowsPerPage]);

    const handleSort = (key: keyof T) => {
        setCurrentPage(1);
        setSortConfig((prev) => {
            if (prev.key === key) {
                return {
                    key,
                    direction: prev.direction === "asc" ? "desc" : "asc",
                };
            }
            return { key, direction: "asc" };
        });
    };

    function selectAll(value: boolean) {
        if (!setSelection) return;
        if (!value) {
            setSelection([]);
            return;
        }
        if (!data) return;
        const selected = data.map((_, index) => index);
        setSelection(selected);
    }

    useEffect(() => {
        setSelection && setSelection([]);
    }, [clearSelectionCount]);

    return (
        <div className="space-y-4">
            <table className="c-table c-text-size min-w-full border border-gray-300 text-left">
                <thead className="">
                    <tr>
                        {setSelection && (
                            <th className="border-b border-r px-4 py-2 font-medium last:border-r-0">
                                <Checkbox
                                    onCheckedChange={(value: boolean) =>
                                        selectAll(value)
                                    }
                                />
                            </th>
                        )}
                        {columns.map((col) => (
                            <th
                                key={String(col.key)}
                                className="h-11 cursor-pointer select-none border-b border-r px-4 py-2 font-medium capitalize last:border-r-0"
                                onClick={() => handleSort(col.key)}
                            >
                                {col.modifyHeader
                                    ? col.modifyHeader(col.key)
                                    : t(col.displayAs) ?? (
                                          <span className="capitalize">
                                              {t(col.key.replaceAll("_", " "))}
                                          </span>
                                      )}
                                {col.hasSort &&
                                    (sortConfig.key !== col.key ? (
                                        <ChevronsUpDown className="ml-2 h-4 w-4" />
                                    ) : sortConfig.direction === ASC ? (
                                        <ArrowUp className="ml-2 h-4 w-4" />
                                    ) : sortConfig.direction === DESC ? (
                                        <ArrowDown className="ml-2 h-4 w-4" />
                                    ) : (
                                        <></>
                                    ))}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {paginatedData.length === 0 ? (
                        <tr>
                            <td
                                colSpan={columns.length}
                                className="h-16 px-4 py-2 text-center text-gray-500"
                            >
                                {t("No data available")}
                            </td>
                        </tr>
                    ) : (
                        paginatedData.map((row, rowIndex) => (
                            <tr key={row.id ?? rowIndex}>
                                {setSelection && (
                                    <td className="border-r border-t px-4 py-2 last:border-r-0">
                                        <Checkbox
                                            checked={selection.includes(
                                                rowIndex
                                            )}
                                            onCheckedChange={(
                                                value: boolean
                                            ) => {
                                                if (value) {
                                                    setSelection((prev) => [
                                                        ...prev,
                                                        rowIndex,
                                                    ]);
                                                } else {
                                                    setSelection((prev) =>
                                                        prev.filter(
                                                            (id) =>
                                                                id !== rowIndex
                                                        )
                                                    );
                                                }
                                            }}
                                        />
                                    </td>
                                )}
                                {columns.map((col) => (
                                    <td
                                        key={String(col.key)}
                                        className="border-r border-t px-4 py-2 last:border-r-0"
                                    >
                                        {col.modify
                                            ? col.modify(row[col.key], rowIndex)
                                            : String(row[col.key])}
                                    </td>
                                ))}
                            </tr>
                        ))
                    )}
                </tbody>
            </table>

            {/* Pagination Controls */}
            {pageCount > 1 && (
                <div className="flex items-center justify-end gap-2 text-sm">
                    <button
                        disabled={currentPage === 1}
                        onClick={() => setCurrentPage((p) => p - 1)}
                        className="rounded border px-2 py-1 disabled:opacity-50"
                    >
                        {t("Prev")}
                    </button>
                    <span>
                        Page {currentPage} of {pageCount}
                    </span>
                    <button
                        disabled={currentPage === pageCount}
                        onClick={() => setCurrentPage((p) => p + 1)}
                        className="rounded border px-2 py-1 disabled:opacity-50"
                    >
                        {t("Next")}
                    </button>
                </div>
            )}
        </div>
    );
}

export default FreeTable;
