import React from "react";
import clsx from "clsx";
import { replaceAll } from "@/lib/utils";
import { Checkbox } from "../base-ui/checkbox";
import {
    FormField,
    FormItem,
    FormControl,
    FormLabel,
    FormMessage,
} from "../base-ui/form";
import { useTranslations } from "next-intl";

type FormCheckboxProps = {
    control: any;
    name: string;
    label?: string;
    styleClass?: string;
    textStyleClass?: string;
    hasLabel?: boolean;
    isDisabled?: boolean;
    onChange?: (checked: boolean) => void;
};

const FormCheckbox = ({
    control,
    name = "",
    label = "",
    styleClass = "",
    textStyleClass = "",
    hasLabel = true,
    isDisabled = false,
    onChange,
}: FormCheckboxProps) => {
    const t = useTranslations("common");

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    <div
                        className={clsx(
                            "flex flex-row items-start space-x-1.5 lg:space-x-2",
                            styleClass
                        )}
                    >
                        <FormControl>
                            <Checkbox
                                checked={!!field.value}
                                onCheckedChange={(checked: boolean) => {
                                    field.onChange(checked);

                                    if (onChange) {
                                        onChange(checked);
                                    }
                                }}
                                disabled={isDisabled}
                            />
                        </FormControl>
                        {hasLabel && (
                            <FormLabel
                                className={clsx(
                                    "cursor-pointer capitalize text-gray-600",
                                    textStyleClass
                                )}
                            >
                                {t(replaceAll(label || name, "_", " "))}
                            </FormLabel>
                        )}
                    </div>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormCheckbox;
