import React, { useState } from "react";
import clsx from "clsx";
import { ChevronDown } from "lucide-react";
import Input, { parsePhoneNumber } from "react-phone-number-input/input";
import {
    getCountries,
    getCountryCallingCode,
} from "react-phone-number-input/input";
import "react-phone-number-input/style.css";
import en from "react-phone-number-input/locale/en.json";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

type FormPhoneInputProps = {
    form: any;
    name: string;
    label?: string;
    hasLabel?: boolean;
    disabled?: boolean;
    isGray?: boolean;
    onChange?: (value: any) => void;
};

const FormPhoneInput = ({
    form,
    name,
    label,
    hasLabel = true,
    disabled = false,
    isGray = false,
    onChange,
}: FormPhoneInputProps) => {
    const t = useTranslations("common");

    function getCountryCode() {
        const phoneNumber = form.getValues(name);
        if (!phoneNumber) return "MY";
        const data =
            typeof phoneNumber === "string"
                ? parsePhoneNumber(phoneNumber)
                : null;
        return data?.country ?? "";
    }

    const [country, setCountry] = useState<any>(getCountryCode());

    return form?.control ? (
        <FormField
            control={form.control}
            name={name}
            render={({ field }) => {
                return (
                    <FormItem>
                        {hasLabel && (
                            <FormLabel className="capitalize">
                                {t(replaceAll(label || name, "_", " "))}
                            </FormLabel>
                        )}
                        <FormControl>
                            <div
                                className={clsx(
                                    "flex items-center gap-x-1",
                                    disabled && "opacity-70"
                                )}
                            >
                                <CountrySelect
                                    labels={en}
                                    value={country}
                                    onChange={(value) => {
                                        form.setValue(name, "");
                                        setCountry(value);
                                    }}
                                    disabled={disabled}
                                />
                                <Input
                                    disabled={disabled}
                                    {...field}
                                    withCountryCallingCode
                                    defaultCountry="MY"
                                    international={true}
                                    country={country}
                                    placeholder={isGray ? "Phone number" : null}
                                    className={clsx(
                                        "c-text-size flex h-[42px] w-full rounded-sm border border-input px-4 py-2.5 font-pjs file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-themeGray3",
                                        !disabled &&
                                            "hover:border-themeGreen2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-themeGreen2",
                                        isGray
                                            ? "border-none bg-gray-100"
                                            : "bg-background"
                                    )}
                                    onChange={(value) => {
                                        field.onChange(value);
                                        onChange && onChange(value);
                                    }}
                                />
                            </div>
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                );
            }}
        />
    ) : (
        <></>
    );
};

const CountrySelect = ({ value, onChange, labels, ...rest }) => (
    <div className="relative">
        <select
            className="h-[42px] rounded-sm border border-input text-[13px] leading-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-themeGreen2"
            {...rest}
            value={value}
            onChange={(event) => {
                onChange(event.target.value || undefined);
            }}
        >
            <option value="">EXT</option>
            {getCountries().map((country) => (
                <option key={country} value={country}>
                    {country} +{getCountryCallingCode(country)}
                </option>
            ))}
        </select>
        <div className="pointer-events-none absolute left-0 top-0 flex h-[42px] w-full items-center justify-center gap-1 rounded-sm border border-input bg-white px-2 text-[13px] font-medium">
            <span>{value}</span>
            <ChevronDown size={16} className="-mr-1" />
        </div>
    </div>
);

export default FormPhoneInput;
