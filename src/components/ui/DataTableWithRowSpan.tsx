import React, { useEffect, useState } from "react";
import {
    flexRender,
    getCoreRowModel,
    useReactTable,
    getSortedRowModel,
    RowSelectionState,
    Table as TableType,
} from "@tanstack/react-table";
import clsx from "clsx";
import { isEmpty } from "lodash";
import {
    Check,
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
} from "lucide-react";
import { Button } from "@/components/base-ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/base-ui/select";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/base-ui/table";
import { TableColumnType } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import TableHeaderSorter from "./TableHeaderSorter";

type DataTableProps = {
    columns: Array<TableColumnType>;
    data: Array<any>;
    actionMenu?: ({ cell }: { cell: any }) => JSX.Element;
    changePage?: (arg: Record<string, any>) => void;
    pagination?: any;
    setPagination?: any;
    sort?: (name: string, direction: string) => void;
    sorted?: Record<string, any>;
    hasPerPage?: boolean;
    selectedRows?: any[];
    onSelect?: (id) => void;
    onMultiSelect?: (list: any[]) => void;
    isForm?: boolean;
    clearCount?: number;
    isSmaller?: boolean;
};

const DataTableWithRowSpan = ({
    columns,
    data,
    actionMenu,
    changePage,
    pagination = {},
    setPagination,
    sort = () => {},
    sorted = {},
    hasPerPage,
    selectedRows,
    onSelect,
    onMultiSelect,
    isForm = false,
    clearCount = 0,
    isSmaller,
}: DataTableProps) => {
    const [selected, setSelected] = useState<any>(null);
    const [rowSelection, setRowSelection] = useState<any>(
        selectedRows
            ?.map((i) => i?.id)
            .reduce((acc, id) => {
                acc[id] = true;
                return acc;
            }, {}) ?? {}
    );

    const tableColumns = columns.map((item) => ({
        accessorKey: item.key,
        header: () => {
            return (
                <div className="thead-item capitalize">
                    {replaceAll(item.displayAs || item.key, "_", " ")}
                </div>
            );
        },
        cell: ({ row }) => {
            const rowData = row.original ?? row;
            const value = rowData[item.key];

            let rowSpan = 1;
            if (item.rowSpan && rowData.isFirstSubRow) {
                rowSpan = rowData.subRowLength;
            } else if (item.rowSpan && !rowData.isFirstSubRow) {
                return null;
            }

            return <>{item.modify ? item.modify(value, rowData) : value}</>;
        },
    }));

    if (actionMenu) {
        tableColumns.push({
            accessorKey: "action",
            header: () => <></>,
            cell: ({ row }) => {
                if (row.original?.isFirstSubRow) {
                    return actionMenu({ cell: row });
                }
                return null;
            },
        });
    }

    const table = useReactTable({
        data: data ?? [],
        columns: tableColumns,
        manualPagination: true,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        pageCount: pagination?.last_page,
        getRowId: (row) => row.id,
        state: {
            rowSelection,
            pagination: {
                pageIndex: pagination?.current_page - 1,
                pageSize: pagination?.per_page,
            },
        },
    });

    if (isEmpty(columns)) return <></>;

    return (
        <>
            <div
                className={clsx(
                    "rounded-md border lg:border-t-0",
                    !isForm && "overflow-auto lg:rounded-none lg:border-x-0"
                )}
            >
                <Table>
                    <TableHeader>
                        {table?.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead
                                            key={header.id}
                                            className={clsx(
                                                isSmaller && "h-10"
                                            )}
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext()
                                                  )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows.length ? (
                            table.getRowModel().rows.map((row, rowIndex) => (
                                <TableRow key={`${row.id}-${rowIndex}`}>
                                    {row
                                        .getVisibleCells()
                                        .map((cell, cellIndex) => {
                                            const cellContent = flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            );

                                            return (
                                                <TableCell
                                                    key={`${cell.id}-${cellIndex}`}
                                                >
                                                    {cellContent}
                                                </TableCell>
                                            );
                                        })}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={tableColumns.length}
                                    className="h-20 text-center text-themeLabel"
                                >
                                    No data
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {!isEmpty(pagination) && (
                <DataTablePagination
                    table={table}
                    rowSelection={rowSelection}
                    changePage={changePage}
                    pagination={pagination}
                    hasPerPage={hasPerPage}
                />
            )}
        </>
    );
};

export default DataTableWithRowSpan;

type DataTablePaginationProps = {
    table: TableType<any>;
    rowSelection?: RowSelectionState;
    changePage?: (arg: Record<string, any>) => void;
    pagination: Record<string, any>;
    hasPerPage?: boolean;
};

const DataTablePagination = ({
    table,
    rowSelection,
    changePage = () => {},
    pagination,
    hasPerPage = true,
}: DataTablePaginationProps) => {
    return (
        <div className="mt-3 flex flex-col items-center justify-between gap-x-3 gap-y-4 px-2 lg:flex-row">
            {/* checkbox selection count */}
            {rowSelection &&
            table?.getFilteredSelectedRowModel().rows.length > 0 ? (
                <div className="flex-1 text-[13px] text-muted-foreground">
                    {table.getFilteredSelectedRowModel().rows.length} of{" "}
                    {table.getFilteredRowModel().rows.length} row(s) selected
                </div>
            ) : (
                <div></div>
            )}

            <div className="flex flex-col items-center justify-center gap-x-5 gap-y-4 lg:flex-row">
                {/* per page dropdown */}
                {hasPerPage && (
                    <div className="order-2 flex items-center gap-x-2 lg:order-1">
                        <p className="text-sm font-medium">Rows per page</p>
                        <Select
                            value={`${table.getState().pagination.pageSize}`}
                            onValueChange={(value) => {
                                table.setPageSize(Number(value));
                                changePage({ per_page: Number(value) });
                            }}
                        >
                            <SelectTrigger className="w-fit text-[14px]">
                                <SelectValue
                                    placeholder={pagination?.per_page}
                                />
                            </SelectTrigger>
                            <SelectContent side="top">
                                {[10, 20, 30, 40, 50].map((pageSize) => (
                                    <SelectItem
                                        className="text-[14px]"
                                        key={pageSize}
                                        value={`${pageSize}`}
                                    >
                                        {pageSize}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                )}

                {/* page */}
                <div className="flex items-center justify-center text-xs font-medium text-gray-500 lg:order-2">
                    Page {table.getState().pagination.pageIndex + 1} of{" "}
                    {table.getPageCount()}
                </div>

                {/* nav */}
                {table.getPageCount() > 1 && (
                    <div className="flex items-center gap-x-2 lg:order-2">
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() => {
                                changePage({ page: 1 });
                            }}
                            disabled={!table.getCanPreviousPage()}
                        >
                            <span className="sr-only">Go to first page</span>
                            <ChevronsLeft className="h-4 w-4 text-themeBlack" />
                        </Button>
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() => {
                                changePage({
                                    page: pagination.current_page - 1,
                                });
                            }}
                            disabled={!table.getCanPreviousPage()}
                        >
                            <span className="sr-only">Go to previous page</span>
                            <ChevronLeft className="h-4 w-4 text-themeBlack" />
                        </Button>
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() => {
                                changePage({
                                    page: pagination.current_page + 1,
                                });
                            }}
                            disabled={!table.getCanNextPage()}
                        >
                            <span className="sr-only">Go to next page</span>
                            <ChevronRight className="h-4 w-4 text-themeBlack" />
                        </Button>
                        <Button
                            variant="outlineGray"
                            size={"icon"}
                            onClick={() =>
                                changePage({ page: pagination.last_page })
                            }
                            disabled={!table.getCanNextPage()}
                        >
                            <span className="sr-only">Go to last page</span>
                            <ChevronsRight className="h-4 w-4 text-themeBlack" />
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
};
