import { useState } from "react";
import React from "react";
import { Upload } from "lucide-react";
import { FormItem, FormLabel } from "@/components/base-ui/form";
import { Input } from "@/components/base-ui/input";
import { replaceAll, strStartCase } from "@/lib/utils";
import { useTranslations } from "next-intl";

type FormExcelInputProps = {
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    register: any;
    errors: any;
    clear?: () => void;
};

const FormExcelInput = ({
    name,
    label,
    placeholder = "Choose File",
    disabled = false,
    register,
    clear = () => {},
    errors,
}: FormExcelInputProps) => {
    const [fileName, setFileName] = useState();
    const t = useTranslations("common");

    return (
        <FormItem>
            <FormLabel className="capitalize">
                {t(replaceAll(label || name, "_", " "))}
            </FormLabel>

            <div className="relative">
                <Input
                    disabled={disabled}
                    type="file"
                    className="pl-2 pt-2"
                    accept=".xls,.xlsx"
                    {...register(name, {
                        required: true,
                        onChange: (e) => {
                            if (e.target.files[0]) {
                                const file = e.target.files[0];
                                console.log("file", file);
                                setFileName(file.name);
                            }
                        },
                    })}
                />
                <div className="pointer-events-none absolute left-0 top-0 flex h-full w-full items-center gap-x-2 rounded-sm border border-input bg-white px-4 text-gray-600">
                    <Upload size={16} />
                    <span className="c-text-size font-medium">
                        {strStartCase(placeholder)}
                    </span>
                </div>
            </div>
            {fileName && (
                <div className="mt-4 flex items-center gap-x-2">
                    <p className="mt-0.5 text-[13px] font-semibold text-themeLabel">
                        Selected File
                    </p>
                    <div className="c-text-size font-medium">{fileName}</div>
                </div>
            )}
            {errors[name] && (
                <div className="mt-2 text-[13px] font-medium text-destructive">
                    {errors[name]?.message.toString() ||
                        "Please upload a excel file"}
                    {console.log(errors[name])}
                </div>
            )}
        </FormItem>
    );
};

export default FormExcelInput;
