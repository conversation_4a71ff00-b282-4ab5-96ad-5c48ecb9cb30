import React from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { Switch } from "@/components/base-ui/switch";
import { ACTIVE, INACTIVE } from "@/lib/constant";
import { useTranslations } from "next-intl";

type StatusFormSwitchProps = {
    control: any;
    name: string;
    label?: string;
    disabled?: boolean;
};

const StatusFormSwitch = ({
    control,
    name,
    label,
    disabled = false,
}: StatusFormSwitchProps) => {
    const t = useTranslations("common");

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    <FormLabel>{t(label) || t(name)}</FormLabel>
                    <div className="ml-1 flex items-center gap-x-3 pt-0.5">
                        <FormControl>
                            <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={disabled}
                                {...field}
                            />
                        </FormControl>
                        <div
                            className={clsx(
                                "-mt-0.5 text-[14px] transition",
                                field.value ? "text-themeGreen2" : "opacity-80"
                            )}
                        >
                            {capitalize(field.value ? t(ACTIVE) : t(INACTIVE))}
                        </div>
                    </div>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default StatusFormSwitch;
