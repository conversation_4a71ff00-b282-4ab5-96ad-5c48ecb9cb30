import React, { ReactNode } from "react";
import { MoreHorizontal } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/base-ui/dropdown-menu";

const ActionDropdown = ({ children }: { children: ReactNode }) => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <MoreHorizontal className="mx-auto ml-auto h-7 w-7 cursor-pointer rounded-md px-1 text-gray-600 transition hover:bg-gray-100" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">{children}</DropdownMenuContent>
        </DropdownMenu>
    );
};

export default ActionDropdown;
