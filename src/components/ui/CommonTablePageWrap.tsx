import { ReactNode, useEffect, useState } from "react";
import React from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import {
    CommonFilterProps,
    CommonFormProps,
    DEFAULT_FILTER_PARAMS,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useFilter, useLanguages, useUserProfile } from "@/lib/store";
import { getPageList, refreshForUpdate, strStartCase } from "@/lib/utils";
import { useTranslations } from "next-intl";
import FilterChevronButton from "./FilterChevronButton";
import FilterFormWrapper from "./FilterFormWrapper";

type CommonTablePageWrapProps = {
    path: string;
    api: string;
    definedColumn: (
        activeLanguages,
        t: (value: string) => void
    ) => TableColumnType[];
    definedData: (data: unknown, t: (value: string) => void) => any[];
    orderBy: (name: string, direction: string) => Record<string, any>;
    form: (formParams: CommonFormProps) => ReactNode;
    filterForm?: (formParams: CommonFilterProps) => ReactNode;
    otherFilterParams?: Record<string, any>;
    locale: string;
    noDelete?: boolean;
    noUpdate?: boolean;
    noCreate?: boolean;
    onlyView?: boolean;
    deleteAPI?: string;
    heading?: string;
    useTableDataForForm?: boolean;
    viewPermit?: string;
    createPermit?: string;
    updatePermit?: string;
    deletePermit?: string;
    hidePagination?: boolean;
    formSize?: "small" | "medium" | "large";
    filterFormSize?: "small" | "medium" | "large";
};

const CommonTablePageWrap = ({
    path,
    api,
    definedColumn,
    definedData,
    orderBy,
    form,
    filterForm,
    otherFilterParams,
    locale,
    noDelete = false,
    noUpdate = false,
    noCreate = false,
    onlyView = false,
    deleteAPI,
    heading,
    useTableDataForForm = false,
    viewPermit,
    createPermit,
    updatePermit,
    deletePermit,
    hidePagination,
    formSize = "small",
    filterFormSize = "small",
}: CommonTablePageWrapProps) => {
    useCheckViewPermit(viewPermit);
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        ...(otherFilterParams?.order_by
            ? { order_by: otherFilterParams?.order_by }
            : {}),
    });
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data, axiosQuery } = useAxios({
        api,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    useEffect(() => {
        fetch();
    }, [filter, locale]);

    useEffect(() => {
        if (data && activeLanguages) {
            const _columns = definedColumn(activeLanguages, t);
            setColumns(_columns);
        }
    }, [data, activeLanguages]);

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: orderBy(name, direction),
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    function fetch() {
        axiosQuery({
            params: {
                ...otherFilterParams,
                ...filter,
            },
        });
    }

    const _heading = getPageList(path)[0] ?? "";

    return (
        <Layout locale={locale} path={path}>
            <Card styleClass="table-card">
                <div className="table-page-top">
                    <h2 className="mr-auto">
                        {heading ?? strStartCase(t(_heading))}
                    </h2>
                    {!noCreate && hasPermit(createPermit) && (
                        <Button
                            size="smallerOnMobile"
                            className="ml-auto capitalize"
                            onClick={() => setOpenCreate(true)}
                        >
                            {t("Add ")}
                            {t(_heading)}
                        </Button>
                    )}
                    {filterForm && <FilterChevronButton />}
                </div>
                {filterForm && (
                    <FilterFormWrapper>
                        {filterForm({
                            filter,
                            setFilter,
                            close: () => setOpenFilter(false),
                        })}
                    </FilterFormWrapper>
                )}
                <DataTable
                    columns={columns}
                    data={definedData(data, t)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    hidePagination={hidePagination}
                    actionMenu={({ cell }) => {
                        const canEdit = !noUpdate && hasPermit(updatePermit);
                        const canDelete = !noDelete && hasPermit(deletePermit);

                        if (!canEdit && !canDelete) {
                            return undefined;
                        } else
                            return (
                                <ActionDropdown>
                                    {canEdit && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {onlyView
                                                ? t("View")
                                                : t("Edit / View")}
                                        </DropdownMenuItem>
                                    )}
                                    {canEdit && canDelete && (
                                        <DropdownMenuSeparator />
                                    )}
                                    {canDelete && (
                                        <>
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                {t("Delete")}
                                            </DropdownMenuItem>
                                        </>
                                    )}
                                </ActionDropdown>
                            );
                    }}
                />
            </Card>

            {/* create */}
            <Modal
                open={openCreate}
                onOpenChange={setOpenCreate}
                size={formSize}
            >
                {form({
                    isCreate: true,
                    refresh: () => refreshForUpdate(filter, setFilter),
                    close: closeForm,
                })}
            </Modal>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size={formSize}>
                {form({
                    id: targetId,
                    refresh: () => refreshForUpdate(filter, setFilter),
                    close: closeForm,
                    currentTableData: useTableDataForForm
                        ? data?.find((item) => item?.id == targetId)
                        : null,
                })}
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={deleteAPI ?? api}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetch}
                />
            </Modal>

            {/* filter */}
            {filterForm && (
                <Modal
                    open={openFilter}
                    onOpenChange={setOpenFilter}
                    size={filterFormSize}
                >
                    {filterForm({
                        filter,
                        setFilter,
                        close: () => setOpenFilter(false),
                    })}
                </Modal>
            )}
        </Layout>
    );
};

export default CommonTablePageWrap;
