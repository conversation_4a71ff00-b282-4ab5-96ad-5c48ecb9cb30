import { X } from "lucide-react";
import { formatUnderscores } from "@/lib/utils";
import { useTranslations } from "next-intl";

const InfoDisplay = ({
    title,
    data,
    hiddenFields,
    smallerFields,
    onRemove,
}: {
    title: string;
    data: Record<any, string>[] | null;
    hiddenFields?: string[];
    smallerFields?: string[];
    onRemove?: (id) => void;
}) => {
    const t = useTranslations("common");
    return (
        <div className="w-full">
            <h3 className="mb-2 ml-0.5 font-bold capitalize text-themeGreenDark lg:col-span-2">
                {t(title)}
            </h3>
            <div className="w-full overflow-auto rounded-md border border-themeGreen px-2 py-3">
                {data && (
                    <table className="info-table">
                        <thead>
                            <tr>
                                {onRemove && <th></th>}
                                {data[0] &&
                                    Object.keys(data[0])?.map((header) =>
                                        !hiddenFields?.includes(header) ? (
                                            <th
                                                key={header}
                                                className="text-left text-[14px] font-medium capitalize leading-tight text-themeLabel"
                                            >
                                                {t(formatUnderscores(header))}
                                            </th>
                                        ) : null
                                    )}
                            </tr>
                        </thead>
                        <tbody>
                            {data?.map((row, index) => (
                                <tr
                                    key={index}
                                    className="rounded-md transition hover:bg-gray-50"
                                >
                                    {onRemove && (
                                        <td style={{ padding: 0 }}>
                                            <div
                                                className="my-1 ml-1 cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80 lg:mr-2"
                                                onClick={() => onRemove(row)}
                                            >
                                                <X
                                                    size={14}
                                                    className="text-themeGreenDark"
                                                />
                                            </div>
                                        </td>
                                    )}
                                    {row &&
                                        Object.entries(row)?.map(
                                            ([key, val]) =>
                                                !hiddenFields?.includes(key) ? (
                                                    <td
                                                        key={`${key + val}`}
                                                        className="c-text-size"
                                                        // className={clsx(
                                                        //     smallerFields?.includes(
                                                        //         key
                                                        //     ) && "text-sm"
                                                        // )}
                                                    >{`${t(val)}`}</td>
                                                ) : null
                                        )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
        </div>
    );
};

export default InfoDisplay;
