import React from "react";
import { Upload } from "lucide-react";
import { useFieldArray } from "react-hook-form";
import toast from "react-hot-toast";
import { FormItem, FormLabel } from "@/components/base-ui/form";
import { Input } from "@/components/base-ui/input";
import { getFiles, replaceAll, strStartCase } from "@/lib/utils";
import OutlinedX from "./OutlinedX";
import { useTranslations } from "next-intl";

const MAX_FILE_SIZE = 2 * 1024 * 1024;

type FormMultipleFilesInputProps = {
    form?: any;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    errors: any;
};

const FormMultipleFilesInput = ({
    form,
    name,
    label,
    placeholder = "Choose File",
    disabled = false,
    errors,
}: FormMultipleFilesInputProps) => {
    const { append, remove } = useFieldArray({
        control: form.control,
        name: "attachments",
    });
    const t = useTranslations("common");

    return (
        <FormItem>
            <FormLabel className="capitalize">
                {t(replaceAll(label || name, "_", " "))}
            </FormLabel>

            <div className="relative max-w-[400px]">
                <Input
                    multiple
                    disabled={disabled}
                    type="file"
                    className="cursor-pointer pl-2 pt-2"
                    accept="image/*,.pdf,.ppt,.pptx,.docx"
                    onChange={(e) => {
                        const filesObj = e.target.files;
                        if (filesObj) {
                            const files = getFiles(filesObj);
                            console.log("files", files);
                            if (
                                files.some((file) => file.size > MAX_FILE_SIZE)
                            ) {
                                toast.error(
                                    `File size should not exceed ${MAX_FILE_SIZE / (1024 * 1024)} MB`
                                );
                            } else {
                                files.forEach((file) => append(file));
                            }
                        }
                    }}
                />
                <div className="pointer-events-none absolute left-0 top-0 flex h-full w-full items-center gap-x-2 rounded-sm border border-input bg-white px-4 text-gray-500">
                    <Upload size={16} />
                    <span className="c-text-size font-medium text-themeLabel">
                        {strStartCase(placeholder)}
                    </span>
                </div>
            </div>
            {form.watch(name)?.map((file, index) => (
                <div
                    key={`${file?.name} + ${index}`}
                    className="mt-2 flex gap-x-2"
                >
                    <OutlinedX onClick={() => remove(index)} />
                    <div className="w-[calc(100%-30px)] text-[13px]">
                        {file?.name}
                    </div>
                </div>
            ))}
            {errors[name] && (
                <div className="mt-2 text-[13px] font-medium text-destructive">
                    {Array.isArray(errors[name])
                        ? errors[name][0]?.message
                        : errors[name]?.message?.toString()}
                    {console.log(errors[name])}
                </div>
            )}
        </FormItem>
    );
};

export default FormMultipleFilesInput;
