import React from "react";
import clsx from "clsx";
import { Control, Controller } from "react-hook-form";
import { Input } from "@/components/base-ui/input";
import {
    getDecimalValues,
    getInputErrorMessage,
    replaceAll,
    strStartCase,
} from "@/lib/utils";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FreeInputDecimalProps = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    type?: string;
    min?: any;
    isSearch?: boolean;
    isSmallerX?: boolean;
    isSmallerY?: boolean;
    hasLabel?: boolean;
    suffixIcon?: string;
    onClickIcon?: () => void;
    onChange?: (value: any) => void;
    error?: any;
    inputRef?: any;
    onFocus?: () => void;
    onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
};

const FreeInputDecimal = ({
    control,
    name,
    label,
    placeholder,
    disabled = false,
    type = "text",
    min = null,
    isSearch,
    isSmallerX = false,
    isSmallerY = false,
    hasLabel = true,
    suffixIcon,
    onClickIcon,
    onChange,
    error,
    inputRef,
    onFocus,
    onBlur,
    onKeyDown,
}: FreeInputDecimalProps) => {
    const t = useTranslations("common");

    return control ? (
        <Controller
            control={control}
            name={name}
            render={({ field }) => (
                <div className="relative">
                    {hasLabel && (
                        <Label className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </Label>
                    )}
                    <Input
                        placeholder={strStartCase(t(placeholder ?? ""))}
                        disabled={disabled}
                        type={type}
                        minLength={min}
                        suffixIcon={suffixIcon}
                        onClickIcon={onClickIcon}
                        {...field}
                        ref={inputRef ?? field.ref}
                        className={clsx(
                            isSearch && "search-input",
                            "min-w-[80px]",
                            isSmallerX && "px-2",
                            isSmallerY && "h-[38px] py-1"
                        )}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onKeyDown={onKeyDown}
                        onChange={(e) => {
                            const sanitizedValue = getDecimalValues(
                                e.target.value
                            );
                            field.onChange(sanitizedValue);
                            onChange && onChange(sanitizedValue);
                        }}
                    />
                    {error && (
                        <div className="warning-text">
                            {`${getInputErrorMessage(error)}`}
                        </div>
                    )}
                </div>
            )}
        />
    ) : (
        <></>
    );
};

export default FreeInputDecimal;
