import { useEffect, useState } from "react";
import clsx from "clsx";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { EMPLOYEE, MULTI, STUDENT } from "@/lib/constant";
import { Button } from "../base-ui/button";
import { Form } from "../base-ui/form";
import FormInput from "./FormInput";
import Modal from "./Modal";
import { useTranslations } from "next-intl";

const PaginatedTableFilter = ({
    type,
    filter,
    setFilter,
    reset,
}: {
    type: typeof STUDENT | typeof EMPLOYEE | typeof MULTI;
    filter: any;
    setFilter: any;
    reset: any;
}) => {
    const [openSearch, setOpenSearch] = useState(false);
    const [isFiltered, setIsFiltered] = useState(false);

    function onSubmit(data) {
        setFilter({ ...filter, ...data });
        setOpenSearch(false);
    }

    function onReset() {
        reset();
        setOpenSearch(false);
        setIsFiltered(false);
    }

    useEffect(() => {
        setIsFiltered(!!filter.name || !!filter.number);
    }, [filter]);

    const t = useTranslations("common");

    return (
        <>
            <div className="ml-auto flex flex-wrap items-center gap-2">
                <Button
                    variant={"outlineGray"}
                    size={"smaller"}
                    onClick={() => setOpenSearch(true)}
                    className={clsx(
                        isFiltered ? "text-themeGreenDark" : "text-themeLabel"
                    )}
                >
                    {t(`Filter${isFiltered ? "ed" : ""}`)}
                </Button>

                {isFiltered && (
                    <div
                        className="rounded-full border border-input p-1 text-gray-500"
                        onClick={onReset}
                    >
                        <X size={18} />
                    </div>
                )}
            </div>
            <Modal open={openSearch} onOpenChange={setOpenSearch}>
                <h2 className="mb-1">{t("Filter")}</h2>
                <FilterForm
                    type={type}
                    filter={filter}
                    onReset={onReset}
                    onSubmit={onSubmit}
                />
            </Modal>
        </>
    );
};

const FilterForm = ({ type, filter, onReset, onSubmit }) => {
    const tableFilterForm = useForm({
        defaultValues: {
            name: filter.name ?? "",
            number: filter.number ?? "",
        },
    });

    const t = useTranslations("common");

    return (
        <Form {...tableFilterForm}>
            <form className="grid gap-y-5">
                <FormInput control={tableFilterForm.control} name="name" />
                <FormInput
                    control={tableFilterForm.control}
                    name="number"
                    label={
                        type === STUDENT
                            ? "student number"
                            : type === EMPLOYEE
                              ? "employee number"
                              : "number"
                    }
                />
                <div className="mt-1 flex justify-end gap-x-3">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                            tableFilterForm.reset();
                            onReset();
                        }}
                    >
                        {t("Clear")}
                    </Button>
                    <Button
                        onClick={() => onSubmit(tableFilterForm.getValues())}
                    >
                        {t("Search")}
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default PaginatedTableFilter;
