import React from "react";
import { Line } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    ChartOptions,
    Plugin,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    ChartDataLabels
);

interface LineChartProps {
    labels: string[];
    data: number[];
    title?: string;
    yLabel?: string;
    xDataLabelSize?: string;
    xDataLabelRotation?: number;
    xDataLabelBottomPadding?: number;
    lineColor?: string;
    backgroundColor?: string;
    displayLegend?: boolean;
    displayValueInGraph?: boolean;
    displayValueAtXAxis?: boolean;
}

const LineChart: React.FC<LineChartProps> = ({
    labels,
    data,
    title = "Line Chart",
    yLabel = "Value",
    xDataLabelSize = "13px",
    xDataLabelRotation = 45,
    xDataLabelBottomPadding = 50,
    lineColor = "rgba(75,192,192,1)",
    backgroundColor = "rgba(75,192,192,0.2)",
    displayLegend = false,
    displayValueInGraph = false,
    displayValueAtXAxis = false,
}) => {
    const drawValuesBelowXAxis: Plugin<"line"> = {
        id: "drawValuesBelowXAxis",
        afterDatasetsDraw: (chart) => {
            const {
                ctx,
                chartArea: { bottom },
                scales: { x },
                data,
            } = chart;

            ctx.save();

            ctx.textBaseline = "top";
            ctx.textAlign = "center";
            ctx.fillStyle = "black";
            ctx.font = `${xDataLabelSize} Arial`;

            const rotate = (xDataLabelSize?.trim?.() ?? "") !== "13px";
            const yPos = rotate
                ? bottom + xDataLabelBottomPadding
                : bottom + 25;

            data.datasets[0].data.forEach((value, index) => {
                let xPos = x.getPixelForTick(index);

                if (rotate) {
                    xPos -= 30;
                    ctx.save();
                    ctx.translate(xPos, yPos);
                    ctx.rotate((-xDataLabelRotation * Math.PI) / 180);
                    ctx.fillText(`${value}`, 0, 0);
                    ctx.restore();
                } else {
                    ctx.fillText(`${value}`, xPos, yPos);
                }
            });

            ctx.restore();
        },
    };

    const chartData = {
        labels,
        datasets: [
            {
                label: yLabel,
                data,
                borderColor: lineColor,
                backgroundColor,
                fill: true,
                tension: 0,
            },
        ],
    };

    const options: ChartOptions<"line"> = {
        responsive: true,
        layout: {
            padding: {
                bottom: 40,
                right: 20,
            },
        },
        plugins: {
            legend: {
                display: displayLegend,
                labels: {
                    color: "#333",
                    font: {
                        size: 12,
                    },
                },
            },
            title: {
                display: true,
                text: title,
                color: "#000000",
                font: {
                    size: 16,
                },
            },
            datalabels: {
                display: displayValueInGraph,
                anchor: "end",
                align: "top",
                formatter: (value) => `${value}`,
                font: {
                    weight: "bold",
                    size: 10,
                },
                color: "#000000",
                offset: 0,
            },
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 105,
                ticks: {
                    color: "#000000",
                    callback: function (value) {
                        return value === 105 ? "" : `${value}`;
                    },
                    stepSize: 10,
                },
                title: {
                    display: true,
                    text: yLabel,
                    color: "#000000",
                    font: {
                        size: 14,
                    },
                },
                grid: {
                    color: (ctx) =>
                        ctx.tick.value === 105
                            ? "transparent"
                            : "rgba(0,0,0,0.1)",
                },
            },
            x: {
                ticks: {
                    color: "#000000",
                },
            },
        },
    };

    const plugins: Plugin<"line">[] = [];
    if (displayValueAtXAxis) {
        plugins.push(drawValuesBelowXAxis);
    }
    if (displayValueInGraph) {
        plugins.push(ChartDataLabels);
    }

    return <Line data={chartData} options={options} plugins={plugins} />;
};

export default LineChart;
