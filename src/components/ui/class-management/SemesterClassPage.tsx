import { useEffect, useState } from "react";
import React from "react";
import { isArray, lowerCase } from "lodash";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import AssignSemesterClassForm from "@/components/forms/class-subject-management/class-management/AssignSemesterClassForm";
import FilterSemesterClassForm from "@/components/forms/class-subject-management/class-management/FilterSemesterClassForm";
import SemesterClassDownloadReportForm from "@/components/forms/class-subject-management/class-management/SemesterClassDownloadReportForm";
import SemesterClassSettingsForm from "@/components/forms/class-subject-management/class-management/SemesterClassSettingsForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    ACTIVE,
    DEFAULT_FILTER_PARAMS,
    ELECTIVE,
    ENGLISH,
    GET_ALL_PARAMS,
    INACTIVE,
    PRIMARY,
    semesterClassAPIFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    SOCIETY,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { combinedNames, refreshForUpdate, strStartCase } from "@/lib/utils";
import { useLocale, useTranslations } from "next-intl";
import SemesterClassNonPrimaryDownloadReportForm from "@/components/forms/class-subject-management/class-management/SemesterClassNonPrimaryDownloadReportForm";
import FilterChevronButton from "../FilterChevronButton";
import FilterFormWrapper from "../FilterFormWrapper";

const _bySemester = "by_semester";

const SemesterClassesPage = ({ classType }) => {
    useCheckViewPermit(`${classType.toLowerCase()}-semester-class-view`);
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const locale = useLocale();

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );
    const [pagination, setPagination] = useState();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetSettingId, setTargetSettingId] = useState(null);
    const [targetReportId, setTargetReportId] = useState<any>(null);
    const [targetNonPrimaryReportId, setTargetNonPrimaryReportId] =
        useState<any>(null);
    const [targetSemesterSettingId, setTargetSemesterSettingId] =
        useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const [currentSemesterId, setCurrentSemesterId] = useState(null);

    const { axiosQuery: getCurrentSemester } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (result) => {
            const currentSemester = result.data.find(
                (item) => item?.is_current_semester === true
            );
            if (currentSemester?.id) {
                setCurrentSemesterId(currentSemester?.id);
                setFilter({
                    ...filter,
                    semester_setting_id: currentSemester?.id,
                });
            }
        },
    });

    const { data, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchSemesterClasses() {
        getSemesterClasses({
            params: {
                class_type: classType,
                ...semesterClassAPIFilter,
                order_by: {
                    semester_setting: { name: "desc" },
                },
                semester_setting_id: currentSemesterId,
                ...filter,
            },
        });
    }

    useEffect(() => {
        if (currentSemesterId) {
            fetchSemesterClasses();
        }
    }, [filter, locale]);

    useEffect(() => {
        getCurrentSemester({
            params: {
                ...GET_ALL_PARAMS,
            },
        });
    }, []);

    const columns: TableColumnType[] = [
        {
            key: "semester_setting",
            displayAs: "semester",
            hasSort: true,
        },
        {
            key: "class",
            hasSort: true,
        },
        {
            key: "type",
            modify: (value) => strStartCase(t(value)),
        },
        {
            key: "grade",
            hasSort: true,
        },
        {
            key: "homeroom_teacher",
            displayAs: "teacher guardian",
        },
        {
            key: "is_active",
            displayAs: "Status",
            modify: (value) => (
                <div className={`cell-status ${value}`}>{t(value)}</div>
            ),
        },
    ];

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  class: combinedNames(item?.class_model?.translations?.name),
                  semester_setting: item?.semester_setting?.name,
                  semester_setting_id: item?.semester_setting?.id,
                  type: item?.class_model?.type,
                  grade: item?.class_model?.grade?.name ?? "-",
                  homeroom_teacher: combinedNames(
                      item?.homeroom_teacher?.translations?.name
                  ),
                  is_active: item
                      ? lowerCase(item.is_active ? ACTIVE : INACTIVE)
                      : "",
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        let orderBy = {};

        switch (name) {
            case "type":
                orderBy = { class: { type: direction } };
                break;
            case "grade":
                orderBy = { grade: { name: direction } };
                break;
            case "semester_setting":
                orderBy = { semester_setting: { name: direction } };
                break;
            default:
                orderBy = { class: { name: direction } };
                break;
        }

        setFilter({
            ...filter,
            order_by: orderBy,
        });
    }

    function getSemesterClassName(id) {
        if (id === _bySemester) return "";
        const target = data?.find((item) => item.id == id);
        return (
            target?.semester_setting?.name +
            " " +
            target?.class_model?.translations?.name?.[locale]
        );
    }

    const t = useTranslations("common");

    return (
        <>
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>
                        {t("Semester Classes")} ({strStartCase(t(classType))})
                    </h2>
                    <div className="flex flex-wrap items-center gap-3">
                        <Button onClick={() => setOpenCreate(true)}>
                            {t("Assign Class to Semester")}
                        </Button>
                        {classType == PRIMARY &&
                            hasPermit(
                                "semester-class-by-students-in-class-report"
                            ) && (
                                <Button
                                    onClick={() =>
                                        setTargetReportId(_bySemester)
                                    }
                                    variant={"outline"}
                                >
                                    {t("Download Report")}
                                </Button>
                            )}
                        {(classType == SOCIETY || classType == ENGLISH) &&
                            hasPermit(
                                "non-primary-semester-class-by-students-in-class-report"
                            ) && (
                                <Button
                                    onClick={() =>
                                        setTargetNonPrimaryReportId(_bySemester)
                                    }
                                    variant={"outline"}
                                >
                                    {t("Download Report")}
                                </Button>
                            )}
                        {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                            excludeFields={["includes"]}
                        /> */}
                        <FilterChevronButton />
                    </div>
                </div>

                {currentSemesterId && (
                    <FilterFormWrapper>
                        <FilterSemesterClassForm
                            classType={classType}
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                            currentSemesterId={currentSemesterId}
                        />
                    </FilterFormWrapper>
                )}

                {data && (
                    <DataTable
                        columns={
                            classType == SOCIETY || classType == ELECTIVE
                                ? columns.filter((col) => col.key !== "grade")
                                : columns
                        }
                        data={definedData(data)}
                        pagination={pagination}
                        setPagination={setPagination}
                        changePage={(arg) => setFilter({ ...filter, ...arg })}
                        sorted={filter?.order_by}
                        sort={onSort}
                        actionMenu={({ cell }) => (
                            <ActionDropdown>
                                {hasPermit(
                                    `${classType.toLowerCase()}-semester-class-update`
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size"
                                        onClick={() =>
                                            setTargetSettingId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        {t("Settings")}
                                    </DropdownMenuItem>
                                )}
                                {classType == PRIMARY &&
                                    hasPermit(
                                        "semester-class-by-students-in-class-report"
                                    ) && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetReportId(
                                                    cell.row.original.id
                                                );
                                            }}
                                        >
                                            {t("Download Report")}
                                        </DropdownMenuItem>
                                    )}
                                {(classType == SOCIETY ||
                                    classType == ENGLISH) &&
                                    hasPermit(
                                        "non-primary-semester-class-by-students-in-class-report"
                                    ) && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetSemesterSettingId(
                                                    cell.row.original
                                                        .semester_setting_id
                                                );
                                                setTargetNonPrimaryReportId(
                                                    cell.row.original.id
                                                );
                                            }}
                                        >
                                            {t("Download Report")}
                                        </DropdownMenuItem>
                                    )}
                                <DropdownMenuSeparator />
                                {hasPermit(
                                    `${classType.toLowerCase()}-semester-class-delete`
                                ) && (
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        {t("Delete")}
                                    </DropdownMenuItem>
                                )}
                            </ActionDropdown>
                        )}
                    />
                )}
            </Card>

            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <AssignSemesterClassForm
                    classType={classType}
                    isCreate={true}
                    refresh={() => () => refreshForUpdate(filter, setFilter)}
                    close={() => setOpenCreate(false)}
                />
            </Modal>

            {/* setting */}
            <Modal
                open={targetSettingId}
                onOpenChange={setTargetSettingId}
                size="medium"
            >
                <SemesterClassSettingsForm
                    classType={classType}
                    id={targetSettingId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={() => setTargetSettingId(null)}
                />
            </Modal>

            {/* report (primary class) */}
            <Modal open={targetReportId} onOpenChange={setTargetReportId}>
                <SemesterClassDownloadReportForm
                    id={targetReportId}
                    semesterClassName={getSemesterClassName(targetReportId)}
                    isSelectSemester={targetReportId === _bySemester}
                />
            </Modal>

            {/* report (non primary class) */}
            <Modal
                open={targetNonPrimaryReportId}
                onOpenChange={setTargetNonPrimaryReportId}
            >
                <SemesterClassNonPrimaryDownloadReportForm
                    classType={classType}
                    classId={targetNonPrimaryReportId}
                    semesterId={targetSemesterSettingId}
                    semesterClassName={getSemesterClassName(
                        targetNonPrimaryReportId
                    )}
                    isSelectSemester={targetNonPrimaryReportId === _bySemester}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={semesterClassesAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchSemesterClasses}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterSemesterClassForm
                    classType={classType}
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                    currentSemesterId={currentSemesterId}
                />
            </Modal>
        </>
    );
};

export default SemesterClassesPage;
