import { useEffect, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ClassSubjectSettingsForm from "@/components/forms/class-subject-management/subject-management/ClassSubjectSettingsForm";
import FilterClassSubjectForm from "@/components/forms/class-subject-management/subject-management/FilterClassSubjectForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import {
    classSubjectAPI,
    classSubjectsAPIFilter,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    semesterSettingAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { useLocale, useTranslations } from "next-intl";
import { replaceAll, strStartCase } from "@/lib/utils";
import FilterChevronButton from "../FilterChevronButton";
import FilterFormWrapper from "../FilterFormWrapper";

const ClassSubjectsPage = ({ classType }) => {
    useCheckViewPermit(`${classType.toLowerCase()}-class-subject-view`);
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const locale = useLocale();

    const { activeLanguages } = useLanguages();
    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>(
        DEFAULT_FILTER_PARAMS
    );

    const [pagination, setPagination] = useState();

    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState<number | null>(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const [currentSemesterId, setCurrentSemesterId] = useState(null);

    const { data, axiosQuery: getClassSubjects } = useAxios({
        api: classSubjectAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    const { axiosQuery: getCurrentSemester } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (result) => {
            const currentSemester = result.data.find(
                (item) => item?.is_current_semester === true
            );
            if (currentSemester?.id) {
                setCurrentSemesterId(currentSemester?.id);
                setFilter({
                    ...filter,
                    semester_setting_id: currentSemester?.id,
                });
            }
        },
    });

    function fetchClassSubjects() {
        getClassSubjects({
            params: {
                ...classSubjectsAPIFilter,
                class_type: classType,
                ...filter,
            },
        });
    }

    useEffect(() => {
        if (currentSemesterId) {
            fetchClassSubjects();
        }
    }, [filter, locale]);

    useEffect(() => {
        getCurrentSemester({
            params: {
                ...GET_ALL_PARAMS,
            },
        });
    }, []);

    useEffect(() => {
        if (data && activeLanguages) {
            defineColumn();
        }
    }, [data, activeLanguages]);

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "semester",
            },
            {
                key: "class_name",
                displayAs: "class",
                hasSort: true,
            },
            {
                key: "subject_name",
                displayAs: "subject",
                hasSort: true,
            },
            {
                key: "type",
                modify: (value) => strStartCase(t(value)),
            },
            {
                key: "number_of_period_per_week",
                modifyHeader: (header) => (
                    <div className="text-center">
                        {strStartCase(t(replaceAll(header, "_", " ")))}
                    </div>
                ),
                modify: (value) => <div className="text-center">{value}</div>,
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  type: item?.subject?.type,
                  class_name: item?.semester_class?.class_model?.name,
                  subject_name: item?.subject?.name,
                  semester: item?.semester_class?.semester_setting?.name,
                  number_of_period_per_week: item?.number_of_period_per_week,
                  ...item?.subject?.translations?.name,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: ["class_name", "subject_name"].includes(name)
                ? { [name]: direction }
                : { name: { [name]: direction } },
        });
    }

    function closeForm() {
        setTargetId(null);
    }

    const t = useTranslations("common");

    return (
        <>
            <Card styleClass="table-card">
                <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <h2>
                        {t("Class Subjects")} ({strStartCase(t(classType))})
                    </h2>
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                        excludeFields={["includes"]}
                    /> */}
                    <FilterChevronButton />
                </div>

                {currentSemesterId && (
                    <FilterFormWrapper>
                        <FilterClassSubjectForm
                            type={classType}
                            filter={filter}
                            setFilter={setFilter}
                            close={() => setOpenFilter(false)}
                            currentSemesterId={currentSemesterId}
                        />
                    </FilterFormWrapper>
                )}

                <DataTable
                    columns={columns}
                    data={definedData(data)}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                    actionMenu={({ cell }) => (
                        <ActionDropdown>
                            {hasPermit(
                                `${classType.toLowerCase()}-class-subject-update`
                            ) && (
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() =>
                                        setTargetId(cell.row.original.id)
                                    }
                                >
                                    {t("Settings")}
                                </DropdownMenuItem>
                            )}
                            {hasPermit(
                                `${classType.toLowerCase()}-class-subject-delete`
                            ) && (
                                <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                        className="c-text-size text-red-600"
                                        onClick={() =>
                                            setTargetDeleteId(
                                                cell.row.original.id
                                            )
                                        }
                                    >
                                        {t("Delete")}
                                    </DropdownMenuItem>
                                </>
                            )}
                        </ActionDropdown>
                    )}
                />
            </Card>

            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <ClassSubjectSettingsForm
                    id={targetId}
                    refresh={fetchClassSubjects}
                    close={closeForm}
                    classType={classType}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={classSubjectAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchClassSubjects}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterClassSubjectForm
                    type={classType}
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                    currentSemesterId={currentSemesterId}
                />
            </Modal>
        </>
    );
};

export default ClassSubjectsPage;
