import React, { useCallback } from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import { Control, Controller } from "react-hook-form";
import { Input } from "@/components/base-ui/input";
import {
    getDecimalValues,
    getInputErrorMessage,
    replaceAll,
} from "@/lib/utils";
import { Checkbox } from "../base-ui/checkbox";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FreeCheckboxProps = {
    control: Control<any>;
    name: string;
    label?: string;
    disabled?: boolean;
    hasLabel?: boolean;
    error?: any;
    onChange?: (value: any) => void;
};

const FreeCheckbox = ({
    control,
    name,
    label,
    disabled = false,
    hasLabel = true,
    error,
    onChange,
}: FreeCheckboxProps) => {
    const t = useTranslations("common");
    const renderInput = useCallback(
        ({ field }) => (
            <>
                <div className="flex items-center">
                    <Checkbox
                        checked={!!field.value}
                        onCheckedChange={(value) => {
                            field.onChange(value);
                            onChange ? onChange(value) : null;
                        }}
                        disabled={disabled}
                    />
                    {hasLabel && (
                        <Label className="ml-2 capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </Label>
                    )}
                </div>
                {error && (
                    <div className="warning-text">
                        {`${getInputErrorMessage(error)}`}
                    </div>
                )}
            </>
        ),
        []
    );
    return control ? (
        <Controller control={control} name={name} render={renderInput} />
    ) : (
        <></>
    );
};

export default FreeCheckbox;
