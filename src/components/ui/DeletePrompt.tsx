import React from "react";
import { useAxios } from "@/lib/hook";
import { Button } from "../base-ui/button";
import { DialogFooter } from "../base-ui/dialog";
import { useTranslations } from "next-intl";

type DeletePromptProps = {
    api: string;
    id: number | string | null;
    refresh: () => void;
    close: () => void;
};

const DeletePrompt = ({ api, id, close, refresh }: DeletePromptProps) => {
    const t = useTranslations("common");
    const { axiosDelete } = useAxios({
        api: api,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit() {
        if (id) {
            axiosDelete({ id });
        }
    }
    return (
        <>
            <p className="mt-3 font-medium">
                {t("Are you sure you want to delete this?")}
            </p>
            <DialogFooter className={"mt-2"}>
                <Button variant="outline" onClick={close}>
                    {t("Cancel")}
                </Button>
                <Button onClick={onSubmit}>{t("Confirm")}</Button>
            </DialogFooter>
        </>
    );
};

export default DeletePrompt;
