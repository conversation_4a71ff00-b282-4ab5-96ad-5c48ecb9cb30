import { useState } from "react";
import { format, parseISO } from "date-fns";
import { isString } from "lodash";
import { CalendarIcon } from "lucide-react";
import { Controller } from "react-hook-form";
import { DATE_FORMAT } from "@/lib/constant";
import { getInputErrorMessage, replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Calendar } from "../base-ui/calendar";
import { Label } from "../base-ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../base-ui/popover";
import { useTranslations } from "next-intl";

const FreeDatePicker = ({
    control,
    name,
    label,
    error,
    placeholder,
    onChange,
    hasLabel = false,
    isSmaller = true,
    showSelection = true,
    isDisabled = false,
    disableDateBefore,
    align = "start",
}: {
    control: any;
    name: string;
    label?: string;
    error?: any;
    onChange?: (value) => void;
    placeholder?: string;
    hasLabel?: boolean;
    isSmaller?: boolean;
    showSelection?: boolean;
    isDisabled?: boolean;
    disableDateBefore?: Date;
    align?: "start" | "end" | "center";
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const t = useTranslations("common");

    return control ? (
        <div className="">
            {hasLabel && (
                <Label className={"label"}>
                    {t(replaceAll(label || name, "_", " "))}
                </Label>
            )}
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <Popover open={isOpen} onOpenChange={setIsOpen}>
                        <PopoverTrigger asChild>
                            <Button
                                onClick={() => setIsOpen(true)}
                                variant={"outlineGray"}
                                size={isSmaller ? "smaller" : "primary"}
                                className={
                                    "w-full rounded-md border-input pl-4 text-left font-normal"
                                }
                                disabled={isDisabled}
                            >
                                <div className="pr-2">
                                    {showSelection && field.value ? (
                                        format(field.value, DATE_FORMAT.DMY)
                                    ) : (
                                        <span className="text-themeLabel">
                                            {t(placeholder ?? "Pick a date")}
                                        </span>
                                    )}
                                </div>
                                <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent
                            className="w-auto bg-white p-0"
                            align={align}
                        >
                            <Calendar
                                mode="single"
                                defaultMonth={field.value}
                                selected={
                                    isString(field.value)
                                        ? parseISO(field.value)
                                        : field.value
                                }
                                onSelect={(date) => {
                                    field.onChange(date);
                                    onChange && onChange(date);
                                    setIsOpen(false);
                                }}
                                disabled={(date) =>
                                    date <
                                    (disableDateBefore
                                        ? disableDateBefore
                                        : new Date("1900-01-01"))
                                }
                                initialFocus
                            />
                        </PopoverContent>
                    </Popover>
                )}
            />
            {error && (
                <div className="warning-text">
                    {`${getInputErrorMessage(error)}`}
                </div>
            )}
        </div>
    ) : (
        <></>
    );
};

export default FreeDatePicker;
