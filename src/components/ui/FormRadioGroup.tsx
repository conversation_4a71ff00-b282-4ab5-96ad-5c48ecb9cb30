import clsx from "clsx";
import { isArray } from "lodash";
import {
    formatForSelect,
    formatStringsForSelect,
    replaceAll,
} from "@/lib/utils";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "../base-ui/form";
import { RadioGroup, RadioGroupItem } from "../base-ui/radio-group";
import { useTranslations } from "next-intl";

type FormRadioGroupProps = {
    control: any;
    name: string;
    label?: string;
    styleClass?: string;
    textStyleClass?: string;
    options: any;
    isStringOptions?: boolean;
    isHorizontal?: boolean;
    hasLabel?: boolean;
    onChange?: (value: any) => void;
};

const FormRadioGroup = ({
    control,
    name = "",
    label = "",
    styleClass = "",
    textStyleClass = "",
    options,
    isStringOptions = false,
    isHorizontal = false,
    hasLabel = true,
    onChange,
}: FormRadioGroupProps) => {
    const t = useTranslations("common");

    const radioGroupName = t(replaceAll(label || name, "_", " "));
    const formattedOptions = isArray(options)
        ? isStringOptions
            ? formatStringsForSelect(options, t)
            : formatForSelect(options)
        : [];

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    <FormLabel className="capitalize">
                        {hasLabel && (
                            <FormLabel className={clsx("capitalize")}>
                                {radioGroupName}
                            </FormLabel>
                        )}
                    </FormLabel>

                    <FormControl>
                        <RadioGroup
                            onValueChange={(value) => {
                                field.onChange(value);
                                onChange && onChange(value);
                            }}
                            defaultValue={field.value}
                            className={clsx(
                                isHorizontal
                                    ? "ml-0.5 mt-3.5 flex flex-row flex-wrap space-x-4"
                                    : "flex flex-col"
                            )}
                        >
                            {formattedOptions &&
                                formattedOptions.map((option) => (
                                    <FormItem
                                        key={option.value}
                                        className={clsx(
                                            "flex flex-row items-start space-x-1.5 lg:space-x-3",
                                            styleClass
                                        )}
                                    >
                                        <FormControl>
                                            <RadioGroupItem
                                                value={option.value}
                                            />
                                        </FormControl>
                                        <FormLabel
                                            className={clsx(
                                                "flex cursor-pointer flex-row font-normal capitalize leading-normal text-themeBlack",
                                                textStyleClass
                                            )}
                                        >
                                            {option.label}
                                        </FormLabel>
                                    </FormItem>
                                ))}
                        </RadioGroup>
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormRadioGroup;
