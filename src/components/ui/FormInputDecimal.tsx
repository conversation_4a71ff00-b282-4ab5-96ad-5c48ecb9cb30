import React from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import { Control } from "react-hook-form";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { Input } from "@/components/base-ui/input";
import {
    firstStartCase,
    getDecimalValues,
    getPositiveDecimalValues,
    replaceAll,
} from "@/lib/utils";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FormInputDecimalProps = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    type?: string;
    min?: any;
    isSearch?: boolean;
    hasLabel?: boolean;
    onlyPositive?: boolean;
    suffixIcon?: string;
    step?: any;
    onClickIcon?: () => void;
    onChange?: (value: string) => void;
};

const FormInputDecimal = ({
    control,
    name,
    label,
    placeholder,
    disabled = false,
    type = "text",
    min = null,
    isSearch,
    hasLabel = true,
    onlyPositive = true,
    suffixIcon,
    step,
    onClickIcon,
    onChange = () => {},
}: FormInputDecimalProps) => {
    const t = useTranslations("common");

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hasLabel && (
                        <FormLabel className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </FormLabel>
                    )}
                    <FormControl>
                        <Input
                            placeholder={firstStartCase(placeholder ?? "")}
                            disabled={disabled}
                            type={type}
                            minLength={min}
                            suffixIcon={suffixIcon}
                            step={step}
                            onClickIcon={onClickIcon}
                            {...field}
                            className={clsx(isSearch && "search-input")}
                            onChange={(e) => {
                                const sanitizedValue = onlyPositive
                                    ? getPositiveDecimalValues(e.target.value)
                                    : getDecimalValues(e.target.value);
                                field.onChange(sanitizedValue);
                                onChange && onChange(sanitizedValue);
                            }}
                        />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormInputDecimal;
