import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { useEffect, useState } from "react";
import { CalendarIcon } from "lucide-react";
import { DateRangePicker as ReactDateRangePicker } from "react-date-range";
import {
    cn,
    getDateRangeDisplay,
    getInputErrorMessage,
    replaceAll,
} from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Label } from "../base-ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../base-ui/popover";
import { useLocale, useTranslations } from "next-intl";
import { enUS, zhCN } from "date-fns/locale";

const DateRangePicker = ({
    label = "",
    placeholder = "",
    range,
    setRange,
    isSmaller = false,
    showSelection = true,
    isDisabled = false,
    error,
}: {
    label?: string;
    placeholder?: string;
    range: any;
    setRange: any;
    isSmaller?: boolean;
    showSelection?: boolean;
    error?: any;
    isDisabled?: boolean;
}) => {
    const t = useTranslations("common");
    const locale = useLocale();

    const defaultRange = {
        startDate: new Date(),
        endDate: new Date(),
        key: "selection",
    };

    const [localRange, setLocalRange] = useState(range ?? defaultRange);
    const [open, setOpen] = useState(false);

    useEffect(() => {
        if (range) {
            setLocalRange(range);
        }
    }, [range]);

    return (
        <>
            {label && (
                <Label className={"label"}>
                    {t(replaceAll(label, "_", " "))}
                </Label>
            )}
            {isDisabled ? (
                <div
                    className={cn(
                        "flex w-full items-center rounded-md border border-themeGray2 px-4 py-2 pl-4 text-left font-normal text-themeLabel"
                    )}
                >
                    <span className="text-[14px]">
                        {range &&
                            getDateRangeDisplay(
                                range.startDate,
                                range.endDate
                                // locale
                            )}
                    </span>
                </div>
            ) : (
                <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                        <Button
                            size={isSmaller ? "smaller" : "primary"}
                            variant={"outlineGray"}
                            className="w-full rounded-md border-input pl-4 text-left font-normal"
                            onClick={() => setOpen(true)}
                        >
                            <div className="pr-2">
                                {range && showSelection ? (
                                    <span className="text-[14px]">
                                        {getDateRangeDisplay(
                                            range.startDate,
                                            range.endDate
                                            // locale
                                        )}
                                    </span>
                                ) : (
                                    <span className="font-medium text-themeLabel">
                                        {placeholder ?? ""}
                                    </span>
                                )}
                            </div>
                            <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent
                        className="w-auto bg-white p-0"
                        align="start"
                    >
                        <div className="px-2 pb-3">
                            <ReactDateRangePicker
                                colors={["#44B656"]}
                                rangeColors={["#44B656"]}
                                ranges={[localRange]}
                                onChange={(value) => {
                                    console.log(value);
                                    setLocalRange(value.selection);
                                }}
                                showDateDisplay={false}
                                showSelectionPreview={false}
                                // locale={locale === "zh" ? zhCN : enUS}
                            />
                            <div className="flex flex-wrap justify-end gap-3">
                                <Button
                                    className="text-[14px] font-medium text-gray-600"
                                    size={"smaller"}
                                    variant={"ghost"}
                                    onClick={() => {
                                        setLocalRange(defaultRange);
                                        setRange(null);
                                    }}
                                >
                                    {t("Reset")}
                                </Button>
                                <Button
                                    size={"smaller"}
                                    variant={"outline"}
                                    onClick={() => {
                                        setOpen(false);
                                        setRange(localRange);
                                    }}
                                >
                                    {t("Confirm")}
                                </Button>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            )}
            {error && (
                <div className="warning-text">
                    {`${getInputErrorMessage(error)}`}
                </div>
            )}
        </>
    );
};

export default DateRangePicker;
