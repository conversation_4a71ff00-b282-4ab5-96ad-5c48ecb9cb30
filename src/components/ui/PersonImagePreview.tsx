import Image from "next/image";
import React, { useState } from "react";

const PersonImagePreview = ({ studentName, url }) => {
    const [isLoadingPhoto, setIsLoadingPhoto] = useState(true);

    return (
        url && (
            <div className="relative flex min-h-10 min-w-10 flex-col items-center justify-center">
                <div className="mb-3 px-4 text-center font-medium">
                    {studentName}
                </div>

                {isLoadingPhoto && (
                    <div className="absolute mb-3 text-center text-themeLabel">
                        Loading...
                    </div>
                )}
                <Image
                    src={url}
                    alt="Profile photo"
                    className="relative z-10 mx-auto h-auto w-[290px] rounded-sm"
                    width={290}
                    height={290}
                    onLoad={() => {
                        setIsLoadingPhoto(false);
                    }}
                    onError={() => setIsLoadingPhoto(false)}
                />
            </div>
        )
    );
};

export default PersonImagePreview;
