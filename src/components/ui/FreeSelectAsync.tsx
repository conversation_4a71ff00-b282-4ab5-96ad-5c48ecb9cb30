import React from "react";
import { Controller } from "react-hook-form";
import AsyncSelect from "react-select/async";
import { selectStyles } from "@/lib/constant";
import { getInputErrorMessage, replaceAll } from "@/lib/utils";
import { Label } from "../base-ui/label";
import { useTranslations } from "next-intl";

type FreeSelectAsyncProps = {
    control: any;
    value: any;
    name: string;
    label?: string;
    placeholder?: string;
    hasLabel?: boolean;
    isDisabled?: boolean;
    minWidth?: number;
    loadOptions?: (inputValue: any, callback: any) => void;
    onChange?: (value: any) => void;
    error?: any;
};

const FreeSelectAsync = ({
    control,
    value,
    name,
    label,
    placeholder,
    hasLabel = true,
    isDisabled,
    minWidth = 200,
    loadOptions,
    onChange = () => {},
    error,
}: FreeSelectAsyncProps) => {
    const t = useTranslations("common");
    const selectName = t(replaceAll(label || name, "_", " "));

    return control ? (
        <div>
            {hasLabel && <Label className={"label"}>{t(selectName)}</Label>}
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <div style={{ minWidth: minWidth }}>
                        <AsyncSelect
                            isClearable
                            cacheOptions
                            defaultOptions
                            loadOptions={loadOptions}
                            {...field}
                            placeholder={t(placeholder ?? "Type to search")}
                            isDisabled={isDisabled}
                            value={value}
                            onChange={(selectedOption) => {
                                field.onChange(selectedOption?.value || null);
                                onChange(selectedOption);
                            }}
                            styles={selectStyles}
                        />
                        {error && (
                            <div className="warning-text">
                                {`${getInputErrorMessage(error)}`}
                            </div>
                        )}
                    </div>
                )}
            />
        </div>
    ) : (
        <></>
    );
};

export default FreeSelectAsync;
