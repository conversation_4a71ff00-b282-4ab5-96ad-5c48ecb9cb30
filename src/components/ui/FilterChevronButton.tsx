import { useFilter } from "@/lib/store";
import clsx from "clsx";
import { ChevronDownCircle } from "lucide-react";

const FilterChevronButton = () => {
    const { isExpanded, setIsExpanded } = useFilter();

    return (
        <ChevronDownCircle
            className={clsx(
                "cursor-pointer text-themeGreen transition",
                isExpanded && "rotate-180"
            )}
            onClick={() => {
                setIsExpanded(!isExpanded);
            }}
        />
    );
};

export default FilterChevronButton;
