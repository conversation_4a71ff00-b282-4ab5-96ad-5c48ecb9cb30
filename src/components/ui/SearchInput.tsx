import React from "react";
import clsx from "clsx";
import { SearchIcon } from "lucide-react";

type SearchInputProps = {
    placeholder?: string;
    isGreen?: boolean;
    styleClass?: string;
};

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
    ({ placeholder = "Search", isGreen = false, styleClass = "" }, ref) => {
        return (
            <div
                className={clsx(
                    "relative flex w-full items-center rounded-lg",
                    isGreen ? "bg-[#3A9C4A]" : "bg-themeGray2",
                    styleClass
                )}
            >
                <div className="absolute left-3 w-fit">
                    <SearchIcon
                        className={isGreen ? "text-white" : "text-themeBlack"}
                        size={18}
                    />
                </div>
                <input
                    ref={ref || null}
                    type="text"
                    placeholder={placeholder}
                    className={clsx(
                        "c-text-size h-10 w-full rounded-lg bg-transparent py-2 pl-10 pr-4 outline-none placeholder:font-medium focus:ring-1",
                        isGreen
                            ? "text-white placeholder:text-white focus:ring-white focus:ring-opacity-50"
                            : "placeholder:text-gray-500 focus:ring-gray-200"
                    )}
                />
            </div>
        );
    }
);

SearchInput.displayName = "SearchInput";

export default SearchInput;
