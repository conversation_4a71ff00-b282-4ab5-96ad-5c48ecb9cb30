import clsx from "clsx";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

const Tabs = ({
    list,
    selected,
    setSelected,
    styleClass = "",
    onChangeTab,
    isBlack = false,
}: {
    list: any;
    selected: any;
    setSelected: any;
    styleClass?: string;
    onChangeTab?: (selected: string) => void;
    isBlack?: boolean;
}) => {
    function getColor(isSelected: boolean) {
        return isSelected
            ? isBlack
                ? "font-bold text-themeBlack border-themeBlack"
                : "font-bold text-themeGreen border-themeGreen"
            : isBlack
              ? "text-themeBlack font-bold"
              : "text-themeLabel font-semibold border-transparent hover:text-opacity-80";
    }

    const t = useTranslations("common");

    return list?.length > 0 ? (
        <div
            className={cn(
                "c-text-size mb-5 flex gap-x-3 border-b leading-none lg:gap-x-5",
                styleClass
            )}
        >
            {list.map((tab) => (
                <div
                    key={tab}
                    className={clsx(
                        "-mb-[1px] flex cursor-pointer items-center border-b-2 px-0.5 pb-2 text-center capitalize",
                        getColor(selected === tab)
                    )}
                    onClick={() => {
                        setSelected(tab);
                        onChangeTab ? onChangeTab(tab) : null;
                    }}
                >
                    {t(tab).toLowerCase()}
                </div>
            ))}
        </div>
    ) : (
        <></>
    );
};

export default Tabs;
