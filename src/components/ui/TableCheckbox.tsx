import React from "react";
import { Checkbox } from "../base-ui/checkbox";

const TableCheckbox = ({ table, row }) => {
    return table ? (
        <Checkbox
            checked={
                table?.getIsAllPageRowsSelected() ||
                (table?.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
                table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
        />
    ) : (
        <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
        />
    );
};

export default TableCheckbox;
