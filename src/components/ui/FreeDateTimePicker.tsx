import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@radix-ui/react-popover";
import { format, parseISO } from "date-fns";
import { isString } from "lodash";
import { CalendarIcon } from "lucide-react";
import { Controller } from "react-hook-form";
import { DATE_FORMAT } from "@/lib/constant";
import { getInputErrorMessage, replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Calendar } from "../base-ui/calendar";
import { Label } from "../base-ui/label";
import { TimePicker } from "./TimePicker";
import { useTranslations } from "next-intl";

const FreeDateTimePicker = ({
    control,
    name,
    label,
    hasLabel = false,
    error,
}: {
    control: any;
    name: string;
    label?: string;
    hasLabel?: boolean;
    error?: any;
}) => {
    const t = useTranslations("common");

    return control ? (
        <div>
            {hasLabel && (
                <Label className={"label"}>
                    {t(replaceAll(label || name, "_", " "))}
                </Label>
            )}
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant={"outlineGray"}
                                className={
                                    "w-full rounded-md border-input px-3 text-left font-normal"
                                }
                            >
                                <div className="pr-2">
                                    {field.value ? (
                                        format(
                                            field.value,
                                            DATE_FORMAT.forDisplay
                                        )
                                    ) : (
                                        <span className="text-themeLabel">
                                            Pick a date &amp; time
                                        </span>
                                    )}
                                </div>
                                <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent
                            className="mb-5 w-auto rounded-sm border bg-white p-0"
                            align="start"
                            side="bottom"
                            avoidCollisions={false}
                        >
                            <Calendar
                                mode="single"
                                defaultMonth={field.value}
                                selected={
                                    isString(field.value)
                                        ? parseISO(field.value)
                                        : field.value
                                }
                                onSelect={(date) => field.onChange(date)}
                                disabled={(date) =>
                                    date < new Date("1900-01-01")
                                }
                            />
                            <div className="border-t border-border p-3 pt-2">
                                <TimePicker
                                    setDate={field.onChange}
                                    date={field?.value ?? undefined}
                                />
                            </div>
                        </PopoverContent>
                    </Popover>
                )}
            />
            {error && (
                <div className="warning-text">
                    {`${getInputErrorMessage(error)}`}
                </div>
            )}
        </div>
    ) : (
        <></>
    );
};

export default FreeDateTimePicker;
