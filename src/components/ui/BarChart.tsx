import React from "react";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ChartOptions,
    Plugin,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ChartDataLabels
);

interface BarChartProps {
    labels: string[];
    data: number[];
    title?: string;
    yLabel?: string;
    yMax?: number;
    xDataLabelSize?: number;
    barColor?: string;
    displayLegend?: boolean;
    displayValueOnBar?: boolean;
    displayValueAtXAxis?: boolean;
}

const BarChart: React.FC<BarChartProps> = ({
    labels,
    data,
    title = "Bar Chart",
    yLabel = "Value",
    yMax,
    xDataLabelSize = "13px",
    barColor = "rgba(75,192,192,0.8)",
    displayLegend = false,
    displayValueOnBar = false,
    displayValueAtXAxis = false,
}) => {
    const drawValuesBelowXAxis: Plugin<"bar"> = {
        id: "drawValuesBelowXAxis",
        afterDatasetsDraw: (chart) => {
            const {
                ctx,
                chartArea: { bottom },
                scales: { x },
                data,
            } = chart;

            ctx.save();

            ctx.textBaseline = "top";
            ctx.textAlign = "center";
            ctx.fillStyle = "black";
            ctx.font = `${xDataLabelSize} Arial`;

            const yPos = bottom + 28;

            data.datasets[0].data.forEach((value, index) => {
                const xPos = x.getPixelForTick(index);
                ctx.fillText(`${value}`, xPos, yPos);
            });

            ctx.restore();
        },
    };

    const chartData = {
        labels,
        datasets: [
            {
                label: yLabel,
                data,
                backgroundColor: barColor,
            },
        ],
    };

    const options: ChartOptions<"bar"> = {
        responsive: true,
        layout: {
            padding: {
                bottom: 30,
                right: 20,
            },
        },
        plugins: {
            legend: {
                display: displayLegend,
                labels: {
                    color: "#333",
                    font: {
                        size: 12,
                    },
                },
            },
            title: {
                display: true,
                text: title,
                color: "#000000",
                font: {
                    size: 16,
                },
                padding: {
                    bottom: 20,
                },
            },
            datalabels: {
                display: displayValueOnBar,
                anchor: "end",
                align: "top",
                formatter: (value) => `${value}`,
                font: {
                    weight: "bold",
                    size: 10,
                },
                color: "#000000",
                offset: 2,
            },
        },
        scales: {
            y: {
                beginAtZero: true,
                max: yMax,
                ticks: {
                    color: "#000000",
                },
                title: {
                    display: true,
                    text: yLabel,
                    color: "#000000",
                    font: {
                        size: 14,
                    },
                },
            },
            x: {
                ticks: {
                    color: "#000000",
                },
            },
        },
    };

    const plugins: Plugin<"bar">[] = [];
    if (displayValueAtXAxis) {
        plugins.push(drawValuesBelowXAxis);
    }

    return <Bar data={chartData} options={options} plugins={plugins} />;
};

export default BarChart;
