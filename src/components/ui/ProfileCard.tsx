import { useLocale, useTranslations } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import clsx from "clsx";
import {
    capitalize,
    chunk,
    groupBy,
    isArray,
    isEmpty,
    lowerCase,
    orderBy,
} from "lodash";
import { ChevronDownCircle, Loader2, RotateCcw } from "lucide-react";
import Image from "next/image";
import {
    accountingUnpaidFeeAPI,
    ACTIVE,
    appCurrencySymbol,
    billingDocumentAPI,
    CHUNKED_FILTER_PARAMS,
    CONFIRMED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    EMPLOYEE,
    GET_ALL_PARAMS,
    INACTIVE,
    PAID,
    PARTIAL,
    POSTED,
    rewardPunishmentRecordsAPI,
    semesterSettingAPI,
    SOCIETY,
    specialCompetitionAPI,
    STUDENT,
    studentComprehensiveAssessmentRecordAPI,
    TableColumnType,
    UNKNOWN,
    UNPAID,
    VOIDED,
} from "@/lib/constant";
import { useAxios, usePaginatedTable } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    convertDateTime,
    displayDateTime,
    formatEnumToString,
    formatNumberForRead,
    formatUnderscores,
    isObjectType,
    isValueTrue,
    optionUserLabel,
    replaceAll,
    strStartCase,
} from "@/lib/utils";
import DataTable from "./DataTable";
import FormDivider from "./FormDivider";
import Modal from "./Modal";
import Tabs from "./Tabs";
import { format } from "date-fns";
import { Button } from "../base-ui/button";
import TableFilterBtn from "./TableFilterBtn";
import ActionDropdown from "./ActionDropdown";
import { DropdownMenuItem } from "../base-ui/dropdown-menu";
import FilterBillingDocumentForm from "../forms/accounting/FilterBillingDocumentForm";
import ViewBillingDocument from "../forms/accounting/ViewBillingDocument";
import FilterUnpaidItemForm from "../forms/accounting/FilterUnpaidItemForm";

const IconProfilePhotoPlaceholder = "/icons/icon-profile-photo-placeholder.svg";

const ProfileCard = ({
    title,
    data,
    type,
    cardStyleClass = "",
}: {
    title?: string;
    data: any;
    type: typeof STUDENT | typeof EMPLOYEE;
    cardStyleClass?: string;
}) => {
    const userProfile = useUserProfile((state) => state.userProfile);
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const _personal_information = "personal information";
    const _guardians = "guardians";
    const _disciplinary_record = "disciplinary record";
    const _comprehensive_assessment_record = "comprehensive assessment record";
    const _academic_report = "academic report";
    const _employment_history = "employment history";
    const _fee_record = "fee record";
    const _activity_report = "activity report";
    const _classes = "classes";
    const _cocu = "society report";
    const _attendance_report = "attendance report";

    const studentTabs = [
        ...(hasPermit("student-tab-personal-information-view")
            ? [_personal_information]
            : []),
        ...(hasPermit("student-tab-guardians-view") ? [_guardians] : []),
        ...(hasPermit("student-report-card-view") ? [_academic_report] : []),
        ...(hasPermit("student-tab-disciplinary-record-view")
            ? [_disciplinary_record]
            : []),
        _cocu,
        _activity_report,
        _attendance_report,
        ...(hasPermit("student-tab-comprehensive-assessment-record-view")
            ? [_comprehensive_assessment_record]
            : []),
        _classes,
        ...(hasPermit("student-tab-fee-records-view") ? [_fee_record] : []),
    ];
    const employeeTabs = hasPermit("employee-tab-personal-information-view")
        ? [_personal_information]
        : null;

    const locale = useLocale();
    const [isLoadingPhoto, setIsLoadingPhoto] = useState(true);
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);

    const [displayType, setDisplayType] = useState(
        type === STUDENT ? studentTabs?.[0] : employeeTabs?.[0]
    );
    const tabList = type === STUDENT ? studentTabs : employeeTabs;

    function basicInformation(data) {
        const basicInfo = {
            name: combinedNames(data?.translations?.name),
            gender:
                data?.gender === UNKNOWN
                    ? "Others"
                    : capitalize(data?.gender ?? "-"),
        };
        if (displayType == _academic_report) {
            return {
                name: basicInfo.name,
            };
        }

        if (type === STUDENT) {
            return {
                ...basicInfo,
                student_number: data?.student_number,
                grade: combinedNames(
                    data?.current_primary_class?.semester_class?.class_model
                        ?.grade?.translations?.name
                ),
                current_class: data?.current_primary_class
                    ? `${combinedNames(
                          data?.current_primary_class?.semester_class
                              ?.class_model?.translations?.name
                      )} (${data?.current_primary_class?.semester_setting?.name})`
                    : "-",
                status: lowerCase(data?.is_active ? ACTIVE : INACTIVE),
                grading_framework:
                    data?.active_grading_framework?.grading_framework?.name ??
                    "-",
            };
        } else {
            return {
                ...basicInfo,
                employee_number: data?.employee_number,
                badge_number: data?.badge_no,
                type: data?.employee_category?.translations?.name?.[locale],
                status: capitalize(data?.status),
            };
        }
    }

    return isObjectType(data) ? (
        <>
            <div>
                {title && <h3 className="text-themeGreenDark">{t(title)}</h3>}
                <div
                    className={clsx(
                        "flex flex-col lg:flex-row lg:gap-x-8",
                        title && "mt-5",
                        cardStyleClass
                    )}
                >
                    {displayType != _academic_report && (
                        <div className="relative w-[100px]">
                            {data?.photo && isLoadingPhoto && (
                                <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                                    <Loader2
                                        className="animate-spin text-themeGray"
                                        size={30}
                                    />
                                </div>
                            )}
                            {data?.photo ? (
                                <Image
                                    src={data?.photo}
                                    alt="Profile Photo"
                                    width={100}
                                    height={100}
                                    className="h-auto w-auto max-w-[100px] cursor-pointer rounded-sm"
                                    onLoad={() => {
                                        setIsLoadingPhoto(false);
                                    }}
                                    onError={() => setIsLoadingPhoto(false)}
                                    onClick={() => {
                                        if (data?.photo) {
                                            setIsImageModalOpen(true);
                                        }
                                    }}
                                />
                            ) : (
                                <Image
                                    src={IconProfilePhotoPlaceholder}
                                    alt="Profile Photo"
                                    width={100}
                                    height={100}
                                    className="h-auto w-auto max-w-[100px] cursor-pointer rounded-sm opacity-10"
                                    unoptimized
                                />
                            )}
                        </div>
                    )}

                    <div className="mt-2 grid flex-grow gap-4 lg:mt-0 lg:grid-cols-3">
                        {Object.entries(basicInformation(data)).map(
                            ([key, value], index) => (
                                <Fragment key={key}>
                                    <div className="flex flex-col">
                                        <p className="font-medium capitalize text-themeLabel">
                                            {t(
                                                replaceAll(
                                                    key === "nric"
                                                        ? "NRIC"
                                                        : key,
                                                    "_",
                                                    " "
                                                )
                                            )}
                                        </p>
                                        <p
                                            className={clsx(
                                                "text-themeText font-medium",
                                                key === "status" &&
                                                    type === STUDENT &&
                                                    `cell-status ${value}`
                                            )}
                                        >
                                            {isEmpty(value?.toString())
                                                ? "-"
                                                : key === "status"
                                                  ? t(value)
                                                  : value}
                                        </p>
                                    </div>
                                </Fragment>
                            )
                        )}
                    </div>
                </div>

                <div className="mt-6 overflow-auto">
                    <div className="w-fit min-w-full bg-gray-50 pt-2">
                        {userProfile && (
                            <Tabs
                                list={tabList}
                                selected={displayType}
                                setSelected={setDisplayType}
                                styleClass="mt-0"
                                isBlack={true}
                            />
                        )}
                    </div>
                    <>
                        {displayType === _personal_information && (
                            <PersonalInformationCard
                                data={data}
                                type={type}
                                locale={locale}
                            />
                        )}

                        {displayType === _guardians && type === STUDENT && (
                            <GuardiansCard data={data} locale={locale} />
                        )}

                        {displayType === _disciplinary_record &&
                            type === STUDENT && (
                                <DisciplinaryRecordCard
                                    data={data}
                                    locale={locale}
                                />
                            )}

                        {displayType === _comprehensive_assessment_record &&
                            type === STUDENT && (
                                <ComrepehensiveAssessmentRecordCard
                                    data={data}
                                    locale={locale}
                                />
                            )}

                        {displayType === _academic_report &&
                            type === STUDENT && (
                                <AcademicReportCard data={data} />
                            )}

                        {displayType === _fee_record && type === STUDENT && (
                            <FeeRecordCard data={data} locale={locale} />
                        )}

                        {displayType === _activity_report && (
                            <ActivityReportCard data={data} locale={locale} />
                        )}

                        {displayType === _classes && (
                            <HistoricalClasses data={data} locale={locale} />
                        )}

                        {displayType === _cocu && (
                            <HistoricalCocu data={data} locale={locale} />
                        )}

                        {displayType === _attendance_report && (
                            <AttendanceRecord data={data} />
                        )}
                    </>
                </div>
            </div>

            <Modal open={isImageModalOpen} onOpenChange={setIsImageModalOpen}>
                <div className="relative flex min-h-10 min-w-10 flex-col items-center justify-center">
                    <div className="mb-3 px-4 text-center font-medium">
                        {optionUserLabel("", data?.translations?.name)}
                    </div>
                    <div className="absolute text-center text-themeLabel">
                        {t("Loading")}...
                    </div>
                    <Image
                        src={data?.photo}
                        alt="Profile photo"
                        className="relative z-10 mx-auto h-auto w-[290px] rounded-sm bg-white"
                        width={290}
                        height={290}
                    />
                </div>
            </Modal>
        </>
    ) : (
        <></>
    );
};

const PersonalInformationCard = ({ data, type, locale }) => {
    const t = useTranslations("common");
    function formatData(data) {
        const commonInfo = {
            nric: data?.nric,
            passport_number: data?.passport_number,
            race: data?.race?.translations?.name[locale],
            religion: data?.religion?.translations?.name[locale],
            date_of_birth: data?.date_of_birth,
            address: data?.address,
            address_2: data?.address_2,
            postal_code: data?.postal_code,
            city: capitalize(data?.city),
            state: capitalize(data?.state?.translations?.name[locale]),
            country: capitalize(data?.country?.translations?.name[locale]),
        };

        if (type == STUDENT) {
            return {
                hostel_applicant: data?.is_hostel ? t("Yes") : t("No"),
                email: data?.email,
                phone_number: data?.phone_number,
                phone_number_2: data?.phone_number_2,
                nationality: data?.nationality?.translations?.name[locale],
                birth_cert_number: data?.birth_cert_number,
                ...commonInfo,
                dietary_restriction: capitalize(data?.dietary_restriction),
                health_concern:
                    data?.health_concern?.translations?.name[locale],
                primary_school:
                    data?.primary_school?.translations?.name[locale],
                admission_type: data?.admission_type,
                admission_year: data?.admission_year,
                admission_grade: combinedNames(
                    data?.admission_grade?.translations?.name
                ),
                join_date: data?.join_date,
                leave_date: data?.leave_date,
                remarks: data?.remarks,
            };
        } else
            return {
                hostel_applicant: data?.is_hostel ? t("Yes") : t("No"),
                phone_number: data?.phone_number,
                email: data?.email,
                personal_email: data?.personal_email,
                ...commonInfo,
                employment_type: capitalize(
                    replaceAll(data?.employment_type, "_", " ")
                ),
                employee_category: capitalize(
                    data?.employee_category?.translations?.name?.[locale]
                ),
                employee_session:
                    data?.employee_session?.translations?.name?.[locale],
                job_title: data?.job_title?.translations?.name?.[locale],
                epf_number: data?.epf_number,
                employment_start_date: data?.employment_start_date,
                employment_end_date: data?.employment_end_date,
                highest_education: capitalize(data?.highest_education),
                highest_education_country:
                    data?.highest_education_country?.translations?.name?.[
                        locale
                    ],
                marriage_status: capitalize(data?.marriage_status),
            };
    }

    return (
        <div className="grid gap-x-3 gap-y-3.5 pb-3 lg:grid-cols-3">
            {Object.entries(formatData(data)).map(([key, value], index) => (
                <Fragment key={key}>
                    <div className="flex flex-col">
                        <p className="text-[14px] font-medium capitalize text-themeLabel">
                            {t(
                                replaceAll(
                                    key === "nric" ? "NRIC" : key,
                                    "_",
                                    " "
                                )
                            )}
                        </p>
                        <p
                            className={clsx(
                                "text-themeText w-full max-w-sm font-medium",
                                key === "status" && `cell-status ${value}`
                            )}
                        >
                            {isEmpty(value?.toString()) ? "-" : value}
                        </p>
                    </div>
                </Fragment>
            ))}
        </div>
    );
};

const GuardiansCard = ({ data, locale }) => {
    const t = useTranslations("common");
    const [isOpenList, setIsOpenList] = useState<number[]>([]);

    function formatGuardianData(guardian) {
        return {
            nric: guardian?.nric,
            passport_number: guardian?.passport_number,
            email: guardian?.email,
            phone_number: guardian?.phone_number,
            marital_status: capitalize(guardian?.married_status),
            occupation: guardian?.occupation,
            occupation_description: guardian?.occupation_description,
            nationality: guardian?.nationality?.translations?.name?.[locale],
            race: guardian?.race?.translations?.name?.[locale],
            religion: guardian?.religion?.translations?.name?.[locale],
            education: guardian?.education?.translations?.name?.[locale],
            is_primary: isValueTrue(guardian?.is_primary) ? t("Yes") : t("No"),
            has_user_account: isValueTrue(guardian?.has_user_account)
                ? t("Yes")
                : t("No"),
            is_direct_dependant: isValueTrue(guardian?.is_direct_dependant)
                ? t("Yes")
                : t("No"),
            live_status: guardian?.live_status ?? "-",
        };
    }

    return (
        <>
            {data?.guardians?.map((person, index) => {
                const guardianData = formatGuardianData(person);

                function onClickChevron() {
                    if (isOpenList.includes(index)) {
                        setIsOpenList(isOpenList.filter((i) => i !== index));
                    } else {
                        setIsOpenList([...isOpenList, index]);
                    }
                }

                return (
                    <div
                        key={index}
                        className="mb-3 rounded-md border px-4 py-3"
                    >
                        <div className="col-span-2 flex items-center justify-between">
                            <div className="c-text-size flex w-[calc(100%-44px)] flex-wrap items-center gap-x-1.5 text-gray-500">
                                <span className="font-medium">
                                    {capitalize(t(person?.type))}
                                </span>
                                <span className="h-[3px] w-[3px] rounded-full bg-gray-400"></span>
                                <span>
                                    {combinedNames(person?.translations?.name)}
                                </span>
                                {person?.is_primary && (
                                    <span className="ml-1 rounded-sm border border-dotted border-themeLabel p-1 text-[10px] font-medium uppercase leading-none">
                                        {t("Primary")}
                                    </span>
                                )}
                            </div>
                            <ChevronDownCircle
                                className={clsx(
                                    "text-themeGreen transition",
                                    isOpenList.includes(index) && "rotate-180"
                                )}
                                onClick={onClickChevron}
                            />
                        </div>

                        <div
                            className={clsx(
                                "grid w-full gap-3",
                                !isOpenList.includes(index) &&
                                    "h-0 overflow-hidden"
                            )}
                        >
                            <FormDivider />

                            {Object.entries(guardianData).map(
                                ([key, value], index) => (
                                    <Fragment key={key}>
                                        <div className="flex flex-col lg:grid-cols-2">
                                            <p className="text-[14px] font-medium capitalize text-themeLabel">
                                                {t(
                                                    replaceAll(
                                                        key === "nric"
                                                            ? "NRIC"
                                                            : key,
                                                        "_",
                                                        " "
                                                    )
                                                )}
                                            </p>
                                            <p
                                                className={
                                                    "text-themeText font-medium"
                                                }
                                            >
                                                {isEmpty(value?.toString())
                                                    ? "-"
                                                    : value}
                                            </p>
                                        </div>
                                    </Fragment>
                                )
                            )}
                        </div>
                    </div>
                );
            })}
        </>
    );
};

const DisciplinaryRecordCard = ({ data, locale }) => {
    const t = useTranslations("common");
    const otherFilterParams = {
        student_id: data.id,
        status: POSTED,
        includes: [
            "rewardPunishment.meritDemeritSettings",
            "studentLatestPrimaryClasses",
        ],
        order_by: { id: "desc" },
    };

    const [pagination, setPagination] = useState();
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        ...otherFilterParams,
    });

    const { data: disciplinaryRecord, axiosQuery: getRewardPunishmentRecords } =
        useAxios({
            api: rewardPunishmentRecordsAPI,
            locale,
            onSuccess: (result) => {
                setPagination(result?.pagination);
            },
        });

    const { data: semesterOptions, axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
    });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS },
        });
    }, []);

    useEffect(() => {
        if (data?.id) {
            getRewardPunishmentRecords({
                params: filter,
            });
        }
    }, [filter]);

    useEffect(() => {
        if (disciplinaryRecord && locale) {
            const _columns: TableColumnType[] = [
                {
                    key: "year",
                },
                {
                    key: "semester",
                },
                {
                    key: "class",
                },
                {
                    key: "date",
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
                {
                    key: "merit_demerit",
                    displayAs: "Merit/ Demerit",
                },
                {
                    key: "reward_punishment",
                    displayAs: "Reward/ Punishment",
                    hasSort: true,
                },
                {
                    key: "average_exam_marks",
                    hasSort: true,
                },
                {
                    key: "conduct_marks",
                    hasSort: true,
                },
                {
                    key: "display_in_report_card",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{t(value)}</div>
                    ),
                },
            ];
            setColumns(_columns);
        }
    }, [disciplinaryRecord, locale]);

    function definedData() {
        return semesterOptions && isArray(disciplinaryRecord)
            ? disciplinaryRecord.map((item) => {
                  return {
                      id: item?.id,
                      year: item?.date?.split("-")[0],
                      semester: semesterOptions?.find(
                          (option) =>
                              option.id ===
                              item?.student_class?.semester_setting_id
                      )?.name,
                      class: item?.student_class?.semester_class?.class_model
                          ?.name?.[locale],
                      date: displayDateTime(item?.date, DATE_FORMAT.DMY),
                      merit_demerit:
                          item?.reward_punishment?.merit_demerit_settings
                              ?.map(
                                  (setting) =>
                                      setting.translations?.name?.[locale]
                              )
                              .join(", "),
                      reward_punishment:
                          item?.reward_punishment?.translations?.name?.[
                              locale
                          ] ?? item?.reward_punishment?.name,
                      average_exam_marks: item?.average_exam_marks,
                      conduct_marks: item?.conduct_marks,
                      display_in_report_card: item?.display_in_report_card
                          ? t("Yes")
                          : t("No"),
                      status: capitalize(item?.status),
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    return (
        <>
            <DataTable
                columns={columns}
                data={definedData()}
                pagination={pagination}
                setPagination={setPagination}
                changePage={(arg) => setFilter({ ...filter, ...arg })}
                sort={onSort}
            />
        </>
    );
};

const ComrepehensiveAssessmentRecordCard = ({ data, locale }) => {
    const t = useTranslations("common");
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [isOpenList, setIsOpenList] = useState<number[]>([]);

    const {
        data: comprehensiveAssessmentRecord,
        axiosQuery: getComprehensiveAssessmentRecord,
    } = useAxios({
        api: studentComprehensiveAssessmentRecordAPI,
        locale,
    });

    useEffect(() => {
        if (comprehensiveAssessmentRecord?.results && locale) {
            const _columns: TableColumnType[] = [
                {
                    key: "category",
                    rowSpan: true,
                },
                {
                    key: "item",
                },
                {
                    key: "result",
                },
            ];
            setColumns(_columns);
        }
    }, [comprehensiveAssessmentRecord]);

    useEffect(() => {
        if (data?.id) {
            getComprehensiveAssessmentRecord({
                params: { student_id: data.id },
            });
        }
    }, []);

    function formatAssessmentRecordByClass(categories) {
        if (!Array.isArray(categories)) return [];

        return categories.flatMap((categoryItem) => {
            const categoryName = combinedNames(
                categoryItem.category.translations.name
            );

            return categoryItem.questions.map((questionItem, index) => ({
                category: index === 0 ? categoryName : null,
                item: combinedNames(questionItem.question),
                result: replaceAll(questionItem.result, "_", " "),
            }));
        });
    }

    return (
        <>
            {!comprehensiveAssessmentRecord?.results ||
                (comprehensiveAssessmentRecord?.results?.length == 0 && (
                    <p className="flex h-20 items-center justify-center text-center text-gray-400">
                        {t("No record available")}
                    </p>
                ))}
            {comprehensiveAssessmentRecord?.results?.map(
                ({ classes, semester_setting }, index) => {
                    function onClickChevron() {
                        if (isOpenList.includes(index)) {
                            setIsOpenList(
                                isOpenList.filter((i) => i !== index)
                            );
                        } else {
                            setIsOpenList([...isOpenList, index]);
                        }
                    }

                    return (
                        <div
                            key={index}
                            className="mb-3 rounded-md border px-4 py-3"
                        >
                            <div className="col-span-2 flex items-center justify-between">
                                <div className="c-text-size flex w-[calc(100%-44px)] flex-wrap items-center gap-x-1.5 text-gray-500">
                                    <span className="font-medium capitalize">
                                        {t("semester")}:{" "}
                                    </span>
                                    <span className="h-[3px] w-[3px] rounded-full bg-gray-400"></span>
                                    <span>
                                        {capitalize(semester_setting?.name)}
                                    </span>

                                    {semester_setting?.is_current_semester && (
                                        <span className="ml-1 rounded-sm border border-dotted border-themeLabel p-1 text-[10px] font-medium uppercase leading-none">
                                            {t("current semester")}
                                        </span>
                                    )}
                                </div>
                                <ChevronDownCircle
                                    className={clsx(
                                        "text-themeGreen transition",
                                        isOpenList.includes(index) &&
                                            "rotate-180"
                                    )}
                                    onClick={onClickChevron}
                                />
                            </div>

                            <div
                                className={clsx(
                                    "grid w-full gap-3",
                                    !isOpenList.includes(index) &&
                                        "h-0 overflow-hidden"
                                )}
                            >
                                <FormDivider />

                                {!isEmpty(classes) && (
                                    <div className="overflow-auto">
                                        {classes?.map((classItem, index) => {
                                            const recordByClass =
                                                formatAssessmentRecordByClass(
                                                    classItem?.categories
                                                );

                                            return (
                                                <div key={index}>
                                                    <div className="report-table-title">
                                                        {combinedNames(
                                                            classItem?.class
                                                                ?.translations
                                                                ?.name
                                                        )}
                                                    </div>
                                                    <div className="mb-5">
                                                        <DataTable
                                                            columns={columns}
                                                            data={recordByClass}
                                                        />
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                }
            )}
        </>
    );
};

const AcademicReportCard = ({ data }) => {
    const reports = data.report_cards.map((report) => ({
        semester: report?.semester_setting?.name,
        file_url: report?.file_url,
    }));
    const semesterList = reports.map((report) => report?.semester);
    const [selectedReportSemester, setSelectedReportSemester] = useState(
        semesterList?.[0]
    );

    return (
        <div className="mx-auto max-w-[1000px]">
            {semesterList?.length > 0 ? (
                <>
                    <div className="overflow-auto">
                        <Tabs
                            list={semesterList}
                            selected={selectedReportSemester}
                            setSelected={setSelectedReportSemester}
                            styleClass="mb-0 ml-0.5"
                        />
                    </div>
                    <iframe
                        src={`${reports.find((report) => report.semester === selectedReportSemester)?.file_url}#toolbar=0&navpanes=0&scrollbar=0&view=FitH`}
                        className="mt-3.5 h-[calc(100vh-310px)] w-full border"
                    />
                </>
            ) : (
                <p className="flex h-20 items-center justify-center text-center text-gray-400">
                    No report available
                </p>
            )}
        </div>
    );
};

const FeeRecordCard = ({ data, locale }) => {
    const t = useTranslations("common");
    const _fees = t("Fees");
    const _invoices = t("Invoices");

    const feeDefaultFilterParams = {
        page: 1,
        per_page: 10,
        period_from: "",
        period_to: "",
        status: UNPAID,
    };

    const selectedStudentId = data.id;

    const [filter, setFilter] = useState<Record<string, any>>(
        feeDefaultFilterParams
    );
    const [pagination, setPagination] = useState({});
    const [totalBeforeDiscount, setTotalBeforeDiscount] = useState(0);
    const [totalAfterDiscount, setTotalAfterDiscount] = useState(0);
    const [openFilter, setOpenFilter] = useState(false);
    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);
    const [displayType, setDisplayType] = useState(_fees);

    const { data: unpaidItems, axiosQuery: getUnpaidItems } = useAxios({
        api: accountingUnpaidFeeAPI,
        locale,
        onSuccess: (result) => {
            if (result?.data) {
                const totalAmountBeforeDiscount = result.data.reduce(
                    (sum, item) => {
                        return sum + (item.amount_before_tax ?? 0);
                    },
                    0
                );

                const totalAmountAfterDiscount = result.data.reduce(
                    (sum, item) => {
                        return (
                            sum + (item.amount_before_tax_after_discount ?? 0)
                        );
                    },
                    0
                );

                const pagination = {
                    total: result.data.length,
                    hasPerPage: false,
                    hideChangePage: true,
                };

                setNavigatedResults([...navigatedResults, ...result.data]);
                setPagination(pagination);
                setTotalBeforeDiscount(totalAmountBeforeDiscount);
                setTotalAfterDiscount(totalAmountAfterDiscount);
            }
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "description",
            displayAs: "fee",
            hasSort: true,
        },
        {
            key: "period",
        },
        {
            key: "product",
        },
        {
            key: "status",
            modify: (value) => (
                <span
                    className={clsx(
                        value === PAID && "text-green-600",
                        value === UNPAID && "text-red-500"
                    )}
                >
                    {capitalize(t(value))}
                </span>
            ),
        },
        {
            key: "amount_before_tax",
            displayAs: `${t("amount")} (${appCurrencySymbol})`,
        },
        {
            key: "amount_before_tax_after_discount",
            displayAs: `${t("amount with discount")} (${appCurrencySymbol})`,
        },
    ];

    function definedData() {
        return isArray(unpaidItems)
            ? unpaidItems.map((item: any) => {
                  return {
                      id: item?.id,
                      product: item?.product?.translations?.name?.[locale],
                      period: format(item?.period, DATE_FORMAT.MY),
                      description: item?.description ?? "-",
                      status: item?.status ?? "-",
                      amount_before_tax: formatNumberForRead(
                          item?.amount_before_tax
                      ),
                      amount_before_tax_after_discount: formatNumberForRead(
                          item?.amount_before_tax_after_discount
                      ),
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function fetchFees() {
        getUnpaidItems({
            params: {
                ...filter,
                status: filter.status ?? UNPAID,
                bill_to_type: STUDENT,
                bill_to_id: selectedStudentId,
                includes: ["product"],
            },
        });
    }

    useEffect(() => {
        if (selectedStudentId) {
            if (displayType === _fees) {
                fetchFees();
            }
            if (displayType === _invoices) {
                fetchBillingDocuments();
            }
        }
    }, [filter, displayType]);

    // invoices
    const [invoiceFilter, setInvoiceFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
    });
    const [viewInvoiceId, setViewInvoiceId] = useState(null);
    const [invoices, setInvoices] = useState<any[]>([]);
    const [invoicePagination, setInvoicePagination] = useState();
    const [openInvoiceFilter, setOpenInvoiceFilter] = useState(false);

    const { axiosQuery: getBillingDocuments } = useAxios({
        api: billingDocumentAPI,
        locale,
        onSuccess: (result) => {
            setInvoices(result.data);
            setInvoicePagination(result?.pagination);
        },
    });

    const invoiceColumns: TableColumnType[] = [
        {
            key: "reference_no",
            hasSort: true,
        },
        {
            key: "payment_reference_no",
        },
        {
            key: "document_date",
            hasSort: true,
            displayAs: "purchase date",
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
        {
            key: "paid_at",
            hasSort: true,
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
        {
            key: "type",
            hasSort: true,
            modify: (value) => {
                return (
                    <span className="text-sm">
                        {replaceAll(value, "_", " ")}
                    </span>
                );
            },
        },
        {
            key: "sub_type",
            hasSort: true,
        },
        {
            key: "payment_status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === PARTIAL && "text-yellow-500",
                        value === PAID && "text-green-600",
                        value === UNPAID && "text-red-500"
                    )}
                >
                    {capitalize(t(value))}
                </span>
            ),
        },
        {
            key: "amount_before_tax_after_less_advance",
            displayAs: `${t("amount")} (${appCurrencySymbol})`,
            hasSort: true,
        },
        {
            key: "status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === CONFIRMED && "text-green-600",
                        value === DRAFT && "text-yellow-500",
                        value === VOIDED && "text-red-500"
                    )}
                >
                    {capitalize(t(value))}
                </span>
            ),
        },
    ];

    function definedInvoiceData() {
        return isArray(invoices)
            ? invoices.map((item: any) => {
                  return {
                      id: item?.id,
                      reference_no: item?.reference_number,
                      document_date: item?.document_date ?? "-",
                      paid_at: item.paid_at
                          ? displayDateTime(
                                item.paid_at,
                                DATE_FORMAT.forDisplay
                            )
                          : "-",
                      type: item?.type ?? "-",
                      sub_type: strStartCase(item?.sub_type ?? "-").replaceAll(
                          "_",
                          " "
                      ),
                      status: item?.status ?? "-",
                      payment_status: item?.payment_status ?? "-",
                      amount_before_tax_after_less_advance:
                          formatNumberForRead(
                              item?.amount_before_tax_after_less_advance
                          ) ?? "-",
                      payment_due_date: item?.payment_due_date ?? "-",
                      payment_reference_no:
                          item?.payments?.find((p) => p?.paid_at !== null)
                              ?.payment_reference_no ?? "-",
                  };
              })
            : [];
    }

    function fetchBillingDocuments() {
        getBillingDocuments({
            params: {
                ...invoiceFilter,
                includes: ["billTo", "payments"],
                bill_to_type: STUDENT,
                bill_to_id: selectedStudentId,
            },
        });
    }

    function refreshInvoices() {
        fetchBillingDocuments();
    }

    function onSortInvoices(name: string, direction: string) {
        setInvoiceFilter({
            ...invoiceFilter,
            order_by: { [name]: direction },
        });
    }

    useEffect(() => {
        if (displayType === _invoices) {
            fetchBillingDocuments();
        }
    }, [invoiceFilter, displayType]);

    return (
        <>
            <div className="pb-7 lg:pt-2">
                <Tabs
                    list={[_fees, _invoices]}
                    selected={displayType}
                    setSelected={setDisplayType}
                    styleClass="w-fit ml-.5"
                />

                {displayType === _fees && (
                    <div className="-mt-14">
                        <div className="ml-auto flex w-fit justify-end gap-x-3 bg-white pb-2 pl-5">
                            <Button
                                type="button"
                                variant="outline"
                                className="flex items-center gap-x-1"
                                onClick={fetchFees}
                            >
                                <RotateCcw size={16} />
                                <span>{t("Refresh")}</span>
                            </Button>

                            <TableFilterBtn
                                filter={filter}
                                onClick={() => setOpenFilter(true)}
                            />
                        </div>

                        <DataTable
                            columns={columns}
                            data={definedData()}
                            pagination={pagination}
                            setPagination={setPagination}
                            changePage={(arg) =>
                                setFilter({ ...filter, ...arg })
                            }
                            sorted={filter?.order_by}
                            sort={onSort}
                            extraRow={[
                                {
                                    value: (
                                        <span className="font-medium capitalize text-themeLabel">
                                            {t("total")} ({appCurrencySymbol})
                                        </span>
                                    ),
                                    colSpan: 4,
                                },
                                {
                                    value: (
                                        <span className="text-[16px] font-medium">
                                            {formatNumberForRead(
                                                totalBeforeDiscount
                                            )}
                                        </span>
                                    ),
                                },
                                {
                                    value: (
                                        <span className="text-[16px] font-medium">
                                            {formatNumberForRead(
                                                totalAfterDiscount
                                            )}
                                        </span>
                                    ),
                                },
                            ]}
                        />
                    </div>
                )}

                {displayType === _invoices && (
                    <div className="-mt-12">
                        <div className="ml-auto flex w-fit justify-end gap-x-3 bg-white pb-3 pl-5">
                            <Button
                                type="button"
                                variant="outline"
                                className="flex items-center gap-x-1"
                                onClick={refreshInvoices}
                            >
                                <RotateCcw size={16} />
                                <span>Refresh</span>
                            </Button>

                            <TableFilterBtn
                                filter={invoiceFilter}
                                onClick={() => setOpenInvoiceFilter(true)}
                            />
                        </div>

                        <DataTable
                            columns={invoiceColumns}
                            data={definedInvoiceData()}
                            pagination={invoicePagination}
                            setPagination={setInvoicePagination}
                            changePage={(arg) =>
                                setInvoiceFilter({
                                    ...invoiceFilter,
                                    ...arg,
                                })
                            }
                            sorted={invoiceFilter?.order_by}
                            sort={onSortInvoices}
                            actionMenu={({ cell }) => (
                                <>
                                    <ActionDropdown>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setViewInvoiceId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {t("View")}
                                        </DropdownMenuItem>
                                    </ActionDropdown>
                                </>
                            )}
                        />
                    </div>
                )}
            </div>

            {/* unpaid fees - filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterUnpaidItemForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>

            {/* invoice -view */}
            <Modal
                open={viewInvoiceId}
                onOpenChange={setViewInvoiceId}
                size="large"
            >
                <ViewBillingDocument
                    id={viewInvoiceId}
                    close={() => setViewInvoiceId(null)}
                />
            </Modal>

            {/* invoice - filter */}
            <Modal
                open={openInvoiceFilter}
                onOpenChange={setOpenInvoiceFilter}
                size="medium"
            >
                <FilterBillingDocumentForm
                    filter={invoiceFilter}
                    setFilter={setInvoiceFilter}
                    close={() => setOpenInvoiceFilter(false)}
                    hideBillTo={true}
                />
            </Modal>
        </>
    );
};

const ActivityReportCard = ({ data, locale }) => {
    const [pagination, setPagination] = useState();
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
        includes: ["department", "records.student", "records.award"],
        student_number: data?.student_number,
        order_by: {
            date: "desc",
        },
    });

    const { data: activityReport, axiosQuery: getActivityReport } = useAxios({
        api: specialCompetitionAPI,
        locale,
        onSuccess: (result) => {
            setPagination(result?.pagination);
        },
    });

    useEffect(() => {
        if (data?.id) {
            getActivityReport({
                params: filter,
            });
        }
    }, [filter]);

    useEffect(() => {
        if (activityReport && locale) {
            const _columns: TableColumnType[] = [
                {
                    key: "date",
                    hasSort: true,
                    modify: (value) => (
                        <span className="whitespace-nowrap text-[14px]">
                            {value}
                        </span>
                    ),
                },
                {
                    key: "name",
                    hasSort: true,
                },

                {
                    key: "department",
                    hasSort: true,
                },
                {
                    key: "records",
                    modify(list) {
                        return (
                            <ul>
                                {list?.length > 0
                                    ? list
                                          .filter(
                                              (record) =>
                                                  record.student?.id == data?.id
                                          )
                                          .map((record, index) => {
                                              const type = capitalize(
                                                  formatUnderscores(
                                                      record?.type_of_bonus
                                                  )
                                              );
                                              return (
                                                  <li
                                                      key={index}
                                                      className="table-item-li"
                                                  >
                                                      <span className="text-[14px]">
                                                          {type}
                                                      </span>
                                                      <span className="whitespace-nowrap text-[14px]">
                                                          {record?.mark
                                                              ? ` ( Mark: ${record.mark} )`
                                                              : ""}
                                                      </span>
                                                  </li>
                                              );
                                          })
                                    : "-"}
                            </ul>
                        );
                    },
                },
                {
                    key: "award",
                    modify(list) {
                        return (
                            <ul>
                                {list?.length > 0
                                    ? list
                                          .filter(
                                              (record) =>
                                                  record.student?.id == data?.id
                                          )
                                          .map((record, index) => {
                                              return (
                                                  <li
                                                      key={index}
                                                      className="table-item-li"
                                                  >
                                                      <span className="whitespace-nowrap text-[14px]">
                                                          {record?.award
                                                              ? record?.award
                                                                    ?.translations
                                                                    ?.name?.[
                                                                    locale
                                                                ]
                                                              : ""}
                                                      </span>
                                                  </li>
                                              );
                                          })
                                    : "-"}
                            </ul>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [activityReport, locale]);

    function definedData() {
        return Array.isArray(activityReport)
            ? activityReport.map((item) => ({
                  id: item?.id,
                  name: item?.name,
                  date: item?.date,
                  department: item?.department?.name,
                  records: item?.records,
                  award: item?.records,
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    return (
        <DataTable
            columns={columns}
            data={definedData()}
            pagination={pagination}
            setPagination={setPagination}
            changePage={(arg) => setFilter({ ...filter, ...arg })}
            sort={onSort}
        />
    );
};

function orderByYearSemester(data) {
    return orderBy(data, ["year", "semester_name"], ["desc", "desc"]);
}

function orderByDate(data) {
    return orderBy(data, ["date"], ["desc"]);
}

const HistoricalClasses = ({ data, locale }) => {
    const columns: TableColumnType[] = [
        {
            key: "year",
            hasSort: true,
        },
        {
            key: "semester",
            hasSort: true,
        },
        {
            key: "class_type",
            hasSort: true,
        },
        {
            key: "class",
            hasSort: true,
        },
    ];

    function definedData() {
        if (!Array.isArray(data.historical_classes)) return [];
        const classList = data.historical_classes.filter(
            (item) => item.class_type != SOCIETY
        );
        return orderByYearSemester(classList).map((item) => ({
            id: item?.id,
            year: item?.year,
            semester: item?.semester_name,
            class_type: strStartCase(item?.class_type),
            class: item?.class?.[locale],
        }));
    }

    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        chunk(definedData(), CHUNKED_FILTER_PARAMS.per_page)
    );

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onSort,
        clonedFilteredChunk,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    return (
        <div className="pb-5">
            {targetsChunk?.length > 0 && pagination && (
                <DataTable
                    isSmaller
                    columns={columns}
                    data={
                        (clonedFilteredChunk ?? targetsChunk)[
                            pagination?.current_page - 1
                        ]
                    }
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    hasPerPage={false}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            )}
        </div>
    );
};

const HistoricalCocu = ({ data, locale }) => {
    const columns: TableColumnType[] = [
        {
            key: "year",
            hasSort: true,
        },
        {
            key: "semester",
            hasSort: true,
        },
        {
            key: "class",
            hasSort: true,
        },
    ];

    function definedData() {
        if (!Array.isArray(data.historical_classes)) return [];
        const classList = data.historical_classes.filter(
            (item) => item.class_type === SOCIETY
        );
        return orderByYearSemester(classList).map((item) => ({
            id: item?.id,
            year: item?.year,
            semester: item?.semester_name,
            class: item?.class?.[locale],
        }));
    }

    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        chunk(definedData(), CHUNKED_FILTER_PARAMS.per_page)
    );

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onSort,
        clonedFilteredChunk,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    return (
        <div className="min-h-[120px] pb-5">
            {targetsChunk?.length > 0 && pagination && (
                <DataTable
                    isSmaller
                    columns={columns}
                    data={
                        (clonedFilteredChunk ?? targetsChunk)[
                            pagination?.current_page - 1
                        ]
                    }
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    hasPerPage={false}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            )}
        </div>
    );
};

const AttendanceRecord = ({ data }) => {
    const t = useTranslations("common");
    const columns: TableColumnType[] = [
        {
            key: "year",
            modifyHeader: (value) => (
                <div className="text-center capitalize">{t(value)}</div>
            ),
            modify: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: "month",
            modifyHeader: (value) => (
                <div className="text-center capitalize">{t(value)}</div>
            ),
            modify: (value) => <div className="text-center">{value}</div>,
        },
        ...Array.from({
            length: 31,
        }).map((_, index) => ({
            key: `${index + 1}`,
            modifyHeader: (value) => <div className="text-center">{value}</div>,
            modify: (value) => (
                <div className="min-w-[30px] text-center text-[14px] leading-none">
                    <div className="">{value?.split(" ")?.[0]}</div>
                    <div className="text-[11px] uppercase text-gray-600">
                        {value?.split(" ")?.[1]}
                    </div>
                </div>
            ),
        })),
    ];

    function definedData() {
        if (!Array.isArray(data.attendances)) return [];

        const groupedByMonth = groupBy(orderByDate(data.attendances), (item) =>
            item.date.slice(0, 7)
        );

        const newData: any[] = [];

        Object.entries(groupedByMonth).forEach(([yearMonth, list]) => {
            const newItem = {
                id: yearMonth,
                year: yearMonth.slice(0, 4),
                month: yearMonth.slice(5, 7)?.replace(/^0+/, ""),
            };
            list.forEach((item) => {
                const day = item.date?.slice(8, 10)?.replace(/^0+/, "");

                newItem[day] = item?.check_in_datetime
                    ? convertDateTime(item?.check_in_datetime, "h:mm aaa")
                    : "";
            });
            newData.push(newItem);
        });

        return newData;
    }

    return (
        <div className="pb-5">
            <DataTable columns={columns} data={definedData()} />
        </div>
    );
};

export default ProfileCard;
