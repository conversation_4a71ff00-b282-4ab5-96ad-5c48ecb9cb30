import { useEffect, useState } from "react";
import { Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { useAxios } from "@/lib/hook";
import { Input } from "../base-ui/input";
import OutlinedX from "./OutlinedX";
import { useTranslations } from "next-intl";
import toast from "react-hot-toast";
import { getAxiosObjectError } from "@/lib/utils";
import { defaultErrorMessage } from "@/lib/constant";
import { capitalize } from "lodash";

type FileInputExcelProps = {
    api?: string;
    extraImportParams?: any;
    handleImportData?: React.Dispatch<any>;
    clearTrigger?: number;
};

const FileInputExcel = ({
    api,
    extraImportParams,
    handleImportData,
    clearTrigger,
}: FileInputExcelProps & { clearTrigger }) => {
    const t = useTranslations("common");
    const form = useForm();
    const [fileName, setFileName] = useState<string | null>(null);

    const { axiosMultipartPost: uploadFiles } = useAxios({
        api,
        onSuccess: (result) => {
            if (handleImportData) {
                handleImportData(result.data);
            }
        },
        onError: (error) => {
            toast.error(
                t(capitalize(getAxiosObjectError(error))) ||
                    t(defaultErrorMessage)
            );
        },
    });

    function submitFiles() {
        form.handleSubmit((data) => {
            if (data.files) {
                delete data.files.length;
                const _files: any[] = Object.values(data.files);

                setFileName(_files[0].name);

                let fileParams = {
                    file: _files[0],
                };

                if (extraImportParams) {
                    fileParams = {
                        ...fileParams,
                        ...extraImportParams,
                    };
                }

                uploadFiles(fileParams, false);
            }
        })();
    }

    function onClear() {
        setFileName(null);
        if (handleImportData) handleImportData([]);
        form.reset({ files: null });
    }

    useEffect(() => {
        if (clearTrigger) {
            onClear();
        }
    }, [clearTrigger]);

    return (
        <div className="w-full">
            <div className="relative w-[170px] hover:bg-themeGreen3">
                <div className="c-text-size flex h-[42px] w-full items-center justify-center gap-x-2 rounded-lg border border-themeGreen font-semibold text-themeGreen">
                    <Upload size={16} className="-ml-2" />
                    <span>{t("Upload File")}</span>
                </div>
                <div className="absolute left-0 top-0 z-10 h-full w-full opacity-0">
                    <Input
                        className="pl-2 pt-2"
                        type="file"
                        accept=".xlsx, .xls"
                        onAbort={() => {}}
                        onClick={onClear}
                        {...form.register("files", {
                            onChange: (e) => {
                                if (e.target.files?.length > 0) {
                                    submitFiles();
                                } else {
                                    onClear();
                                }
                            },
                        })}
                    />
                </div>
            </div>
            {fileName && (
                <div className="ml-1 mt-2 flex gap-x-2">
                    <OutlinedX onClick={onClear} />
                    <div className="mt-0.5 w-[calc(100%-40px)] overflow-hidden text-ellipsis text-[13px] font-medium leading-tight text-themeGreen2">
                        {fileName}
                    </div>
                </div>
            )}
        </div>
    );
};

export default FileInputExcel;
