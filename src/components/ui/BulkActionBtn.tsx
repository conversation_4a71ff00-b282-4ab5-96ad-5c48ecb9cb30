import React from "react";
import { ChevronDown } from "lucide-react";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/base-ui/dropdown-menu";

const BulkActionBtn = () => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outlineBlack">
                    <div>Bulk Action</div>
                    <div className="translate-x-2">
                        <ChevronDown className="text-themeBlack" size={20} />
                    </div>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={"end"}>
                <DropdownMenuItem>Edit</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Delete</DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default BulkActionBtn;
