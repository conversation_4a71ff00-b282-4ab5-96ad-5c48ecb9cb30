import React, { ReactNode } from "react";
import clsx from "clsx";

type CardProps = {
    children: ReactNode;
    styleClass?: string;
    isWhiteBg?: boolean;
};

const Card = ({ children, styleClass = "", isWhiteBg = true }: CardProps) => {
    return (
        <div
            className={clsx(
                "lg:rounded-xl lg:p-5",
                isWhiteBg && "lg:bg-white",
                styleClass
            )}
        >
            {children}
        </div>
    );
};

export default Card;
