import React from "react";
import CreatableSelect from "react-select/creatable";
import {
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { selectStyles } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

type FormSelectCreatableProps = {
    control: any;
    value: any;
    name: string;
    label?: string;
    isMulti?: boolean;
    isDisabled?: boolean;
    isClearable?: boolean;
    hideLabel?: boolean;
    onChange?: (value: any) => void;
};

const FormSelectCreatable = ({
    control,
    value,
    name,
    label,
    isMulti = false,
    isDisabled,
    isClearable = true,
    hideLabel = false,
    onChange = () => {},
}: FormSelectCreatableProps) => {
    const t = useTranslations("common");
    const selectName = t(replaceAll(label || name, "_", " "));

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hideLabel == false && (
                        <FormLabel className="capitalize">
                            {selectName}
                        </FormLabel>
                    )}

                    <CreatableSelect
                        isMulti={isMulti}
                        closeMenuOnSelect={isMulti ? false : true}
                        isClearable={isClearable}
                        {...field}
                        placeholder={t("Type to add")}
                        isDisabled={isDisabled}
                        value={value?.map((item: any) => ({
                            value: item,
                            label: item,
                        }))}
                        onChange={(selectedOption) => {
                            const selectedValue = isMulti
                                ? selectedOption.map((item) => item?.value)
                                : selectedOption?.value ?? null;
                            if (isMulti) {
                                field.onChange(selectedValue);
                                onChange(selectedValue);
                            } else {
                                field.onChange(selectedValue);
                                onChange(selectedValue);
                            }
                        }}
                        styles={selectStyles}
                    />
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormSelectCreatable;
