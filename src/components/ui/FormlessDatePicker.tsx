import { useState } from "react";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@radix-ui/react-popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { DATE_FORMAT } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Calendar } from "../base-ui/calendar";
import { Label } from "../base-ui/label";

const FormlessDatePicker = ({
    label,
    placeholder,
    onSelect,
    isSmaller = false,
    showSelection = false,
}: {
    label?: string;
    placeholder?: string;
    onSelect: (date: Date | undefined) => void;
    isSmaller?: boolean;
    showSelection?: boolean;
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [date, setDate] = useState<Date>();

    return (
        <div className="z-10 bg-white">
            {label && (
                <Label className={"label"}>{replaceAll(label, "_", " ")}</Label>
            )}
            {
                <Popover open={isOpen} onOpenChange={setIsOpen}>
                    <PopoverTrigger asChild>
                        <Button
                            size={isSmaller ? "smaller" : "primary"}
                            onClick={() => setIsOpen(true)}
                            variant={"outlineGray"}
                            className={
                                "w-full rounded-md border-input pl-4 text-left font-normal"
                            }
                        >
                            <div className="pr-2">
                                {showSelection && date ? (
                                    format(date, DATE_FORMAT.DMY)
                                ) : (
                                    <span className="font-medium text-themeLabel">
                                        {placeholder ?? "Pick a date"}
                                    </span>
                                )}
                            </div>
                            <CalendarIcon className="ml-auto h-4 w-4 text-themeLabel" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent
                        className="w-auto rounded-sm border bg-white p-0"
                        align="start"
                    >
                        <Calendar
                            mode="single"
                            defaultMonth={date}
                            selected={date}
                            onSelect={(date) => {
                                onSelect(date);
                                setDate(date);
                                setIsOpen(false);
                            }}
                            disabled={(date) => date < new Date("1900-01-01")}
                            initialFocus
                        />
                    </PopoverContent>
                </Popover>
            }
        </div>
    );
};

export default FormlessDatePicker;
