import React from "react";
import clsx from "clsx";
import Select from "react-select";
import {
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { replaceAll, selectStyles } from "@/lib/utils";
import { Skeleton } from "../base-ui/skeleton";
import { useTranslations } from "next-intl";

type FormGroupedSelectProps = {
    control: any;
    name: string;
    options: any;
    label?: string;
    placeholder?: string;
    isDisabled?: boolean;
    onChange?: (value: any) => void;
    isLoading?: boolean;
    isStringOptions?: boolean;
    isMulti?: boolean;
    hasLabel?: boolean;
    isClearable?: boolean;
    isSmaller?: boolean;
    isSortByName?: boolean;
    hasSelectAll?: boolean;
    noChevron?: boolean;
    displayItemSelected?: boolean;
    hideSelectedOptions?: boolean;
    maxMenuHeight?: number;
};

const FormGroupedSelect = ({
    control,
    name,
    options,
    label,
    placeholder = "",
    isDisabled,
    onChange = (value: any) => {},
    isLoading = false,
    isMulti = false,
    hasLabel = true,
    isClearable = true,
    isSmaller = false,
    noChevron = false,
    hideSelectedOptions = true,
    maxMenuHeight = 160,
}: FormGroupedSelectProps) => {
    const t = useTranslations("common");

    const selectName = t(replaceAll(label || name, "_", " "));

    function onValue(field) {
        const optionList = options.flatMap((group) => group.options);

        if (isMulti) {
            const valueList =
                optionList.filter((option) =>
                    field.value?.includes(option.value)
                ) || null;
            return valueList.length > 0 ? valueList : [];
        }
        return (
            optionList.find((option) => option.value === field.value) || null
        );
    }

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hasLabel && (
                        <FormLabel
                            className={clsx(
                                "min-w-[180px] capitalize",
                                isSmaller && "mb-1 text-[12px]"
                            )}
                        >
                            {selectName}
                        </FormLabel>
                    )}
                    {isLoading ? (
                        <Skeleton className="h-[42px] w-full border border-transparent" />
                    ) : (
                        <Select
                            maxMenuHeight={maxMenuHeight}
                            id={name}
                            instanceId={name}
                            {...field}
                            isClearable={isClearable}
                            isMulti={isMulti}
                            closeMenuOnSelect={isMulti ? false : true}
                            options={options}
                            placeholder={t(placeholder)}
                            isDisabled={isDisabled}
                            hideSelectedOptions={hideSelectedOptions}
                            components={{
                                ...(noChevron && {
                                    DropdownIndicator: () => null,
                                    IndicatorSeparator: () => null,
                                }),
                            }}
                            value={onValue(field)}
                            onChange={(selectedOption: any) => {
                                onChange(selectedOption);
                                field.onChange(
                                    isMulti
                                        ? selectedOption.map(
                                              (option: any) => option.value
                                          )
                                        : selectedOption?.value
                                );
                            }}
                            styles={selectStyles(isSmaller)}
                        />
                    )}
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormGroupedSelect;
