import React, { useState } from "react";
import clsx from "clsx";
import { Eye, EyeOff } from "lucide-react";
import { Control } from "react-hook-form";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { Input } from "@/components/base-ui/input";
import { password } from "@/lib/constant";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FormInputProps = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    type?: string;
    min?: any;
    max?: any;
    isSearch?: boolean;
    hasLabel?: boolean;
    suffixIcon?: string;
    onClickIcon?: () => void;
    isUpperCase?: boolean;
    onChange?: (value: any) => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
};

const FormInput = ({
    control,
    name,
    label,
    placeholder,
    disabled = false,
    type = "text",
    min = null,
    max = null,
    isSearch,
    hasLabel = true,
    suffixIcon,
    onClickIcon,
    isUpperCase = false,
    onChange = (value: any) => {},
    onKeyDown,
}: FormInputProps) => {
    const t = useTranslations("common");

    const [InputType, setInputType] = useState(type);
    const isPassword = type === password;

    const disabledAutoCompleteNameList = [
        "password",
        "password_confirmation",
        "address",
        "address_2",
        "city",
        "postal_code",
    ];

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hasLabel && (
                        <FormLabel className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </FormLabel>
                    )}
                    <FormControl>
                        <div className="relative">
                            <Input
                                autoComplete={
                                    disabledAutoCompleteNameList.includes(name)
                                        ? `new-${name}`
                                        : undefined
                                }
                                placeholder={placeholder ?? ""}
                                disabled={disabled}
                                type={InputType}
                                minLength={min}
                                maxLength={max}
                                suffixIcon={suffixIcon}
                                onClickIcon={onClickIcon}
                                {...field}
                                className={clsx(
                                    isSearch && "search-input",
                                    isPassword && "pr-9"
                                )}
                                onChange={(e) => {
                                    const value = isUpperCase
                                        ? e.target.value.toUpperCase()
                                        : e.target.value;
                                    field.onChange(value);
                                    onChange(value);
                                }}
                                onKeyDown={onKeyDown}
                            />
                            {isPassword &&
                                (InputType === password ? (
                                    <Eye
                                        size={20}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-600"
                                        onClick={() => setInputType("text")}
                                    />
                                ) : (
                                    <EyeOff
                                        size={20}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-600"
                                        onClick={() => setInputType(password)}
                                    />
                                ))}
                        </div>
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormInput;
