import React from "react";
import { useEffect } from "react";
import { Period, updateDateAmPm } from "@/lib/time-picker-utils";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../base-ui/select";
import { TimePickerInput } from "../base-ui/time-picker-input";

interface TimePickerProps {
    date: Date | undefined;
    setDate: (date: Date | undefined) => void;
    onUpdateAmPm?: () => void;
}

export function TimePicker({ date, setDate, onUpdateAmPm }: TimePickerProps) {
    function getPeriod() {
        return date ? (date.getHours() >= 12 ? "PM" : "AM") : undefined;
    }

    const [period, setPeriod] = React.useState<Period | undefined>(getPeriod());

    const minuteRef = React.useRef<HTMLInputElement>(null);
    const hourRef = React.useRef<HTMLInputElement>(null);
    const secondRef = React.useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (date) {
            setPeriod(getPeriod());
        }
    }, [date]);

    return (
        <div className="flex items-end gap-1">
            <div className="grid gap-1 text-center">
                <TimePickerInput
                    picker="12hours"
                    period={period}
                    date={date}
                    setDate={setDate}
                    ref={hourRef}
                    onRightFocus={() => minuteRef.current?.focus()}
                />
            </div>
            <div className="grid gap-1 text-center">
                <TimePickerInput
                    picker="minutes"
                    id="minutes12"
                    date={date}
                    setDate={setDate}
                    ref={minuteRef}
                    onLeftFocus={() => hourRef.current?.focus()}
                    onRightFocus={() => secondRef.current?.focus()}
                />
            </div>
            <div className="grid gap-1 text-center">
                <Select
                    onValueChange={(_period) => {
                        setPeriod(_period as Period | undefined);
                        if (_period === "-") {
                            setDate(undefined);
                        } else if (date) {
                            const newDate = updateDateAmPm(
                                date,
                                _period as Period
                            );
                            setDate(newDate);
                        }
                        if (onUpdateAmPm) {
                            onUpdateAmPm();
                        }
                    }}
                    value={period}
                >
                    <SelectTrigger>
                        <SelectValue defaultValue={"AM"} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectGroup>
                            <SelectItem value="-">
                                <span className="text-[12px]">-</span>
                            </SelectItem>
                            <SelectItem value="AM">
                                <span className="text-[12px]">AM</span>
                            </SelectItem>
                            <SelectItem value="PM">
                                <span className="text-[12px]">PM</span>
                            </SelectItem>
                        </SelectGroup>
                    </SelectContent>
                </Select>
            </div>
        </div>
    );
}
