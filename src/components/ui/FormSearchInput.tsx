import clsx from "clsx";
import { FormField, FormItem, FormLabel, FormMessage } from "../base-ui/form";
import OutlinedX from "./OutlinedX";

const FormSearchInput = ({
    name,
    control,
    label,
    displayValue,
    onClick,
    styleClass = "",
    onClear,
}: {
    name: string;
    control: any;
    label: string;
    displayValue: string;
    onClick: () => void;
    styleClass?: string;
    onClear?: () => void;
}) => {
    return control ? (
        <FormField
            name={name}
            control={control}
            render={({ field }) => {
                const displayValueIsNotEmpty =
                    displayValue?.trim().replace(/-/g, "").length > 0;
                return (
                    <FormItem>
                        <div className={clsx("w-full", styleClass)}>
                            <FormLabel>{label}</FormLabel>
                            <div
                                className={clsx(
                                    "relative rounded-md border border-input",
                                    onClear && "pr-7"
                                )}
                            >
                                <div
                                    className={clsx(
                                        "c-text-size flex min-h-[42px] w-full items-center px-3.5",
                                        onClear && "pr-0"
                                    )}
                                    onClick={onClick}
                                >
                                    {displayValueIsNotEmpty ? displayValue : ""}
                                </div>
                                {onClear && displayValueIsNotEmpty && (
                                    <div className="absolute right-1.5 top-3">
                                        <OutlinedX onClick={onClear} />
                                    </div>
                                )}
                            </div>
                        </div>
                        <FormMessage />
                    </FormItem>
                );
            }}
        />
    ) : (
        <></>
    );
};

export default FormSearchInput;
