import React from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import { Control } from "react-hook-form";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { Input } from "@/components/base-ui/input";
import { replaceAll } from "@/lib/utils";
import { useTranslations } from "next-intl";

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

type FormInputIntergerProps = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    type?: string;
    min?: any;
    max?: any;
    isSearch?: boolean;
    isPercent?: boolean;
    hasLabel?: boolean;
    suffixIcon?: string;
    onClickIcon?: () => void;
};

const FormInputInterger = ({
    control,
    name,
    label,
    placeholder,
    disabled = false,
    type = "text",
    min = null,
    max = null,
    isSearch,
    isPercent = false,
    hasLabel = true,
    suffixIcon,
    onClickIcon,
}: FormInputIntergerProps) => {
    const t = useTranslations("common");

    return control ? (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    {hasLabel && (
                        <FormLabel className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </FormLabel>
                    )}
                    <FormControl>
                        <Input
                            placeholder={capitalize(placeholder ?? "")}
                            disabled={disabled}
                            type={type}
                            minLength={min}
                            maxLength={max}
                            suffixIcon={suffixIcon}
                            onClickIcon={onClickIcon}
                            {...field}
                            className={clsx(isSearch && "search-input")}
                            onChange={(e) => {
                                let sanitizedValue = e.target.value.replace(
                                    /[^0-9]/g,
                                    ""
                                );
                                if (isPercent) {
                                    sanitizedValue = Math.min(
                                        Math.max(
                                            parseInt(sanitizedValue || "0", 10),
                                            0
                                        ),
                                        100
                                    ).toString(); // Clamp between 0-100
                                }
                                field.onChange(sanitizedValue);
                            }}
                        />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormInputInterger;
