import React from "react";
import { capitalize } from "lodash";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/base-ui/form";
import { replaceAll } from "@/lib/utils";
import { Textarea } from "../base-ui/textarea";
import { useTranslations } from "next-intl";

type FormTextareaProps = {
    control: any;
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    forTable?: boolean;
    rows?: number;
    rules?: any;
};

const FormTextarea = ({
    control,
    name,
    label,
    placeholder = "",
    disabled = false,
    forTable = false,
    rows = 2,
    rules,
    ...props
}: FormTextareaProps) => {
    const t = useTranslations("common");

    return control ? (
        <FormField
            control={control}
            name={name}
            rules={rules}
            render={({ field }) => (
                <FormItem>
                    {!forTable && (
                        <FormLabel className="capitalize">
                            {t(replaceAll(label || name, "_", " "))}
                        </FormLabel>
                    )}
                    <FormControl>
                        <Textarea
                            placeholder={capitalize(placeholder)}
                            disabled={disabled}
                            rows={rows}
                            {...field}
                            {...props}
                        />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    ) : (
        <></>
    );
};

export default FormTextarea;
