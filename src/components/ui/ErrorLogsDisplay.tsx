import React from "react";
import { But<PERSON> } from "../base-ui/button";
import { DialogFooter } from "../base-ui/dialog";
import { useTranslations } from "next-intl";

type ErrorLogsDisplayProps = {
    errors: any[];
    refresh: () => void;
    close: () => void;
};

const ErrorLogsDisplay = ({
    errors,
    close,
    refresh,
}: ErrorLogsDisplayProps) => {
    const t = useTranslations("common");

    return (
        <>
            <h3 className="mb-2 text-lg font-semibold">{t("Error Logs")}</h3>
            <div className="mb-4 max-h-[300px] space-y-2 overflow-auto">
                {errors.length === 0 ? (
                    <p className="text-sm text-muted">
                        {t("No errors to display")}
                    </p>
                ) : (
                    errors.map((err, idx) => (
                        <div
                            key={idx}
                            className="rounded bg-red-100 p-2 text-sm text-red-700"
                        >
                            {typeof err === "string"
                                ? err
                                : JSON.stringify(err)}
                        </div>
                    ))
                )}
            </div>
            <DialogFooter>
                <Button variant="outline" onClick={close}>
                    {t("Close")}
                </Button>
            </DialogFooter>
        </>
    );
};

export default ErrorLogsDisplay;
