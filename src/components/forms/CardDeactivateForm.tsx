import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { CommonFormProps, INACTIVE, cardAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import FormTextarea from "../ui/FormTextarea";

const CardDeactivateForm = (props: CommonFormProps & { userable?: any }) => {
    const locale = useLocale();

    const { data: card, axiosQuery: getCard } = useAxios({
        api: cardAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCard({ id: props.id });
        }
    }, []);

    return props.isCreate || card ? (
        <FormWrap card={card} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    card: any;
    userable?: any;
};

const FormWrap = ({ card, refresh, close, userable }: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            card_number: card?.card_number ?? "",
            card_number2: card?.card_number2 ?? "",
            card_number3: card?.card_number3 ?? "",
            status: INACTIVE,
            remarks: card?.remarks ?? "",
            update_library_card_number: true,
            userable_id:
                userable?.userable_id ?? card?.userable?.userable_id ?? null,
            userable_type:
                userable?.userable_type ??
                card?.userable?.userable_type ??
                null,
        },
    });

    const { axiosPut: updateCard, error: putError } = useAxios({
        api: cardAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.name = data.card_number;
            data.card_type = "PROXIMITY";
            updateCard({ id: card.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <div>
            <h2 className="mb-5 capitalize">
                {t("Deactivate ")}
                {t("smart card")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormTextarea
                        control={form.control}
                        name="remarks"
                        label={t("remarks") + "*"}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CardDeactivateForm;
