import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    courseAPI,
    GET_ALL_PARAMS,
    semesterSettingAPI,
    semesterYearsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError, toYMD } from "@/lib/utils";
import { DatePicker } from "../../ui/DatePicker";
import FormSelect from "../../ui/FormSelect";
import StatusFormSwitch from "../../ui/StatusFormSwitch";

const SemesterSettingForm = (props: CommonFormProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const { data: currentData, axiosQuery: getSemesterSetting } = useAxios({
        api: semesterSettingAPI,
        locale,
        onError: props.close,
    });

    const { data: semesterYears, axiosQuery: getSemesterYears } = useAxios({
        api: semesterYearsAPI,
        locale,
        onError: props.close,
        onSuccess: (result) => {
            if (isEmpty(result?.data)) {
                props.close();
                toast(t("Please add semester year first"));
            }
        },
    });

    const { data: courses, axiosQuery: getCourses } = useAxios({
        api: courseAPI,
        locale,
        onError: props.close,
        onSuccess: (result) => {
            if (isEmpty(result?.data)) {
                props.close();
                toast(t("Please add course first"));
            }
        },
    });

    useEffect(() => {
        getSemesterYears({
            params: { ...GET_ALL_PARAMS, order_by: { year: "desc" } },
        });
        getCourses({ params: GET_ALL_PARAMS });
        if (!props.isCreate && props.id) {
            getSemesterSetting({ id: props.id });
        }
    }, []);

    return semesterYears && courses && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            semesterYears={semesterYears}
            courses={courses}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    semesterYears: any;
    courses: any;
};

const FormWrap = ({
    currentData,
    isCreate = false,
    semesterYears,
    courses,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            course_id: currentData?.course?.id ?? "",
            semester_year_setting_id:
                currentData?.semester_year_setting?.id ?? "",
            name: currentData?.name ?? "",
            from: currentData?.from ?? "",
            to: currentData?.to ?? "",
            is_current_semester: isCreate
                ? true
                : currentData?.is_current_semester ?? false,
        },
    });

    const { axiosPost: createSemesterSetting, error: postError } = useAxios({
        api: semesterSettingAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSemesterSetting, error: putError } = useAxios({
        api: semesterSettingAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.to = toYMD(data.to);
            data.from = toYMD(data.from);
            if (isCreate) {
                createSemesterSetting(data);
            } else {
                updateSemesterSetting({ id: currentData.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("semester setting")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[600px]"
                >
                    <FormSelect
                        control={form.control}
                        name="course_id"
                        label={t("course") + "*"}
                        options={courses}
                    />

                    <FormSelect
                        control={form.control}
                        name="semester_year_setting_id"
                        label={t("semester year") + "*"}
                        options={semesterYears}
                        isSortByName={false}
                    />
                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                    />

                    <DatePicker
                        control={form.control}
                        name="from"
                        label={t("from") + "*"}
                    />

                    <DatePicker
                        control={form.control}
                        name="to"
                        label={t("to") + "*"}
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_current_semester"
                        label={t("current semester")}
                    />
                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default SemesterSettingForm;
