import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { useAxios } from "@/lib/hook";
import { optionUserLabel, showBackendFormError } from "@/lib/utils";
import FormSearchInput from "../ui/FormSearchInput";
import StaffSearchEngine from "../ui/search-engines/StaffSearchEngine";
import Modal from "../ui/Modal";
import { hostelPersonInChargeAPI, WORKING } from "@/lib/constant";
import { useTranslations } from "next-intl";

const HostelPersonInChargeForm = ({ refresh, close }) => {
    const t = useTranslations("common");
    const [selectedPerson, setSelectedPerson] = useState<any>();
    const [openSearch, setOpenSearch] = useState(false);

    const form = useForm({
        defaultValues: {
            employee_id: null,
        },
    });

    const { axiosPost: addPIC, error: postError } = useAxios({
        api: `${hostelPersonInChargeAPI}/add`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            addPIC(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-5">{t("Add ") + t("person in charge")}</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <div>
                        <FormSearchInput
                            control={form.control}
                            name="employee_id"
                            label={t("employee") + "*"}
                            displayValue={optionUserLabel(
                                selectedPerson?.employee_number,
                                selectedPerson?.translations?.name
                            )}
                            onClick={() => setOpenSearch(true)}
                        />
                        {selectedPerson?.photo && (
                            <img
                                src={selectedPerson.photo}
                                className="mt-2 h-auto w-40 rounded-sm"
                            />
                        )}
                    </div>
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StaffSearchEngine
                    otherFilterParams={{
                        status: WORKING,
                    }}
                    setSelection={(person) => {
                        if (person) {
                            form.setValue("employee_id", person.id);
                            form.clearErrors("employee_id");
                            setSelectedPerson(person);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </div>
    );
};

export default HostelPersonInChargeForm;
