import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { CommonFormProps, calendarTargetAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { getChunkedStudentData, showBackendFormError } from "@/lib/utils";
import { Label } from "@/components/base-ui/label";
import StudentsPicker from "@/components/ui/PickersWithPaginatedTable/StudentsPicker";
import { flatten } from "lodash";

const CalendarTargetForm = (
    props: CommonFormProps & { calendarName: string }
) => {
    const locale = useLocale();

    const { data: calendarTargets, axiosQuery: getCalendarTargets } = useAxios({
        api: calendarTargetAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCalendarTargets({
                params: {
                    calendar_id: props.id,
                    includes: ["calendarTargetable", "calendar"],
                    per_page: -1,
                },
            });
        }
    }, []);

    return props.isCreate || calendarTargets ? (
        <FormWrap
            calendarId={props.id}
            calendarTargets={calendarTargets}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    calendarId: any;
    calendarName: string;
    calendarTargets: any;
};

const FormWrap = ({
    calendarId,
    calendarName,
    calendarTargets,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            priority: calendarTargets?.[0]?.priority ?? 1,
            calendar_targets: calendarTargets ?? [],
        },
    });

    const formattedCurrentData = calendarTargets?.map((target) => ({
        id: target?.calendar_targetable_id,
        student_number: target?.student?.student_number,
        translations: target?.student?.translations,
    }));
    console.log("formattedCurrentData", formattedCurrentData);
    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        getChunkedStudentData(formattedCurrentData, true)
    );
    console.log("targetsChunk", targetsChunk);
    const { axiosPost: updateCalendar, error: postError } = useAxios({
        api: `/calendars/${calendarId}/calendar-targets`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.calendar_targets = flatten(targetsChunk).map((student) => ({
                id: student.id,
                type: "App\\Models\\Student",
            }));
            updateCalendar(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-4">Edit Calendar Targets</h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid gap-y-4 lg:min-w-[800px]"
                >
                    <div className="grid flex-wrap items-start gap-4 gap-x-7 border-b border-dashed pb-5 lg:flex">
                        <div className="grid gap-y-2 lg:order-2">
                            <Label>Calendar Name</Label>
                            <div className="font-medium leading-none text-gray-600">
                                {calendarName}
                            </div>
                        </div>
                        <div className="max-w-[200px]">
                            <FormInputInterger
                                control={form.control}
                                name="priority"
                                label="Priority*"
                            />
                        </div>
                    </div>
                    <StudentsPicker
                        targetsChunk={targetsChunk}
                        setTargetsChunk={setTargetsChunk}
                        errorMessage={
                            form.formState.errors?.calendar_targets?.message
                        }
                        otherFilterParams={{
                            only_active_class: 1,
                            // response: "FULL",
                            // includes: [
                            //     "currentSemesterPrimaryClass.semesterClass.classModel",
                            // ],
                        }}
                        // showPrimaryClass={true}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CalendarTargetForm;
