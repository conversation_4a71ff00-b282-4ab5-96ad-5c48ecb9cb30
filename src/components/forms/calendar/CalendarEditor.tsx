import { displayDateTime, isValueTrue, toYMD } from "@/lib/utils";
import dayGridPlugin from "@fullcalendar/daygrid";
import FullCalendar from "@fullcalendar/react";
import clsx from "clsx";
import {
    eachDayOfInterval,
    endOfMonth,
    isWeekend,
    startOfMonth,
} from "date-fns";
import { PenBoxIcon, PlusCircle, XCircle } from "lucide-react";
import { useEffect, useState } from "react";
import Modal from "../../ui/Modal";
import { DialogFooter } from "../../base-ui/dialog";
import { Button } from "../../base-ui/button";
import { useForm } from "react-hook-form";
import FormCheckbox from "../../ui/FormCheckbox";
import FormTextarea from "../../ui/FormTextarea";
import { Form } from "../../base-ui/form";
import { useAxios } from "@/lib/hook";
import { useLocale } from "next-intl";
import { calendarAPI, DATE_FORMAT } from "@/lib/constant";
import { Label } from "@/components/base-ui/label";

type AssignedItemProps = {
    start: Date | null;
    title: string;
    isAttendanceRequired: boolean;
};

const CalenderEditor = ({ id, close }) => {
    const locale = useLocale();

    const [currentMonthStart, setCurrentMonthStart] = useState<Date>(
        startOfMonth(new Date())
    );
    const [assignedItems, setAssignedItems] = useState<any[] | undefined>();

    const [targetDeleteDate, setTargetDeleteDate] = useState<Date | null>(null);
    const [targetEdit, setTargetEdit] = useState<AssignedItemProps | null>(
        null
    );

    const { axiosQuery: getCalendar } = useAxios({
        api: calendarAPI,
        locale,
        onError: close,
        onSuccess: (result) => {
            const settings = result?.data?.settings;
            const formattedSettings = settings.map((item) => ({
                id: item.id,
                start: new Date(item.date),
                title: item.description ?? "",
                isAttendanceRequired: isValueTrue(item.is_attendance_required),
            }));

            setAssignedItems(formattedSettings);
        },
    });

    const { axiosPost: updateCalendar } = useAxios({
        api: "/calendar-settings/edit",
        toastMsg: "Updated successfully",
        onSuccess: () => {
            fetchSettings();
            setTargetEdit(null);
        },
    });

    const { axiosPost: deleteDate } = useAxios({
        api: "/calendar-settings/edit",
        toastMsg: "Deleted successfully",
        onSuccess: () => {
            fetchSettings();
            setTargetDeleteDate(null);
        },
    });

    function fetchSettings() {
        getCalendar({ id });
    }

    function onSubmit(data) {
        updateCalendar(data, { showErrorInToast: true });
    }

    function onDelete() {
        const data: Record<string, any> = {
            calendar_id: id,
            dates: [
                {
                    date: toYMD(targetDeleteDate),
                    is_delete: true,
                },
            ],
        };
        deleteDate(data, { showErrorInToast: true });
    }

    function initiateCalender() {
        if (!currentMonthStart || !assignedItems) return;

        const daysInMonth = eachDayOfInterval({
            start: startOfMonth(currentMonthStart),
            end: endOfMonth(currentMonthStart),
        });

        const weekdays = daysInMonth.filter(
            (date) =>
                !isWeekend(date) &&
                !assignedItems.some(
                    (item) => item.start?.toDateString() === date.toDateString()
                )
        );
        const weekends = daysInMonth.filter(
            (date) =>
                isWeekend(date) &&
                !assignedItems.some(
                    (item) => item.start?.toDateString() === date.toDateString()
                )
        );
        const data: Record<string, any> = {
            calendar_id: id,
            dates: [],
        };
        weekdays.forEach((date) => {
            data.dates.push({
                date: toYMD(date),
                is_attendance_required: true,
                description: "School Day",
            });
        });

        weekends.forEach((date) => {
            data.dates.push({
                date: toYMD(date),
                is_attendance_required: false,
                description: "Weekend",
            });
        });

        console.log("data", data);
        onSubmit(data);
    }

    function isAssigned(date: Date) {
        return assignedItems?.some((item) => toYMD(item.start) === toYMD(date));
    }

    useEffect(() => {
        console.log("assignedItems", assignedItems);
    }, [assignedItems]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            fetchSettings();
        }, 200);

        return () => clearTimeout(timeout);
    }, []);

    return (
        <>
            <div className="-mt-1 min-h-10 w-[90vw] max-w-[1100px] overflow-auto border-dashed md:border-r lg:border-none">
                <div className="calendar w-full min-w-[800px] max-w-none">
                    {assignedItems && (
                        <FullCalendar
                            plugins={[dayGridPlugin]}
                            initialView="dayGridMonth"
                            showNonCurrentDates={false}
                            events={assignedItems}
                            datesSet={(dateInfo) => {
                                setCurrentMonthStart(dateInfo.start);
                            }}
                            dayMaxEventRows={1}
                            headerToolbar={{
                                right: "initiateCalendar prev,next today close",
                                left: "title",
                            }}
                            customButtons={{
                                initiateCalendar: {
                                    text: "Initiate Calendar",
                                    click: initiateCalender,
                                },
                                close: {
                                    text: "close",
                                    click: close,
                                },
                            }}
                            dayCellContent={(arg) => {
                                return (
                                    <>
                                        <div className="day-number text-center font-semibold">
                                            {arg.dayNumberText}
                                        </div>

                                        {!isAssigned(arg.date) && (
                                            <PlusCircle
                                                size={20}
                                                className="mb-1 mt-1.5 cursor-pointer text-themeGray hover:text-themeGreen"
                                                onClick={() =>
                                                    setTargetEdit({
                                                        start: arg.date,
                                                        title: "",
                                                        isAttendanceRequired:
                                                            false,
                                                    })
                                                }
                                            />
                                        )}
                                    </>
                                );
                            }}
                            eventContent={({ event }) => {
                                return (
                                    <div
                                        className={clsx(
                                            "flex h-full w-full flex-grow flex-col items-center justify-between gap-2 px-1 text-[14px] font-medium",
                                            event.extendedProps
                                                .isAttendanceRequired
                                                ? "text-blue-700"
                                                : "text-pink-500"
                                        )}
                                    >
                                        <div>{event.title}</div>
                                        <div className="flex w-full justify-between px-1">
                                            <XCircle
                                                onClick={() =>
                                                    setTargetDeleteDate(
                                                        event.start
                                                    )
                                                }
                                                size={20}
                                                className="cursor-pointer text-themeGray hover:text-gray-500"
                                            />
                                            <PenBoxIcon
                                                className="ml-auto cursor-pointer text-gray-400 hover:text-themeGreen"
                                                size={18}
                                                onClick={() =>
                                                    setTargetEdit({
                                                        start: event.start,
                                                        title: event.title,
                                                        isAttendanceRequired:
                                                            event.extendedProps
                                                                .isAttendanceRequired,
                                                    })
                                                }
                                            />
                                        </div>
                                    </div>
                                );
                            }}
                            height={"auto"}
                        />
                    )}
                </div>
            </div>

            {/* edit */}
            <Modal
                open={targetEdit != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetEdit(null);
                }}
            >
                {targetEdit ? (
                    <CalendarSettingForm
                        calendarId={id}
                        currentData={targetEdit}
                        submit={onSubmit}
                    />
                ) : (
                    <div className="h-[320px]"></div>
                )}
            </Modal>

            {/* delete */}
            <Modal
                open={targetDeleteDate != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetDeleteDate(null);
                }}
            >
                <p className="mt-3 font-medium">
                    Are you sure you want to delete for{" "}
                    {displayDateTime(targetDeleteDate, DATE_FORMAT.DMY)}?
                </p>
                <DialogFooter className={"mt-2"}>
                    <Button
                        variant="outline"
                        onClick={() => setTargetDeleteDate(null)}
                    >
                        Cancel
                    </Button>
                    <Button onClick={onDelete}>Confirm</Button>
                </DialogFooter>
            </Modal>
        </>
    );
};

const CalendarSettingForm = ({
    calendarId,
    currentData,
    submit,
}: {
    calendarId: any;
    currentData: AssignedItemProps;
    submit: (data: any) => void;
}) => {
    console.log("currentData", currentData);
    const form = useForm({
        defaultValues: {
            is_attendance_required: isValueTrue(
                currentData.isAttendanceRequired
            ),
            description: currentData?.title ?? "",
        },
    });

    return (
        <Form {...form}>
            <form
                className="flex flex-col gap-4"
                onSubmit={form.handleSubmit((data) => {
                    const postData: Record<string, any> = {
                        calendar_id: calendarId,
                        dates: [],
                    };
                    postData.dates.push({
                        ...data,
                        date: toYMD(currentData?.start),
                    });
                    submit(postData);
                })}
            >
                <h2>Edit</h2>
                {currentData.start && (
                    <div className="grid gap-y-1">
                        <Label>Date</Label>
                        <div className="font-medium text-gray-600">
                            {displayDateTime(
                                currentData.start,
                                DATE_FORMAT.DMY
                            )}
                        </div>
                    </div>
                )}
                <FormTextarea
                    control={form.control}
                    name="description"
                    rules={{
                        required: "This field is required",
                    }}
                />
                <FormCheckbox
                    control={form.control}
                    name="is_attendance_required"
                />

                <Button type="submit" className="ml-auto">
                    Save
                </Button>
            </form>
        </Form>
    );
};

export default CalenderEditor;
