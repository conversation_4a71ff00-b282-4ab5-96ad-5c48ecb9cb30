import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { CommonFormProps, calendarAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isValueTrue, showBackendFormError } from "@/lib/utils";
import FormCheckbox from "@/components/ui/FormCheckbox";

const CalendarForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: calendar, axiosQuery: getCalendar } = useAxios({
        api: calendarAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCalendar({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || calendar) ? (
        <FormWrap
            calendar={calendar}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    calendar: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    calendar,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: calendar?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            year: calendar?.year ?? "",
            is_default: isValueTrue(calendar?.is_default),
            is_active: isValueTrue(calendar?.is_active),
        },
    });

    const { axiosPost: createCalendar, error: postError } = useAxios({
        api: calendarAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateCalendar, error: putError } = useAxios({
        api: calendarAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createCalendar(data);
            } else {
                updateCalendar({ id: calendar.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Calendar</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="year"
                        label="Year*"
                    />
                    <div className="pl-.5 grid gap-y-1.5">
                        <FormCheckbox
                            control={form.control}
                            name="is_default"
                        />
                        <FormCheckbox control={form.control} name="is_active" />
                    </div>

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CalendarForm;
