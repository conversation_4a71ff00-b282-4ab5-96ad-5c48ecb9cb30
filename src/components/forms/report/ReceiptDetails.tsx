import InfoCard from "@/components/ui/InfoCard";
import TableDisplay from "@/components/ui/TableDisplay";
import { appCurrencySymbol } from "@/lib/constant";
import { getNameWithCurrency, getValueWithCurrency } from "@/lib/utils";

const ReceiptDetails = ({ currentData }) => {
    const info: any = {
        order_reference_no: currentData?.order_reference_number,
        student_number: currentData?.student_number,
        student_name: currentData?.student_name,
        student_semester_name: currentData?.student_semester_name,
        student_class_name: currentData?.student_class_name,
        total_payment: appCurrencySymbol + " " + currentData?.total,
        purchase_date: currentData?.purchase_date,
    };

    const data =
        currentData?.items &&
        Object.values(currentData?.items).map((item: any) => {
            return {
                product_name: item?.product_name,
                [getNameWithCurrency("price per unit", appCurrencySymbol)]:
                    getValueWithCurrency(
                        appCurrencySymbol,
                        item?.product_unit_price
                    ),

                quantity: item?.quantity,
                [getNameWithCurrency("total", appCurrencySymbol)]:
                    getValueWithCurrency(appCurrencySymbol, item?.total),
            };
        });
    return (
        <div>
            <div className="mb-3 font-semibold text-themeGreenDark lg:text-lg">
                {currentData?.billing_document_reference_number}
            </div>

            <InfoCard data={info} noBorder />

            <div className="pb-3 pt-5">
                <TableDisplay title="Item List" data={data} />
            </div>
        </div>
    );
};

export default ReceiptDetails;
