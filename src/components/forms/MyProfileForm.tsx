import React, { useEffect } from "react";
import { isArray, isEmpty, lowerCase } from "lodash";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    ACTIVE,
    changePasswordAPI,
    CommonFormProps,
    INACTIVE,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { optionUserLabel, showBackendFormError } from "@/lib/utils";
import { Button } from "../base-ui/button";
import { Label } from "../base-ui/label";

const MyProfileForm = (props: CommonFormProps) => {
    const userProfile = useUserProfile((state) => state.userProfile);

    return userProfile ? (
        <FormWrap userData={userProfile} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    userData: any;
};

const FormWrap = ({ userData, refresh, close }: FormWrapProps) => {
    const directUserables = isArray(userData?.direct_userables)
        ? userData?.direct_userables.map(
              (userable) =>
                  optionUserLabel(
                      userable?.number,
                      userable?.translations?.name
                  ) + ` (${userable?.user_type_description})`
          )
        : [];

    const accessibleStudentUserables = isArray(
        userData?.accessible_student_userables
    )
        ? userData?.accessible_student_userables.map(
              (userable) =>
                  optionUserLabel(
                      userable?.number,
                      userable?.translations?.name
                  ) + ` (${userable?.user_type_description})`
          )
        : [];

    const userStatus = lowerCase(userData.is_active ? ACTIVE : INACTIVE);

    const form = useForm({
        defaultValues: {
            password: "",
            password_confirmation: "",
        },
    });

    const { axiosPost: updatePassword, error: postError } = useAxios({
        api: changePasswordAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            updatePassword(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-3">My Profile</h2>
            <div className="grid gap-y-2">
                <div>
                    <Label>Email</Label>
                    <p>{userData?.email}</p>
                </div>
                <div>
                    <Label>Phone Number</Label>
                    <p>
                        {isEmpty(userData?.phone_number)
                            ? "-"
                            : userData?.phone_number}
                    </p>
                </div>
                <div className="pt-1">
                    <Label>User Info</Label>
                    <ul>
                        {directUserables.length > 0
                            ? directUserables.map((user, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{user}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                </div>
                <div className="pt-1">
                    <Label>Access Students</Label>
                    <ul>
                        {accessibleStudentUserables.length > 0
                            ? accessibleStudentUserables.map((user, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{user}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                </div>
                <div className="pt-1">
                    <Label>Roles</Label>
                    <ul>
                        {userData.roles.length > 0
                            ? userData.roles.map((role, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{role.name}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                </div>
                <div>
                    <Label>Status</Label>
                    <div className={`cell-status ${userStatus}`}>
                        {userStatus}
                    </div>
                </div>
            </div>
            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-5 grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="password"
                        type="password"
                        label={"New Password"}
                    />

                    <FormInput
                        control={form.control}
                        name="password_confirmation"
                        type="password"
                        label={"New Password Confirmation"}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default MyProfileForm;
