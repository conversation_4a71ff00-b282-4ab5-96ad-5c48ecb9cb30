import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { axiosInstance } from "@/lib/api";
import {
    CommonFilterProps,
    countryAPI,
    DEFAULT_FILTER_PARAMS,
    employeeJobTitlesAPI,
    employeeStatusOptions,
    raceAPI,
    religionAPI,
    stateAPI,
    genderOptions,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { configForGetAll } from "@/lib/utils";
import { Button } from "../base-ui/button";
import FormInputInterger from "../ui/FormInputInterger";
import FormSelect from "../ui/FormSelect";

const FilterEmployeeForm = ({
    filter,
    setFilter,
    close,
    isTeacher,
}: CommonFilterProps & { isTeacher: boolean }) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            job_title_id: filter?.job_title_id ?? "",
            email: filter?.email ?? "",
            employee_number: filter?.employee_number ?? "",
            badge_no: filter?.badge_no ?? "",
            nric: filter?.nric ?? "",
            race_id: filter?.race_id ?? "",
            religion_id: filter?.religion_id ?? "",
            country_id: filter?.country_id ?? "",
            state_id: filter?.state_id ?? "",
            status: filter?.status ?? "",
            is_hostel: filter?.is_hostel ?? undefined,
            gender: filter?.gender ?? "",
        },
    });

    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.jobTitles = res[0].data.data;
            _options.religions = res[1].data.data;
            _options.races = res[2].data.data;
            _options.countries = res[3].data.data;
            setOptions(_options);
        },
    });

    const locale = useLocale();
    const t = useTranslations("common");

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(employeeJobTitlesAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(countryAPI, _config),
        ]);
    }, [locale]);

    const { data: stateOptions, axiosQuery: getStateOptions } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        const countryId = form.watch("country_id");
        if (countryId) {
            getStateOptions({ params: { country_id: countryId } });
        }
    }, [form.watch("country_id")]);

    function onSubmit(data: Record<string, any>) {
        Object.keys(data).forEach((key) => {
            if (!data[key]) {
                data[key] = undefined;
            }
        });
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
            is_teacher: isTeacher ? 1 : 0,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />
                <FormInput control={form.control} name={"email"} type="email" />
                <FormInput control={form.control} name={"employee_number"} />
                <FormInput
                    control={form.control}
                    name={"badge_no"}
                    label="badge number"
                />
                <FormInputInterger
                    control={form.control}
                    name="nric"
                    label="NRIC"
                />

                <FormSelect
                    name="job_title_id"
                    label="job title"
                    control={form.control}
                    options={options?.jobTitles}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    options={employeeStatusOptions.map((status) => ({
                        id: status,
                        name: t(status),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="gender"
                    isSortByName={false}
                    options={genderOptions.map((gender) => ({
                        id: gender.id,
                        name: t(gender.name),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="race_id"
                    label="race"
                    options={options?.races}
                />

                <FormSelect
                    control={form.control}
                    name="religion_id"
                    label="religion"
                    options={options?.religions}
                />

                <FormSelect
                    control={form.control}
                    name="country_id"
                    label="country"
                    options={options?.countries}
                    onChange={(val) => {
                        getStateOptions({ params: { country_id: val } });
                        form.setValue("state_id", "");
                    }}
                />

                <FormSelect
                    control={form.control}
                    name="state_id"
                    label="state"
                    options={stateOptions}
                    isDisabled={!form.watch("country_id")}
                />

                <FormSelect
                    control={form.control}
                    name={"is_hostel"}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons lg:col-span-2">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterEmployeeForm;
