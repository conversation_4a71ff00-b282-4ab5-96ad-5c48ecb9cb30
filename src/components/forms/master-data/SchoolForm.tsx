import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    COLLEGE,
    CommonFormProps,
    countryAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    schoolAPI,
    SECONDARY,
    stateAPI,
    UNIVERSITY,
    VOCATIONAL,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, strStartCase } from "@/lib/utils";

const SchoolForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentSchool, axiosQuery: getSchool } = useAxios({
        api: schoolAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getSchool({ id: props.id });
        }
    }, [locale]);

    return activeLanguages && (props.isCreate || currentSchool) ? (
        <FormWrap
            currentSchool={currentSchool}
            activeLanguages={activeLanguages}
            locale={locale}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    currentSchool: any;
    activeLanguages: Array<Record<string, any>>;
    locale: any;
};

const FormWrap = ({
    currentSchool,
    activeLanguages,
    isCreate = false,
    locale,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentSchool?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            level: currentSchool?.level ?? "",
            country_id: currentSchool?.state?.country?.id ?? "",
            state_id: currentSchool?.state?.id ?? "",
        },
    });

    const {
        data: countriesList,
        axiosQuery: getCountriesList,
        isLoading: isCountriesLoading,
    } = useAxios({
        api: countryAPI,
        locale,
    });

    const {
        data: statesList,
        axiosQuery: getStatesList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    const { axiosPost: createSchool, error: postError } = useAxios({
        api: schoolAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSchool, error: putError } = useAxios({
        api: schoolAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (data.country_id && !data.state_id) {
                form.setError("state_id", {
                    type: "custom",
                    message: t("State is required when a country is selected"),
                });
                return;
            }

            if (isCreate) {
                createSchool(data);
            } else {
                updateSchool({ id: currentSchool.id, data });
            }
        })();
    }

    useEffect(() => {
        if (currentSchool?.state?.country?.id) {
            getStatesList({
                params: { country_id: currentSchool?.state?.country?.id },
            });
        }
        getCountriesList({ params: GET_ALL_PARAMS });
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);
    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("school")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormSelect
                        control={form.control}
                        name="level"
                        label={t("level") + "*"}
                        isSortByName={false}
                        options={[
                            PRIMARY,
                            SECONDARY,
                            VOCATIONAL,
                            COLLEGE,
                            UNIVERSITY,
                        ].map((level) => ({
                            id: level,
                            name: strStartCase(
                                t(
                                    level === PRIMARY
                                        ? "primary_school_level"
                                        : level === SECONDARY
                                          ? "secondary_school_level"
                                          : level
                                )
                            ),
                        }))}
                    />
                    <FormSelect
                        control={form.control}
                        name="country_id"
                        label={t("country")}
                        isLoading={isCountriesLoading}
                        options={countriesList}
                        onChange={(val) => {
                            getStatesList({ params: { country_id: val } });
                            form.setValue("state_id", "");
                        }}
                    />
                    <FormSelect
                        control={form.control}
                        name="state_id"
                        label={t("state")}
                        options={statesList}
                        isLoading={isStatesLoading}
                        isDisabled={!form.getValues("country_id")}
                    />
                    <Button type="submit" className="mb-3 ml-auto mt-20">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SchoolForm;
