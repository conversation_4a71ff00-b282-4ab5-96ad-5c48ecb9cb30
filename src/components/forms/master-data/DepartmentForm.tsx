import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormTextarea from "@/components/ui/FormTextarea";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { CommonFormProps, departmentAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const DepartmentForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: department, axiosQuery: getDepartment } = useAxios({
        api: departmentAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getDepartment({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || department) ? (
        <FormWrap
            department={department}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    department: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    department,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            code: department?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: department?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            description: department?.description ?? "",
            sequence: department?.sequence ?? sequenceDefaultValue,
            is_active: isCreate ? true : department?.is_active ?? false,
        },
    });

    const { axiosPost: createDepartment, error: postError } = useAxios({
        api: departmentAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateDepartment, error: putError } = useAxios({
        api: departmentAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createDepartment(data);
            } else {
                updateDepartment({ id: department.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("department")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="sequence"
                        label={t("sequence") + "*"}
                    />
                    <FormTextarea
                        control={form.control}
                        name="description"
                        label={t("description")}
                    />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={t("Status")}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default DepartmentForm;
