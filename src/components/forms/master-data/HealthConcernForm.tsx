import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { CommonFormProps, healthConcernAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const HealthConcernForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: HealthConcern, axiosQuery: getHealthConcern } = useAxios({
        api: healthConcernAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getHealthConcern({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || HealthConcern) ? (
        <FormWrap
            HealthConcern={HealthConcern}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    HealthConcern: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    HealthConcern,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: HealthConcern?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: HealthConcern?.sequence ?? sequenceDefaultValue,
        },
    });

    const { axiosPost: createHealthConcern, error: postError } = useAxios({
        api: healthConcernAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateHealthConcern, error: putError } = useAxios({
        api: healthConcernAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createHealthConcern(data);
            } else {
                updateHealthConcern({ id: HealthConcern.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("health concern")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="sequence"
                        label={t("sequence") + "*"}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default HealthConcernForm;
