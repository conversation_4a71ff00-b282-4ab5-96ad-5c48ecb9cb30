import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    COLLEGE,
    CommonFilterProps,
    countryAPI,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    PRIMARY,
    SECONDARY,
    stateAPI,
    UNIVERSITY,
    VOCATIONAL,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import { strStartCase } from "@/lib/utils";

const FilterSchoolForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const t = useTranslations("common");
    const locale = useLocale();
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            level: filter?.level ?? undefined,
            country_id: filter?.country_id ?? undefined,
            state_id: filter?.state_id ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        form.clearErrors();
        if (data.country_id && !data.state_id) {
            form.setError("state_id", {
                type: "custom",
                message: t("State is required when a country is selected"),
            });
            return;
        }

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const {
        data: countriesList,
        axiosQuery: getCountriesList,
        isLoading: isCountriesLoading,
    } = useAxios({
        api: countryAPI,
        locale,
    });

    const {
        data: statesList,
        axiosQuery: getStatesList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        getCountriesList({ params: GET_ALL_PARAMS });
    }, [locale]);

    useEffect(() => {
        const countryId = form.watch("country_id");
        if (countryId) {
            getStatesList({
                params: {
                    country_id: countryId,
                    ...GET_ALL_PARAMS,
                },
            });
            form.clearErrors("state_id");
        }
    }, [form.getValues("country_id")]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">{t("Filter")}</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name="level"
                    isSortByName={false}
                    options={[
                        PRIMARY,
                        SECONDARY,
                        VOCATIONAL,
                        COLLEGE,
                        UNIVERSITY,
                    ].map((level) => ({
                        id: level,
                        name: strStartCase(
                            t(
                                level === PRIMARY
                                    ? "primary_school_level"
                                    : level === SECONDARY
                                      ? "secondary_school_level"
                                      : level
                            )
                        ),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="country_id"
                    label={t("country")}
                    options={countriesList}
                    isLoading={isCountriesLoading}
                    onChange={(val) => {
                        getStatesList({ params: { country_id: val } });
                        form.setValue("state_id", "");
                    }}
                />
                <FormSelect
                    control={form.control}
                    name="state_id"
                    label={t("state")}
                    isLoading={isStatesLoading}
                    options={statesList}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSchoolForm;
