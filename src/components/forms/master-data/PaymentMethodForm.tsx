import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormTextarea from "@/components/ui/FormTextarea";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { CommonFormProps, paymentMethodsAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import { useTranslations } from "next-intl";

const PaymentMethodForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    return activeLanguages ? (
        <FormWrap activeLanguages={activeLanguages} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentTableData: paymentMethod,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const sequenceDefaultValue = 1;
    const form = useForm({
        defaultValues: {
            name: paymentMethod?.name ?? "",
            code: paymentMethod?.code ?? "",
            is_active: isCreate ? true : paymentMethod?.is_active ?? false,
            description: paymentMethod?.description ?? "",
        },
    });

    const { axiosPost: createPaymentMethod, error: postError } = useAxios({
        api: paymentMethodsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updatePaymentMethod, error: putError } = useAxios({
        api: paymentMethodsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createPaymentMethod(data);
            } else {
                updatePaymentMethod({ id: paymentMethod?.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("payment method")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />
                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                    />
                    <FormTextarea control={form.control} name={"description"} />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={t("Status")}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default PaymentMethodForm;
