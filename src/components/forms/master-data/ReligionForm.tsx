import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { CommonFormProps, religionAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const ReligionForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: religion, axiosQuery: getReligion } = useAxios({
        api: religionAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getReligion({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || religion) ? (
        <FormWrap
            religion={religion}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    religion: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    religion,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: religion?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: religion?.sequence ?? sequenceDefaultValue,
        },
    });

    const { axiosPost: createReligion, error: postError } = useAxios({
        api: religionAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateReligion, error: putError } = useAxios({
        api: religionAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createReligion(data);
            } else {
                updateReligion({ id: religion.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("religion")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="sequence"
                        label={t("sequence") + "*"}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ReligionForm;
