import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { CommonFormProps, educationAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const EducationForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: education, axiosQuery: getEducation } = useAxios({
        api: educationAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getEducation({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || education) ? (
        <FormWrap
            education={education}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    education: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    education,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: education?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: education?.sequence ?? sequenceDefaultValue,
        },
    });

    const { axiosPost: createEducation, error: postError } = useAxios({
        api: educationAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateEducation, error: putError } = useAxios({
        api: educationAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createEducation(data);
            } else {
                updateEducation({ id: education.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("education")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="sequence"
                        label={t("sequence") + "*"}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default EducationForm;
