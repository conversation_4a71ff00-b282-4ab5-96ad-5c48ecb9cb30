import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFormProps,
    countryAPI,
    GET_ALL_PARAMS,
    stateAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const StateForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: state, axiosQuery: getState } = useAxios({
        api: stateAPI,
        locale,
        onError: props.close,
    });

    const { data: countries, axiosQuery: getcountries } = useAxios({
        api: countryAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getcountries({ params: GET_ALL_PARAMS });
        if (!props.isCreate && props.id) {
            getState({ id: props.id });
        }
    }, []);

    return activeLanguages && countries && (props.isCreate || state) ? (
        <FormWrap
            countries={countries}
            state={state}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    state: any;
    activeLanguages: Array<Record<string, any>>;
    countries: any;
};

const FormWrap = ({
    state,
    isCreate = false,
    activeLanguages,
    countries,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            country_id: state?.country?.id ?? null,
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: state?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createState, error: postError } = useAxios({
        api: stateAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateState, error: putError } = useAxios({
        api: stateAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createState(data);
            } else {
                updateState({ id: state.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("state")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name={"country_id"}
                        label={t("country") + "*"}
                        options={countries}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("state")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default StateForm;
