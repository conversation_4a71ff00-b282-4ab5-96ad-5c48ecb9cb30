import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, countryAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const CountryForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: country, axiosQuery: getCountry } = useAxios({
        api: countryAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCountry({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || country) ? (
        <FormWrap
            country={country}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    country: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    country,
    isCreate = false,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: country?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createCountry, error: postError } = useAxios({
        api: countryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateCountry, error: putError } = useAxios({
        api: countryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            console.log(data);
            if (isCreate) {
                createCountry(data);
            } else {
                updateCountry({ id: country.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {" "}
                {isCreate ? t("Create ") : t("Update ")}
                {t("country")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CountryForm;
