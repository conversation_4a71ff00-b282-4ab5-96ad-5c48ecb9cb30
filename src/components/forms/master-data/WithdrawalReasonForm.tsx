import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormTextarea from "@/components/ui/FormTextarea";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { CommonFormProps, withdrawalReasonsAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import { useTranslations } from "next-intl";

const WithdrawalReasonForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    return activeLanguages ? (
        <FormWrap activeLanguages={activeLanguages} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentTableData: withdrawalReason,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const sequenceDefaultValue = 1;
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            withdrawalReason?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: withdrawalReason?.sequence ?? sequenceDefaultValue,
            is_active: isCreate ? true : withdrawalReason?.is_active ?? false,
            description: withdrawalReason?.description ?? "",
        },
    });

    const { axiosPost: createWithdrawalReason, error: postError } = useAxios({
        api: withdrawalReasonsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateWithdrawalReason, error: putError } = useAxios({
        api: withdrawalReasonsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createWithdrawalReason(data);
            } else {
                updateWithdrawalReason({ id: withdrawalReason?.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("withdrawal reason")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="sequence"
                        label={t("sequence") + "*"}
                    />
                    <FormTextarea control={form.control} name={"description"} />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={t("Status")}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default WithdrawalReasonForm;
