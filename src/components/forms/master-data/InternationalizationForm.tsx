import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, internationalizationAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import StatusFormSwitch from "../../ui/StatusFormSwitch";

const InternationalizationForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: currentData, axiosQuery: getInternationalization } = useAxios(
        {
            api: internationalizationAPI,
            locale,
            onError: props.close,
        }
    );

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getInternationalization({ id: props.id });
        }
    }, []);

    return props.isCreate || currentData ? (
        <FormWrap currentData={currentData} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
};

const FormWrap = ({
    currentData,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: isCreate ? "" : currentData?.name ?? "",
            code: isCreate ? "" : currentData?.code ?? "",
            is_active: isCreate ? true : currentData?.is_active ?? false,
        },
    });
    const t = useTranslations("common");
    const { languages, setLanguages } = useLanguages((state) => state);

    function updatedLanguages(data) {
        const updatedLanguageList = [...(languages ?? [])].map((language) =>
            language.id === data.id ? data : language
        );
        return updatedLanguageList;
    }

    const { axiosPost: createInternationalization, error: postError } =
        useAxios({
            api: internationalizationAPI,
            onSuccess: (result) => {
                setLanguages(updatedLanguages(result.data));
                close();
                refresh();
            },
        });

    const { axiosPut: updateInternationalization, error: putError } = useAxios({
        api: internationalizationAPI,
        onSuccess: (result) => {
            setLanguages(updatedLanguages(result.data));
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createInternationalization(data);
            } else {
                updateInternationalization({ id: currentData.id, data });
            }
        })();
    }
    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}{" "}
                {t("internationalization")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                    />
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                        disabled={!isCreate}
                    />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={t("status")}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default InternationalizationForm;
