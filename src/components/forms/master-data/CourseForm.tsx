import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, courseAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import StatusFormSwitch from "../../ui/StatusFormSwitch";

const CourseForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: course, axiosQuery: getCourse } = useAxios({
        api: courseAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCourse({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || course) ? (
        <FormWrap
            course={course}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    course: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    course,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: course?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            is_active: isCreate ? true : course?.is_active ?? false,
        },
    });

    const { axiosPost: createCourse, error: postError } = useAxios({
        api: courseAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateCourse, error: putError } = useAxios({
        api: courseAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createCourse(data);
            } else {
                updateCourse({ id: course.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("course")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={t("Status")}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CourseForm;
