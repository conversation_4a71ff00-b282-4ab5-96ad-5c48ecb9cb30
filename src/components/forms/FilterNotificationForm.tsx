import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFilterProps } from "@/lib/constant";
import { Button } from "../base-ui/button";
import FormSelect from "../ui/FormSelect";
import { getNumBooleanValue, NO, YES } from "@/lib/utils";
import { useTranslations } from "next-intl";

const FilterNotificationForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            title: filter?.title ?? "",
            message: filter?.message ?? "",
            is_read: getNumBooleanValue(filter?.is_hostel),
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        data.is_read =
            data.is_read === YES ? 1 : data.is_read == NO ? 0 : undefined;
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            page: 1,
            order_by: {
                created_at: "desc",
            },
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="grid gap-y-5"
            >
                <FormInput control={form.control} name={"title"} />
                <FormInput control={form.control} name={"message"} />

                <FormSelect
                    control={form.control}
                    name="is_read"
                    isStringOptions={true}
                    options={[YES, NO]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterNotificationForm;
