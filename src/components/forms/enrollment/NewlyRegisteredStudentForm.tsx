import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize, flatten, orderBy } from "lodash";
import { useFieldArray, useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { axiosInstance } from "@/lib/api";
import {
    countryAPI,
    raceAPI,
    gradeAPI,
    religionAPI,
    CommonFormProps,
    stateAPI,
    educationAPI,
    GUARDIAN,
    schoolAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    healthConcernAPI,
    genderOptions,
    enrollmentAPI,
    studentAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    combinedNames,
    configForGetAll,
    isChineseCode,
    isMY,
    isProperPhoneNumber,
    isV<PERSON>ueTrue,
    optionUserLabel,
    showBackendFormError,
    firstStartCase,
    toYMD,
    strStartCase,
    downloadFile,
} from "@/lib/utils";
import { DatePicker } from "@/components/ui/DatePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormDivider from "@/components/ui/FormDivider";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import EnrollmentGuardiansForm from "./EnrollmentGuardiansForm";
import { Label } from "@/components/base-ui/label";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import OutlinedX from "@/components/ui/OutlinedX";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";

const NewlyRegisteredStudentForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const [student, setStudent] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.countries = res[0].data.data;
            _options.races = res[1].data.data;
            _options.religions = res[2].data.data;
            _options.grades = res[3].data.data.sort(
                (a, b) => b.sequence - a.sequence
            );
            _options.educations = res[4].data.data;
            _options.primarySchools = res[5].data.data;
            _options.healthConcerns = res[6].data.data.sort(
                (a, b) => b.sequence - a.sequence
            );
            setOptions(_options);
            // TODO: update when api ready

            if (res[7]) {
                setStudent(res[7].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(countryAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(gradeAPI, _config),
            axiosInstance.get(educationAPI, _config),
            axiosInstance.get(schoolAPI, {
                params: { level: PRIMARY, ...GET_ALL_PARAMS },
                headers: { "Accept-Language": locale },
            }),
            axiosInstance.get(healthConcernAPI, _config),
            props.id
                ? axiosInstance.get(`${enrollmentAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return activeLanguages && options ? (
        <FormWrap
            activeLanguages={activeLanguages}
            options={options}
            student={student}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    student: any;
    activeLanguages: Array<Record<string, any>>;
    options: Record<string, any>;
};

const FormWrap = ({
    isCreate = false,
    activeLanguages,
    options,
    student,
    refresh,
    close,
}: FormWrapProps) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const viewOnly = !hasPermit("enrollment-user-update");

    const form = useForm<any>({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: student?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            email: student?.email ?? "",
            phone_number: student?.phone_number ?? "",
            admission_year: student?.admission_year ?? "",
            admission_grade_id: student?.admission_grade?.id ?? "",
            admission_type: student?.admission_type ?? "",
            join_date: student?.join_date ?? "",
            birth_cert_number: student?.birth_cert_number ?? "",
            nric: student?.nric ?? "",
            passport_number: student?.passport_number ?? "",
            gender: student?.gender ?? "",
            date_of_birth: student?.date_of_birth ?? "",
            birthplace: student?.birthplace ?? "",
            nationality_id: student?.nationality?.id ?? "",
            race_id: student?.race?.id ?? "",
            religion_id: student?.religion?.id ?? "",
            address: student?.address ?? "",
            postal_code: student?.postal_code ?? "",
            city: student?.city ?? "",
            state_id: student?.state?.id ?? "",
            country_id: student?.country?.id ?? "",
            is_hostel: isValueTrue(student?.is_hostel),
            have_siblings: isValueTrue(student?.have_siblings),
            _student_number: "",
            remarks: student?.remarks ?? "",
            dietary_restriction: student?.dietary_restriction ?? "",
            health_concern_id: student?.health_concern?.id ?? "",
            primary_school_id: student?.primary_school?.id ?? "",
            enrollment_status: student?.enrollment_status,
            sibling_student_numbers:
                student?.siblings?.map((sibling) => sibling?.student_number) ??
                [],

            guardians: orderBy(
                student?.guardians?.map((guardian) => ({
                    id: guardian?.id ?? undefined,
                    guardian_type: guardian?.guardian_type ?? "",
                    is_primary: isValueTrue(guardian?.is_primary),
                    name: guardian?.translations?.name ?? "",
                    email: guardian?.email ?? "",
                    phone_number: guardian?.phone_number ?? "",
                    nric: guardian?.nric ?? "",
                    passport_number: guardian?.passport_number ?? "",
                    nationality_id: guardian?.nationality?.id ?? "",
                    race_id: guardian?.race?.id ?? "",
                    religion_id: guardian?.religion?.id ?? "",
                    education_id: guardian?.education?.id ?? "",
                    occupation: guardian?.occupation ?? "",
                    occupation_description:
                        guardian?.occupation_description ?? "",
                    live_status: guardian?.live_status ?? "",
                    married_status: guardian?.married_status ?? "",
                })) ?? [],
                [(guardian) => (guardian.type === GUARDIAN ? 1 : 0), "type"],
                ["asc", "asc"]
            ),

            enrollment_exams:
                student?.enrollment_exams?.map((exam) => ({
                    exam_id: exam?.id,
                    exam_slip_number: exam?.exam_slip_number ?? "",
                    total_average: exam?.total_average ?? "",
                    marks: exam?.marks ?? "",
                })) ?? [],
        },
    });

    const [siblingStudents, setSiblingStudents] = useState<any[]>(
        student?.siblings?.map((sibling) => ({
            label: optionUserLabel(
                sibling?.student_number,
                sibling?.translations?.name
            ),
            value: sibling?.student_number,
        }))
    );

    const { loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        isStudentNumber: true,
        useCommonSearch: true,
    });

    const { axiosPut: updateStudent, error: putError } = useAxios({
        api: enrollmentAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                if (
                    !isProperPhoneNumber(
                        form,
                        "phone_number",
                        data.phone_number
                    )
                ) {
                    return;
                }

                if (data.nric?.trim() === "") {
                    data.nric = null;
                }
                if (data.passport_number?.trim() === "") {
                    data.passport_number = null;
                }

                if (data.join_date) {
                    data.join_date = toYMD(data.join_date);
                }

                if (data.date_of_birth) {
                    data.date_of_birth = toYMD(data.date_of_birth);
                }

                data.is_hostel = isValueTrue(data.is_hostel) ? 1 : 0;
                data.have_siblings = isValueTrue(data.have_siblings) ? 1 : 0;

                data.sibling_student_numbers =
                    siblingStudents?.map((student) => student?.value) || [];

                const filteredGuardians: any[] = [];

                [...data.guardians].forEach((guardian) => {
                    if (guardian.nric.trim() === "") {
                        guardian.nric = null;
                    }
                    if (guardian.passport_number.trim() === "") {
                        guardian.passport_number = null;
                    }
                    const filledNames = Object.values(guardian.name).filter(
                        (name: any) => name
                    );
                    if (guardian.name && filledNames.length === 0) {
                        delete guardian.name;
                    }
                    filteredGuardians.push({
                        ...guardian,
                        is_primary: guardian.is_primary ? 1 : 0,
                    });
                });

                data.guardians = filteredGuardians;
                data.enrollment_exams = data.enrollment_exams.map((exam) => ({
                    ...exam,
                    marks: exam.marks.map((item) => ({
                        exam_mark_id: item.id,
                        mark: item.mark,
                    })),
                }));
                console.log(data);
                updateStudent({ id: student.id, data });
            })
        );
    }

    const locale = useLocale();

    const {
        data: stateList,
        axiosQuery: getStateList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    const { axiosQuery: getDownloadURL } = useAxios({
        api: `/admin/enrollments/${student.id}/print-details`,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    function downloadPDF() {
        getDownloadURL({
            params: { report_language: locale },
        });
    }

    useEffect(() => {
        if (student?.country?.id) {
            getStateList({ params: { country_id: student?.country?.id } });
        }
        if (isCreate) {
            const defaultCountryId = options?.countries.find((country) =>
                isMY(country.name?.toLowerCase())
            )?.id;
            if (defaultCountryId) {
                form.setValue("nationality_id", defaultCountryId);
                form.setValue("country_id", defaultCountryId);
                getStateList({ params: { country_id: defaultCountryId } });
            }
        }
    }, []);

    const t = useTranslations("common");

    return (
        <div className="lg:px-2">
            <h2 className="mb-5 lg:mt-2">
                {t(isCreate ? "Create " : "Update ")}
                {t("Newly Registered Student")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form pb-5 lg:min-w-[800px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                            disabled={viewOnly}
                            isUpperCase={!isChineseCode(lang?.code)}
                        />
                    ))}

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="admission_year"
                        options={[2025, 2026, 2027, 2028, 2029, 2030]}
                        isDisabled={viewOnly}
                        // TODO: update when api ready
                    />

                    <FormSelect
                        control={form.control}
                        name="admission_grade_id"
                        label="admission grade"
                        options={options.grades}
                        isDisabled={viewOnly}
                        isSortByName={false}
                    />

                    <FormSelect
                        control={form.control}
                        name="admission_type"
                        label="admission type"
                        isDisabled={viewOnly}
                        isSortByName={false}
                        options={["NEW", "TRANSFERRED"].map((type) => ({
                            id: type,
                            name: t(type === "NEW" ? "new_student" : type),
                        }))}
                    />

                    <DatePicker
                        control={form.control}
                        name="join_date"
                        label="join_date"
                        isDisabled={viewOnly}
                    />

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="enrollment_status"
                        isStringOptions={true}
                        options={["APPROVED", "REJECTED", "SHORTLISTED"]}
                        isDisabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="birth_cert_number"
                        label={t("birth cert number") + "*"}
                        disabled={viewOnly}
                    />

                    <FormInputInterger
                        control={form.control}
                        name="nric"
                        label={t("NRIC") + "*"}
                        max={12}
                        disabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="passport_number"
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="gender"
                        label={t("gender") + "*"}
                        options={genderOptions.map((option) => ({
                            id: option.id,
                            name: capitalize(t(option.name)),
                        }))}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="race_id"
                        label={t("race") + "*"}
                        options={options.races}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="religion_id"
                        label={t("religion") + "*"}
                        options={options.religions}
                        isDisabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="email"
                        type="email"
                        label={t("email") + "*"}
                        disabled={viewOnly}
                    />

                    <FormPhoneInput
                        form={form}
                        name="phone_number"
                        label={t("phone number") + "*"}
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="primary_school_id"
                        label={t("primary school") + "*"}
                        options={options.primarySchools}
                        isDisabled={viewOnly}
                    />

                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="birthplace"
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="nationality_id"
                        label={t("nationality") + "*"}
                        options={options.countries}
                        isDisabled={viewOnly}
                    />

                    <DatePicker
                        control={form.control}
                        name={"date_of_birth"}
                        label={t("date of birth") + "*"}
                        isDisabled={viewOnly}
                    />

                    <FormDivider />

                    <div className="lg:col-span-2">
                        <FormInput
                            control={form.control}
                            name="address"
                            label={t("address") + "*"}
                            isUpperCase={true}
                            disabled={viewOnly}
                        />
                    </div>

                    <FormInput
                        control={form.control}
                        name="city"
                        label={t("city") + "*"}
                        isUpperCase={true}
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="country_id"
                        label={t("country") + "*"}
                        options={options.countries}
                        onChange={(val) => {
                            getStateList({ params: { country_id: val } });
                            form.setValue("state_id", "");
                        }}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="state_id"
                        label={t("state") + "*"}
                        options={stateList}
                        isLoading={isStatesLoading}
                        isDisabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="postal_code"
                        label={t("postal code") + "*"}
                        disabled={viewOnly}
                    />

                    <FormDivider />

                    <FormCheckbox
                        control={form.control}
                        name="is_hostel"
                        label={t("is hostel")}
                        isDisabled={viewOnly}
                    />

                    <FormCheckbox
                        control={form.control}
                        name="have_siblings"
                        label={t("have siblings")}
                        isDisabled={viewOnly}
                    />

                    <div className="grid gap-y-1 border-b border-dashed pb-5 lg:col-span-2">
                        <div className="">
                            <Label className="label">
                                {t("Siblings (As Students)")}
                            </Label>
                            <FreeSelectAsync
                                control={form.control}
                                name="_student_number"
                                placeholder={t("Type to search student")}
                                minWidth={300}
                                hasLabel={false}
                                loadOptions={loadAsyncOptions}
                                value={null}
                                onChange={(option) => {
                                    if (
                                        option?.value &&
                                        !siblingStudents?.find(
                                            (sibling) =>
                                                sibling.value === option.value
                                        )
                                    ) {
                                        setSiblingStudents([
                                            ...siblingStudents,
                                            option,
                                        ]);
                                    }
                                    form.setValue("_student_number", "");
                                }}
                            />
                        </div>
                        {siblingStudents?.map((student, index) => (
                            <div
                                key={student.id ?? index}
                                className="c-text-size ml-0.5 mt-1.5 flex items-center gap-x-2"
                            >
                                <OutlinedX
                                    onClick={() => {
                                        setSiblingStudents([
                                            ...siblingStudents.filter(
                                                (sibling) =>
                                                    sibling.value !==
                                                    student.value
                                            ),
                                        ]);
                                    }}
                                />
                                {student.label}
                            </div>
                        ))}
                    </div>

                    <FormSelect
                        control={form.control}
                        name="dietary_restriction"
                        label={t("dietary restriction") + "*"}
                        isStringOptions={true}
                        options={["NONE", "VEGETARIAN"]}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="health_concern_id"
                        label={t("health concern" + "*")}
                        options={options.healthConcerns}
                        isSortByName={false}
                        isDisabled={viewOnly}
                    />

                    <div className="lg:col-span-2">
                        <FormTextarea
                            control={form.control}
                            name="remarks"
                            disabled={viewOnly}
                        />
                    </div>

                    <FormDivider />

                    <div className="lg:col-span-2">
                        <h3 className="mb-2.5 text-themeGreenDark">
                            {t("Guardians")}
                        </h3>
                        <EnrollmentGuardiansForm
                            form={form}
                            options={options}
                            viewOnly={viewOnly}
                        />
                    </div>

                    <hr className="col-span-2 mb-1.5 border-dashed border-gray-200" />

                    {student?.enrollment_exams?.length > 0 && (
                        <div className="lg:col-span-2">
                            <h3 className="mb-3 capitalize text-themeGreenDark">
                                {t("exams")}
                            </h3>

                            {form
                                .watch("enrollment_exams")
                                ?.map((exam, index) => (
                                    <div
                                        className="grid-form mt-3 pb-3"
                                        key={index}
                                    >
                                        <FormInput
                                            control={form.control}
                                            name={`enrollment_exams.${index}.exam_slip_number`}
                                            label="exam slip number"
                                            disabled={true}
                                        />
                                        <FormInputDecimal
                                            control={form.control}
                                            name={`enrollment_exams.${index}.total_average`}
                                            label={"total average marks"}
                                            disabled={viewOnly}
                                        />
                                        <div className="grid-form col-span-2 border-l border-dashed border-gray-300 pb-1 pl-4">
                                            <div className="col-span-2 -mt-1 ml-0.5 flex items-end gap-x-3 pb-0.5 pt-1 text-[15px] font-semibold leading-none text-gray-500">
                                                <span className="whitespace-nowrap">
                                                    {t("Subjects Marks")}
                                                </span>
                                            </div>
                                            {exam?.marks?.map(
                                                (item, itemIndex) => (
                                                    <div key={itemIndex}>
                                                        <Label className="label">
                                                            {
                                                                item?.subject
                                                                    ?.name
                                                            }
                                                        </Label>
                                                        <FormInputDecimal
                                                            control={
                                                                form.control
                                                            }
                                                            name={`enrollment_exams.${index}.marks.${itemIndex}.mark`}
                                                            label="Marks"
                                                            disabled={viewOnly}
                                                            hasLabel={false}
                                                        />
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                ))}
                        </div>
                    )}

                    <div className="mt-2 flex flex-wrap justify-end gap-2 lg:col-span-2">
                        {hasPermit("enrollment-print-details") && (
                            <Button
                                variant={"outline"}
                                className="block w-40"
                                onClick={downloadPDF}
                            >
                                {t("Download PDF")}
                            </Button>
                        )}
                        {!viewOnly && (
                            <Button type="submit" className="block w-32">
                                {t("Save")}
                            </Button>
                        )}
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default NewlyRegisteredStudentForm;
