import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import InfoCard from "@/components/ui/InfoCard";
import { enrollmentAPI } from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { showBackendFormError, toYMD } from "@/lib/utils";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const ExtendRegistrationExpiryForm = ({ id, data, refresh, close }) => {
    const form = useForm<any>({ defaultValues: { expiry_date: null } });

    const { axiosPost: updateExpiry, error: postError } = useAxios({
        api: `${enrollmentAPI}/${id}/extend-expiry`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                if (data.expiry_date) {
                    data.expiry_date = toYMD(data.expiry_date);
                }
                updateExpiry(data);
            })
        );
    }

    return (
        <div>
            <h2 className="mb-5 lg:mt-2">Extend Expiry Date</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-6">
                    {data && (
                        <InfoCard
                            noBorder
                            data={{
                                name: data.name,
                                guardian_name:
                                    data?.enrollment_user?.name ?? "-",
                                guardian_email:
                                    data?.enrollment_user?.email ?? "-",
                                current_expiry_date: data.expiry_date,
                            }}
                        />
                    )}
                    <DatePicker control={form.control} name={"expiry_date"} />
                    <Button className="ml-auto mt-2" type="submit">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ExtendRegistrationExpiryForm;
