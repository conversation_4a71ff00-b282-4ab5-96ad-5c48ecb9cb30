import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import InfoCard from "@/components/ui/InfoCard";
import { APPROVED, REJECTED, enrollmentAPI } from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const ChangeEnrollmentStatusForm = ({ id, data, refresh, close }) => {
    const form = useForm<any>({
        defaultValues: { status: null },
    });

    const { axiosPut: updateStatus, error: putError } = useAxios({
        api: `${enrollmentAPI}/${id}/update-status`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                updateStatus({ data });
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <div>
            <h2 className="mb-5 lg:mt-2">Change Enrollment Status</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-6">
                    {data && (
                        <InfoCard
                            noBorder
                            data={{
                                name: data.name,
                                guardian_name:
                                    data?.enrollment_user?.name ?? "-",
                                guardian_email:
                                    data?.enrollment_user?.email ?? "-",
                                current_enrollment_status:
                                    data.enrollment_status,
                            }}
                        />
                    )}
                    <FormSelect
                        control={form.control}
                        name="status"
                        label="Enrollment Status*"
                        isStringOptions={true}
                        options={[APPROVED, REJECTED]}
                    />
                    <Button className="ml-auto mt-2" type="submit">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ChangeEnrollmentStatusForm;
