import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFilterProps,
    courseAPI,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useAxios } from "@/lib/hook";
import { useLocale, useTranslations } from "next-intl";
import FormSelect from "@/components/ui/FormSelect";
import { formatStringToDate, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";

const FilterEnrollmentSessionForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");
    const t_es = useTranslations("enrollment_session");

    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            code: filter?.code ?? "",
            course_id: filter?.course_id ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });

    const [dateRange, setDateRange] = useState<any>(
        filter?.from_date || filter?.to_date
            ? {
                  startDate: formatStringToDate(filter?.from_date),
                  endDate: formatStringToDate(
                      filter?.to_date ?? filter?.from_date
                  ),
                  key: "selection",
              }
            : null
    );

    const { data: courseOptions, axiosQuery: getCourses } = useAxios({
        api: courseAPI,
        locale,
    });

    function onSubmit(data: Record<string, any>) {
        if (dateRange) {
            data.from_date = toYMD(dateRange.startDate);
            data.to_date = toYMD(dateRange.endDate);
            if (data.from_date === data.to_date) {
                delete data.to_date;
            }
        } else {
            data.from_date = undefined;
            data.to_date = undefined;
        }
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    useEffect(() => {
        getCourses({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">{t("Filter")}</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormInput control={form.control} name={"code"} />

                <FormSelect
                    control={form.control}
                    name="course_id"
                    label={t("course")}
                    options={courseOptions}
                />

                <div className="">
                    <DateRangePicker
                        label={t_es("Eligible Period")}
                        range={dateRange}
                        setRange={setDateRange}
                    />
                </div>

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label={t("Status")}
                    options={[
                        {
                            name: t("Active"),
                            id: "1",
                        },
                        {
                            name: t("Inactive"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterEnrollmentSessionForm;
