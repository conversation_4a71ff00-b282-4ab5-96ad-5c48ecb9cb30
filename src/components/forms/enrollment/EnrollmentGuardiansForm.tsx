import { useEffect, useState } from "react";
import clsx from "clsx";
import { capitalize, isArray } from "lodash";
import { ChevronDownCircle, X } from "lucide-react";
import { useFieldArray } from "react-hook-form";
import toast from "react-hot-toast";
import Select from "react-select";
import { v4 as uuidv4 } from "uuid";
import {
    FATHER,
    MOTHER,
    GUARDIAN,
    selectStyles,
    maritalStatusOptions,
} from "@/lib/constant";
import { useLanguages } from "@/lib/store";
import {
    combinedNames,
    formatStringsForSelect,
    getInputErrorMessage,
    isChineseCode,
    isMY,
} from "@/lib/utils";
import { Button } from "@/components/base-ui/button";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import { useTranslations } from "next-intl";

const EnrollmentGuardiansForm = ({
    form,
    options,
    children,
    viewOnly = false,
}: {
    form: any;
    options: Record<string, any>;
    children?: React.ReactNode;
    viewOnly?: boolean;
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const t = useTranslations("common");

    const guardianTypeOptions = formatStringsForSelect(
        [FATHER, MOTHER, GUARDIAN],
        t
    );
    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "guardians",
    });

    const [isOpenList, setIsOpenList] = useState<number[]>([]);
    const [selectedGuardianType, setSelectedGuardianType] = useState<any>();

    function canAdd() {
        if (!selectedGuardianType) return false;
        if ([FATHER, MOTHER].includes(selectedGuardianType)) {
            const hasAdded = fields.some(
                (person: any) => person.guardian_type === selectedGuardianType
            );
            if (hasAdded) {
                toast(
                    `Only one ${selectedGuardianType.toLowerCase()} is allowed to be added`
                );
                return false;
            }
        }
        return true;
    }
    function onAddNew() {
        if (!canAdd()) return;
        form.clearErrors("guardians");
        append({
            id: uuidv4(),
            guardian_type: selectedGuardianType,
            is_primary: false,
            _isNew: true,
            name: activeLanguages
                ?.map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: "",
                    }),
                    {}
                ),
            nric: "",
            passport_number: "",
            email: "",
            phone_number: "",
            nationality_id:
                options?.countries.find((country) =>
                    isMY(country.name?.toLowerCase())
                )?.id ?? "",
            race_id: "",
            religion_id: "",
            education_id: "",
            married_status: "",
            occupation: "",
            occupation_description: "",
            live_status: "",
        });
        setIsOpenList([fields.length]);
    }

    useEffect(() => {
        if (form.formState.errors?.guardians) {
            console.log(
                "form.formState.errors?.guardians",
                form.formState.errors?.guardians
            );
            if (isArray(form.formState.errors?.guardians)) {
                setIsOpenList(
                    form.formState.errors?.guardians
                        ?.map((error, index) => index)
                        .filter((i) => i !== undefined)
                );
            }
        }
    }, [form.formState.errors?.guardians]);

    useEffect(() => {
        console.log("form.watch('guardians')", form.watch("guardians"));
    }, [form.watch("guardians")]);

    return (
        <div className="lg:col-span-2">
            {!viewOnly && (
                <>
                    {" "}
                    <div className="c-text-size mb-1 font-medium normal-case text-themeLabel">
                        {t("Select a guardian type and add guardian")}
                    </div>
                    <div className="mb-4 mt-3 flex w-full flex-wrap items-center gap-2">
                        <div className="lg:w-[300px]">
                            <Select
                                placeholder={t("Select Guardian Type")}
                                options={guardianTypeOptions}
                                value={
                                    guardianTypeOptions?.find(
                                        (option) =>
                                            option.value ===
                                            selectedGuardianType
                                    ) ?? null
                                }
                                onChange={(selectedOption) => {
                                    setSelectedGuardianType(
                                        selectedOption?.value
                                    );
                                }}
                                styles={selectStyles}
                            />
                        </div>
                        <Button
                            size={"smaller"}
                            variant={"outline"}
                            className="flex items-center gap-x-1"
                            onClick={onAddNew}
                        >
                            {t("Add ")}
                            {t("Guardian")}
                        </Button>
                    </div>
                    {form.formState.errors?.guardians?.message && (
                        <div className="warning-text -mt-2">
                            {`${getInputErrorMessage(form.formState.errors?.guardians?.message)}`}
                        </div>
                    )}
                </>
            )}
            {children}
            {form.watch("guardians").map((guardian: any, index) => {
                return (
                    <Form
                        key={guardian.id.toString() + index}
                        form={form}
                        currentGuardian={guardian}
                        index={index}
                        options={options}
                        isOpenList={isOpenList}
                        setIsOpenList={setIsOpenList}
                        remove={remove}
                        viewOnly={viewOnly}
                    />
                );
            })}
        </div>
    );
};

const Form = ({
    index,
    currentGuardian,
    form,
    options,
    isOpenList,
    setIsOpenList,
    remove,
    viewOnly = false,
}: {
    index: number;
    currentGuardian: any;
    form: any;
    options: Record<string, any>;
    isOpenList: number[];
    setIsOpenList: any;
    remove: any;
    viewOnly?: boolean;
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const t = useTranslations("common");

    const [names, setNames] = useState<Record<string, any>>(
        currentGuardian?.name
    );

    function onClickChevron() {
        if (isOpenList.includes(index)) {
            setIsOpenList(isOpenList.filter((i) => i !== index));
        } else {
            setIsOpenList([...isOpenList, index]);
        }
    }

    return (
        <div className="mb-3 rounded-md border px-4 py-3">
            <div className="col-span-2 flex items-center justify-between">
                {!viewOnly && (
                    <X
                        size={20}
                        className="mt-0.5 cursor-pointer rounded-full border border-themeLabel bg-white p-0.5 text-themeLabel"
                        onClick={() => {
                            return remove(index);
                        }}
                    />
                )}
                <div className="c-text-size flex w-[calc(100%-44px)] flex-wrap items-center gap-x-1.5 px-3 text-gray-500">
                    <span className="font-medium">
                        {capitalize(currentGuardian.guardian_type)}
                    </span>
                    <span className="h-[3px] w-[3px] rounded-full bg-gray-400"></span>
                    <span>{combinedNames(names)}</span>
                    {form.watch(`guardians[${index}].is_primary`) && (
                        <span className="ml-1 rounded-sm border border-dotted border-themeLabel p-1 text-[10px] font-medium uppercase leading-none">
                            {t("Primary")}
                        </span>
                    )}
                </div>
                <ChevronDownCircle
                    className={clsx(
                        "cursor-pointer text-themeGreen transition",
                        isOpenList.includes(index) && "rotate-180"
                    )}
                    onClick={onClickChevron}
                />
            </div>

            <div
                className={clsx(
                    "grid w-full gap-3 lg:grid-cols-2",
                    !isOpenList.includes(index) && "h-0 overflow-hidden"
                )}
            >
                <FormDivider />

                {isArray(activeLanguages) &&
                    activeLanguages.map((lang) => (
                        <FormInput
                            key={"guardian" + lang?.code}
                            control={form.control}
                            name={`guardians[${index}].name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                            isUpperCase={!isChineseCode(lang?.code)}
                            onChange={(value) => {
                                setNames({
                                    ...names,
                                    [lang?.code]: value,
                                });
                            }}
                        />
                    ))}
                {form.formState.errors?.guardians?.[index]?.name && (
                    <div className="warning-text -mt-1.5 lg:col-span-2">
                        {t("Name is required")}
                    </div>
                )}
                <FormDivider />

                <FormInput
                    control={form.control}
                    name={`guardians[${index}].nric`}
                    label={t("NRIC") + "*"}
                    max={12}
                />
                <FormInput
                    control={form.control}
                    name={`guardians[${index}].passport_number`}
                    label={t("passport number")}
                />

                <FormInput
                    control={form.control}
                    type="email"
                    name={`guardians[${index}].email`}
                    label={t("email")}
                />
                <FormPhoneInput
                    form={form}
                    name={`guardians[${index}].phone_number`}
                    label={t("phone number") + "*"}
                />

                <FormSelect
                    control={form.control}
                    name={`guardians[${index}].live_status`}
                    label="live status"
                    isStringOptions={true}
                    options={["NORMAL", "SICK", "PASSED_AWAY", "UNKNOWN"]}
                />

                <FormSelect
                    control={form.control}
                    name={`guardians[${index}].nationality_id`}
                    label="nationality"
                    options={options?.countries}
                />
                <FormSelect
                    control={form.control}
                    name={`guardians[${index}].race_id`}
                    label="race"
                    options={options?.races}
                />
                <FormSelect
                    control={form.control}
                    name={`guardians[${index}].religion_id`}
                    label="religion"
                    options={options?.religions}
                />
                <FormSelect
                    control={form.control}
                    name={`guardians[${index}].married_status`}
                    label="marital status"
                    isStringOptions={true}
                    options={maritalStatusOptions}
                />
                <FormSelect
                    control={form.control}
                    name={`guardians[${index}].education_id`}
                    label="education"
                    options={options?.educations}
                />
                <FormInput
                    control={form.control}
                    name={`guardians[${index}].occupation`}
                    label={"occupation"}
                />
                <FormTextarea
                    control={form.control}
                    name={`guardians[${index}].occupation_description`}
                    label={"occupation_description"}
                />
                <FormCheckbox
                    control={form.control}
                    name={`guardians[${index}].is_primary`}
                    label={`is_primary`}
                />
            </div>
        </div>
    );
};

export default EnrollmentGuardiansForm;
