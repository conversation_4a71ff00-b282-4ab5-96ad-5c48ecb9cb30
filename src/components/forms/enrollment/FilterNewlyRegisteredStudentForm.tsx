import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFilterProps,
    genderOptions,
    GET_ALL_PARAMS,
    gradeAPI,
    PAID,
    UNPAID,
    PENDING,
    APPROVED,
    REJECTED,
    SHORTLISTED,
    enrollmentAPI,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useAxios } from "@/lib/hook";
import { useLocale, useTranslations } from "next-intl";
import FormSelect from "@/components/ui/FormSelect";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { DatePicker } from "@/components/ui/DatePicker";
import { toYMD } from "@/lib/utils";
import { capitalize } from "lodash";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";

const FilterNewlyRegisteredStudentForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const [asyncValue, setAsyncValue] = useState<Record<string, any> | null>(
        null
    );

    const { loadAsyncOptions } = useAsyncSelect({
        api: enrollmentAPI,
        useCommonSearch: true,
    });

    const form = useForm({
        defaultValues: {
            id: filter?.id ?? "",
            nric: filter?.nric ?? "",
            passport_number: filter?.passport_number ?? "",
            admission_grade_id: filter?.admission_grade_id ?? "",
            gender: filter?.gender ?? "",
            payment_status: filter?.payment_status ?? "",
            enrollment_status: filter?.enrollment_status ?? "",
            registration_date: filter?.registration_date ?? "",
            expiry_date: filter?.expiry_date ?? "",
            is_hostel: filter?.is_hostel ?? "",
            is_foreigner: filter?.is_foreigner ?? "",
        },
    });

    const { data: gradeOptions, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onError: close,
    });

    function onSubmit(data: Record<string, any>) {
        if (data.expiry_date) {
            data.expiry_date = toYMD(data.expiry_date);
        }
        if (data.registration_date) {
            data.registration_date = toYMD(data.registration_date);
        }
        setFilter({
            ...filter,
            ...data,
            page: 1,
        });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            page: 1,
            per_page: filter?.per_page,
            order_by: {
                created_at: "desc",
            },
        });
        close();
        setAsyncValue(null);
    }

    useEffect(() => {
        getGrades({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <div className="lg:col-span-2">
                    <FreeSelectAsync
                        control={form.control}
                        name="id"
                        label="quick_search"
                        placeholder="Type to search student"
                        loadOptions={loadAsyncOptions}
                        value={asyncValue}
                        onChange={(option) => {
                            form.setValue("id", option?.value ?? "");
                            setAsyncValue(option);
                            setFilter({
                                ...filter,
                                page: 1,
                                name: option?.name ?? undefined,
                            });
                        }}
                    />
                </div>

                <FormSelect
                    control={form.control}
                    name="gender"
                    isSortByName={false}
                    options={genderOptions.map((option) => ({
                        id: option.id,
                        name: capitalize(t(option.name)),
                    }))}
                />
                <FormInputInterger
                    control={form.control}
                    name="nric"
                    label={"NRIC"}
                    max={12}
                />

                <FormInput control={form.control} name="passport_number" />

                <FormSelect
                    control={form.control}
                    name="admission_grade_id"
                    label="admission grade"
                    options={gradeOptions}
                />

                <FormSelect
                    control={form.control}
                    name="enrollment_status"
                    isSortByName={false}
                    options={[APPROVED, SHORTLISTED, REJECTED].map((type) => ({
                        id: type,
                        name: capitalize(t(type)),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="payment_status"
                    isSortByName={false}
                    options={[PAID, UNPAID, PENDING].map((type) => ({
                        id: type,
                        name: capitalize(t(type)),
                    }))}
                />

                <DatePicker control={form.control} name="registration_date" />

                <DatePicker control={form.control} name="expiry_date" />

                <FormSelect
                    control={form.control}
                    name="is_hostel"
                    isSortByName={false}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <FormSelect
                    control={form.control}
                    name="is_foreigner"
                    isSortByName={false}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterNewlyRegisteredStudentForm;
