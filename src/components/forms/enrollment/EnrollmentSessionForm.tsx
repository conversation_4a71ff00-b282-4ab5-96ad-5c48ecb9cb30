import { useLocale, useTranslations } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    accountingProductAPI,
    appCurrencySymbol,
    CommonFormProps,
    courseAPI,
    DATE_FORMAT,
    enrollmentConditionAPI,
    enrollmentSessionsAPI,
    gradeAPI,
    subjectAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    configForGetAll,
    convertDateToYearMonth,
    convertYearMonthToDate,
    formatStringToDate,
    isObjectType,
    isValueTrue,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import FormSelect from "@/components/ui/FormSelect";
import { CornerDownRight, Plus, X } from "lucide-react";
import FreeMonthPicker from "@/components/ui/FreeMonthPicker";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import { axiosInstance } from "@/lib/api";
import clsx from "clsx";
import Tabs from "@/components/ui/Tabs";
import { fromPairs, sortBy, toPairs } from "lodash";

const EnrollmentSessionForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [currentData, setCurrentData] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.courses = res[0].data.data;
            _options.subjects = res[1].data.data;
            _options.products = res[2].data.data;
            _options.grades = res[3].data.data;
            _options.conditions = res[4]?.data?.data ?? [];
            setOptions(_options);
            if (res[5]) {
                setCurrentData(res[5].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(courseAPI, _config),
            axiosInstance.get(subjectAPI, _config),
            axiosInstance.get(accountingProductAPI, _config),
            axiosInstance.get(gradeAPI, _config),
            hasPermit("enrollment-session-update")
                ? axiosInstance.get(enrollmentConditionAPI, _config)
                : null,
            props.id
                ? axiosInstance.get(`${enrollmentSessionsAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return activeLanguages && options && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            options={options}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
    options: any;
};

const FormWrap = ({
    isCreate = false,
    currentData,
    options,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            name: currentData?.name ?? "",
            code: currentData?.code ?? "",
            course_id: currentData?.course?.id,
            admission_year: currentData?.admission_year ?? "",
            admission_grade_id: currentData?.admission_grade?.id ?? "",
            subject_ids:
                currentData?.exam_subjects?.map((subject) => subject?.id) ?? [],
            is_active: isValueTrue(currentData?.is_active),
            fee_assignment_settings:
                currentData?.fee_assignment_settings?.map((setting) => {
                    return {
                        conditions:
                            setting?.conditions?.map((condition) => ({
                                field: condition.field,
                                operator: condition.operator,
                                value:
                                    condition.value === true
                                        ? "1"
                                        : condition.value === false
                                          ? "0"
                                          : condition.value,
                            })) ?? [],
                        outcome: {
                            ...setting.outcome,
                            period:
                                setting?.outcome?.period &&
                                convertDateToYearMonth(setting.outcome.period),
                        },
                    };
                }) ?? [],
        },
    });
    const t = useTranslations("common");
    const t_es = useTranslations("enrollment_session");

    const hasPermit = useUserProfile((state) => state.hasPermit);
    const viewOnly = !hasPermit("enrollment-session-update");

    const _summaryTab = t_es("Summary");
    const _feeAssignmentTab = t_es("Fee Assignment");

    const [tab, setTab] = useState(isCreate ? _feeAssignmentTab : _summaryTab);

    const [dateRange, setDateRange] = useState<any>(
        currentData?.from_date && currentData?.to_date
            ? {
                  startDate: formatStringToDate(currentData?.from_date),
                  endDate: formatStringToDate(currentData?.to_date),
                  key: "selection",
              }
            : null
    );

    const { axiosPost: createSession, error: postError } = useAxios({
        api: enrollmentSessionsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSession, error: putError } = useAxios({
        api: enrollmentSessionsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                if (dateRange) {
                    data.from_date = toYMD(dateRange.startDate);
                    data.to_date = toYMD(dateRange.endDate);
                }
                if (data.fee_assignment_settings) {
                    data.fee_assignment_settings =
                        data.fee_assignment_settings.map((item) => ({
                            conditions: item.conditions?.map((condition) => ({
                                field: condition.field,
                                operator: condition.operator,
                                value:
                                    condition.value === "1"
                                        ? true
                                        : condition.value === "0"
                                          ? false
                                          : condition.value,
                            })),
                            outcome: {
                                ...item.outcome,
                                period: item.outcome.period
                                    ? convertYearMonthToDate(
                                          item.outcome.period,
                                          DATE_FORMAT.YMD
                                      )
                                    : null,
                            },
                        }));
                }
                if (isCreate) {
                    createSession(data);
                } else {
                    updateSession({ id: currentData.id, data });
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t_es("Enrollment Session")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid-form">
                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                        disabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="course_id"
                        label={t("course") + "*"}
                        options={options.courses}
                        isDisabled={viewOnly}
                    />

                    <div className="">
                        <DateRangePicker
                            label={t_es("Eligible Period") + "*"}
                            range={dateRange}
                            setRange={setDateRange}
                            error={
                                form.formState.errors?.from_date?.message ||
                                form.formState.errors?.to_date?.message
                            }
                            isDisabled={viewOnly}
                        />
                    </div>

                    <FormSelect
                        control={form.control}
                        name="admission_year"
                        label={t("admission year") + "*"}
                        options={[2025, 2026, 2027, 2028, 2029, 2030]}
                        // TODO: update when api ready
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="admission_grade_id"
                        label={t("admission grade") + "*"}
                        options={options.grades}
                        isDisabled={viewOnly}
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name={"is_active"}
                        label={t("status") + "*"}
                        disabled={viewOnly}
                    />

                    <div className="grid gap-y-4 lg:col-span-2">
                        <FormSelect
                            control={form.control}
                            isMulti={true}
                            name="subject_ids"
                            label={t("subjects") + "*"}
                            options={options.subjects}
                            isDisabled={viewOnly}
                        />

                        <div className="mt-3">
                            <Tabs
                                list={
                                    isCreate
                                        ? [_feeAssignmentTab]
                                        : [
                                              _summaryTab,
                                              ...(viewOnly
                                                  ? []
                                                  : [_feeAssignmentTab]),
                                          ]
                                }
                                selected={tab}
                                setSelected={setTab}
                            />

                            {tab === _summaryTab && (
                                <SummaryTab summary={currentData.summary} />
                            )}

                            {tab === _feeAssignmentTab && (
                                <FeeAssignmentTab
                                    form={form}
                                    options={options}
                                />
                            )}
                        </div>
                        {!viewOnly && (
                            <Button type="submit" className="mb-2 ml-auto mt-3">
                                {t("Submit")}
                            </Button>
                        )}
                    </div>
                </form>
            </Form>
        </div>
    );
};

const SummaryTab = ({ summary }) => {
    const t = useTranslations("enrollment_summary");

    const pairs = toPairs(summary).filter(
        ([key, value]) => !isObjectType(value)
    );
    const sortedPairs = sortBy(pairs, ([key, value]) => {
        return Array.isArray(value) ? 1 : 0;
    });
    const sortedSummary = fromPairs(sortedPairs);

    return (
        <div className="c-text-size grid gap-5 pb-3 sm:grid-cols-2 md:grid-cols-3">
            {Object.entries(sortedSummary).map(([key, value]: any) => {
                return (
                    <div key={key} className="">
                        {Array.isArray(value) ? (
                            <>
                                <div className="pb-1 font-semibold capitalize text-gray-700">
                                    {t(key)}
                                </div>
                                {value.map((listItem, listItemIndex) => (
                                    <div
                                        key={listItemIndex}
                                        className="mt-1 grid max-w-[240px] grid-cols-2 border-b pb-1"
                                    >
                                        <div className="text-gray-700">
                                            {listItem.label}
                                        </div>
                                        <div className="pr-2 text-right">
                                            {listItem.value}
                                        </div>
                                    </div>
                                ))}
                            </>
                        ) : (
                            !isObjectType(value) && (
                                <div
                                    key={key}
                                    className="flex items-start gap-2 lg:pb-2"
                                >
                                    <span className="font-semibold capitalize text-gray-700">
                                        {t(key)}:
                                    </span>
                                    <span className="">
                                        {key === "total_fee_collection" &&
                                            `${appCurrencySymbol} `}
                                        {value ?? "-"}
                                    </span>
                                </div>
                            )
                        )}
                    </div>
                );
            })}
        </div>
    );
};

const FeeAssignmentTab = ({ form, options }) => {
    const t = useTranslations("common");
    const t_es = useTranslations("enrollment_session");

    const hasPermit = useUserProfile((state) => state.hasPermit);
    const viewOnly = !hasPermit("enrollment-session-update");

    const { append, remove } = useFieldArray({
        control: form.control,
        name: "fee_assignment_settings",
    });

    function onAddFeeGroup() {
        append(
            {
                conditions: [
                    {
                        field: null,
                        operator: null,
                        value: null,
                    },
                ],
                outcome: {
                    product_id: null,
                    period: null,
                    amount: "",
                },
            },
            { shouldFocus: false }
        );
    }

    function onAddFeeGroupWithoutCondition() {
        append({
            conditions: [],
            outcome: {
                product_id: null,
                period: null,
                amount: "",
            },
        });
    }

    return (
        <div className="">
            <div className="mt-3 grid min-w-[800px] gap-3">
                {form.watch("fee_assignment_settings")?.map((item, index) => (
                    <div
                        key={index}
                        className={clsx(
                            "relative grid items-center gap-1 rounded-sm border bg-themeGreen3 p-2 pb-3",
                            viewOnly && "pointer-events-none"
                        )}
                    >
                        {!viewOnly && (
                            <div className="absolute right-3 top-2 z-10">
                                <X
                                    size={20}
                                    className="cursor-pointer text-themeGreen2"
                                    onClick={() => remove(index)}
                                />
                            </div>
                        )}
                        <div className="mb-1 flex flex-wrap items-center gap-2">
                            <p className="ml-1 font-semibold text-themeGreen2">
                                {t_es("Conditions")}
                            </p>
                            {!viewOnly && (
                                <div
                                    className="cursor-pointer rounded-full border border-themeGreen2 bg-white p-1 text-themeGreen2 transition hover:border-themeGreenDark hover:text-themeGreenDark"
                                    onClick={() => {
                                        form.setValue(
                                            `fee_assignment_settings.${index}.conditions`,
                                            [
                                                ...item.conditions,
                                                {
                                                    field: null,
                                                    operator: null,
                                                    value: null,
                                                },
                                            ]
                                        );
                                    }}
                                >
                                    <Plus size={14} />
                                </div>
                            )}
                        </div>

                        {/* conditions */}
                        <div className="grid w-full grid-cols-7 items-start gap-2">
                            {item?.conditions?.length === 0 && (
                                <div className="c-text-size col-span-7 rounded-sm border bg-white p-3 text-center text-sm leading-none text-gray-400">
                                    {t_es("NO CONDITION")}
                                </div>
                            )}
                            {item?.conditions?.map((_, condIndex) => (
                                <Fragment key={condIndex}>
                                    <div className="col-span-3">
                                        <FormSelect
                                            control={form.control}
                                            name={`fee_assignment_settings.${index}.conditions.${condIndex}.field`}
                                            options={options.conditions?.map(
                                                (option) => ({
                                                    id: option?.value,
                                                    name: t_es(option?.label),
                                                })
                                            )}
                                            hasLabel={false}
                                        />
                                    </div>
                                    <div className="col-span-2">
                                        <FormSelect
                                            control={form.control}
                                            name={`fee_assignment_settings.${index}.conditions.${condIndex}.operator`}
                                            placeholder={t_es("Operator")}
                                            options={[
                                                {
                                                    id: "=",
                                                    name: t_es("Equals"),
                                                },
                                                {
                                                    id: "!=",
                                                    name: t_es("Not Equals"),
                                                },
                                            ]}
                                            hasLabel={false}
                                        />
                                    </div>
                                    <div className="col-span-2">
                                        <FormSelect
                                            control={form.control}
                                            name={`fee_assignment_settings.${index}.conditions.${condIndex}.value`}
                                            options={
                                                options.conditions
                                                    ?.find(
                                                        (option) =>
                                                            option?.value ===
                                                            form.watch(
                                                                `fee_assignment_settings.${index}.conditions.${condIndex}.field`
                                                            )
                                                    )
                                                    ?.options?.map(
                                                        (option) => ({
                                                            id:
                                                                option?.value ===
                                                                true
                                                                    ? "1"
                                                                    : option?.value ===
                                                                        false
                                                                      ? "0"
                                                                      : option?.value,
                                                            name: t(
                                                                option?.label
                                                            ),
                                                        })
                                                    ) ?? []
                                            }
                                            hasLabel={false}
                                        />
                                    </div>
                                </Fragment>
                            ))}
                        </div>

                        {/* outcome */}
                        <p className="mb-1 ml-0.5 mt-1 flex items-center gap-1.5 font-semibold text-themeGreen2">
                            <CornerDownRight size={16} />
                            <span>{t_es("Outcome")}</span>
                        </p>
                        <div className="grid w-full grid-cols-7 items-start gap-x-2">
                            <div className="col-span-3">
                                <FormSelect
                                    control={form.control}
                                    name={`fee_assignment_settings.${index}.outcome.product_id`}
                                    hasLabel={false}
                                    placeholder={t("product")}
                                    options={options?.products}
                                    onChange={(value) => {
                                        const amount = options?.products?.find(
                                            (product) => product.id == value
                                        )?.unit_price;
                                        form.setValue(
                                            `fee_assignment_settings.${index}.outcome.amount`,
                                            amount ?? ""
                                        );
                                    }}
                                />
                            </div>
                            <div className="col-span-2">
                                <FreeMonthPicker
                                    control={form.control}
                                    name={`fee_assignment_settings.${index}.outcome.period`}
                                    hasLabel={false}
                                    placeholder={t("period")}
                                    error={
                                        form.formState.errors
                                            ?.fee_assignment_settings?.[index]
                                            ?.outcome?.period?.message
                                    }
                                />
                            </div>
                            <div className="col-span-2">
                                <FormInputDecimal
                                    control={form.control}
                                    name={`fee_assignment_settings.${index}.outcome.amount`}
                                    hasLabel={false}
                                    placeholder={`${t("amount")} (${appCurrencySymbol})`}
                                />
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            {!viewOnly && (
                <div
                    className={clsx(
                        "flex flex-wrap justify-end gap-3",
                        form.watch("fee_assignment_settings")?.length > 0 &&
                            "mt-5"
                    )}
                >
                    <Button variant={"outline"} onClick={onAddFeeGroup}>
                        {t_es("Add New Condition Group")}
                    </Button>
                    <Button
                        variant={"outline"}
                        onClick={onAddFeeGroupWithoutCondition}
                    >
                        {t_es("Add Group Without Condition")}
                    </Button>
                </div>
            )}
        </div>
    );
};

export default EnrollmentSessionForm;
