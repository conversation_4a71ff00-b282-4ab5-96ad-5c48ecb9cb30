import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, enrollmentUsersAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isProperPhoneNumber, showBackendFormError } from "@/lib/utils";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import { capitalize } from "lodash";

const EnrollmentUserForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentUser, axiosQuery: getUser } = useAxios({
        api: enrollmentUsersAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getUser({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentUser) ? (
        <FormWrap
            currentUser={currentUser}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentUser: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentUser,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentUser?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            email: currentUser?.email ?? "",
            phone_number: currentUser?.phone_number ?? "",
            nric: currentUser?.nric ?? "",
        },
    });

    const { axiosPut: updateUser, error: putError } = useAxios({
        api: enrollmentUsersAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (!isProperPhoneNumber(form, "phone_number", data.phone_number)) {
                return;
            }
            updateUser({ id: currentUser.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {capitalize(t("user"))}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <FormInput
                        control={form.control}
                        name="email"
                        label={t("email") + "*"}
                    />

                    <FormPhoneInput
                        form={form}
                        name="phone_number"
                        label={t("phone number") + "*"}
                    />

                    <FormInputInterger
                        control={form.control}
                        name="nric"
                        label={t("NRIC") + "*"}
                        max={12}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default EnrollmentUserForm;
