import { enrollmentAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLocale } from "next-intl";
import { useEffect } from "react";

const NewlyRegisteredStudentView = ({ id, close }) => {
    const locale = useLocale();
    const { data, axiosQuery: getStudentData } = useAxios({
        api: `${enrollmentAPI}/${id}`,
        locale,
        onError: close,
    });

    useEffect(() => {
        getStudentData();
    }, []);

    return <div></div>;
};

export default NewlyRegisteredStudentView;
