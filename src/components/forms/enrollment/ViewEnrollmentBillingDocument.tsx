import clsx from "clsx";
import { capitalize, isArray } from "lodash";
import { Paperclip } from "lucide-react";
import Link from "next/link";
import DataTable from "@/components/ui/DataTable";
import InfoCard from "@/components/ui/InfoCard";
import {
    appCurrencySymbol,
    CONFIRMED,
    DATE_FORMAT,
    DRAFT,
    PAID,
    PARTIAL,
    UNPAID,
    VOIDED,
} from "@/lib/constant";
import {
    displayDateTime,
    formatNumberForRead,
    optionUserLabel,
    strStartCase,
} from "@/lib/utils";
import { useTranslations } from "next-intl";

const ViewEnrollmentBillingDocument = ({ billingDocument, close }) => {
    function formattedData1(data) {
        return {
            status: data?.status ? (
                <span
                    className={clsx(
                        data?.status === CONFIRMED && "text-green-600",
                        data?.status === DRAFT && "text-yellow-500",
                        data?.status === VOIDED && "text-red-500"
                    )}
                >
                    {capitalize(t(data?.status))}
                </span>
            ) : (
                "-"
            ),
            type: capitalize(data?.type),
            sub_type: strStartCase(data?.sub_type.replaceAll("_", " ")),
            reference_number: data?.reference_number ?? "-",
            document_date: data?.document_date ?? "-",
            paid_at: displayDateTime(data?.paid_at, DATE_FORMAT.YMD_HMS) ?? "-",

            payment_status: data?.payment_status ? (
                <span
                    className={clsx(
                        data?.payment_status === PARTIAL && "text-yellow-500",
                        data?.payment_status === PAID && "text-green-600",
                        data?.payment_status === UNPAID && "text-red-500"
                    )}
                >
                    {capitalize(t(data?.payment_status))}
                </span>
            ) : (
                "-"
            ),
            payment_due_date: data?.payment_due_date ?? "-",
            amount_before_tax: data?.amount_before_tax
                ? `${appCurrencySymbol} ${formatNumberForRead(data?.amount_before_tax)}`
                : "-",
            amount_before_tax_after_less_advance:
                data?.amount_before_tax_after_less_advance
                    ? `${appCurrencySymbol} ${formatNumberForRead(
                          data?.amount_before_tax_after_less_advance
                      )}`
                    : "-",
            amount_after_tax: data?.amount_after_tax
                ? `${appCurrencySymbol} ${formatNumberForRead(data?.amount_after_tax)}`
                : "-",
            tax_code: data?.tax_code ?? "-",
            tax_amount: formatNumberForRead(data?.tax_amount),
            tax_description: data?.tax_description ?? "-",
            tax_percentage: formatNumberForRead(data?.tax_percentage),
        };
    }

    function formattedData2(data) {
        return {
            legal_entity_name: data?.legal_entity_name ?? "-",
            legal_entity_address: data?.legal_entity_address ?? "-",

            bill_to_name: data?.bill_to_name,
            bill_to_reference_number: data?.bill_to_reference_number ?? "-",
            bill_to_address: data?.bill_to_address,

            remit_to_account_name: data?.remit_to_account_name ?? "-",
            remit_to_account_number: data?.remit_to_account_number ?? "-",
            remit_to_bank_name: data?.remit_to_account_bank_name ?? "-",
            remit_to_bank_address: data?.remit_to_account_address ?? "-",
            remit_to_swift_code: data?.remit_to_account_swift_code ?? "-",
        };
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  ...item,
                  is_discount: item?.is_discount ? "YES" : "NO",
              }))
            : [];
    }

    function definedPaymentData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  ...item,
                  payment_reference_no_2: item?.payment_reference_no_2 ?? "-",
                  amount_received: formatNumberForRead(item?.amount_received),
                  paid_at: displayDateTime(item?.paid_at, DATE_FORMAT.YMD_HMS),
                  remarks: item?.remarks ?? "-",
                  bank: item?.bank
                      ? `${item?.bank?.translations?.name?.en} (${item?.bank?.swift_code})`
                      : "-",
                  payment_method: item?.payment_method?.name ?? "-",
                  created_by: optionUserLabel(
                      item?.created_by?.employee_number,
                      item?.created_by?.translations?.name
                  ),
              }))
            : [];
    }

    const t = useTranslations("common");

    return billingDocument ? (
        <div className="pb-20 lg:min-w-[900px]">
            <h3>{t("Billing Document Details")}</h3>
            <div className="mt-4 grid gap-x-8 lg:grid-cols-2">
                <InfoCard
                    cardStyleClass="w-full max-w-[500px]"
                    noBorder
                    data={formattedData1(billingDocument)}
                />

                <InfoCard
                    cardStyleClass="w-full max-w-[500px] border-t mt-1.5 pt-1.5 lg:mt-0 lg:pt-0 lg:border-t-0 lg:pt-0"
                    noBorder
                    data={formattedData2(billingDocument)}
                />
            </div>
            <div className="c-text-size mb-1.5 mt-6 border-t border-dashed pt-5 font-semibold text-themeGreenDark">
                {strStartCase(t("line items"))}
            </div>
            <DataTable
                columns={[
                    { key: "description" },
                    { key: "uom_code", displayAs: "UOM Code" },
                    { key: "is_discount" },
                    {
                        key: "unit_price",
                        displayAs: `${t("unit price")} (${appCurrencySymbol})`,
                    },
                    { key: "quantity" },
                    {
                        key: "amount_before_tax",
                        displayAs: `${t("amount")} (${appCurrencySymbol})`,
                    },
                ]}
                data={definedData(billingDocument.line_items)}
                isSmaller={true}
            />

            <div className="c-text-size mb-1.5 mt-4 font-semibold capitalize text-themeGreenDark">
                {t("payments")}
            </div>
            <DataTable
                columns={[
                    { key: "payment_reference_no" },
                    { key: "payment_reference_no_2" },
                    { key: "paid_at" },
                    { key: "remarks" },
                    { key: "bank" },
                    { key: "payment_method" },
                    { key: "created_by" },
                    {
                        key: "amount_received",
                        displayAs: `${t("amount received")} (${appCurrencySymbol})`,
                    },
                ]}
                data={definedPaymentData(billingDocument.payments)}
                isSmaller={true}
            />

            {billingDocument.receipt_url && (
                <div className="mb-1 mt-2 flex items-center gap-x-2 pt-2 text-[14px]">
                    <span className="font-semibold text-themeGreenDark">
                        Receipt
                    </span>
                    <Link
                        href={billingDocument.receipt_url}
                        className="flex items-center gap-x-1.5"
                        target="_blank"
                    >
                        <Paperclip size={16} className="text-gray-500" />
                        <div className="text-sm leading-none text-gray-500 underline">
                            {billingDocument.receipt_url
                                .split("/")
                                .pop()
                                ?.split(".")[0] || "File"}
                        </div>
                    </Link>
                </div>
            )}
        </div>
    ) : (
        <div className="h-10"></div>
    );
};

export default ViewEnrollmentBillingDocument;
