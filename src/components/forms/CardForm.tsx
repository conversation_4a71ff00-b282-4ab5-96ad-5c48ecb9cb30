import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { ACTIVE, CommonFormProps, INACTIVE, cardAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import FormCheckbox from "../ui/FormCheckbox";
import FormTextarea from "../ui/FormTextarea";

const CardForm = (props: CommonFormProps & { userable?: any }) => {
    const locale = useLocale();

    const { data: card, axiosQuery: getCard } = useAxios({
        api: cardAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCard({ id: props.id });
        }
    }, []);

    return props.isCreate || card ? (
        <FormWrap card={card} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    card: any;
    userable?: any;
};

const FormWrap = ({
    isCreate = false,
    card,
    refresh,
    close,
    userable,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            card_number: card?.card_number ?? "",
            card_number2: card?.card_number2 ?? "",
            card_number3: card?.card_number3 ?? "",
            status: isCreate ? true : card?.status === ACTIVE,
            remarks: card?.remarks ?? "",
            update_library_card_number: true,
            userable_id:
                userable?.userable_id ?? card?.userable?.userable_id ?? null,
            userable_type:
                userable?.userable_type ??
                card?.userable?.userable_type ??
                null,
        },
    });

    const { axiosPost: createCard, error: postError } = useAxios({
        api: cardAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateCard, error: putError } = useAxios({
        api: cardAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.status = data.status ? ACTIVE : INACTIVE;
            data.name = data.card_number;
            data.card_type = "PROXIMITY";
            if (isCreate || data.status === ACTIVE) {
                delete data.remarks;
            }
            if (isCreate) {
                createCard(data);
            } else {
                updateCard({ id: card.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5 capitalize">
                {isCreate ? t("Create ") : t("Update ")}
                {t("smart card")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="card_number"
                        label={t("Card number") + "*"}
                        max={10}
                    />
                    <FormInput
                        control={form.control}
                        name="card_number2"
                        label={t("Card number") + " 2"}
                        max={3}
                    />
                    <FormInput
                        control={form.control}
                        name="card_number3"
                        label={t("Card number") + " 3"}
                        max={5}
                    />

                    <FormCheckbox
                        control={form.control}
                        name="update_library_card_number"
                        label={`${t("Update ")}${t("library ")}${t("card number")}`}
                    />

                    {!isCreate && (
                        <StatusFormSwitch
                            control={form.control}
                            name="status"
                        />
                    )}

                    {!isCreate && !form.watch("status") && (
                        <FormTextarea control={form.control} name="remarks" />
                    )}

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CardForm;
