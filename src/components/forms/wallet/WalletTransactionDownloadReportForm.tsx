import { useLocale, useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    DEPOSIT,
    EXCEL,
    firstDayOfCurrentMonth,
    lastDayOfCurrentMonth,
    REFUND,
    TRANSACTION,
    TRANSFER,
    walletTransactionsReportAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { useState } from "react";

const WalletTransactionDownloadReportForm = () => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const t = useTranslations("common");

    const [dateRange, setDateRange] = useState<any>({
        startDate: firstDayOfCurrentMonth,
        endDate: lastDayOfCurrentMonth,
        key: "selection",
    });

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            type: "",
            transaction_from: null,
            transaction_to: null,
            export_type: EXCEL,
        },
    });

    const { axiosQuery: downloadReport } = useAxios({
        api: walletTransactionsReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    function getReport() {
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log(data);

            let hasError = false;
            const requiredFields = ["type", "report_language", "export_type"];
            requiredFields.forEach((key) => {
                if (!data[key]) {
                    hasError = true;
                    form.setError(key, {
                        type: "manual",
                        message: "This field is required",
                    });
                }
            });
            if (hasError) return;

            if (dateRange) {
                data.transaction_from = toYMD(dateRange.startDate);
                data.transaction_to = toYMD(dateRange.endDate);
            }

            downloadReport({ params: data });
        })();
    }

    function onDownloadReport(e) {
        e.preventDefault();
        getReport();
    }

    return (
        <Form {...form}>
            <form className="grid gap-y-3" onSubmit={onDownloadReport}>
                <h3 className="mb-2">{t("Download Report")}</h3>

                <FormSelect
                    control={form.control}
                    name="report_language"
                    label={t("report language") + "*"}
                    options={activeLanguages?.map((language) => ({
                        id: language?.code,
                        name: t(language?.name),
                    }))}
                />

                <FormSelect
                    isMulti={true}
                    control={form.control}
                    name="type"
                    label={t("type") + "*"}
                    options={[DEPOSIT, TRANSFER, TRANSACTION, REFUND].map(
                        (type) => ({
                            id: type,
                            name: t(type),
                        })
                    )}
                />

                <div className="lg:col-span-1">
                    <DateRangePicker
                        label={t("Transaction Date Range") + "*"}
                        range={dateRange}
                        setRange={setDateRange}
                    />
                </div>

                <div className="mt-3 flex items-center justify-end gap-2">
                    <Button type="submit">{t("Download Report")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default WalletTransactionDownloadReportForm;
