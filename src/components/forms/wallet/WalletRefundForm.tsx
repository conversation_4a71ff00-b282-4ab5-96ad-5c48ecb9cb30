import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { appCurrencySymbol } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { formatNumberForRead, showBackendFormError } from "@/lib/utils";
import FormTextarea from "../../ui/FormTextarea";
import InfoCard from "../../ui/InfoCard";

const WalletRefundForm = ({ data, refresh, close }) => {
    const form = useForm({
        defaultValues: {
            remark: "",
        },
    });

    const { axiosPost: refundWallet, error: postError } = useAxios({
        api: `/admin/wallets/transactions/${data?.id}/refund`,
        toastMsg: "Refunded successfully",
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            refundWallet(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const amount = `amount (${appCurrencySymbol})`;

    return (
        <div>
            <h2 className="mb-3">Refund</h2>
            <div className="ml-0.5">
                <InfoCard
                    noBorder
                    data={{
                        reference_no: data?.reference_no,
                        description: data?.description,
                        [amount]: formatNumberForRead(data?.total_amount),
                    }}
                />
            </div>
            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-6 grid gap-y-5">
                    <FormTextarea
                        control={form.control}
                        name="remark"
                        label="Remark*"
                    />
                    <Button type="submit" className="ml-auto">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default WalletRefundForm;
