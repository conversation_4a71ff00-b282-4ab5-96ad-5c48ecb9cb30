import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    EMPL<PERSON><PERSON><PERSON>,
    FAILED,
    GUARDIAN,
    PENDING,
    STUDENT,
    SUCCESS,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterWalletTransactionUserForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            reference_number: filter?.reference_number ?? "",
            card_number: filter?.card_number ?? "",
            user_type: filter?.user_type ?? "",
            status: filter?.status ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">{t("Filter")}</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name="reference_number" />

                <FormInput control={form.control} name="card_number" />

                <FormSelect
                    control={form.control}
                    name="user_type"
                    options={[STUDENT, EMPLOYEE, GUARDIAN].map((type) => ({
                        id: type,
                        name: t(type),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    isSortByName={false}
                    options={[PENDING, SUCCESS, FAILED].map((type) => ({
                        id: type,
                        name: t(type),
                    }))}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterWalletTransactionUserForm;
