import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormTextarea from "@/components/ui/FormTextarea";
import InfoCard from "@/components/ui/InfoCard";
import { appCurrencySymbol, walletAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { formatNumberForRead, showBackendFormError } from "@/lib/utils";
import { useTranslations } from "next-intl";

const AdjustWalletForm = ({ currentData, refresh, close }) => {
    const form = useForm({
        defaultValues: {
            amount: "",
            remark: "",
        },
    });

    const { axiosPost: adjustWallet, error: postError } = useAxios({
        api: `${walletAPI}/${currentData?.id}/adjustment`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            adjustWallet(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">{t("Adjust Wallet")}</h2>
            <div className="ml-0.5">
                <InfoCard
                    noBorder
                    data={{
                        email: currentData?.user?.email,
                        [`${t("balance")} (${appCurrencySymbol})`]:
                            formatNumberForRead(currentData?.balance),
                    }}
                />
            </div>
            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-6 grid gap-y-5">
                    <div className="">
                        <FormInputDecimal
                            control={form.control}
                            name="amount"
                            label={`${t("amount")} (${appCurrencySymbol})*`}
                            onlyPositive={false}
                        />
                        <div className="ml-0.5 mt-1.5 text-[13px] font-medium leading-tight text-themeLabel">
                            {t("adjust_wallet_note")}
                        </div>
                    </div>
                    <FormTextarea
                        control={form.control}
                        name="remark"
                        label={t("remarks") + " *"}
                    />

                    <Button type="submit" className="ml-auto">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default AdjustWalletForm;
