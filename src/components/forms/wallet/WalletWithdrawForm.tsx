import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import { appCurrencySymbol, walletAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { formatNumberForRead, showBackendFormError } from "@/lib/utils";
import FormTextarea from "../../ui/FormTextarea";
import InfoCard from "../../ui/InfoCard";
import { useTranslations } from "next-intl";

const WalletWithdrawForm = ({ currentData, refresh, close }) => {
    const form = useForm({
        defaultValues: {
            amount: currentData?.balance
                ? currentData?.balance.toString().includes(".")
                    ? formatNumberForRead(currentData?.balance)
                    : currentData?.balance
                : "",
            remark: "",
        },
    });
    const t = useTranslations("common");

    const { axiosPost: withdrawWallet, error: postError } = useAxios({
        api: `${walletAPI}/${currentData?.id}/withdraw`,
        toastMsg: "Withdrawed successfully",
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            withdrawWallet(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-5">{t("Withdraw")}</h2>
            <div className="ml-0.5">
                <InfoCard
                    noBorder
                    data={{
                        email: currentData?.user?.email,
                        [`${t("balance")} (${appCurrencySymbol})`]:
                            formatNumberForRead(currentData?.balance),
                    }}
                />
            </div>
            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-6 grid gap-y-5">
                    <FormInputDecimal
                        control={form.control}
                        name="amount"
                        label={`${t("amount")} (${appCurrencySymbol})*`}
                    />

                    <FormTextarea
                        control={form.control}
                        name="remark"
                        label={t("remarks") + " *"}
                    />

                    <Button type="submit" className="ml-auto">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default WalletWithdrawForm;
