import React from "react";
import { capitalize } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    EMPLOYEE,
    GUARDIAN,
    STUDENT,
} from "@/lib/constant";
import { getUserableType } from "@/lib/utils";
import { useTranslations } from "next-intl";

const FilterWalletForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            user_email: filter?.user_email ?? "",
            user_phone_number: filter?.user_phone_number ?? "",
            user_type: filter?.user_type ?? "",
            user_number: filter?.user_number ?? "",
            user_name: filter?.user_name ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        Object.keys(data).forEach((key) => {
            if (!data[key]) {
                data[key] = undefined;
            }
        });
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput
                    control={form.control}
                    name="user_email"
                    type="email"
                />

                <FormInput control={form.control} name="user_phone_number" />

                <FormSelect
                    control={form.control}
                    name="user_type"
                    label="user type"
                    options={[
                        {
                            id: getUserableType(STUDENT),
                            name: capitalize(STUDENT),
                        },
                        {
                            id: getUserableType(EMPLOYEE),
                            name: capitalize(EMPLOYEE),
                        },
                        {
                            id: getUserableType(GUARDIAN),
                            name: capitalize(GUARDIAN),
                        },
                    ]}
                />

                <FormInput
                    control={form.control}
                    name="user_number"
                    label="student/employee number"
                />

                <FormInput control={form.control} name="user_name" />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterWalletForm;
