import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    DEPOSI<PERSON>,
    FAILED,
    PENDING,
    REFUND,
    SUCCESS,
    TRANSACTION,
    TRANSFER,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterWalletTransactionForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            reference_no: filter?.reference_no ?? "",
            card_number: filter?.card_number ?? "",
            type: filter?.type ?? "",
            status: filter?.status ?? "",
            user_name: filter?.user_name ?? "",
            user_email: filter?.user_email ?? "",
            user_phone_number: filter?.user_phone_number ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput
                    control={form.control}
                    name="reference_no"
                    label={t("reference number")}
                />

                <FormInput control={form.control} name="card_number" />

                <FormSelect
                    control={form.control}
                    name="type"
                    options={[DEPOSIT, TRANSFER, TRANSACTION, REFUND].map(
                        (type) => ({
                            id: type,
                            name: t(type),
                        })
                    )}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    options={[PENDING, SUCCESS, FAILED].map((type) => ({
                        id: type,
                        name: t(type),
                    }))}
                />

                <FormInput control={form.control} name="user_name" />

                <FormInput
                    control={form.control}
                    name="user_email"
                    type="email"
                />

                <FormInput control={form.control} name="user_phone_number" />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterWalletTransactionForm;
