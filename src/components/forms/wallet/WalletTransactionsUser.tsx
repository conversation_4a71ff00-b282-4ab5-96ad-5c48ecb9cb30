import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { capitalize, isArray } from "lodash";
import {
    appCurrencyCode,
    appCurrencySymbol,
    DATE_FORMAT,
    FAILED,
    PENDING,
    SUCCESS,
    TableColumnType,
    walletTransactionsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { displayDateTime, formatNumberForRead, isValueTrue } from "@/lib/utils";
import DataTable from "../../ui/DataTable";
import Modal from "../../ui/Modal";
import TableFilterBtn from "../../ui/TableFilterBtn";
import FilterWalletTransactionUserForm from "./FilterWalletTransactionUserForm";
import WalletRefundForm from "./WalletRefundForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const WalletTransactionsUser = ({ user, id }) => {
    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
    });
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);
    const [targetRefundId, setTargetRefundId] = useState(null);

    const { data, axiosQuery: getTransactions } = useAxios({
        api: walletTransactionsAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchTransations(isRefresh = false) {
        getTransactions({
            params: {
                wallet_id: id,
                currency_code: appCurrencyCode,
                order_by: { created_at: "desc" },
                ...filter,
                ...(isRefresh ? { page: 1 } : {}),
            },
        });
    }

    useEffect(() => {
        fetchTransations();
    }, [filter]);

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    const columns: TableColumnType[] = [
        {
            key: "reference_no",
            modify: (value) => <span className="text-[14px]">{value}</span>,
            hasSort: true,
        },
        {
            key: "card_number",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "type",
            hasSort: true,
        },
        {
            key: "status",
            hasSort: true,
            modify: (value) => (
                <span
                    className={clsx(
                        value === SUCCESS && "text-green-600",
                        value === PENDING && "text-yellow-500",
                        value === FAILED && "text-red-500"
                    )}
                >
                    {capitalize(t(value ?? "-"))}
                </span>
            ),
        },
        {
            key: "transaction_amount",
            displayAs: `${t("transaction amount")} (${appCurrencySymbol})`,
            modify: (value) => <div className="min-w-[110px]">{value}</div>,
        },
        {
            key: "wallet_balance_before",
            displayAs: `${t("wallet balance before")} (${appCurrencySymbol})`,
            modify: (value) => <div className="min-w-[110px]">{value}</div>,
        },
        {
            key: "wallet_balance_after",
            displayAs: `${t("wallet balance after")} (${appCurrencySymbol})`,
            modify: (value) => <div className="min-w-[110px]">{value}</div>,
        },
        {
            key: "description",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "remarks",
            modify: (value) => <span className="text-[14px]">{value}</span>,
        },
        {
            key: "created_at",
            modify: (value) => (
                <span className="whitespace-nowrap text-[14px]">{value}</span>
            ),
        },
    ];

    const refundActionColumn = {
        key: "can_refund",
        displayAs: "action",
        modify: (canRefund, cell) =>
            canRefund ? (
                <span
                    className="cursor-pointer rounded-md border border-gray-300 px-2 py-1.5 text-[12px] shadow-sm transition hover:border-gray-600"
                    onClick={() => setTargetRefundId(cell.row.original.id)}
                >
                    Refund
                </span>
            ) : (
                <span className="text-gray-400">-</span>
            ),
    };

    function definedData() {
        return isArray(data)
            ? data.map((item) => {
                  return {
                      id: item?.id,
                      reference_no: item?.reference_no ?? "-",
                      card_number: item?.card?.card_number ?? "-",
                      type: item?.type?.label,
                      status: item?.status?.value,
                      transaction_amount: formatNumberForRead(
                          item?.total_amount
                      ),
                      wallet_balance_before: formatNumberForRead(
                          item?.balance_before
                      ),
                      wallet_balance_after: formatNumberForRead(
                          item?.balance_after
                      ),
                      can_refund: isValueTrue(item?.can_refund),
                      description: item?.description ?? "-",
                      remarks: item?.remark ?? "-",
                      created_at: item?.created_at
                          ? displayDateTime(
                                item?.created_at,
                                DATE_FORMAT.forDisplay
                            )
                          : "-",
                  };
              })
            : [];
    }

    return (
        <>
            <div className="mt-5 w-full max-w-[90vw]">
                <div className="mb-3 flex w-full items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                    <div className="flex flex-wrap items-end gap-x-4">
                        <h2 className="capitalize">
                            {t("Wallet Transactions")}
                        </h2>
                        <h3 className="font-medium capitalize">{user ?? ""}</h3>
                    </div>
                    {/* <TableFilterBtn
                        filter={filter}
                        onClick={() => setOpenFilter(true)}
                    /> */}
                    <FilterChevronButton />
                </div>
                <FilterFormWrapper>
                    <FilterWalletTransactionUserForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </FilterFormWrapper>

                <DataTable
                    columns={
                        hasPermit("transaction-admin-refund")
                            ? [...columns, refundActionColumn]
                            : columns
                    }
                    data={definedData()}
                    pagination={pagination}
                    setPagination={setPagination}
                    changePage={(arg) => setFilter({ ...filter, ...arg })}
                    sorted={filter?.order_by}
                    sort={onSort}
                />
            </div>
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterWalletTransactionUserForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
            <Modal open={targetRefundId} onOpenChange={setTargetRefundId}>
                <WalletRefundForm
                    data={data?.find((item) => item.id === targetRefundId)}
                    refresh={() => fetchTransations(true)}
                    close={() => setTargetRefundId(null)}
                />
            </Modal>
        </>
    );
};

export default WalletTransactionsUser;

export async function getStaticProps(context) {
    return {
        props: {
            messages: (await import(`/public/locales/${context.locale}.json`))
                .default,
            locale: context.locale,
        },
    };
}
