import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { isEmpty } from "lodash";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { axiosInstance } from "@/lib/api";
import {
    appCurrencySymbol,
    CommonFormProps,
    countryAPI,
    defaultErrorMessage,
    enrollmentAPI,
    raceAPI,
    religionAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    configForGetAll,
    guardiansData,
    isProperPhoneNumber,
    showBackendFormError,
} from "@/lib/utils";
import EnrollmentProgressBar from "../ui/EnrollmentProgressBar";
import FileInput from "../ui/FileInput";
import FormCheckbox from "../ui/FormCheckbox";
import FormDivider from "../ui/FormDivider";
import FormInputInterger from "../ui/FormInputInterger";
import FormSelect from "../ui/FormSelect";
import EnrollmentFormProfileSection from "./EnrollmentFormProfileSection";
import GuardiansForm from "./GuardiansForm";

const formStyle = "grid-form lg:min-w-[600px] border-t border-dashed pt-7 pb-3";
const _save = "SAVE_AS_DRAFT" as const;
const _next = "NEXT" as const;

const EnrollmentForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [step, setStep] = useState(0);
    const [enrollment, setEnrollment] = useState(null);
    const [options, setOptions] = useState({});

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};

            if (res[0]) {
                const _data = res[0].data.data;
                setEnrollment(_data);

                if (_data?.step) {
                    setStep(_data.step);
                } else {
                    toast(defaultErrorMessage);
                    props.close();
                }
            }
            _options.countries = res[1].data.data;
            _options.races = res[2].data.data;
            _options.religions = res[3].data.data;
            //TODO: update admission grades and years when api ready
            setOptions(_options);
        },
        onError: () => props.close(),
    });

    const locale = useLocale();

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            props.id ? axiosInstance.get(`${enrollmentAPI}/${props.id}`) : null,
            axiosInstance.get(countryAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(religionAPI, _config),
            //TODO: update when api ready
        ]);
    }, []);

    function onPrev(): void {
        setStep(step - 1);
    }
    function onNext(): void {
        setStep(step + 1);
    }

    return !activeLanguages || isEmpty(options) ? (
        <div className="h-10"></div>
    ) : step === 0 ? (
        <InitForm
            enrollment={enrollment}
            setEnrollment={setEnrollment}
            options={options}
            onNext={onNext}
            refresh={props.refresh}
        />
    ) : step === 1 ? (
        <EProfileForm
            enrollment={enrollment}
            setEnrollment={setEnrollment}
            options={options}
            onNext={onNext}
            refresh={props.refresh}
            close={props.close}
        />
    ) : step === 2 ? (
        <EGuardianForm
            enrollment={enrollment}
            setEnrollment={setEnrollment}
            options={options}
            onPrev={onPrev}
            onNext={onNext}
            refresh={props.refresh}
            close={props.close}
        />
    ) : step === 3 ? (
        <EFilesUploadForm
            enrollment={enrollment}
            setEnrollment={setEnrollment}
            onPrev={onPrev}
            onNext={onNext}
            refresh={props.refresh}
            close={props.close}
        />
    ) : step === 4 ? (
        <ETermsForm
            enrollment={enrollment}
            setEnrollment={setEnrollment}
            onPrev={onPrev}
            onNext={onNext}
            refresh={props.refresh}
            close={props.close}
        />
    ) : step === 5 ? (
        <EPaymentForm enrollment={enrollment} />
    ) : (
        <></>
    );
};

type InitFormProps = {
    options: Record<string, any>;
    enrollment: any;
    setEnrollment: React.Dispatch<any>;
    onNext: () => void;
    refresh: () => void;
};

const InitForm = ({
    options,
    enrollment,
    setEnrollment,
    onNext,
    refresh,
}: InitFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm({
        defaultValues: {
            student_name: activeLanguages
                ?.map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: enrollment?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            admission_year: "", //TODO: update when api ready
            admission_grade_id: "", //TODO: update when api ready
            nric_no: "",
            passport_no: "",
            is_foreigner: false,
            //TODO: add entrance exam
        },
    });

    const { axiosPost: createEnrollment, error: postError } = useAxios({
        api: enrollmentAPI,
        onSuccess: (result) => {
            if (result?.data) {
                setEnrollment(result.data);
            }
            refresh();
            onNext();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (data.is_foreigner) {
                delete data.nric_no;
            } else {
                delete data.passport_no;
            }
            createEnrollment(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    useEffect(() => {
        form.setValue("nric_no", "");
        form.setValue("passport_no", "");
    }, [form.watch("is_foreigner")]);

    return (
        <>
            <div>
                <h2 className="text-center">Create Enrollment</h2>
            </div>
            <Form {...form}>
                <form onSubmit={onSubmit} className={formStyle}>
                    {activeLanguages?.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`student_name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="admission_year"
                        label="admission year*"
                        // options={options.admissionYears}
                        options={[2024]}
                    />

                    <FormSelect
                        control={form.control}
                        name="admission_grade_id"
                        label="admission grade*"
                        // options={options.admissionGrades}
                        options={[{ id: 1, name: "grade 1" }]}
                    />

                    <FormDivider />

                    <FormCheckbox
                        control={form.control}
                        name="is_foreigner"
                        styleClass="mt-3 ml-1"
                        textStyleClass="text-gray-500 font-medium"
                    />

                    {form.watch("is_foreigner") ? (
                        <FormInput
                            control={form.control}
                            name="passport_no"
                            label="passport number*"
                        />
                    ) : (
                        <FormInputInterger
                            control={form.control}
                            name="nric_no"
                            label="NRIC number*"
                        />
                    )}

                    <div className="mt-3 lg:col-span-2">
                        <Button type="submit" className="ml-auto">
                            Create
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

type EProfileFormProps = {
    options: Record<string, any>;
    enrollment: any;
    setEnrollment: React.Dispatch<any>;
    onNext: () => void;
    refresh: () => void;
    close: () => void;
};

const EProfileForm = ({
    options,
    enrollment,
    setEnrollment,
    onNext,
    refresh,
    close,
}: EProfileFormProps) => {
    const _step = 1;

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm({
        defaultValues: {
            student_name: activeLanguages!
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            enrollment?.translations?.student_name?.[key] ?? "",
                    }),
                    {}
                ),
            birth_cert_no: enrollment?.birth_cert_no ?? "",
            nric_no: enrollment?.nric_no ?? "",
            passport_no: enrollment?.passport_no ?? "",
            gender: enrollment?.gender ?? "",
            date_of_birth: enrollment?.date_of_birth ?? "",

            email: enrollment?.email ?? "",
            phone_no: enrollment?.phone_no ?? "",

            birthplace_id: enrollment?.birthplace?.id ?? "",
            nationality_id: enrollment?.nationality?.id ?? "",
            race_id: enrollment?.race?.id ?? "",
            religion_id: enrollment?.religion?.id ?? "",

            address: enrollment?.address ?? "",
            postal_code: enrollment?.postal_code ?? "",
            city: enrollment?.city ?? "",
            state_id: enrollment?.state?.id ?? "",
            country_id: enrollment?.country?.id ?? "",
        },
    });

    const { axiosPut: updateEnrollment, error: putError } = useAxios({
        api: enrollmentAPI,
        onSuccess: (result) => {
            refresh();
            if (result?.data) {
                setEnrollment(result.data);
                if (result.data?.step > _step) {
                    onNext();
                }
            }
        },
    });

    function onSubmit(action: typeof _next | typeof _save) {
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (!isProperPhoneNumber(form, "phone_no", data.phone_no)) {
                return;
            }
            console.log(data);
            data.step = _step;
            data.action = action;
            updateEnrollment({ id: enrollment.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <>
            <div>
                <h2 className="text-center">Student Profile</h2>
                <EnrollmentProgressBar step={_step} />
            </div>
            <Form {...form}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit(_next);
                    }}
                    className={formStyle}
                >
                    {activeLanguages?.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`student_name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}

                    <FormDivider />

                    <EnrollmentFormProfileSection
                        form={form}
                        options={options}
                        countryId={enrollment?.country?.id}
                    />

                    <div className="mt-4 flex justify-end gap-x-4 lg:col-span-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => onSubmit(_save)}
                        >
                            Save
                        </Button>
                        <Button type="submit">Next</Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

type EGuardianFormProps = {
    options: Record<string, any>;
    enrollment: any;
    setEnrollment: React.Dispatch<any>;
    onPrev: () => void;
    onNext: () => void;
    refresh: () => void;
    close: () => void;
};

const EGuardianForm = ({
    options,
    enrollment,
    setEnrollment,
    onPrev,
    onNext,
    refresh,
    close,
}: EGuardianFormProps) => {
    const _step = 2;
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm({
        defaultValues: {
            guardians: guardiansData(enrollment.guardians, activeLanguages!),
        },
    });

    const { axiosPut: updateGuardians, error: putError } = useAxios({
        api: enrollmentAPI,
        onSuccess: (result) => {
            refresh();
            if (result?.data) {
                setEnrollment(result.data);
                if (result.data?.step > _step) {
                    onNext();
                }
            }
        },
    });

    function onSubmit(action: typeof _next | typeof _save) {
        form.clearErrors();

        form.handleSubmit((data: any) => {
            console.log(data);
            data.step = _step;
            data.action = action;
            updateGuardians({ id: enrollment.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <>
            <div>
                <h2 className="text-center">Guardian Profile</h2>
                <EnrollmentProgressBar step={_step} />
            </div>
            <Form {...form}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit(_next);
                    }}
                    className={formStyle}
                >
                    <GuardiansForm form={form} options={options} />

                    <div className="mt-3 flex justify-between gap-x-3 lg:col-span-2">
                        <Button
                            type="button"
                            variant="outlineGray"
                            onClick={onPrev}
                        >
                            Back
                        </Button>
                        <Button
                            type="button"
                            variant="outline"
                            className="ml-auto"
                            onClick={() => onSubmit(_save)}
                        >
                            Save
                        </Button>
                        <Button type="submit">Next</Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

type EFilesUploadFormProps = {
    enrollment: any;
    setEnrollment: React.Dispatch<any>;
    onPrev: () => void;
    onNext: () => void;
    refresh: () => void;
    close: () => void;
};

const EFilesUploadForm = ({
    enrollment,
    setEnrollment,
    onPrev,
    onNext,
    refresh,
    close,
}: EFilesUploadFormProps) => {
    const _step = 3;

    const [fileOptions, setFileOptions] = useState<any[]>([]);
    const [files, setFiles] = useState<Record<string, any>>({});
    const [warning, setWarning] = useState<boolean>(false);

    const locale = useLocale();

    const { axiosQuery: getFileOptions } = useAxios({
        api: "/enrollments/get-uploadable-file-options",
        locale,
        onSuccess: (result) => {
            setFileOptions(result.data);
            const _files = {};
            result.data?.forEach((option) => {
                _files[option.value] =
                    enrollment[option.value]?.[0]?.id ?? null;
            });
            setFiles(_files);
        },
    });

    const { axiosPut: uploadDocs, error: putError } = useAxios({
        api: enrollmentAPI,
        onSuccess: (result) => {
            refresh();
            if (result?.data) {
                setEnrollment(result.data);
                if (result.data?.step > _step) {
                    onNext();
                }
            }
        },
    });

    useEffect(() => {
        getFileOptions();
    }, []);

    function onSubmit(action: typeof _next | typeof _save) {
        console.log("files", files);
        if (
            files.isEmpty ||
            (files && Object.values(files).some((file) => !file))
        ) {
            setWarning(true);
            return;
        }
        setWarning(false);
        const data: any = {
            step: _step,
            action: action,
            documents: Object.entries(files)?.map(([name, id]) => ({
                id,
                name,
            })),
        };
        console.log("data", data);

        uploadDocs({
            id: enrollment.id,
            data,
        });
    }

    useEffect(() => {
        if (putError) {
            toast(defaultErrorMessage);
        }
    }, [putError]);

    return (
        <>
            <div>
                <h2 className="text-center">Upload Documents</h2>
                <EnrollmentProgressBar step={_step} />
            </div>

            <div className="grid gap-y-4">
                <p className="mb-1 mt-2 text-themeLabel">
                    The following documents are required
                </p>

                {fileOptions?.map((option, index) => (
                    <div
                        className="ml-0.5 flex items-start gap-x-3"
                        key={option?.value ?? index}
                    >
                        <p className="mt-2.5 min-w-[90px] font-semibold">
                            {option?.label}
                        </p>
                        <FileInput
                            setId={(id) =>
                                setFiles({ ...files, [option?.value]: id })
                            }
                        />
                    </div>
                ))}
            </div>

            {warning && (
                <p className="text-[13px] text-destructive">
                    Please make sure all documents are included
                </p>
            )}

            <div className={clsx(formStyle, "mt-3")}>
                <div className="flex justify-between gap-x-3 lg:col-span-2">
                    <Button
                        type="button"
                        variant="outlineGray"
                        onClick={onPrev}
                    >
                        Back
                    </Button>
                    <Button
                        type="button"
                        variant="outline"
                        className="ml-auto"
                        onClick={() => onSubmit(_save)}
                    >
                        Save
                    </Button>
                    <Button type="button" onClick={() => onSubmit(_next)}>
                        Next
                    </Button>
                </div>
            </div>
        </>
    );
};

type ETermsFormProps = {
    enrollment: any;
    setEnrollment: React.Dispatch<any>;
    onPrev: () => void;
    onNext: () => void;
    refresh: () => void;
    close: () => void;
};

const ETermsForm = ({
    enrollment,
    setEnrollment,
    onPrev,
    onNext,
    refresh,
    close,
}: ETermsFormProps) => {
    const _step = 4;

    const form = useForm({
        defaultValues: {
            terms_and_conditions: false,
        },
    });

    const { axiosPut: agreeToTerms, error: putError } = useAxios({
        api: enrollmentAPI,
        onSuccess: (result) => {
            refresh();
            const resultData = result?.data;
            if (resultData) {
                setEnrollment(resultData);
                if (resultData?.step > _step) {
                    onNext();
                } else {
                    close();
                }
            }
        },
    });

    function onSubmit() {
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (!data.terms_and_conditions) {
                form.setError("terms_and_conditions", {
                    type: "custom",
                    message: "The terms and conditions field must be accepted",
                });
                return;
            }
            data.step = _step;
            data.action = _next;
            data.terms_and_conditions = data.terms_and_conditions ? 1 : 0;
            console.log(data);
            agreeToTerms({ id: enrollment.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <>
            <div>
                <h2 className="text-center">Terms and Conditions</h2>
                <EnrollmentProgressBar step={_step} />
            </div>
            <Form {...form}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit();
                    }}
                    className={"pt-5 lg:min-w-[800px]"}
                >
                    <FormCheckbox
                        control={form.control}
                        name="terms_and_conditions"
                        label="I agree to the terms and conditions."
                        textStyleClass="normal-case"
                    />

                    <div className="mt-6 flex justify-between gap-x-3 lg:col-span-2">
                        <Button
                            type="button"
                            variant="outlineGray"
                            onClick={onPrev}
                        >
                            Back
                        </Button>
                        <Button type="submit">Next</Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

type EPaymentFormProps = {
    enrollment: any;
};

const EPaymentForm = ({ enrollment }: EPaymentFormProps) => {
    const _step = 5;
    const router = useRouter();
    const locale = useLocale();

    const { axiosQuery: getPaymentUrl, error } = useAxios({
        api: `${enrollmentAPI}/${enrollment.id}/make-payment`,
        locale,
        onSuccess: (result) => {
            const url = result.data?.payment_url;
            window.open(url, "_blank");
        },
    });

    function makePayment() {
        getPaymentUrl({
            params: {
                customer_email: enrollment.email,
                customer_name: enrollment.student_name,
                payment_type: "FPX",
            },
        });
    }

    return (
        <>
            <div>
                <h2 className="text-center">Make Payment</h2>
                <EnrollmentProgressBar step={_step} />
            </div>
            <div className="my-3 flex flex-col items-center">
                <div className="mb-6 flex flex-col items-center rounded-md border px-10 pb-3 pt-4">
                    <h3>Payable Amount</h3>
                    <div className="flex items-center gap-x-2">
                        <p className="font-medium">{appCurrencySymbol}</p>
                        <div className="text-[30px] font-semibold">
                            {enrollment.payable_fees}
                        </div>
                    </div>
                </div>
                <Button onClick={makePayment} className="w-[204px]">
                    Make Payment
                </Button>
            </div>
        </>
    );
};

export default EnrollmentForm;
