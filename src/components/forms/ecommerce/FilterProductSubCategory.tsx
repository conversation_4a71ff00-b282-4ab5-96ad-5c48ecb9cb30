import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    GET_ALL_PARAMS,
    productCategoryAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";

const FilterProductSubCategoryForm = ({
    filter,
    setFilter,
    close,
    type,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            product_category_id: filter?.product_category_id ?? "",
        },
    });
    const t = useTranslations("common");
    const locale = useLocale();

    const {
        data: productCategoryOptions,
        axiosQuery: getProductCategories,
        isLoading: isLoadingProductCategories,
    } = useAxios({
        api: productCategoryAPI,
        locale,
    });

    useEffect(() => {
        getProductCategories({
            params: { ...GET_ALL_PARAMS, type: type },
        });
    }, [locale]);

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            page: 1,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name={"product_category_id"}
                    label={"product_category"}
                    options={productCategoryOptions}
                    isLoading={isLoadingProductCategories}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterProductSubCategoryForm;
