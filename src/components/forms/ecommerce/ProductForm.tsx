import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { eachDayOfInterval, format } from "date-fns";
import { Plus, X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormDivider from "@/components/ui/FormDivider";
import FormFileInput from "@/components/ui/FormFileInput";
import FormInput from "@/components/ui/FormInput";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { axiosInstance } from "@/lib/api";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    productCategoryAPI,
    productAPI,
    productGroupAPI,
    productSubCategoryAPI,
    productTagAPI,
    BOOKSHOP,
    CANTEEN,
    DATE_FORMAT,
    appCurrencyCode,
} from "@/lib/constant";
import { useAxios, useMerchants, useSubmit } from "@/lib/hook";
import {
    getDateRangeDisplay,
    getInputErrorMessage,
    isValueTrue,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";

const ProductForm = (
    props: CommonFormProps & { merchantType: typeof BOOKSHOP | typeof CANTEEN }
) => {
    const locale = useLocale();

    const [product, setProduct] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.productCategoryOptions = res[0].data.data;
            _options.productGroupOptions = res[1].data.data;
            _options.productTagOptions = res[2].data.data;
            setOptions(_options);

            if (res[3]) {
                setProduct(res[3].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = {
            params: {
                ...GET_ALL_PARAMS,
                type: props.merchantType,
                is_active: 1,
            },
            headers: { "Accept-Language": locale },
        };
        axiosMultipleQueries([
            axiosInstance.get(productCategoryAPI, _config),
            axiosInstance.get(productGroupAPI, _config),
            axiosInstance.get(productTagAPI, _config),
            props.id
                ? axiosInstance.get(`${productAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return options && (props.isCreate || product) ? (
        <FormWrap product={product} options={options} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    merchantType: typeof BOOKSHOP | typeof CANTEEN;
    product: any;
    options: Record<string, any>;
};

const FormWrap = ({
    merchantType,
    isCreate = false,
    product,
    options,
    refresh,
    close,
}: FormWrapProps) => {
    const sequenceDefaultValue = 1;

    const form = useForm<any>({
        defaultValues: {
            merchant_id: product?.merchant?.id ?? "",
            name: product?.name ?? "",
            code: product?.code ?? "",
            sequence: product?.sequence ?? sequenceDefaultValue,

            currency_code: product?.currency_code ?? appCurrencyCode,
            price_before_tax: product?.price_before_tax ?? "",

            delivery_dates:
                product?.delivery_dates?.map((item) => item?.date) ?? [],
            available_dates:
                product?.available_dates?.map((item) => item?.date) ?? [],

            _product_category_id:
                product?.sub_categories?.[0]?.category?.id ?? "",
            product_sub_category_ids:
                product?.sub_categories?.map((item) => item?.id) ?? [],
            product_group_ids: product?.groups?.map((item) => item?.id) ?? [],
            product_tag_ids: product?.tags?.map((tag) => tag?.id) ?? [],

            description: product?.description ?? "",
            is_active: isCreate ? true : isValueTrue(product?.is_active),
        },
    });

    const { remove: removeDeliveryDate } = useFieldArray({
        control: form.control,
        name: "delivery_dates",
    });

    const { remove: removeAvailableDate } = useFieldArray({
        control: form.control,
        name: "available_dates",
    });

    const locale = useLocale();

    const [deliveryDateRange, setDeliveryDateRange] = useState<any>(null);
    const [deliveryDateRangeList, setDeliveryDateRangeList] = useState<any[]>(
        []
    );

    const [availableDateRange, setAvailableDateRange] = useState<any>(null);
    const [availableDateRangeList, setAvailableDateRangeList] = useState<any[]>(
        []
    );

    function addDeliveryDateRange() {
        if (!deliveryDateRange) return;
        setDeliveryDateRangeList([
            ...deliveryDateRangeList,
            { id: uuidv4(), ...deliveryDateRange },
        ]);
        setDeliveryDateRange(null);
    }

    function removeDeliveryDateRange(id) {
        setDeliveryDateRangeList(
            deliveryDateRangeList.filter((item) => item.id !== id)
        );
    }

    function addAvailableDateRange() {
        if (!availableDateRange) return;
        setAvailableDateRangeList([
            ...availableDateRangeList,
            { id: uuidv4(), ...availableDateRange },
        ]);
        setAvailableDateRange(null);
    }

    function removeAvailableDateRange(id) {
        setAvailableDateRangeList(
            availableDateRangeList.filter((item) => item.id !== id)
        );
    }

    const {
        data: productSubCategoryOptions,
        axiosQuery: getProductSubCategories,
        isLoading: isLoadingProductSubCategories,
    } = useAxios({
        api: productSubCategoryAPI,
        locale,
    });

    const { merchantOptions, isLoadingMerchants } = useMerchants({
        params: { ...GET_ALL_PARAMS, type: merchantType },
    });

    useEffect(() => {
        const categoryId = form.watch("_product_category_id");
        if (categoryId) {
            if (productSubCategoryOptions) {
                form.setValue("product_sub_category_ids", []);
            }
            getProductSubCategories({
                params: { product_category_id: categoryId, ...GET_ALL_PARAMS },
            });
        }
    }, [form.watch("_product_category_id")]);

    const { axiosMultipartPost: createProduct, error: postError } = useAxios({
        api: productAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosMultipartPut: updateProduct, error: putError } = useAxios({
        api: productAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function getDatesOfRange(dateRangeList) {
        const datesInYMD: any[] = [];

        dateRangeList.forEach((item) => {
            const dates = eachDayOfInterval({
                start: item.startDate,
                end: item.endDate,
            });
            dates.forEach((date) => {
                datesInYMD.push(toYMD(date));
            });
        });

        return [...new Set(datesInYMD)];
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                data.merchant_type = merchantType;
                data.tax_id = 1;

                data.is_active = data.is_active ? 1 : 0;

                if (data.photo) {
                    data.photo = data.photo?.[0];
                } else {
                    data.photo = null;
                }

                delete data._product_category_id;

                data.delivery_dates = [
                    ...new Set([
                        ...data.delivery_dates,
                        ...getDatesOfRange(deliveryDateRangeList),
                    ]),
                ];
                data.available_dates = [
                    ...new Set([
                        ...data.available_dates,
                        ...getDatesOfRange(availableDateRangeList),
                    ]),
                ];

                if (isCreate) {
                    createProduct(data);
                } else {
                    updateProduct({ id: product.id, data });
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Product</h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormSelect
                        control={form.control}
                        name={"merchant_id"}
                        label={"merchant*"}
                        options={merchantOptions}
                        isLoading={isLoadingMerchants}
                    />

                    <FormInput
                        control={form.control}
                        name={"name"}
                        label={"name*"}
                    />
                    <FormInput
                        control={form.control}
                        name={"code"}
                        label={"code*"}
                    />

                    <FormInputInterger control={form.control} name="sequence" />

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name={"currency_code"}
                        label={"currency*"}
                        options={[appCurrencyCode]}
                    />

                    <FormInputDecimal
                        control={form.control}
                        name={"price_before_tax"}
                        label={"price*"}
                    />

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name={"_product_category_id"}
                        label={"product category"}
                        options={options.productCategoryOptions}
                    />

                    <FormSelect
                        isMulti
                        control={form.control}
                        name={"product_sub_category_ids"}
                        label={"product subcategories"}
                        options={productSubCategoryOptions}
                        isLoading={isLoadingProductSubCategories}
                        isDisabled={!form.watch("_product_category_id")}
                    />

                    <FormSelect
                        isMulti
                        control={form.control}
                        name={"product_group_ids"}
                        label={"product group"}
                        options={options?.productGroupOptions}
                    />

                    <FormSelect
                        isMulti
                        control={form.control}
                        name={"product_tag_ids"}
                        label={"product tag"}
                        options={options?.productTagOptions}
                    />

                    <FormDivider />

                    {merchantType === CANTEEN && (
                        <div>
                            <div className="flex items-end gap-x-2">
                                <div className="flex-grow">
                                    <DateRangePicker
                                        label={"delivery dates"}
                                        range={deliveryDateRange}
                                        setRange={setDeliveryDateRange}
                                    />
                                </div>
                                <Plus
                                    className="mb-3 cursor-pointer text-gray-500"
                                    onClick={addDeliveryDateRange}
                                />
                            </div>
                            {form.formState.errors?.delivery_dates?.message && (
                                <div className="warning-text">
                                    {`${getInputErrorMessage(form.formState.errors?.delivery_dates?.message)}`}
                                </div>
                            )}
                            {form
                                .watch("delivery_dates")
                                ?.map((date, index) => (
                                    <div
                                        className="ml-1 mt-1.5 flex items-center gap-x-2"
                                        key={index}
                                    >
                                        <X
                                            size={18}
                                            className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                            onClick={() =>
                                                removeDeliveryDate(index)
                                            }
                                        />
                                        <div className="text-[14px]">
                                            {format(date, DATE_FORMAT.DMY)}
                                        </div>
                                    </div>
                                ))}
                            {deliveryDateRangeList?.map((dateRange) => (
                                <div
                                    className="ml-1 mt-1.5 flex items-center gap-x-2"
                                    key={dateRange.id}
                                >
                                    <X
                                        size={18}
                                        className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                        onClick={() =>
                                            removeDeliveryDateRange(
                                                dateRange.id
                                            )
                                        }
                                    />
                                    <div className="text-[14px]">
                                        {getDateRangeDisplay(
                                            dateRange.startDate,
                                            dateRange.endDate
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                    {merchantType === BOOKSHOP && (
                        <div>
                            <div className="flex items-end gap-x-2">
                                <div className="flex-grow">
                                    <DateRangePicker
                                        label={"available dates"}
                                        range={availableDateRange}
                                        setRange={setAvailableDateRange}
                                    />
                                </div>
                                <Plus
                                    className="mb-3 cursor-pointer text-gray-500"
                                    onClick={addAvailableDateRange}
                                />
                            </div>
                            {form.formState.errors?.available_dates
                                ?.message && (
                                <div className="warning-text">
                                    {`${getInputErrorMessage(form.formState.errors?.available_dates?.message)}`}
                                </div>
                            )}
                            {form
                                .watch("available_dates")
                                ?.map((date, index) => (
                                    <div
                                        className="ml-1 mt-1.5 flex items-center gap-x-2"
                                        key={index}
                                    >
                                        <X
                                            size={18}
                                            className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                            onClick={() =>
                                                removeAvailableDate(index)
                                            }
                                        />
                                        <div className="text-[14px]">
                                            {format(date, DATE_FORMAT.DMY)}
                                        </div>
                                    </div>
                                ))}
                            {availableDateRangeList?.map((dateRange) => (
                                <div
                                    className="ml-1 mt-1.5 flex items-center gap-x-2"
                                    key={dateRange.id}
                                >
                                    <X
                                        size={18}
                                        className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                        onClick={() =>
                                            removeAvailableDateRange(
                                                dateRange.id
                                            )
                                        }
                                    />
                                    <div className="text-[14px]">
                                        {getDateRangeDisplay(
                                            dateRange.startDate,
                                            dateRange.endDate
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                    <FormDivider />

                    <FormTextarea
                        control={form.control}
                        name={"description"}
                        label={"description"}
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name={"is_active"}
                        label="Status"
                    />

                    <FormFileInput
                        name="photo"
                        currentFileUrl={product?.photo}
                        register={form.register}
                        errors={form.formState.errors}
                    />

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default ProductForm;
