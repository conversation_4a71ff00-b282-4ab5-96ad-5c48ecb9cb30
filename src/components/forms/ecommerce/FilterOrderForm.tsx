import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FormSearchInput from "@/components/ui/FormSearchInput";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    EMPLOYEE,
    STUDENT,
} from "@/lib/constant";
import { getUserableType, optionUserLabel } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterOrderForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            order_reference_number: filter?.order_reference_number ?? "",
            payment_status: filter?.payment_status ?? "",
            status: filter?.status ?? "",
            userable_id: filter?.userable_id ?? null,
            userable_type: filter?.userable_type ?? null,
            userable_name: filter?.userable_name ?? null,
        },
    });
    const t = useTranslations("common");

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [openEmployeeSearch, setOpenEmployeeSearch] = useState(false);

    function onSubmit(data: Record<string, any>) {
        console.log(data);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
            order_by: {
                created_at: "desc",
            },
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput
                    control={form.control}
                    name={"order_reference_number"}
                />

                <FormSelect
                    control={form.control}
                    name={"status"}
                    isStringOptions={true}
                    options={["NEW", "PROCESSING", "COMPLETED", "CANCELED"]}
                />

                <FormSelect
                    control={form.control}
                    name={"payment_status"}
                    isStringOptions={true}
                    options={["PENDING", "PAID", "FAILED", "REFUNDED"]}
                />

                <FormDivider hasColSpan={false} />

                <FormSearchInput
                    control={form.control}
                    name="student_id"
                    label="Student Buyer"
                    displayValue={
                        form.watch("userable_type") === getUserableType(STUDENT)
                            ? form.watch("userable_name")
                            : null
                    }
                    onClick={() => setOpenStudentSearch(true)}
                />

                <FormSearchInput
                    control={form.control}
                    name="employee_id"
                    label="Employee Buyer"
                    displayValue={
                        form.watch("userable_type") ===
                        getUserableType(EMPLOYEE)
                            ? form.watch("userable_name")
                            : null
                    }
                    onClick={() => setOpenEmployeeSearch(true)}
                />

                <Modal
                    open={openStudentSearch}
                    onOpenChange={setOpenStudentSearch}
                    size="large"
                >
                    <StudentSearchEngine
                        setSelection={(student) => {
                            console.log(student);
                            if (student) {
                                form.setValue("userable_id", student.id);
                                form.setValue(
                                    "userable_type",
                                    getUserableType(STUDENT)
                                );
                                form.setValue(
                                    "userable_name",
                                    optionUserLabel(
                                        student?.student_number,
                                        student?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenStudentSearch(false)}
                    />
                </Modal>

                <Modal
                    open={openEmployeeSearch}
                    onOpenChange={setOpenEmployeeSearch}
                    size="large"
                >
                    <StaffSearchEngine
                        setSelection={(employee) => {
                            console.log(employee);
                            if (employee) {
                                form.setValue("userable_id", employee.id);
                                form.setValue(
                                    "userable_type",
                                    getUserableType(EMPLOYEE)
                                );
                                form.setValue(
                                    "userable_name",
                                    optionUserLabel(
                                        employee?.employee_number,
                                        employee?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenEmployeeSearch(false)}
                    />
                </Modal>

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterOrderForm;
