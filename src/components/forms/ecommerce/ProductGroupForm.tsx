import { useEffect } from "react";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import SelectToAdd from "@/components/ui/SelectToAdd";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFormProps,
    productAPI,
    productGroupAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { formatForSelect, showBackendFormError } from "@/lib/utils";

const ProductGroupForm = (
    props: CommonFormProps & { type: typeof BOOKSHOP | typeof CANTEEN }
) => {
    return props.isCreate || props.currentTableData ? (
        <FormWrap productGroup={props.currentTableData} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: typeof BOOKSHOP | typeof CANTEEN;
    productGroup: any;
};

const FormWrap = ({
    type,
    isCreate = false,
    productGroup,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: productGroup?.name ?? "",
            products: formatForSelect(productGroup?.products) ?? [],
            product_ids: [],
        },
    });

    const { append, remove } = useFieldArray({
        control: form.control,
        name: "products",
    });

    const { axiosPost: createProductGroup, error: postError } = useAxios({
        api: productGroupAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateProductGroup, error: putError } = useAxios({
        api: productGroupAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.type = type;
            data.product_ids = data.products.map((product) => product.value);
            delete data.products;

            console.log("data", data);

            if (isCreate) {
                createProductGroup(data);
            } else {
                updateProductGroup({ id: productGroup.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Product Group
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name={"name"}
                        label="name*"
                    />

                    <SelectToAdd
                        api={productAPI}
                        params={{ merchant_type: type, is_active: 1 }}
                        name={"products"}
                        form={form}
                        append={append}
                        remove={remove}
                        errorMessage={
                            form.formState.errors?.product_ids?.message
                        }
                    />
                    <Button type="submit" className="ml-auto mt-12">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ProductGroupForm;
