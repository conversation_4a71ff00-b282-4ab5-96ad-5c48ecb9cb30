import { useEffect, useState } from "react";
import React from "react";
import { flatten } from "lodash";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import MultiTypeTargetPicker from "@/components/ui/PickersWithPaginatedTable/MultiTypeTargetPicker";
import SelectToAdd from "@/components/ui/SelectToAdd";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFormProps,
    EMPLOYEE,
    GUARDIAN,
    productAPI,
    productTagAPI,
    STUDENT,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    formatForSelect,
    getChunkedUserableData,
    getUserableType,
    showBackendFormError,
} from "@/lib/utils";

const ProductTagForm = (
    props: CommonFormProps & { type: typeof BOOKSHOP | typeof CANTEEN }
) => {
    const { data: targets, axiosQuery: getTargets } =
        useAxios({
            api: `${productTagAPI}/${props.id}/get-targets`,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getTargets();
        }
    }, []);

    return props.isCreate || (props.currentTableData && targets) ? (
        <FormWrap 
            productTag={props.currentTableData} 
            targets={targets} 
            {...props} 
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: typeof BOOKSHOP | typeof CANTEEN;
    productTag: any;
    targets: any;
};

const FormWrap = ({
    type,
    isCreate = false,
    productTag,
    targets,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: productTag?.name ?? "",
            products: formatForSelect(productTag?.products) ?? [],
            product_ids: [],
        },
    });

    const { append: appendProduct, remove: removeProduct } = useFieldArray({
        control: form.control,
        name: "products",
    });

    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        getChunkedUserableData(targets)
    );

    const { axiosPost: createProductTag, error: postError } = useAxios({
        api: productTagAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateProductTag, error: putError } = useAxios({
        api: productTagAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.type = type;
            data.product_ids = data.products.map((product) => product.value);
            delete data.products;

            data.userables = flatten(targetsChunk).map((target) => ({
                id: target.target_id,
                type: getUserableType(target.type),
            }));

            if (isCreate) {
                createProductTag(data);
            } else {
                updateProductTag({ id: productTag.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div className="lg:min-w-[800px]">
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Product Tag
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name={"name"}
                        label="name*"
                    />

                    <SelectToAdd
                        api={productAPI}
                        params={{ merchant_type: type, is_active: 1 }}
                        name={"products"}
                        form={form}
                        append={appendProduct}
                        remove={removeProduct}
                        errorMessage={
                            form.formState.errors?.product_ids?.message
                        }
                    />

                    <MultiTypeTargetPicker
                        title="Targets"
                        targetsChunk={targetsChunk}
                        setTargetsChunk={setTargetsChunk}
                        targetTypes={[STUDENT, EMPLOYEE, GUARDIAN]}
                    />

                    <Button type="submit" className="ml-auto mt-2">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ProductTagForm;
