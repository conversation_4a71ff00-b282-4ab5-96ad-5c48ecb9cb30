import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFormProps,
    productCategoryAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const ProductCategoryForm = (
    props: CommonFormProps & { type: typeof BOOKSHOP | typeof CANTEEN }
) => {
    const locale = useLocale();

    const { data: productCategory, axiosQuery: getProductCategory } = useAxios({
        api: productCategoryAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getProductCategory({ id: props.id });
        }
    }, []);

    return props.isCreate || productCategory ? (
        <FormWrap productCategory={productCategory} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    productCategory: any;
    type: typeof BOOKSHOP | typeof CANTEEN;
};

const FormWrap = ({
    type,
    isCreate = false,
    productCategory,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: productCategory?.name ?? "",
        },
    });

    const { axiosPost: createProductCategory, error: postError } = useAxios({
        api: productCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateProductCategory, error: putError } = useAxios({
        api: productCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.type = type;
            if (isCreate) {
                createProductCategory(data);
            } else {
                updateProductCategory({ id: productCategory.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Product Category
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name={"name"}
                        label={"name*"}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ProductCategoryForm;
