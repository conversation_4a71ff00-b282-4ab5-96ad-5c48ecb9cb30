import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFormProps,
    GET_ALL_PARAMS,
    productCategoryAPI,
    productSubCategoryAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const ProductSubcategoryForm = (
    props: CommonFormProps & { type: typeof BOOKSHOP | typeof CANTEEN }
) => {
    const locale = useLocale();

    const { data: productSubCategory, axiosQuery: getProductSubCategory } =
        useAxios({
            api: productSubCategoryAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getProductSubCategory({ id: props.id });
        }
    }, []);

    return props.isCreate || productSubCategory ? (
        <FormWrap productSubCategory={productSubCategory} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: typeof BOOKSHOP | typeof CANTEEN;
    productSubCategory: any;
};

const FormWrap = ({
    type,
    isCreate = false,
    productSubCategory,
    refresh,
    close,
}: FormWrapProps) => {
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: productSubCategory?.name ?? "",
            product_category_id: productSubCategory?.category?.id ?? "",
            sequence: productSubCategory?.sequence ?? sequenceDefaultValue,
        },
    });

    const locale = useLocale();

    const {
        data: productCategoryOptions,
        axiosQuery: getProductCategories,
        isLoading: isLoadingProductCategories,
    } = useAxios({
        api: productCategoryAPI,
        locale,
    });

    useEffect(() => {
        getProductCategories({ params: { ...GET_ALL_PARAMS, type } });
    }, []);

    const { axiosPost: createProductSubCategory, error: postError } = useAxios({
        api: productSubCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateProductSubCategory, error: putError } = useAxios({
        api: productSubCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createProductSubCategory(data);
            } else {
                updateProductSubCategory({ id: productSubCategory.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Product Subcategory
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name={"name"}
                        label="name*"
                    />

                    <FormSelect
                        control={form.control}
                        name={"product_category_id"}
                        label={"product category*"}
                        options={productCategoryOptions}
                        isLoading={isLoadingProductCategories}
                    />

                    <FormInputInterger control={form.control} name="sequence" />

                    <Button type="submit" className="ml-auto mt-10">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ProductSubcategoryForm;
