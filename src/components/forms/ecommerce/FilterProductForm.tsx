import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import { axiosInstance } from "@/lib/api";
import {
    CommonFilterProps,
    GET_ALL_PARAMS,
    productCategoryAPI,
    productGroupAPI,
    productSubCategoryAPI,
    productTagAPI,
} from "@/lib/constant";
import { useAxios, useMerchants } from "@/lib/hook";
import { Button } from "../../base-ui/button";

const FilterProductForm = ({
    filter,
    setFilter,
    close,
    type,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            code: filter?.code ?? "",
            merchant_id: filter?.merchant_id ?? "",
            category_id: filter?.category_id ?? "",
            sub_category_id: filter?.sub_category_id ?? [],
            group_id: filter?.group_id ?? [],
            tag_id: filter?.tag_id ?? [],
            is_active: filter?.is_active ?? undefined,
        },
    });
    const t = useTranslations("common");

    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.productCategoryOptions = res[0].data.data;
            _options.productGroupOptions = res[1].data.data;
            _options.productTagOptions = res[2].data.data;
            setOptions(_options);
        },
        onError: () => close(),
    });

    const locale = useLocale();

    useEffect(() => {
        const _config = {
            params: {
                ...GET_ALL_PARAMS,
                type: type,
                is_active: 1,
            },
            headers: { "Accept-Language": locale },
        };
        axiosMultipleQueries([
            axiosInstance.get(productCategoryAPI, _config),
            axiosInstance.get(productGroupAPI, _config),
            axiosInstance.get(productTagAPI, _config),
        ]);
    }, []);

    const { merchantOptions, isLoadingMerchants } = useMerchants({
        params: { ...GET_ALL_PARAMS, type: type },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            page: 1,
            per_page: filter?.per_page,
        });
        close();
    }

    const {
        data: productSubCategoryOptions,
        axiosQuery: getProductSubCategories,
        isLoading: isLoadingProductSubCategories,
    } = useAxios({
        api: productSubCategoryAPI,
        locale,
    });

    useEffect(() => {
        const categoryId = form.watch("category_id");
        if (categoryId) {
            if (productSubCategoryOptions) {
                form.setValue("sub_category_id", []);
            }
            getProductSubCategories({
                params: { category_id: categoryId, ...GET_ALL_PARAMS },
            });
        }
    }, [form.watch("category_id"), locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />
                <FormInput control={form.control} name={"code"} />

                <FormSelect
                    control={form.control}
                    name={"merchant_id"}
                    label={"merchant"}
                    options={merchantOptions}
                    isLoading={isLoadingMerchants}
                />

                <FormSelect
                    control={form.control}
                    name={"category_id"}
                    label={"product category"}
                    options={options?.productCategoryOptions ?? []}
                />

                <FormSelect
                    control={form.control}
                    name={"sub_category_id"}
                    label={"product subcategories"}
                    options={productSubCategoryOptions}
                    isLoading={isLoadingProductSubCategories}
                    isDisabled={!form.watch("category_id")}
                />

                <FormSelect
                    control={form.control}
                    name={"group_id"}
                    label={"product group"}
                    options={options?.productGroupOptions}
                />

                <FormSelect
                    control={form.control}
                    name={"tag_id"}
                    label={"product tag"}
                    options={options?.productTagOptions}
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: "Active",
                            id: "1",
                        },
                        {
                            name: "Inactive",
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterProductForm;
