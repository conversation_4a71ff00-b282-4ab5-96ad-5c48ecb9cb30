import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFormProps,
    merchantAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    isChineseCode,
    isProperPhoneNumber,
    isValueTrue,
    showBackendFormError,
} from "@/lib/utils";
import FormDivider from "@/components/ui/FormDivider";

const MerchantForm = (
    props: CommonFormProps & { type: typeof BOOKSHOP | typeof CANTEEN }
) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data: merchant, axiosQuery: getMerchant } = useAxios({
        api: merchantAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getMerchant({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || merchant) ? (
        <FormWrap
            merchant={merchant}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: typeof BOOKSHOP | typeof CANTEEN;
    merchant: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    type,
    isCreate = false,
    merchant,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: merchant?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            label: merchant?.label ?? "",
            phone_number: merchant?.phone_number ?? "",
            email: merchant?.email ?? "",
            password: "",
            password_confirmation: "",
            is_active: isCreate ? true : isValueTrue(merchant?.is_active),
            with_user_account: merchant?.user ? true : false,
        },
    });

    const { axiosPost: createMerchant, error: postError } = useAxios({
        api: merchantAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateMerchant, error: putError } = useAxios({
        api: merchantAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.type = type;

            if (!isProperPhoneNumber(form, "phone_number", data.phone_number)) {
                return;
            }

            if (isCreate) {
                createMerchant(data);
            } else {
                updateMerchant({ id: merchant.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Merchant</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid-form">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                            isUpperCase={!isChineseCode(lang?.code)}
                        />
                    ))}
                    <FormDivider />
                    <FormInput
                        control={form.control}
                        name={"label"}
                        label="label*"
                    />
                    <FormPhoneInput
                        form={form}
                        name="phone_number"
                        label="phone number*"
                    />
                    <FormInput
                        control={form.control}
                        name="email"
                        type="email"
                        label="email*"
                    />

                    <FormInput
                        control={form.control}
                        name="password"
                        type="password"
                    />

                    <FormInput
                        control={form.control}
                        name="password_confirmation"
                        type="password"
                    />

                    <div className="flex items-end justify-between lg:col-span-2">
                        <StatusFormSwitch
                            control={form.control}
                            name={"is_active"}
                            label="Status"
                        />
                        <FormCheckbox
                            control={form.control}
                            name="with_user_account"
                            styleClass="items-end"
                            textStyleClass="items-end mb-0"
                            isDisabled={!isCreate && merchant?.user}
                        />
                    </div>

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-10">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default MerchantForm;
