import { useEffect, useRef, useState } from "react";
import React from "react";
import { isArray } from "lodash";
import { Button } from "@/components/base-ui/button";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import FilterTimetableForm from "@/components/forms/timetable/FilterTimetableForm";
import ActionDropdown from "@/components/ui/ActionDropdown";
import DataTable from "@/components/ui/DataTable";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import {
    CANTEEN,
    TableColumnType,
    appCurrencySymbol,
    productAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    formatNumberForRead,
    refreshForUpdate,
    strStartCase,
} from "@/lib/utils";
import { useUserProfile } from "@/lib/store";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import { useLocale, useTranslations } from "next-intl";
import FilterProductForm from "@/components/forms/ecommerce/FilterProductForm";
import ProductForm from "@/components/forms/ecommerce/ProductForm";
import { useForm } from "react-hook-form";
import FreeInput from "@/components/ui/FreeInput";
import FreeInputDecimal from "@/components/ui/FreeInputDecimal";
import FreeCheckbox from "@/components/ui/FreeCheckbox";
import { CheckCircle2, Pen, XCircle } from "lucide-react";
import { Checkbox } from "@/components/base-ui/checkbox";
import clsx from "clsx";

const ProductPage = ({ type, groupOptions, tagOptions }) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const locale = useLocale();

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
        order_by: { code: "asc" },
    });
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [pagination, setPagination] = useState<any>();

    const [openCreate, setOpenCreate] = useState(false);
    const [openFilter, setOpenFilter] = useState(false);

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const _columns: TableColumnType[] = [
        {
            key: "code",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
            modify: (value, cell) => {
                const fieldName = `list.${cell.row.index}.name`;
                return (
                    <div className="min-w-[200px]">
                        <SingleField
                            form={form}
                            fieldName={fieldName}
                            onUpdate={() => {
                                onUpdateProduct({
                                    ...cell.row.original,
                                    name: form.getValues(fieldName),
                                });
                            }}
                        />
                    </div>
                );
            },
        },
        {
            key: "price_before_tax",
            displayAs: "Price",
            hasSort: true,
            modify: (value, cell) => {
                const fieldName = `list.${cell.row.index}.price_before_tax`;
                return (
                    <SingleField
                        isDecimal={true}
                        form={form}
                        fieldName={fieldName}
                        onUpdate={() => {
                            onUpdateProduct({
                                ...cell.row.original,
                                price_before_tax: form.getValues(fieldName),
                            });
                        }}
                    />
                );
            },
        },
        {
            key: "tags",
            modify(list, cell) {
                const fieldName = `list.${cell.row.index}.product_tag_ids`;
                return (
                    <MultiField
                        options={tagOptions}
                        idValues={form.watch(fieldName)}
                        onUpdate={(ids) => {
                            form.setValue(fieldName, ids);
                            onUpdateProduct({
                                ...cell.row.original,
                                product_tag_ids: ids,
                            });
                        }}
                    />
                );
            },
        },
        {
            key: "groups",
            modify(list, cell) {
                const fieldName = `list.${cell.row.index}.product_group_ids`;
                return (
                    <MultiField
                        options={groupOptions}
                        idValues={form.watch(fieldName)}
                        onUpdate={(ids) => {
                            form.setValue(fieldName, ids);
                            onUpdateProduct({
                                ...cell.row.original,
                                product_group_ids: ids,
                            });
                        }}
                    />
                );
            },
        },
        {
            key: "sequence",
            modify: (value, cell) => {
                const fieldName = `list.${cell.row.index}.sequence`;
                return (
                    <SingleField
                        form={form}
                        fieldName={fieldName}
                        onUpdate={() => {
                            onUpdateProduct({
                                ...cell.row.original,
                                sequence: form.getValues(fieldName),
                            });
                        }}
                    />
                );
            },
        },
        {
            key: "is_active",
            displayAs: "Status",
            hasSort: true,
            modify: (value, cell) => (
                <div className="min-w-[88px]">
                    <FreeCheckbox
                        control={form.control}
                        name={`list.${cell.row.index}.is_active`}
                        label="Is Active"
                        onChange={(checked) => {
                            onUpdateProduct({
                                ...cell.row.original,
                                is_active: checked ? 1 : 0,
                            });
                        }}
                    />
                </div>
            ),
        },
    ];

    const _bookstoreColumns = _columns;
    const _canteenColumns = [{ key: "merchant" }, ..._columns];

    const { data, axiosQuery: getProducts } = useAxios({
        api: productAPI,
        locale,
        onSuccess: (result) => {
            form.setValue(
                "list",
                result?.data?.map((item) => ({
                    ...item,
                    product_tag_ids: item?.tags?.map((tag) => tag?.id) ?? [],
                    product_group_ids:
                        item?.groups?.map((group) => group?.id) ?? [],
                })) ?? []
            );
            setColumns(type === CANTEEN ? _canteenColumns : _bookstoreColumns);
            setPagination(result?.pagination);
        },
    });

    const { axiosPut: updateProduct } = useAxios({
        api: productAPI,
        onSuccess: () => {
            fetchProducts();
        },
        onError: () => {
            fetchProducts();
        },
    });

    function onUpdateProduct(data) {
        updateProduct({
            id: data.id,
            data: {
                ...data,
            },
            showErrorInToast: true,
        });
    }

    const form = useForm<any>({
        defaultValues: {
            list: [],
        },
    });

    function fetchProducts() {
        getProducts({
            params: {
                merchant_type: type,
                ...filter,
            },
        });
    }

    useEffect(() => {
        fetchProducts();
    }, [filter, locale]);

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  ...item,
                  tax_id: 1,
                  merchant_id: item?.merchant?.id,
                  merchant_type: item?.merchant?.type,
                  merchant: item?.merchant?.label,
                  available_dates: item?.available_dates?.map((i) => i?.date),
                  delivery_dates: item?.delivery_dates?.map((i) => i?.date),
                  product_tag_ids: item?.tags?.map((tag) => tag?.id) ?? [],
                  product_group_ids:
                      item?.groups?.map((group) => group?.id) ?? [],
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setOpenCreate(false);
        setTargetId(null);
    }

    const t = useTranslations("common");

    return (
        <>
            <div className="mb-3 flex w-full flex-wrap items-center justify-between gap-3 lg:mb-4 lg:gap-x-3.5 lg:px-1">
                <h2>{strStartCase(type)} Products</h2>
                <div className="flex items-center gap-x-3">
                    {hasPermit("product-create") && (
                        <Button onClick={() => setOpenCreate(true)}>
                            Add Product
                        </Button>
                    )}
                    {/* <TableFilterBtn
                            filter={filter}
                            onClick={() => setOpenFilter(true)}
                        /> */}
                    <FilterChevronButton />
                    {/* <LucidePrinter
                            size={20}
                            className="mx-1 cursor-pointer text-themeGreen"
                            onClick={print}
                        /> */}
                </div>
            </div>

            <FilterFormWrapper>
                <FilterTimetableForm
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </FilterFormWrapper>

            <DataTable
                columns={columns}
                data={definedData(data)}
                pagination={pagination}
                setPagination={setPagination}
                changePage={(arg) => setFilter({ ...filter, ...arg })}
                sorted={filter?.order_by}
                sort={onSort}
                actionMenu={({ cell }) => (
                    <ActionDropdown>
                        <DropdownMenuItem
                            className="c-text-size"
                            onClick={() => {
                                setTargetId(cell.row.original.id);
                            }}
                        >
                            Edit / View Product
                        </DropdownMenuItem>
                        {hasPermit("product-delete") && (
                            <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    className="c-text-size text-red-600"
                                    onClick={() =>
                                        setTargetDeleteId(cell.row.original.id)
                                    }
                                >
                                    Delete
                                </DropdownMenuItem>
                            </>
                        )}
                    </ActionDropdown>
                )}
            />

            <Modal open={openCreate} onOpenChange={setOpenCreate} size="medium">
                <ProductForm
                    isCreate={true}
                    merchantType={type}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                <ProductForm
                    merchantType={type}
                    id={targetId}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                    close={closeForm}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={productAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={() => refreshForUpdate(filter, setFilter)}
                />
            </Modal>

            {/* filter */}
            <Modal open={openFilter} onOpenChange={setOpenFilter}>
                <FilterProductForm
                    type={type}
                    filter={filter}
                    setFilter={setFilter}
                    close={() => setOpenFilter(false)}
                />
            </Modal>
        </>
    );
};

const SingleField = ({ form, fieldName, onUpdate, isDecimal = false }) => {
    const [isEdit, setIsEdit] = useState(false);

    return isEdit ? (
        isDecimal ? (
            <div className="flex items-center gap-x-1.5">
                <span>{appCurrencySymbol}</span>
                <FreeInputDecimal
                    control={form.control}
                    name={fieldName}
                    hasLabel={false}
                    isSmallerX={true}
                    isSmallerY={true}
                    onBlur={(e) => {
                        e.preventDefault();
                        setIsEdit(false);
                        onUpdate();
                    }}
                    onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === "Tab") {
                            e.preventDefault();
                            setIsEdit(false);
                            onUpdate();
                        }
                    }}
                />
            </div>
        ) : (
            <div
                className={clsx(fieldName.includes(".name") && "min-w-[280px]")}
            >
                <FreeInput
                    control={form.control}
                    name={fieldName}
                    hasLabel={false}
                    isSmallerX={true}
                    isSmallerY={true}
                    onBlur={(e) => {
                        e.preventDefault();
                        setIsEdit(false);
                        onUpdate();
                    }}
                    onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === "Tab") {
                            e.preventDefault();
                            setIsEdit(false);
                            onUpdate();
                        }
                    }}
                />
            </div>
        )
    ) : (
        <div
            className="min-h-[20px] w-fit cursor-pointer border-b border-dashed border-themeGreen2 border-opacity-30 text-themeGreen2"
            onClick={() => {
                setIsEdit(true);
                setTimeout(() => {
                    form.setFocus(fieldName);
                }, 10);
            }}
        >
            {isDecimal
                ? `${appCurrencySymbol} ${formatNumberForRead(form.getValues(fieldName))}`
                : form.getValues(fieldName)}
        </div>
    );
};

const MultiField = ({ idValues, onUpdate, options }) => {
    const [isEdit, setIsEdit] = useState(false);
    const [checkedList, setCheckedList] = useState(idValues);

    const ref = useRef<any>(null);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (ref.current && !ref.current.contains(event.target)) {
                setIsEdit(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div className="h-full w-full" ref={ref}>
            {isEdit ? (
                <>
                    <div className="grid min-w-[200px] gap-y-2">
                        {options?.map((option, index) => (
                            <div
                                key={option.id ?? index}
                                className="flex items-start gap-x-1.5"
                            >
                                <Checkbox
                                    checked={checkedList?.includes(option.id)}
                                    onCheckedChange={(checked) => {
                                        if (checked) {
                                            setCheckedList([
                                                ...checkedList,
                                                option.id,
                                            ]);
                                        } else {
                                            setCheckedList((prev) =>
                                                prev.filter(
                                                    (id) => id !== option.id
                                                )
                                            );
                                        }
                                    }}
                                />
                                <div className="mt-[1px] text-[14px] leading-none text-gray-600">
                                    {option?.name}
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="mt-3.5 flex gap-2">
                        <XCircle
                            size={28}
                            className="cursor-pointer text-gray-400"
                            onClick={() => {
                                setCheckedList(idValues);
                                setIsEdit(false);
                            }}
                        />
                        <CheckCircle2
                            size={28}
                            className="cursor-pointer text-themeGreen"
                            onClick={() => onUpdate(checkedList)}
                        />
                    </div>
                </>
            ) : (
                <div
                    onClick={() => setIsEdit(true)}
                    className="flex min-h-[20px] w-fit cursor-pointer flex-wrap items-center text-themeGreen2"
                >
                    {idValues?.length > 0 ? (
                        <div className="table-chips-wrap">
                            {options
                                .filter((option) =>
                                    idValues?.includes(option.id)
                                )
                                ?.map((option) => option.name)
                                .map((tag, index) => (
                                    <span
                                        key={index}
                                        className="table-chip border-themeGreen2 border-opacity-30"
                                    >
                                        {tag}
                                    </span>
                                ))}
                        </div>
                    ) : (
                        <Pen size={18} className="ml-2 text-themeGreen2" />
                    )}
                </div>
            )}
        </div>
    );
};

export default ProductPage;
