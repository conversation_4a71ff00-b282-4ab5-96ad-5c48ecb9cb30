import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { capitalize, orderBy } from "lodash";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { axiosInstance } from "@/lib/api";
import {
    countryAPI,
    studentAPI,
    raceAPI,
    gradeAPI,
    religionAPI,
    CommonFormProps,
    stateAPI,
    educationAPI,
    GUARDIAN,
    schoolAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    healthConcernAPI,
    genderOptions,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages, useLoading, useUserProfile } from "@/lib/store";
import {
    configFor<PERSON>et<PERSON>ll,
    isChineseCode,
    isMY,
    isProperPhone<PERSON>umber,
    isUnauthenticated,
    isValueTrue,
    showBackendFormError,
    strStartCase,
    toYMD,
} from "@/lib/utils";
import { DialogFooter } from "../base-ui/dialog";
import { Label } from "../base-ui/label";
import { DatePicker } from "../ui/DatePicker";
import FormCheckbox from "../ui/FormCheckbox";
import FormDivider from "../ui/FormDivider";
import FormFileInput from "../ui/FormFileInput";
import FormInputInterger from "../ui/FormInputInterger";
import FormPhoneInput from "../ui/FormPhoneInput";
import FormSelect from "../ui/FormSelect";
import FormTextarea from "../ui/FormTextarea";
import Modal from "../ui/Modal";
import GuardiansForm from "./GuardiansForm";

const StudentForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const [student, setStudent] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.countries = res[0].data.data;
            _options.races = res[1].data.data;
            _options.religions = res[2].data.data;
            _options.grades = res[3].data.data.sort(
                (a, b) => b.sequence - a.sequence
            );
            _options.educations = res[4].data.data;
            _options.primarySchools = res[5].data.data;
            _options.healthConcerns = res[6].data.data.sort(
                (a, b) => b.sequence - a.sequence
            );
            setOptions(_options);
            // TODO: update when api ready

            if (res[7]) {
                setStudent(res[7].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(countryAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(gradeAPI, _config),
            axiosInstance.get(educationAPI, _config),
            axiosInstance.get(schoolAPI, {
                params: { level: PRIMARY, ...GET_ALL_PARAMS },
                headers: { "Accept-Language": locale },
            }),
            axiosInstance.get(healthConcernAPI, _config),
            props.id
                ? axiosInstance.get(`${studentAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                      params: {
                          response: "FULL",
                          includes: [
                              "admissionGrade",
                              "nationality",
                              "race",
                              "religion",
                              "state",
                              "country",
                              "healthConcern",
                              "primarySchool",
                              "guardians",
                              "guardians.country",
                              "guardians.race",
                              "guardians.religion",
                              "guardians.education",
                          ],
                      },
                  })
                : null,
        ]);
    }, []);

    return activeLanguages && options ? (
        <FormWrap
            activeLanguages={activeLanguages}
            options={options}
            student={student}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    student: any;
    activeLanguages: Array<Record<string, any>>;
    options: Record<string, any>;
};

const FormWrap = ({
    isCreate = false,
    activeLanguages,
    options,
    student,
    refresh,
    close,
}: FormWrapProps) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const viewOnly = !hasPermit("student-update");

    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            student_number: student?.student_number ?? undefined,
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: student?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            admission_year: student?.admission_year ?? "",
            admission_grade_id: student?.admission_grade?.id ?? "",
            admission_type: student?.admission_type ?? "",
            join_date: student?.join_date ?? "",

            birth_cert_number: student?.birth_cert_number ?? "",
            nric: student?.nric ?? "",
            passport_number: student?.passport_number ?? "",
            gender: student?.gender ?? "",
            date_of_birth: student?.date_of_birth ?? "",
            // leave_date: student?.leave_date ?? "",

            // email: student?.email ?? "",
            phone_number: student?.phone_number ?? "",
            phone_number_2: student?.phone_number_2 ?? "",

            birthplace_id: student?.birthplace?.id ?? "",
            birthplace: student?.birthplace ?? "",
            nationality_id: student?.nationality?.id ?? "",
            race_id: student?.race?.id ?? "",
            religion_id: student?.religion?.id ?? "",

            address: student?.address ?? "",
            address_2: student?.address_2 ?? "",
            postal_code: student?.postal_code ?? "",
            city: student?.city ?? "",
            state_id: student?.state?.id ?? "",
            country_id: student?.country?.id ?? "",

            remarks: student?.remarks ?? "",
            is_hostel: isValueTrue(student?.is_hostel),

            primary_school_id: student?.primary_school?.id ?? "",
            dietary_restriction: student?.dietary_restriction ?? "",
            health_concern_id: student?.health_concern?.id ?? "",

            guardians: orderBy(
                student?.guardians?.map((guardian) => ({
                    id: guardian?.id ?? undefined,
                    type: guardian?.type ?? "",
                    name: guardian?.translations?.name ?? "",
                    nric: guardian?.nric ?? "",
                    passport_number: guardian?.passport_number ?? "",
                    email: guardian?.email ?? "",
                    live_status: guardian?.live_status ?? "",
                    phone_number: guardian?.phone_number ?? "",
                    married_status: guardian?.married_status ?? "",
                    occupation: guardian?.occupation ?? "",
                    occupation_description:
                        guardian?.occupation_description ?? "",

                    nationality_id: guardian?.nationality?.id ?? "",
                    race_id: guardian?.race?.id ?? "",
                    religion_id: guardian?.religion?.id ?? "",
                    education_id: guardian?.education?.id ?? "",
                    is_primary: isValueTrue(guardian?.is_primary),
                    with_user_account: isValueTrue(guardian?.has_user_account),
                    _with_user_account: isValueTrue(guardian?.has_user_account),
                    is_direct_dependant: isValueTrue(
                        guardian?.is_direct_dependant
                    ),
                })) ?? [],
                [(guardian) => (guardian.type === GUARDIAN ? 1 : 0), "type"],
                ["asc", "asc"]
            ),
        },
    });

    const [promptGenerate, setPromptGenerate] = useState(false);
    const setLoading = useLoading((state) => state.setLoading);

    const { axiosMultipartPost: createStudent, error: postError } = useAxios({
        api: studentAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosMultipartPut: updateStudent, error: putError } = useAxios({
        api: studentAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function regenerateStudentNumber() {
        setLoading(true);
        axiosInstance
            .post(`/students/${student?.id}/regenerate-student-number`)
            .then((res) => {
                setStudentNumber(res.data.data?.student_number);
                toast(`Student number updated successfully`);
            })
            .catch((error) => {
                if (isUnauthenticated(error)) {
                    return;
                }
                console.log(error);
                toast.error("Failed to regenerate student number");
            })
            .finally(() => setLoading(false));
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    function generateNewNumber() {
        setPromptGenerate(false);
        regenerateStudentNumber();
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                if (
                    !isProperPhoneNumber(
                        form,
                        "phone_number",
                        data.phone_number
                    )
                ) {
                    return;
                }
                if (
                    !isProperPhoneNumber(
                        form,
                        "phone_number_2",
                        data.phone_number_2
                    )
                ) {
                    return;
                }
                if (data.join_date) {
                    data.join_date = toYMD(data.join_date);
                }
                // if (data.leave_date) {
                //     data.leave_date = toYMD(data.leave_date);
                // }
                if (data.date_of_birth) {
                    data.date_of_birth = toYMD(data.date_of_birth);
                }
                if (data.photo) {
                    data.photo = data.photo?.[0];
                } else {
                    data.photo = null;
                }
                data.is_hostel = isValueTrue(data.is_hostel) ? 1 : 0;

                const filteredGuardians: any[] = [];

                [...data.guardians].forEach((guardian) => {
                    if (guardian._isNew) {
                        delete guardian.id;
                    }
                    const filledNames = Object.values(guardian.name).filter(
                        (name: any) => name
                    );
                    if (guardian.name && filledNames.length === 0) {
                        delete guardian.name;
                    }
                    guardian.with_user_account = guardian.with_user_account
                        ? 1
                        : 0;

                    delete guardian._with_user_account;

                    guardian.is_direct_dependant = guardian.is_direct_dependant
                        ? 1
                        : 0;

                    filteredGuardians.push({
                        ...guardian,
                        is_primary: guardian.is_primary ? 1 : 0,
                    });
                });

                data.guardians = filteredGuardians;

                console.log(data);

                if (isCreate) {
                    createStudent(data);
                } else {
                    updateStudent({ id: student.id, data });
                }
            })
        );
    }

    function setStudentNumber(studentNumber) {
        const hasTag = studentNumber.charAt(0) === "H";
        const isHostel = form.watch("is_hostel");

        if (isHostel && !hasTag) {
            form.setValue("student_number", `H${studentNumber}`);
            return;
        }
        if (!isHostel && hasTag) {
            form.setValue("student_number", studentNumber.slice(1));
            return;
        }
        form.setValue("student_number", studentNumber);
    }

    useEffect(() => {
        if (!isCreate) {
            setStudentNumber(form.watch("student_number"));
        }
    }, [form.watch("is_hostel")]);

    const locale = useLocale();
    const {
        data: stateList,
        axiosQuery: getStateList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        if (student?.country?.id) {
            getStateList({ params: { country_id: student?.country?.id } });
        }
        if (isCreate) {
            const defaultCountryId = options?.countries.find((country) =>
                isMY(country.name?.toLowerCase())
            )?.id;
            if (defaultCountryId) {
                form.setValue("nationality_id", defaultCountryId);
                form.setValue("country_id", defaultCountryId);
                getStateList({ params: { country_id: defaultCountryId } });
            }
        }
    }, []);

    return (
        <div className="lg:px-2">
            <h2 className="mb-5 lg:mt-2">
                {isCreate ? t("Create ") : t("Update ")}
                {capitalize(t("student"))}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form pb-5 lg:min-w-[800px]"
                >
                    <div>
                        <Label className="label">
                            {strStartCase(t("student number"))}
                        </Label>
                        <div
                            className={clsx(
                                "c-text-input flex h-[42px] w-full items-center rounded-sm border border-gray-200 px-4",
                                isCreate ? "text-themeLabel" : "text-gray-600"
                            )}
                        >
                            {form.watch("student_number") ??
                                t("Auto-generated")}
                        </div>
                        {isCreate && (
                            <div className="ml-1 mt-1.5 text-[12px] leading-tight text-themeLabel lg:-mb-2">
                                {t(
                                    "The student number will be automatically generated using a sequential number"
                                )}
                            </div>
                        )}
                    </div>

                    {!viewOnly && (
                        <div
                            className={clsx(
                                "grid gap-5 lg:mt-6 lg:grid-cols-2",
                                !isCreate && "items-center"
                            )}
                        >
                            {!isCreate && (
                                <Button
                                    variant={"outline"}
                                    onClick={() => setPromptGenerate(true)}
                                >
                                    {t("Generate New")}
                                </Button>
                            )}
                            <FormCheckbox
                                control={form.control}
                                name="is_hostel"
                                label={"hostel applicant"}
                                isDisabled={viewOnly}
                            />
                        </div>
                    )}
                    <FormDivider />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                            disabled={viewOnly}
                            isUpperCase={!isChineseCode(lang?.code)}
                        />
                    ))}

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="admission_year"
                        label={t("admission year") + "*"}
                        options={[
                            2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025,
                        ]}
                        isDisabled={viewOnly}
                        // TODO: update when api ready
                    />

                    <FormSelect
                        control={form.control}
                        name="admission_grade_id"
                        label={t("admission grade") + "*"}
                        options={options.grades}
                        isDisabled={viewOnly}
                        isSortByName={false}
                    />

                    <FormSelect
                        control={form.control}
                        name="admission_type"
                        label={t("admission type") + "*"}
                        isDisabled={viewOnly}
                        isSortByName={false}
                        options={["NEW", "TRANSFERRED"].map((type) => ({
                            id: type,
                            name: t(type === "NEW" ? "new_student" : type),
                        }))}
                    />

                    <DatePicker
                        control={form.control}
                        name="join_date"
                        label={t("join date") + "*"}
                        isDisabled={viewOnly}
                    />

                    {/* <DatePicker
                        control={form.control}
                        name="leave_date"
                        isDisabled={viewOnly}
                    /> */}

                    <FormInput
                        control={form.control}
                        name="birth_cert_number"
                        label={t("birth cert number") + "*"}
                        disabled={viewOnly}
                    />

                    <FormInputInterger
                        control={form.control}
                        name="nric"
                        label={t("NRIC") + "*"}
                        max={12}
                        disabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="passport_number"
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="gender"
                        label={t("gender") + "*"}
                        isSortByName={false}
                        options={genderOptions.map((option) => ({
                            id: option.id,
                            name: capitalize(t(option.name)),
                        }))}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="race_id"
                        label={t("race") + "*"}
                        options={options.races}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="religion_id"
                        label={t("religion") + "*"}
                        options={options.religions}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="primary_school_id"
                        label={t("primary school") + "*"}
                        options={options.primarySchools}
                        isDisabled={viewOnly}
                    />

                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="birthplace"
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="nationality_id"
                        label={t("nationality") + "*"}
                        options={options.countries}
                        isDisabled={viewOnly}
                    />

                    <DatePicker
                        control={form.control}
                        name={"date_of_birth"}
                        label={t("date of birth") + "*"}
                        isDisabled={viewOnly}
                    />

                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="address"
                        label={t("address") + "*"}
                        isUpperCase={true}
                        disabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="address_2"
                        isUpperCase={true}
                        disabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="city"
                        label={t("city") + "*"}
                        isUpperCase={true}
                        disabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="country_id"
                        label={t("country") + "*"}
                        options={options.countries}
                        onChange={(val) => {
                            getStateList({ params: { country_id: val } });
                            form.setValue("state_id", "");
                        }}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="state_id"
                        label={t("state") + "*"}
                        options={stateList}
                        isLoading={isStatesLoading}
                        isDisabled={viewOnly}
                    />

                    <FormInput
                        control={form.control}
                        name="postal_code"
                        label={t("postal code") + "*"}
                        disabled={viewOnly}
                    />

                    <FormDivider />

                    {/* <FormInput
                        control={form.control}
                        name="email"
                        type="email"
                        label={t("email") + "*"}
                    /> */}

                    <FormPhoneInput
                        form={form}
                        name="phone_number"
                        label={t("phone number") + "*"}
                        disabled={viewOnly}
                    />

                    <FormPhoneInput
                        form={form}
                        name="phone_number_2"
                        disabled={viewOnly}
                    />

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="dietary_restriction"
                        label={t("dietary restriction") + "*"}
                        options={[
                            { id: "NONE", name: t("NONE") },
                            { id: "VEGETARIAN", name: t("VEGETARIAN") },
                        ]}
                        isDisabled={viewOnly}
                    />

                    <FormSelect
                        control={form.control}
                        name="health_concern_id"
                        label={t("health concern") + "*"}
                        options={options.healthConcerns}
                        isSortByName={false}
                        isDisabled={viewOnly}
                    />

                    <FormDivider />

                    <div className="lg:col-span-2">
                        <FormTextarea
                            control={form.control}
                            name="remarks"
                            disabled={viewOnly}
                        />
                    </div>

                    <FormDivider />

                    <FormFileInput
                        name="photo"
                        currentFileUrl={student?.photo}
                        register={form.register}
                        errors={form.formState.errors}
                        disabled={viewOnly}
                    />

                    <FormDivider />

                    <div className="lg:col-span-2">
                        <h3 className="mb-2.5 text-themeGreenDark">
                            {t("Guardians")}
                        </h3>
                        <GuardiansForm form={form} options={options} />
                    </div>
                    <FormDivider />

                    {!viewOnly && (
                        <div className="lg:col-span-2">
                            <Button
                                type="submit"
                                className="ml-auto mt-2 block w-32"
                            >
                                {t("Save")}
                            </Button>
                        </div>
                    )}
                </form>
            </Form>
            <Modal open={promptGenerate} onOpenChange={setPromptGenerate}>
                <>
                    <p className="mt-3 font-medium">
                        {t(
                            "Are you sure you want to generate new student number?"
                        )}
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setPromptGenerate(false)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button onClick={generateNewNumber}>
                            {t("Confirm")}
                        </Button>
                    </DialogFooter>
                </>
            </Modal>
        </div>
    );
};

export default StudentForm;
