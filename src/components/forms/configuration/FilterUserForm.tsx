import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    CONTRACTOR,
    EMPLOYEE,
    GUARDIAN,
    STUDENT,
    MERCHANT,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterUserForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            user_name: filter?.user_name ?? "",
            email: filter?.email ?? "",
            phone_number: filter?.phone_number ?? "",
            user_type: filter?.user_type ?? "",
            user_number: filter?.user_number ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name="user_name" />

                <FormInput control={form.control} name="email" type="email" />

                <FormPhoneInput form={form} name="phone_number" />

                <FormSelect
                    control={form.control}
                    name="user_type"
                    isSortByName={false}
                    options={[
                        STUDENT,
                        EMPLOYEE,
                        GUARDIAN,
                        CONTRACTOR,
                        MERCHANT,
                    ].map((type) => ({
                        id: type,
                        name: t(type),
                    }))}
                />

                <FormInput
                    control={form.control}
                    name="user_number"
                    label="student/employee number"
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: t("Active"),
                            id: "1",
                        },
                        {
                            name: t("Inactive"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterUserForm;
