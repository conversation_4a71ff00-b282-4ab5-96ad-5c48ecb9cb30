import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    OPERATOR_APP,
} from "@/lib/constant";
// import { useMerchants } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterTerminalForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            code: filter?.code ?? "",
            name: filter?.name ?? "",
            type: filter?.type ?? undefined,
            // merchant_id: filter?.merchant_id ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    // Disabled first: due to API did not support filter by merchant
    // const type = form.watch("type");
    // const { merchantOptions = [], isLoadingMerchants = false } = useMerchants({
    //     params:
    //         type === BOOKSHOP || type === CANTEEN
    //             ? { ...GET_ALL_PARAMS, type }
    //             : null,
    // });

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"code"} />

                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name="type"
                    isStringOptions={true}
                    options={[BOOKSHOP, CANTEEN, OPERATOR_APP]}
                />

                {/* {(type == BOOKSHOP || type == CANTEEN) && (
                    <FormSelect
                        control={form.control}
                        name={"merchant_id"}
                        label={"Merchant"}
                        options={merchantOptions}
                        isLoading={isLoadingMerchants}
                    />
                )} */}

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterTerminalForm;
