import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFormProps,
    DATE_FORMAT,
    GET_ALL_PARAMS,
    terminalAPI,
    terminalKeyAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError, toMyZonedTime, toUTC } from "@/lib/utils";

const TerminalKeyForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: posTerminalKey, axiosQuery: getPOSTerminalKey } = useAxios({
        api: terminalKeyAPI,
        locale,
        onError: props.close,
    });

    const { data: terminalOptions, axiosQuery: getTerminalOptions } = useAxios({
        api: terminalAPI,
        locale,
    });

    useEffect(() => {
        getTerminalOptions({ params: { ...GET_ALL_PARAMS } });
        if (!props.isCreate && props.id) {
            getPOSTerminalKey({ id: props.id });
        }
    }, []);

    return props.isCreate || posTerminalKey ? (
        <FormWrap
            posTerminalKey={posTerminalKey}
            terminalOptions={terminalOptions}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    posTerminalKey: any;
    terminalOptions: any;
};

const FormWrap = ({
    posTerminalKey,
    terminalOptions,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: posTerminalKey?.name ?? "",
            expires_at: posTerminalKey?.expires_at
                ? toMyZonedTime(posTerminalKey?.expires_at)
                : null,
            terminal_id: posTerminalKey?.terminal?.id ?? "",
        },
    });
    const t = useTranslations("common");

    const { axiosPost: createPOSTerminalKey, error: postError } = useAxios({
        api: terminalKeyAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updatePOSTerminalKey, error: putError } = useAxios({
        api: terminalKeyAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (data.expires_at) {
                data.expires_at = toUTC(data.expires_at, DATE_FORMAT.YMD_HMS);
            }

            if (isCreate) {
                createPOSTerminalKey(data);
            } else {
                updatePOSTerminalKey({ id: posTerminalKey.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {t("terminal key")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                    />

                    <FormSelect
                        control={form.control}
                        name="terminal_id"
                        label={t("terminal") + "*"}
                        options={terminalOptions}
                    />

                    <DateTimePicker control={form.control} name="expires_at" />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default TerminalKeyForm;
