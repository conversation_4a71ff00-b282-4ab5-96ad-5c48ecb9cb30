import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { groupBy, intersection, union } from "lodash";
import { useForm } from "react-hook-form";
import Select from "react-select";
import { Button } from "@/components/base-ui/button";
import { Checkbox } from "@/components/base-ui/checkbox";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFormProps,
    EMPLOYEE,
    GET_ALL_PARAMS,
    GUARDIAN,
    GUEST,
    MERCHANT,
    permissionsAPI,
    roleAPI,
    selectStyles,
    STUDENT,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { replaceAll, showBackendFormError, strStartCase } from "@/lib/utils";

const RoleForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: role, axiosQuery: getRole } = useAxios({
        api: roleAPI,
        locale,
        onError: props.close,
    });

    const { data: permissions, axiosQuery: getPermissions } = useAxios({
        api: permissionsAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getPermissions({ params: GET_ALL_PARAMS });
        if (!props.isCreate && props.id) {
            getRole({ id: props.id });
        }
    }, []);

    return props.isCreate || role ? (
        <FormWrap permissions={permissions} role={role} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    permissions: any;
    role: any;
};

const FormWrap = ({
    role,
    permissions,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const groupedPermissions = groupBy(permissions, "category");
    const categories = Object.keys(groupedPermissions);
    const categoryOptions = categories.map((category) => ({
        label: strStartCase(replaceAll(category, "-", " ")),
        value: category,
    }));

    const t = useTranslations("common");

    function getPermissionSubcategoryOptions(category: string) {
        return groupBy(groupedPermissions[category], "sub_category");
    }

    const form = useForm<any>({
        defaultValues: {
            name: role?.name ?? "",
            default_models: role?.default_models ?? [],
            permission_ids:
                role?.permissions?.map((permission) => permission.id) ?? [],
        },
    });

    console.log("form values", form.getValues());

    const [selectedCategory, setSelectedCategory] = useState<string>(
        categories[0]
    );

    const { axiosPost: createRole, error: postError } = useAxios({
        api: roleAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateRole, error: putError } = useAxios({
        api: roleAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onCheckSubcategory(checked: boolean, checkedIds: any[]) {
        const currentPermissionIds: (number | string)[] =
            form.getValues("permission_ids");
        if (checked) {
            form.setValue(
                "permission_ids",
                union(currentPermissionIds, checkedIds)
            );
        } else {
            form.setValue(
                "permission_ids",
                currentPermissionIds.filter(
                    (id: number | string) => !checkedIds.includes(id)
                )
            );
        }
    }

    function onCheckPermission(checked: boolean, checkedId: number | string) {
        {
            const currentPermissionIds = form.getValues("permission_ids");
            if (checked) {
                form.setValue("permission_ids", [
                    ...currentPermissionIds,
                    checkedId,
                ]);
            } else {
                form.setValue(
                    "permission_ids",
                    currentPermissionIds.filter(
                        (id: number) => id !== checkedId
                    )
                );
            }
        }
    }

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            console.log(data);
            if (isCreate) {
                createRole(data);
            } else {
                updateRole({ id: role.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {t("role")}
            </h2>

            <Form {...form}>
                <form onSubmit={onSubmit} className="grid-form">
                    <FormInput
                        control={form.control}
                        name={"name"}
                        label={t("name") + "*"}
                    />
                    <FormSelect
                        isMulti
                        control={form.control}
                        name={"default_models"}
                        label={t("system default role")}
                        options={[
                            STUDENT,
                            EMPLOYEE,
                            GUARDIAN,
                            MERCHANT,
                            GUEST,
                        ].map((type) => ({
                            id: type,
                            name: t(type),
                        }))}
                    />

                    <div className="border-t border-dashed pt-3 lg:col-span-2">
                        <p className="mb-1.5 font-medium capitalize text-themeLabel">
                            {t("permissions")}*
                        </p>
                        <div className="max-w-sm">
                            <Select
                                isClearable
                                options={categoryOptions}
                                value={
                                    categoryOptions?.find(
                                        (option) =>
                                            option.value === selectedCategory
                                    ) ?? null
                                }
                                placeholder={t("Select Permission Category")}
                                onChange={(selectedOption: any) => {
                                    setSelectedCategory(
                                        selectedOption?.value ?? null
                                    );
                                }}
                                styles={selectStyles}
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-x-5 gap-y-5 px-1 lg:col-span-2">
                        {Object.entries(
                            getPermissionSubcategoryOptions(selectedCategory)
                        ).map(([subcategory, items]) => (
                            <div key={subcategory}>
                                <div className="mb-2 flex w-fit items-start space-x-1.5 lg:space-x-2">
                                    <Checkbox
                                        isOutlined
                                        className="border-opacity-40 lg:mt-0.5"
                                        checked={
                                            intersection(
                                                form.watch("permission_ids"),
                                                items.map((item) => item.id)
                                            ).length === items.length
                                        }
                                        onCheckedChange={(checked: boolean) =>
                                            onCheckSubcategory(
                                                checked,
                                                items.map((item) => item.id)
                                            )
                                        }
                                    />
                                    <div className="c-text-size border-b pb-1 font-medium capitalize text-themeLabel">
                                        {replaceAll(subcategory, "-", " ")}
                                    </div>
                                </div>
                                <div className="grid gap-y-2">
                                    {items.map((item) => (
                                        <div
                                            className="flex items-start space-x-1.5 lg:space-x-2"
                                            key={item?.id}
                                        >
                                            <Checkbox
                                                className="lg:mt-0.5"
                                                checked={form
                                                    .watch("permission_ids")
                                                    ?.includes(item?.id)}
                                                onCheckedChange={(
                                                    checked: boolean
                                                ) =>
                                                    onCheckPermission(
                                                        checked,
                                                        item?.id
                                                    )
                                                }
                                            />
                                            <div className="c-text-size capitalize">
                                                {replaceAll(
                                                    item?.name,
                                                    "-",
                                                    " "
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="lg:col-span-2">
                        <Button
                            type="submit"
                            className={clsx(
                                "ml-auto",
                                selectedCategory ? "mt-5" : "mt-32"
                            )}
                        >
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default RoleForm;
