import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { capitalize, isArray } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import FormInput from "@/components/ui/FormInput";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FormSelect from "@/components/ui/FormSelect";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    roleAPI,
    userAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    isProperPhoneNumber,
    isValueTrue,
    optionUserLabel,
    showBackendFormError,
} from "@/lib/utils";

const UserForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: userData, axiosQuery: getUserData } = useAxios({
        api: userAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (props.id) {
            getUserData({ id: props.id });
        }
    }, []);

    return userData ? (
        <FormWrap userData={userData} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    userData: any;
};

const FormWrap = ({ userData, refresh, close }: FormWrapProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const directUserables = isArray(userData?.direct_userables)
        ? userData?.direct_userables.map(
              (userable) =>
                  optionUserLabel(
                      userable?.number,
                      userable?.translations?.name
                  ) + ` (${userable?.user_type_description})`
          )
        : [];

    const accessibleStudentUserables = isArray(
        userData?.accessible_student_userables
    )
        ? userData?.accessible_student_userables.map(
              (userable) =>
                  optionUserLabel(
                      userable?.number,
                      userable?.translations?.name
                  ) + ` (${userable?.user_type_description})`
          )
        : [];

    const form = useForm({
        defaultValues: {
            email: userData?.email ?? "",
            phone_number: userData?.phone_number ?? "",
            password: "",
            password_confirmation: "",
            is_active: isValueTrue(userData?.is_active),
            role_ids: userData?.roles.map((role) => role.id) ?? [],
        },
    });

    const { data: roleOptions, axiosQuery: getRoleOptions } = useAxios({
        api: roleAPI,
        locale,
        onError: close,
    });

    const { axiosPut: updateUserData, error: putError } = useAxios({
        api: userAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (!isProperPhoneNumber(form, "phone_number", data.phone_number)) {
                return;
            }
            updateUserData({ id: userData.id, data });
        })();
    }

    useEffect(() => {
        getRoleOptions({ params: GET_ALL_PARAMS });
    }, []);

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <div>
            <h2 className="mb-3">
                {t("Update ")}
                {capitalize(t("user"))}
            </h2>
            <div className="grid gap-y-2">
                <div className="pt-1">
                    <Label>{t("User Info")}</Label>
                    <ul>
                        {directUserables.length > 0
                            ? directUserables.map((user, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{user}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                </div>
                <div className="pt-1">
                    <Label>{t("Access Students")}</Label>
                    <ul>
                        {accessibleStudentUserables.length > 0
                            ? accessibleStudentUserables.map((user, index) => (
                                  <li key={index} className="table-item-li">
                                      <span>{user}</span>
                                  </li>
                              ))
                            : "-"}
                    </ul>
                </div>
            </div>
            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-5 grid gap-y-5">
                    <FormInput control={form.control} name="email" />
                    <FormPhoneInput form={form} name="phone_number" />
                    <FormSelect
                        isMulti
                        control={form.control}
                        name="role_ids"
                        label="roles"
                        options={roleOptions}
                    />

                    <FormInput
                        control={form.control}
                        name="password"
                        type="password"
                        label={t("New Password")}
                    />

                    <FormInput
                        control={form.control}
                        name="password_confirmation"
                        type="password"
                        label={t("New Password Confirmation")}
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="status"
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default UserForm;
