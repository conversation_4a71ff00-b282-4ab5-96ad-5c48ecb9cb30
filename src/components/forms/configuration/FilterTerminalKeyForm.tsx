import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    terminalAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";

const FilterTerminalKeyForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const t = useTranslations("common");
    const locale = useLocale();
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            terminal_id: filter?.terminal_id ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const { data: terminalOptions, axiosQuery: getTerminalOptions } = useAxios({
        api: terminalAPI,
        locale,
    });

    useEffect(() => {
        getTerminalOptions({ params: { ...GET_ALL_PARAMS } });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name="terminal_id"
                    label={t("terminal")}
                    options={terminalOptions}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterTerminalKeyForm;
