import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    BOOKSHOP,
    CANTEEN,
    CommonFormProps,
    GET_ALL_PARAMS,
    OPERATOR_APP,
    terminalAPI,
} from "@/lib/constant";
import { useAxios, useMerchants } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const TerminalForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: currentTerminal, axiosQuery: getCurrentTerminal } = useAxios({
        api: terminalAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCurrentTerminal({ id: props.id });
        }
    }, []);

    return props.isCreate || currentTerminal ? (
        <FormWrap currentTerminal={currentTerminal} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentTerminal: any;
};

const FormWrap = ({
    currentTerminal,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            code: currentTerminal?.code ?? "",
            name: currentTerminal?.name ?? "",
            type: currentTerminal?.type ?? "",
            merchant_id: currentTerminal?.merchant?.id ?? "",
        },
    });
    const t = useTranslations("common");

    const { axiosPost: createTerminal, error: postError } = useAxios({
        api: terminalAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateTerminal, error: putError } = useAxios({
        api: terminalAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createTerminal(data);
            } else {
                updateTerminal({ id: currentTerminal.id, data });
            }
        })();
    }

    const type = form.watch("type");
    const { merchantOptions = [], isLoadingMerchants = false } = useMerchants({
        params:
            type === BOOKSHOP || type === CANTEEN
                ? { ...GET_ALL_PARAMS, type }
                : null,
    });

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Terminal</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name={"code"}
                        label={t("code") + "*"}
                    />

                    <FormInput
                        control={form.control}
                        name={"name"}
                        label={t("name") + "*"}
                    />

                    <FormSelect
                        control={form.control}
                        name="type"
                        label={t("type") + "*"}
                        options={[
                            { id: BOOKSHOP, name: t(BOOKSHOP) },
                            { id: CANTEEN, name: t(CANTEEN) },
                            { id: OPERATOR_APP, name: t(OPERATOR_APP) },
                        ]}
                    />

                    {(type == BOOKSHOP || type == CANTEEN) && (
                        <FormSelect
                            control={form.control}
                            name={"merchant_id"}
                            label={t("merchant")}
                            options={merchantOptions}
                            isLoading={isLoadingMerchants}
                        />
                    )}

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default TerminalForm;
