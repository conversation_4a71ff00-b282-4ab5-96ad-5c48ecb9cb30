import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { format, parse } from "date-fns";
import { Controller, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form, FormLabel } from "@/components/base-ui/form";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import { TimePicker } from "@/components/ui/TimePicker";
import {
    CommonFormProps,
    DATE_FORMAT,
    daysOfWeek,
    employeeSessionsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import StatusFormSwitch from "../../ui/StatusFormSwitch";

const EmployeeSessionForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: employeeSession, axiosQuery: getEmployeeSession } = useAxios({
        api: employeeSessionsAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getEmployeeSession({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || employeeSession) ? (
        <FormWrap
            employeeSession={employeeSession}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    employeeSession: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    employeeSession,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const t_daysOfWeek = useTranslations("daysOfWeek");

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: employeeSession?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            employee_session_settings: daysOfWeek.map((day) => {
                const session =
                    employeeSession?.employee_session_settings?.find(
                        (s) => s.day === day.value
                    );
                return {
                    day: day.value,
                    start_time: session?.start_time
                        ? parse(
                              session.start_time,
                              DATE_FORMAT.timeHms,
                              new Date()
                          )
                        : parse("00:00:00", DATE_FORMAT.timeHms, new Date()),
                    end_time: session?.end_time
                        ? parse(
                              session.end_time,
                              DATE_FORMAT.timeHms,
                              new Date()
                          )
                        : parse("00:00:00", DATE_FORMAT.timeHms, new Date()),
                };
            }),
            is_active: isCreate ? true : employeeSession?.is_active ?? false,
            determine_attendance_status:
                employeeSession?.determine_attendance_status ?? false,
        },
    });

    const handleAllSameWithMonday = () => {
        const mondaySettings = form.getValues("employee_session_settings.0");

        daysOfWeek.forEach((day, index) => {
            if (index !== 0) {
                form.setValue(
                    `employee_session_settings.${index}.start_time`,
                    mondaySettings.start_time
                );
                form.setValue(
                    `employee_session_settings.${index}.end_time`,
                    mondaySettings.end_time
                );
            }
        });
    };

    const { axiosPost: createEmployeeSession, error: postError } = useAxios({
        api: employeeSessionsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateEmployeeSession, error: putError } = useAxios({
        api: employeeSessionsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            const formattedSessionSettings = data.employee_session_settings.map(
                (session) => ({
                    ...session,
                    start_time: session.start_time
                        ? format(session.start_time, DATE_FORMAT.timeHms)
                        : undefined,
                    end_time: session.end_time
                        ? format(session.end_time, DATE_FORMAT.timeHms)
                        : undefined,
                })
            );

            const formattedData = {
                ...data,
                employee_session_settings: formattedSessionSettings,
            };

            if (isCreate) {
                createEmployeeSession(formattedData);
            } else {
                updateEmployeeSession({
                    id: employeeSession.id,
                    data: formattedData,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("employee session")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="space-y-4 lg:min-w-[600px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <FormCheckbox
                        control={form.control}
                        name="determine_attendance_status"
                        label={t("determine attendance status")}
                        textStyleClass="text-gray-500 font-medium"
                        styleClass="ml-0.5"
                    />

                    <div className="lg:max-w-xl">
                        {daysOfWeek.map((day, index) => (
                            <div
                                key={day.value}
                                className="mb-1 flex items-center gap-5"
                            >
                                <FormLabel className="min-w-[120px] capitalize">
                                    {t_daysOfWeek(day.label)}
                                </FormLabel>
                                <Controller
                                    name={`employee_session_settings.${index}.start_time`}
                                    control={form.control}
                                    render={({ field }) => (
                                        <div className="flex h-12 min-w-[120px] items-center justify-center">
                                            <TimePicker
                                                date={field.value}
                                                setDate={field.onChange}
                                            />
                                        </div>
                                    )}
                                />
                                -
                                <Controller
                                    name={`employee_session_settings.${index}.end_time`}
                                    control={form.control}
                                    render={({ field }) => (
                                        <div className="flex h-12 min-w-[120px] items-center justify-center">
                                            <TimePicker
                                                date={field.value}
                                                setDate={field.onChange}
                                            />
                                        </div>
                                    )}
                                />
                            </div>
                        ))}
                    </div>

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={t("status")}
                    />

                    <div className="mb-2 ml-auto flex justify-end gap-x-3">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleAllSameWithMonday}
                        >
                            {t("All same with Monday Setting")}
                        </Button>

                        <Button type="submit">{t("Submit")}</Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default EmployeeSessionForm;
