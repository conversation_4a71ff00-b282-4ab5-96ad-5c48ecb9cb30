import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray, lowerCase } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import InfoCard from "@/components/ui/InfoCard";
import {
    hostelDisciplinaryRecordsAPI,
    DATE_FORMAT,
    FULL,
    studentAPI,
    TableColumnType,
    rewardPunishmentSettingsAPI,
    DRAFT,
    rewardPunishmentRecordsAPI,
    CONFIRMED,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import {
    combinedNames,
    combineSemesterClass,
    displayDateTime,
    formatWithBrackets,
    getTableSelection,
    optionUserLabel,
    refreshForUpdate,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import ActionDropdown from "../../ui/ActionDropdown";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "../../base-ui/dropdown-menu";
import { useLanguages, useUserProfile } from "../../../lib/store";
import Modal from "../../ui/Modal";
import DeletePrompt from "../../ui/DeletePrompt";
import { DatePicker } from "@/components/ui/DatePicker";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import RewardPunishmentRecordForm from "./RewardPunishmentRecordForm";
import FilterRewardPunishmentRecordForm from "./FilterRewardPunishmentRecordForm";
import clsx from "clsx";
import RewardPunishmentRecordBulkChangeStatusForm from "./RewardPunishmentRecordBulkChangeStatusForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const CreateHostelDisciplinaryRecordForm = () => {
    useCheckViewPermit("hostel-reward-punishment-record-create");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const form = useForm<any>({
        defaultValues: {
            _select_student: null,
            student_id: "",
            date: new Date(),
            reward_punishment_id: "",
            average_exam_marks: 0,
            conduct_marks: 0,
            display_in_report_card: false,
            status: DRAFT,
        },
    });

    const { loadAsyncOptions: loadStudentAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
        params: {
            response: FULL,
            includes: [
                "currentSemesterPrimaryClass.semesterSetting",
                "currentSemesterPrimaryClass.semesterClass.classModel",
            ],
        },
        optionFormatter: (item) => ({
            value: item?.id,
            photo: item?.photo,
            name: combinedNames(item?.translations?.name),
            student_number: item?.student_number,
            class: combineSemesterClass(item?.current_primary_class),
            label: optionUserLabel(
                item?.student_number,
                item?.translations?.name
            ),
        }),
    });

    const { asyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: rewardPunishmentSettingsAPI,
        params: { includes: ["meritDemeritSettings"] },
        optionFormatter: (item) => ({
            value: item?.id,
            label: `${item?.name} ${formatWithBrackets(item?.merit_demerit_settings?.[0]?.name)}`,
            data: {
                conduct_marks: item?.conduct_marks,
                average_exam_marks: item?.average_exam_marks,
                display_in_report_card: item?.display_in_report_card,
            },
        }),
    });

    function reset() {
        form.clearErrors();
        form.reset();
        setSelection([]);
    }

    const { axiosPost: createRewardPunishmentRecord, error: postError } =
        useAxios({
            api: rewardPunishmentRecordsAPI,
            onSuccess: () => {
                fetchRewardPunishmentRecords();
                reset();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.date = toYMD(data.date);
            data.student_ids = [selectedStudent.value];
            createRewardPunishmentRecord(data);
        })();
    }

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
    });
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);
    const [openBulkChangeStatus, setOpenBulkChangeStatus] = useState(false);
    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const [selectedStudent, setSelectedStudent] = useState<any>();

    const [navigatedResults, setNavigatedResults] = useState<any[]>([]);
    const [selection, setSelection] = useState<any[]>([]);

    const { data, axiosQuery: getRewardPunishmentRecords } = useAxios({
        api: rewardPunishmentRecordsAPI,
        locale,
        onSuccess: (result) => {
            setNavigatedResults([...navigatedResults, ...result.data]);
            setPagination(result?.pagination);
        },
    });

    function closeForm() {
        setTargetId(null);
    }

    function definedData() {
        return isArray(data)
            ? data.map((item) => {
                  return {
                      id: item?.id,
                      class: item?.student_class?.semester_class?.class_model
                          ?.name?.[locale],
                      merit_demerit_settings:
                          item?.reward_punishment?.merit_demerit_settings
                              ?.map(
                                  (setting) =>
                                      setting.translations?.name?.[locale]
                              )
                              .join(", "),
                      reward_punishment_name:
                          item?.reward_punishment?.translations?.name?.[
                              locale
                          ] ?? item?.reward_punishment?.name,
                      average_exam_marks: item?.average_exam_marks,
                      conduct_marks: item?.conduct_marks,
                      display_in_report_card: lowerCase(
                          item?.display_in_report_card ? "YES" : "NO"
                      ),
                      date: displayDateTime(item?.date, DATE_FORMAT.DMY),
                      status: item?.status ?? "-",
                      notification_sent_at: displayDateTime(
                          item?.notification_sent_at,
                          DATE_FORMAT.DMY
                      ),
                  };
              })
            : [];
    }

    const handleRewardPunishmentChange = (item) => {
        if (!item) {
            form.setValue("reward_punishment_id", "");
            form.setValue("average_exam_marks", 0);
            form.setValue("conduct_marks", 0);
            form.setValue("display_in_report_card", false);
            return;
        }

        form.setValue("average_exam_marks", item.data?.average_exam_marks);
        form.setValue("conduct_marks", item.data?.conduct_marks);
        form.setValue(
            "display_in_report_card",
            item.data?.display_in_report_card
        );
    };

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(selectedIds, navigatedResults);
        setSelection(_selection);
    }

    function fetchRewardPunishmentRecords(isRefresh = false) {
        getRewardPunishmentRecords({
            params: {
                order_by: { id: "desc" },
                ...filter,
                ...(isRefresh ? { page: 1 } : {}),
                student_id: selectedStudent?.value,
                includes: [
                    "studentLatestPrimaryClasses",
                    "rewardPunishment.meritDemeritSettings",
                ],
            },
        });
    }

    useEffect(() => {
        if (selectedStudent) {
            fetchRewardPunishmentRecords();
        }
    }, [selectedStudent, filter]);

    useEffect(() => {
        if (data && activeLanguages) {
            const _columns: TableColumnType[] = [
                {
                    key: "date",
                    hasSort: true,
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
                {
                    key: "class",
                },
                {
                    key: "merit_demerit_settings",
                    displayAs: "Merit/ Demerit",
                    modify: (value) => (
                        <div className="min-w-[110px]">{value}</div>
                    ),
                },
                {
                    key: "reward_punishment_name",
                    displayAs: "Reward/ Punishment",
                    hasSort: true,
                },
                {
                    key: "average_exam_marks",
                    hasSort: true,
                },
                {
                    key: "conduct_marks",
                    hasSort: true,
                },
                {
                    key: "display_in_report_card",
                    hasSort: true,
                    modify: (value) => (
                        <div className={`cell-status ${value}`}>{value}</div>
                    ),
                },
                {
                    key: "status",
                    hasSort: true,
                    modify: (value) => (
                        <span
                            className={clsx(
                                value === CONFIRMED && "text-green-600",
                                value === DRAFT && "text-yellow-500"
                            )}
                        >
                            {capitalize(value)}
                        </span>
                    ),
                },
                {
                    key: "notification_sent_at",
                    modify(value, cell) {
                        return (
                            <span className="whitespace-nowrap text-[14px]">
                                {value}
                            </span>
                        );
                    },
                },
            ];
            setColumns(_columns);
        }
    }, [data, activeLanguages]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const t = useTranslations("common");

    return (
        <Card styleClass="max-w-screen-2xl mx-auto">
            <div className="pb-7 lg:px-2">
                <h2 className="mb-5 pt-2">Create Reward/ Punishment Record</h2>
                <div className="flex items-center gap-x-3">
                    <FreeSelectAsync
                        control={form.control}
                        name="student_number"
                        placeholder={"Type to search student"}
                        minWidth={300}
                        hasLabel={false}
                        loadOptions={loadStudentAsyncOptions}
                        value={selectedStudent}
                        onChange={(option) => {
                            reset();
                            setSelectedStudent(option);
                            form.setValue(
                                "student_number",
                                option?.student_number
                            );
                        }}
                    />
                </div>

                {selectedStudent && (
                    <>
                        <div className="mb-3 pt-3">
                            {selectedStudent?.photo && (
                                <div className="mb-4 flex flex-wrap items-end gap-5">
                                    <img
                                        src={selectedStudent?.photo}
                                        className="h-auto w-40 rounded-sm"
                                    />
                                    <div className="mb-2">
                                        <InfoCard
                                            noBorder={true}
                                            data={{
                                                student_number:
                                                    selectedStudent?.student_number,
                                                name: selectedStudent?.name,
                                                class: selectedStudent?.class,
                                            }}
                                        />
                                    </div>
                                </div>
                            )}
                            <Form {...form}>
                                <form
                                    onSubmit={onSubmit}
                                    className="grid-form mt-5 border-dashed pt-4"
                                >
                                    <DatePicker
                                        control={form.control}
                                        name="date"
                                        label="date*"
                                    />

                                    <FormCheckbox
                                        control={form.control}
                                        name="display_in_report_card"
                                        label="Display in Report Card"
                                        styleClass="mt-10"
                                        textStyleClass="text-gray-500 font-medium"
                                    />

                                    <div className="lg:col-span-2">
                                        <FreeSelectAsync
                                            control={form.control}
                                            name="reward_punishment_id"
                                            label="Reward/Punishment*"
                                            minWidth={300}
                                            loadOptions={loadAsyncOptions}
                                            value={
                                                form.getValues(
                                                    "reward_punishment_id"
                                                )
                                                    ? asyncOptions.find(
                                                          (option) =>
                                                              option.value ===
                                                              form.getValues(
                                                                  "reward_punishment_id"
                                                              )
                                                      )
                                                    : null
                                            }
                                            onChange={(option) => {
                                                handleRewardPunishmentChange(
                                                    option
                                                );
                                            }}
                                            error={
                                                form.formState.errors
                                                    ?.reward_punishment_id
                                            }
                                        />
                                    </div>
                                    <FormInput
                                        control={form.control}
                                        type="number"
                                        name="average_exam_marks"
                                        label="Exam Total Average Marks*"
                                    />

                                    <FormInput
                                        control={form.control}
                                        type="number"
                                        name="conduct_marks"
                                        label="Conduct Marks*"
                                    />

                                    <div className="pb-5 lg:col-span-2">
                                        <Button
                                            type="submit"
                                            className="ml-auto mt-1 w-32"
                                        >
                                            Submit
                                        </Button>
                                    </div>
                                </form>
                            </Form>
                        </div>

                        <div className="border-t border-dashed pt-3">
                            <div className="table-page-top">
                                <h2 className="mr-auto capitalize">
                                    Reward/ Punishment Records
                                </h2>

                                {hasPermit(
                                    "reward-punishment-record-update"
                                ) && (
                                    <Button
                                        variant="outline"
                                        size="smallerOnMobile"
                                        disabled={selection?.length < 1}
                                        onClick={() =>
                                            setOpenBulkChangeStatus(true)
                                        }
                                    >
                                        Bulk Change Status
                                    </Button>
                                )}

                                {/* <TableFilterBtn
                                    filter={filter}
                                    onClick={() => setOpenFilter(true)}
                                /> */}
                                <FilterChevronButton />
                            </div>

                            <FilterFormWrapper>
                                <FilterRewardPunishmentRecordForm
                                    hideFilterByStudent={true}
                                    filter={filter}
                                    setFilter={setFilter}
                                    close={() => setOpenFilter(false)}
                                />
                            </FilterFormWrapper>

                            <DataTable
                                onMultiSelect={onMultiSelect}
                                columns={columns}
                                data={definedData()}
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                sorted={filter?.order_by}
                                sort={onSort}
                                actionMenu={({ cell }) => {
                                    const id = cell.row.original.id;
                                    const status = cell.row.original.status;
                                    return (
                                        <>
                                            <ActionDropdown>
                                                {status.toLowerCase() ===
                                                    DRAFT.toLowerCase() &&
                                                hasPermit(
                                                    "reward-punishment-record-update"
                                                ) ? (
                                                    <DropdownMenuItem
                                                        className="c-text-size"
                                                        onClick={() =>
                                                            setTargetId(id)
                                                        }
                                                    >
                                                        Edit
                                                    </DropdownMenuItem>
                                                ) : (
                                                    <DropdownMenuItem
                                                        className="c-text-size"
                                                        onClick={() => {
                                                            setTargetId(id);
                                                        }}
                                                    >
                                                        View
                                                    </DropdownMenuItem>
                                                )}
                                                {status.toLowerCase() ===
                                                    DRAFT.toLowerCase() &&
                                                    hasPermit(
                                                        "reward-punishment-record-delete"
                                                    ) && (
                                                        <>
                                                            <DropdownMenuSeparator />
                                                            <DropdownMenuItem
                                                                className="c-text-size text-red-600"
                                                                onClick={() =>
                                                                    setTargetDeleteId(
                                                                        id
                                                                    )
                                                                }
                                                            >
                                                                Delete
                                                            </DropdownMenuItem>
                                                        </>
                                                    )}
                                            </ActionDropdown>
                                        </>
                                    );
                                }}
                            />
                        </div>
                    </>
                )}

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId}>
                    <RewardPunishmentRecordForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* bulk change status */}
                <Modal
                    open={openBulkChangeStatus}
                    onOpenChange={setOpenBulkChangeStatus}
                >
                    <RewardPunishmentRecordBulkChangeStatusForm
                        records={selection}
                        refresh={() => {
                            refreshForUpdate(filter, setFilter);
                            setSelection([]);
                        }}
                        close={() => setOpenBulkChangeStatus(false)}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={hostelDisciplinaryRecordsAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={() => {
                            fetchRewardPunishmentRecords(true);
                        }}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterRewardPunishmentRecordForm
                        hideFilterByStudent={true}
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </div>
        </Card>
    );
};

export default CreateHostelDisciplinaryRecordForm;
