import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    CONFIRMED,
    DRAFT,
    POSTED,
    rewardPunishmentRecordsBulkChangeStatusAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import { Button } from "../../base-ui/button";

const RewardPunishmentRecordBulkChangeStatusForm = (props) => {
    const form = useForm({
        defaultValues: { status: "" },
    });

    const { axiosPutInBulk: bulkUpdateStatus, error: putError } = useAxios({
        api: rewardPunishmentRecordsBulkChangeStatusAPI,
        onSuccess: () => {
            props.close();
            props.refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            const rewardPunishmentRecordIds = props.records.map(
                (record: any) => record.id
            );

            data.reward_punishment_record_ids = rewardPunishmentRecordIds;
            bulkUpdateStatus(data);
            props.close();
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">Bulk Change Status</h2>
            <form onSubmit={onSubmit} className="grid gap-y-5">
                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[DRAFT, POSTED, CONFIRMED]}
                />

                <Button type="submit" className="mb-8 ml-auto mt-2">
                    Submit
                </Button>
            </form>
        </Form>
    );
};

export default RewardPunishmentRecordBulkChangeStatusForm;
