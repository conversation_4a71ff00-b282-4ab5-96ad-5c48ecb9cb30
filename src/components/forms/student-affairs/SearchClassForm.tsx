import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    GET_ALL_PARAMS,
    semesterSettingAPI,
    semesterClassesAPI,
    semesterClassPrimaryDropdownFilter,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";

type ExtraFields = {
    defaultValues: Record<string, any>;
    renderFields: (form: any) => JSX.Element;
};

type SearchClassFormProps = {
    locale: string;
    onClassSelect: (classId: string, className?: string) => void;
    extraFields?: ExtraFields | null;
};

const SearchClassForm = ({
    locale,
    onClassSelect,
    extraFields = null,
}: SearchClassFormProps) => {
    const form = useForm({
        defaultValues: {
            semester_setting_id: "",
            semester_class_id: "",
            ...extraFields?.defaultValues,
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );
            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
            setSemesterOptions(response.data);
        },
    });

    const { data: semesterClasses, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
    });

    const classes =
        semesterClasses?.map((data) => ({
            id: data.id,
            name: data?.class_model?.name,
        })) || [];

    const { initLoader } = useSubmit();

    function onFilter(e) {
        e.preventDefault();
        initLoader(
            form.handleSubmit((data) => {
                const className = classes.find(
                    (classData) => classData.id == data.semester_class_id
                )?.name;
                const selectedSemester =
                    semesterOptions.find(
                        (item) => item.id === data.semester_setting_id
                    )?.name ?? "-";

                onClassSelect(
                    data.semester_class_id,
                    selectedSemester + " - " + className
                );
            })
        );
    }

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                    // is_active: 1,
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    useEffect(() => {
        getSemesterOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    semester_year: "desc",
                },
            },
        });
    }, [locale]);

    return (
        <Form {...form}>
            <form
                className="mb-5 grid items-end gap-x-3 gap-y-4 lg:grid-cols-3"
                onSubmit={onFilter}
            >
                <FormSelect
                    control={form.control}
                    name="semester_setting_id"
                    label="semester*"
                    options={semesterOptions}
                    isSortByName={false}
                    onChange={() => {
                        form.setValue("semester_class_id", "");
                    }}
                />

                <FormSelect
                    control={form.control}
                    name="semester_class_id"
                    label="semester class*"
                    options={classes}
                    isDisabled={!form.watch("semester_setting_id")}
                />

                <Button
                    variant="outline"
                    type="submit"
                    disabled={
                        !form.watch("semester_setting_id") ||
                        !form.watch("semester_class_id")
                    }
                >
                    Filter
                </Button>

                {extraFields && extraFields.renderFields(form)}
            </form>
        </Form>
    );
};

export default SearchClassForm;
