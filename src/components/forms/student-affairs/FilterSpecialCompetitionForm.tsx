import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    departmentAPI,
    GET_ALL_PARAMS,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { combinedNames, toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import { axiosInstance } from "@/lib/api";

const FilterSpecialCompetitionForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const [students, setStudents] = useState<any[]>([]);
    const [studentName, setStudentName] = useState();
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            date: filter?.date ?? "",
            department_id: filter?.department_id ?? undefined,
            student_number: filter?.student_number ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        const _filter: any = { ...filter, ...data, page: 1 };
        if (data.student_number && studentName) {
            _filter.studentName = studentName;
        }
        if (data.date) {
            _filter.date = toYMD(data.date);
        }
        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            per_page: filter?.per_page,
            page: 1,
        });
        close();
    }

    const { data: departmentOptions, axiosQuery: getDepartmentOptions } =
        useAxios({
            api: departmentAPI,
            locale,
            onError: () => close(),
        });

    useEffect(() => {
        if (filter?.student_number && filter?.studentName) {
            loadStudentOptions(filter.studentName, setStudents);
        }
        getDepartmentOptions({
            params: {
                ...GET_ALL_PARAMS,
                is_active: 1,
                order_by: { sequence: "desc" },
            },
        });
    }, [locale]);

    const { handleError: handleStudentError } = useAxios({ api: studentAPI });

    const loadStudentOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(studentAPI, {
                    params: {
                        common_search: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: `${item.student_number} - ${combinedNames(item?.translations?.name)}`,
                        value: item.student_number,
                        name: item.name,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleStudentError(error);
                });
        } else {
            callback([]);
        }
    };

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name="department_id"
                    label="Department*"
                    options={departmentOptions}
                    isSortByName={false}
                />

                <FormSelectAsync
                    control={form.control}
                    name="student_number"
                    label="Student"
                    loadOptions={loadStudentOptions}
                    value={students.find(
                        (option) =>
                            option.value === form.watch("student_number")
                    )}
                    onChange={(val) => setStudentName(val?.name)}
                />

                <FormInput control={form.control} name={"name"} />
                <DatePicker control={form.control} name="date" />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSpecialCompetitionForm;
