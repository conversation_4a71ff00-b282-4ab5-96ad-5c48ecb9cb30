import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { chunk, flatten, indexOf } from "lodash";
import { X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import { DatePicker } from "@/components/ui/DatePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FreeInputDecimal from "@/components/ui/FreeInputDecimal";
import FreeSelect from "@/components/ui/FreeSelect";
import Modal from "@/components/ui/Modal";
import PaginatedTableFilter from "@/components/ui/PaginatedTableFilter";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    awardAPI,
    CHUNKED_FILTER_PARAMS,
    CommonFormProps,
    departmentAPI,
    GET_ALL_PARAMS,
    specialCompetitionAPI,
    STUDENT,
} from "@/lib/constant";
import { useAxios, usePaginatedTable, useSubmit } from "@/lib/hook";
import { combinedNames, showBackendFormError, toYMD } from "@/lib/utils";

const CompetitionSpecialActivityForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: currentData, axiosQuery: getCompetition } = useAxios({
        api: specialCompetitionAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCompetition({ id: props.id });
        }
    }, []);

    return props.isCreate || currentData ? (
        <FormWrap currentData={currentData} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
};

const FormWrap = ({
    isCreate = false,
    currentData,
    refresh,
    close,
}: FormWrapProps) => {
    const locale = useLocale();

    const form = useForm({
        defaultValues: {
            name: currentData?.name ?? "",
            department_id: currentData?.department?.id ?? "",
            date: currentData?.date ?? null,
            records:
                currentData?.records?.map((record) => ({
                    award_id: record?.award?.id ?? "",
                    type_of_bonus: record?.type_of_bonus ?? "",
                    id: record.student.id ?? "",

                    student_number: record?.student?.student_number ?? "-",
                    name: combinedNames(record?.student?.translations?.name),
                    mark: record?.mark,
                })) ?? [],
        },
    });

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        chunk(form.getValues().records, CHUNKED_FILTER_PARAMS.per_page)
    );

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
        resetClonedFilter,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    const { append: appendStudent, remove: removeStudent } = useFieldArray({
        control: form.control,
        name: "records",
    });

    const studentColumns = [
        {
            key: "_",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("records").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            removeStudent(index);
                            onRemove(cell.row.original?.id);
                        }}
                    />
                );
            },
        },
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
            modify: (value, cell) => {
                const index = indexOf(
                    form.watch("records").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="grid min-w-[200px] gap-y-1">
                        <span>{value}</span>
                        {form.formState.errors?.records?.[index]
                            ?.student_id && (
                            <span className="text-xs text-destructive">
                                Invalid student
                            </span>
                        )}
                    </div>
                );
            },
        },
        {
            key: "award_id",
            displayAs: "Award*",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("records").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="h-full min-w-[220px]">
                        <FreeSelect
                            control={form.control}
                            name={`records[${index}].award_id`}
                            options={awardOptions}
                            hasLabel={false}
                            error={
                                form.formState.errors?.records?.[index]
                                    ?.award_id
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "type_of_bonus",
            displayAs: "Type of Bonus*",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("records").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="h-full min-w-[180px]">
                        <FreeSelect
                            control={form.control}
                            isStringOptions
                            name={`records[${index}].type_of_bonus`}
                            options={["PERFORMANCE", "OFF_CAMPUS"]}
                            hasLabel={false}
                            error={
                                form.formState.errors?.records?.[index]
                                    ?.type_of_bonus
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "mark",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("records").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="h-full min-w-[100px]">
                        <FreeInputDecimal
                            control={form.control}
                            name={`records[${index}].mark`}
                            hasLabel={false}
                            error={
                                form.formState.errors?.records?.[index]?.mark
                            }
                            onChange={() =>
                                setTimeout(() => {
                                    form.setFocus(`records.${index}.mark`);
                                }, 0)
                            }
                        />
                    </div>
                );
            },
        },
    ];

    function onAddStudent(selection) {
        const currentIds = form
            .getValues()
            .records.map((student) => student.id.toString());

        const newStudents = selection
            .filter((student) => !currentIds.includes(student.id.toString()))
            .map((student) => ({
                id: student?.id,
                student_number: student?.student_number,
                name: combinedNames(student?.translations?.name),
                award_id: null,
                type_of_bonus: null,
                mark: "",
            }));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        onAdd(selection);
    }

    const { data: awardOptions, axiosQuery: getAwardOptions } = useAxios({
        api: awardAPI,
        locale,
        onError: () => close(),
    });

    const { data: departmentOptions, axiosQuery: getDepartmentOptions } =
        useAxios({
            api: departmentAPI,
            locale,
            onError: () => close(),
        });

    const { axiosPost: createCompetition, error: postError } = useAxios({
        api: specialCompetitionAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateCompetition, error: putError } = useAxios({
        api: specialCompetitionAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                data.date = toYMD(data.date);
                data.records = data.records.map((record) => ({
                    student_id: record.id,
                    award_id: record.award_id,
                    type_of_bonus: record.type_of_bonus,
                    mark: record.mark,
                }));
                if (isCreate) {
                    createCompetition(data);
                } else {
                    updateCompetition({ id: currentData.id, data });
                }
            })
        );
    }

    useEffect(() => {
        getDepartmentOptions({
            params: {
                ...GET_ALL_PARAMS,
                is_active: 1,
                order_by: { sequence: "desc" },
            },
        });
        getAwardOptions({
            params: {
                ...GET_ALL_PARAMS,
                is_active: 1,
                order_by: { sequence: "desc" },
            },
        });
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5 mt-2">
                {isCreate ? "Create" : "Update"} Competition/Special Activity
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <div className="grid gap-3 lg:min-w-[820px] lg:grid-cols-3">
                        <FormInput
                            control={form.control}
                            name="name"
                            label="Competition/Special Activity Name*"
                        />
                        <FormSelect
                            control={form.control}
                            name="department_id"
                            label="Department*"
                            options={departmentOptions}
                            isSortByName={false}
                        />
                        <DatePicker
                            control={form.control}
                            name="date"
                            label="Date*"
                        />
                    </div>

                    <div>
                        <div className="flex gap-x-2">
                            <div className="c-text-size mb-2 font-medium text-themeLabel">
                                Students
                            </div>
                            {targetsChunk?.length > 0 && (
                                <div className="mt-0.5 text-xs text-gray-500">
                                    <span className="font-semibold">
                                        {flatten(targetsChunk)?.length}
                                    </span>{" "}
                                    in total
                                </div>
                            )}
                        </div>
                        <div className="z-10 mb-4 flex items-center gap-3">
                            <Button
                                variant={"outline"}
                                size={"smaller"}
                                onClick={() => setOpenStudentSearch(true)}
                            >
                                Select Students
                            </Button>

                            {targetsChunk?.length > 0 && (
                                <PaginatedTableFilter
                                    type={STUDENT}
                                    filter={filter}
                                    setFilter={setFilter}
                                    reset={resetClonedFilter}
                                />
                            )}
                        </div>
                        {form.formState.errors?.records?.message && (
                            <p className="warning-text mb-2">
                                {`${form.formState.errors?.records?.message}`}
                            </p>
                        )}
                        {targetsChunk?.length > 0 && pagination && (
                            <DataTable
                                isSmaller
                                columns={studentColumns}
                                data={
                                    (clonedFilteredChunk ?? targetsChunk)[
                                        pagination?.current_page - 1
                                    ]
                                }
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                hasPerPage={false}
                                sorted={filter?.order_by}
                                sort={onSort}
                                canOverflow={true}
                            />
                        )}
                    </div>
                    <Button type="submit" className="ml-auto mt-10">
                        Submit
                    </Button>
                </form>
            </Form>

            <Modal
                open={openStudentSearch}
                onOpenChange={setOpenStudentSearch}
                size="large"
            >
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(selection) => onAddStudent(selection)}
                    close={() => setOpenStudentSearch(false)}
                />
            </Modal>
        </div>
    );
};

export default CompetitionSpecialActivityForm;
