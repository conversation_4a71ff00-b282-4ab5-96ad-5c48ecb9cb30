import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    comprehensiveAssessmentCategoryAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const ComprehensiveAssessmentCategoryForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const {
        data: currentData,
        axiosQuery: getComprehensiveAssessmentCategory,
    } = useAxios({
        api: comprehensiveAssessmentCategoryAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getComprehensiveAssessmentCategory({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentData,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const {
        axiosPost: createComprehensiveAssessmentCategory,
        error: postError,
    } = useAxios({
        api: comprehensiveAssessmentCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateComprehensiveAssessmentCategory, error: putError } =
        useAxios({
            api: comprehensiveAssessmentCategoryAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createComprehensiveAssessmentCategory(data);
            } else {
                updateComprehensiveAssessmentCategory({
                    id: currentData.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Comprehensive Assessment
                Category
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ComprehensiveAssessmentCategoryForm;
