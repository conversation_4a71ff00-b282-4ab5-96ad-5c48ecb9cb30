import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    comprehensiveAssessmentCategoryAPI,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";

const FilterComprehensiveAssessmentQuestionForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            question: filter?.question ?? "",
            comprehensive_assessment_category_id:
                filter?.comprehensive_assessment_category_id ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset({ question: "", comprehensive_assessment_category_id: "" });
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const locale = useLocale();

    const {
        data: assessmentCategoriesList,
        axiosQuery: getComprehensiveAssessmentCategories,
    } = useAxios({
        api: comprehensiveAssessmentCategoryAPI,
        locale,
    });

    useEffect(() => {
        getComprehensiveAssessmentCategories({
            params: GET_ALL_PARAMS,
        });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"question"} />

                <FormSelect
                    control={form.control}
                    name="comprehensive_assessment_category_id"
                    label="Category"
                    options={assessmentCategoriesList}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterComprehensiveAssessmentQuestionForm;
