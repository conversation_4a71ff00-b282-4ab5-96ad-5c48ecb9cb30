import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterClassLeadershipRecordForm = ({
    filterLeadershipPositionType,
    setFilterLeadershipPositionType,
    close,
    leadershipTypes,
}: {
    close: () => void;
    filterLeadershipPositionType: any;
    setFilterLeadershipPositionType: React.Dispatch<React.SetStateAction<any>>;
    leadershipTypes: any;
}) => {
    const form = useForm({
        defaultValues: {
            leadership_position_id: filterLeadershipPositionType ?? null,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilterLeadershipPositionType(data.leadership_position_id);
        close();
    }

    function onClear() {
        form.reset({ leadership_position_id: null });
        setFilterLeadershipPositionType(null);
        close();
    }

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="grid gap-y-5"
            >
                <FormSelect
                    control={form.control}
                    name="leadership_position_id"
                    label="Leadership Position Type"
                    options={leadershipTypes}
                />

                <div className="mt-20 flex justify-end gap-x-3">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterClassLeadershipRecordForm;
