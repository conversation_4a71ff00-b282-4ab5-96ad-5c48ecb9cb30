import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    rewardPunishmentCategoriesAPI,
    rewardPunishmentSubCategoriesAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import FormSelect from "../../ui/FormSelect";

const FilterRewardPunishmentSettingForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            category_id: filter?.category_id ?? "",
            sub_category_id: filter?.sub_category_id ?? "",
            display_in_report_card: filter?.display_in_report_card ?? undefined,
            is_active: filter?.is_active ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const locale = useLocale();

    const {
        data: rewardPunishmentCategories,
        axiosQuery: getRewardPunishmentCategories,
    } = useAxios({
        api: rewardPunishmentCategoriesAPI,
        locale,
    });

    const {
        data: rewardPunishmentSubCategories,
        axiosQuery: getRewardPunishmentSubCategories,
    } = useAxios({
        api: rewardPunishmentSubCategoriesAPI,
        locale,
    });

    useEffect(() => {
        getRewardPunishmentCategories({ params: GET_ALL_PARAMS });
        getRewardPunishmentSubCategories({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name="name" />

                <FormSelect
                    control={form.control}
                    name="category_id"
                    label="Reward/ Punishment Category"
                    options={rewardPunishmentCategories}
                />

                <FormSelect
                    control={form.control}
                    name="sub_category_id"
                    label="Reward/ Punishment Sub Category"
                    options={rewardPunishmentSubCategories}
                />

                <FormSelect
                    control={form.control}
                    isSortByName={false}
                    name="display_in_report_card"
                    options={[
                        {
                            name: "Yes",
                            id: "1",
                        },
                        {
                            name: "No",
                            id: "0",
                        },
                    ]}
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: "Active",
                            id: "1",
                        },
                        {
                            name: "Inactive",
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterRewardPunishmentSettingForm;
