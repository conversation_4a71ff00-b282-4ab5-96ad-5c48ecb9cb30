import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFormProps,
    comprehensiveAssessmentCategoryAPI,
    comprehensiveAssessmentQuestionAPI,
    GET_ALL_PARAMS,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const ComprehensiveAssessmentQuestionForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const {
        data: currentData,
        axiosQuery: getComprehensiveAssessmentQuestion,
    } = useAxios({
        api: comprehensiveAssessmentQuestionAPI,
        locale,
        onError: props.close,
    });

    const {
        data: comprehensiveAssessmentCategoryList,
        axiosQuery: getComprehensiveAssessmentCategory,
    } = useAxios({
        api: comprehensiveAssessmentCategoryAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getComprehensiveAssessmentCategory({ params: GET_ALL_PARAMS });

        if (!props.isCreate && props.id) {
            getComprehensiveAssessmentQuestion({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            comprehensiveAssessmentCategoryList={
                comprehensiveAssessmentCategoryList
            }
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
    comprehensiveAssessmentCategoryList: any;
};

const FormWrap = ({
    currentData,
    activeLanguages,
    comprehensiveAssessmentCategoryList,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            comprehensive_assessment_category_id:
                currentData?.comprehensive_assessment_category?.id ?? "",
            question: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.translations?.question?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const {
        axiosPost: createComprehensiveAssessmentQuestion,
        error: postError,
    } = useAxios({
        api: comprehensiveAssessmentQuestionAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateComprehensiveAssessmentQuestion, error: putError } =
        useAxios({
            api: comprehensiveAssessmentQuestionAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createComprehensiveAssessmentQuestion(data);
            } else {
                updateComprehensiveAssessmentQuestion({
                    id: currentData.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Comprehensive Assessment
                Question
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name="comprehensive_assessment_category_id"
                        label="Comprehensive Assessment Category*"
                        options={comprehensiveAssessmentCategoryList}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`question[${lang?.code}]`}
                            label={`Question in ${lang?.name}*`}
                        />
                    ))}
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ComprehensiveAssessmentQuestionForm;
