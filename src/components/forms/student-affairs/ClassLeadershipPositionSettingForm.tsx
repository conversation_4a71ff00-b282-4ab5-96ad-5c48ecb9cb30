import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { CommonFormProps, leadershipPositionSettingsAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const ClassLeadershipPositionSettingForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentData, axiosQuery: getLeadershipPositionSetting } =
        useAxios({
            api: leadershipPositionSettingsAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getLeadershipPositionSetting({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    activeLanguages: Array<Record<string, any>>;
    currentData: any;
};

const FormWrap = ({
    isCreate = false,
    currentData,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: currentData?.sequence ?? sequenceDefaultValue,
            is_active: isCreate ? true : currentData?.is_active ?? false,
        },
    });

    const { axiosPost: createLeadershipPositionSetting, error: postError } =
        useAxios({
            api: leadershipPositionSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateLeadershipPositionSetting, error: putError } =
        useAxios({
            api: leadershipPositionSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createLeadershipPositionSetting(data);
            } else {
                updateLeadershipPositionSetting({ id: currentData.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Class Leadership Position
                Setting
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <FormInputInterger control={form.control} name="sequence" />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="Status"
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ClassLeadershipPositionSettingForm;
