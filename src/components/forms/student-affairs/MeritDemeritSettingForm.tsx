import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    DEMERIT,
    MERIT,
    meritDemeritSettingsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import FormRadioGroup from "../../ui/FormRadioGroup";

const MeritDemeritSettingForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentSetting, axiosQuery: getMeritDemeritSetting } =
        useAxios({
            api: meritDemeritSettingsAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getMeritDemeritSetting({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentSetting) ? (
        <FormWrap
            currentSetting={currentSetting}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentSetting: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentSetting,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            type: currentSetting?.type ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentSetting?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            average_exam_marks: currentSetting?.average_exam_marks ?? 0,
            conduct_marks: currentSetting?.conduct_marks ?? 0,
        },
    });

    const { axiosPost: createMeritDemeritSetting, error: postError } = useAxios(
        {
            api: meritDemeritSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        }
    );

    const { axiosPut: updateMeritDemeritSetting, error: putError } = useAxios({
        api: meritDemeritSettingsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createMeritDemeritSetting(data);
            } else {
                updateMeritDemeritSetting({
                    id: currentSetting.id,
                    data,
                });
            }
        })();
    }
    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Merit/ Demerit Setting
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormRadioGroup
                        control={form.control}
                        name="type"
                        isStringOptions={true}
                        isHorizontal={true}
                        options={[MERIT, DEMERIT]}
                        label="type*"
                    />
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <FormInput
                        control={form.control}
                        type="number"
                        name="average_exam_marks"
                        label="Exam Total Average Marks*"
                    />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="conduct_marks"
                        label="Conduct Marks*"
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default MeritDemeritSettingForm;
