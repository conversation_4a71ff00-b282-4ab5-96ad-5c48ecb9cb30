import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import { axiosInstance } from "@/lib/api";
import {
    CommonFilterProps,
    CONFIRMED,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    POSTED,
    rewardPunishmentSettingsAPI,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { combinedNames, toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import FormSelect from "../../ui/FormSelect";

const FilterRewardPunishmentRecordForm = ({
    filter,
    setFilter,
    close,
    hideFilterByStudent = false,
}: CommonFilterProps & { hideFilterByStudent?: boolean }) => {
    const [students, setStudents] = useState<any[]>([]);
    const [studentName, setStudentName] = useState();
    const [rewardPunishments, setRewardPunishments] = useState<any[]>([]);
    const [rewardPunishmentName, setRewardPunishmentName] = useState();

    const form = useForm({
        defaultValues: {
            student_id: filter?.student_id ?? "",
            reward_punishment_id: filter?.reward_punishment_id ?? "",
            date: filter?.date ?? "",
            display_in_report_card: filter?.display_in_report_card ?? undefined,
            status: filter?.status ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        const _filter: any = { ...filter, ...data, page: 1 };
        if (data.student_id && studentName) {
            _filter.studentName = studentName;
        }

        if (data.reward_punishment_id && rewardPunishmentName) {
            _filter.rewardPunishmentName = rewardPunishmentName;
        }

        if (data.date) {
            _filter.date = toYMD(data.date);
        }

        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const locale = useLocale();

    const { handleError: handleStudentError } = useAxios({ api: studentAPI });

    const loadStudentOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(studentAPI, {
                    params: {
                        common_search: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: `${combinedNames(item?.translations?.name)}
                        ${item.student_number ? ` (${item.student_number})` : ""}`,
                        value: item.id,
                        name: item.name,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleStudentError(error);
                });
        } else {
            callback([]);
        }
    };

    const { handleError: handleRewardPunishmentError } = useAxios({
        api: rewardPunishmentSettingsAPI,
    });

    const loadRewardPunishmentOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(rewardPunishmentSettingsAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: item?.translations?.name?.[locale] ?? item?.name,
                        value: item.id,
                        name: item.name,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleRewardPunishmentError(error);
                });
        } else {
            callback([]);
        }
    };

    useEffect(() => {
        if (filter?.student_id && filter?.studentName) {
            loadStudentOptions(filter.studentName, setStudents);
        }

        if (filter?.reward_punishment_id && filter?.rewardPunishmentName) {
            loadRewardPunishmentOptions(
                filter.rewardPunishmentName,
                setRewardPunishments
            );
        }
    }, []);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                {!hideFilterByStudent && (
                    <FormSelectAsync
                        control={form.control}
                        name="student_id"
                        label="Student"
                        loadOptions={loadStudentOptions}
                        value={students.find(
                            (option) =>
                                option.value === form.watch("student_id")
                        )}
                        onChange={(val) => setStudentName(val?.name)}
                    />
                )}

                <div className="lg:col-span-2">
                    <FormSelectAsync
                        control={form.control}
                        name="reward_punishment_id"
                        label="Reward/ Punishment"
                        loadOptions={loadRewardPunishmentOptions}
                        value={rewardPunishments.find(
                            (option) =>
                                option.value ===
                                form.watch("reward_punishment_id")
                        )}
                        onChange={(val) => setRewardPunishmentName(val?.name)}
                    />
                </div>

                <DatePicker control={form.control} name="date" />

                <FormSelect
                    control={form.control}
                    name="display_in_report_card"
                    isSortByName={false}
                    options={[
                        {
                            name: "Yes",
                            id: "1",
                        },
                        {
                            name: "No",
                            id: "0",
                        },
                    ]}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[DRAFT, POSTED, CONFIRMED]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterRewardPunishmentRecordForm;
