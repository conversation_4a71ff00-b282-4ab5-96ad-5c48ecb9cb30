import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { isEmpty } from "lodash";
import { ChevronDown } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import FreeDatePicker from "@/components/ui/FreeDatePicker";
import FreeSelect from "@/components/ui/FreeSelect";
import {
    conductDeadlineAPI,
    GET_ALL_PARAMS,
    semesterClassPrimaryDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { arrayContainsAny, showBackendFormError, toYMD } from "@/lib/utils";

const _conduct_deadline_update = "conduct-deadline-update";

const ConductDeadlineForm = () => {
    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [groupedClasses, setGroupedClasses] = useState({});
    const [collapsedGrades, setCollapsedGrades] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
            classes: [],
        },
    });

    function onSearchSemester() {
        setIsLoading(true);
        setGroupedClasses({});
        getSemesterClasses({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassPrimaryDropdownFilter(locale),
                semester_setting_id: form.getValues().semester_setting_id,
                is_active: 1,
            },
        });
    }

    const { data: semesterSettings, axiosQuery: getSemesterOptions } = useAxios(
        {
            api: semesterSettingAPI,
            locale,
            onSuccess: (response) => {
                const currentSemester = response.data.find(
                    (option) => option.is_current_semester === true
                );
                if (currentSemester) {
                    form.setValue("semester_setting_id", currentSemester.id);
                }
            },
        }
    );

    const { axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
        onSuccess: (res) => {
            const grouped = {};

            res.data.forEach((item) => {
                const gradeName =
                    item?.class_model?.grade?.translations?.name?.[locale] ??
                    item?.class_model?.grade?.name;
                if (!grouped[gradeName]) {
                    grouped[gradeName] = {
                        sequence: item?.class_model?.grade?.sequence,
                        classes: [],
                    };
                }
                grouped[gradeName].classes.push({
                    id: item?.class_model?.id,
                    name:
                        item?.class_model?.translations?.name?.[locale] ??
                        item?.class_model?.name,
                });
            });

            const sortedGrouped = Object.keys(grouped)
                .sort((a, b) => grouped[b].sequence - grouped[a].sequence)
                .reduce((acc, key) => {
                    acc[key] = grouped[key];
                    return acc;
                }, {});

            let index = 0;
            for (const gradeKey in sortedGrouped) {
                sortedGrouped[gradeKey].classes.forEach((classObj) => {
                    classObj.index = index++;
                    form.setValue(`classes[${classObj.index}]`, {
                        class_id: classObj.id,
                        from: null,
                        to: null,
                    });
                });
            }
            setGroupedClasses(sortedGrouped);
            setIsLoading(false);
        },
        onError: () => setIsLoading(false),
    });

    const { axiosQuery: getConductDeadline } = useAxios({
        api: conductDeadlineAPI,
        onSuccess: (result) => {
            const classList = Object.values(groupedClasses).flatMap(
                (grade: any) => grade.classes
            );

            result.data.forEach((item) => {
                const matchedClass = classList.find(
                    (classObj) => classObj.id == item.class.id
                );
                if (matchedClass) {
                    form.setValue(
                        `classes[${matchedClass.index}].from`,
                        item.from
                    );
                    form.setValue(`classes[${matchedClass.index}].to`, item.to);
                }
            });
        },
    });

    useEffect(() => {
        if (!isEmpty(groupedClasses)) {
            getConductDeadline({
                params: {
                    semester_setting_id: form.getValues("semester_setting_id"),
                },
            });
        }
    }, [groupedClasses]);

    const { axiosPut: updateConductDeadline, error: putError } = useAxios({
        api: conductDeadlineAPI,
    });

    const toggleCollapse = (grade: string) => {
        setCollapsedGrades((prev) =>
            prev.includes(grade)
                ? prev.filter((item) => item !== grade)
                : [...prev, grade]
        );
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                if (data.semester_year_setting_id) {
                    delete data.semester_year_setting_id;
                }

                Object.keys(data).forEach((key) => {
                    if (key[0] === "_") {
                        delete data[key];
                    }
                });

                data.classes.forEach((classObj) => {
                    if (classObj.from) {
                        classObj.from = toYMD(classObj.from);
                    }
                    if (classObj.to) {
                        classObj.to = toYMD(classObj.to);
                    }
                });

                console.log("data", data);
                updateConductDeadline({ data });
            })
        );
    }

    function expandTabWithError() {
        const _errorIndexes = Array.isArray(form.formState.errors?.classes)
            ? form.formState.errors.classes
                  .map((classError, index) =>
                      isEmpty(classError) ? null : index
                  )
                  .filter((classIndex) => classIndex !== null)
            : [];

        Object.keys(groupedClasses).forEach((grade) => {
            const classIndexes = [
                ...groupedClasses[grade].classes.map(
                    (classObj) => classObj.index
                ),
            ];
            const hasError = arrayContainsAny(_errorIndexes, classIndexes);

            if (hasError) {
                setCollapsedGrades((prev) =>
                    prev.filter((item) => item !== grade)
                );
            }
        });
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        showBackendFormError(form, putError);
        expandTabWithError();
    }, [putError]);

    return (
        <div>
            <h2 className="mb-5">Conduct Deadline Setting</h2>
            <div className="grid max-w-[800px]">
                <div className="mb-4 flex items-end gap-3">
                    <div className="lg:min-w-[300px]">
                        <FreeSelect
                            control={form.control}
                            name="semester_setting_id"
                            label="semester"
                            options={semesterSettings}
                            isClearable={false}
                        />
                    </div>
                    <Button onClick={onSearchSemester} variant={"outline"}>
                        Search
                    </Button>
                </div>

                {isLoading ? (
                    <div className="ml-2 text-[14px] text-themeLabel">
                        Loading...
                    </div>
                ) : (
                    !isEmpty(groupedClasses) && (
                        <div>
                            {Object.keys(groupedClasses).map((grade) => {
                                const isCollapsed =
                                    collapsedGrades.includes(grade);
                                return (
                                    <div key={grade} className="mb-2">
                                        {/* Header */}
                                        <div className="flex min-h-[46px] items-center justify-between gap-2 border border-themeGreen4 bg-themeGreen3 px-4 py-1 text-xl">
                                            <div
                                                className="c-text-size cursor-pointer font-semibold text-themeGreenDark"
                                                onClick={() =>
                                                    toggleCollapse(grade)
                                                }
                                            >
                                                {grade}
                                            </div>
                                            {hasPermit(
                                                _conduct_deadline_update
                                            ) &&
                                                !isCollapsed && (
                                                    <>
                                                        <div className="ml-auto">
                                                            <FreeDatePicker
                                                                control={
                                                                    form.control
                                                                }
                                                                name={`_${grade}_from`}
                                                                hasLabel={false}
                                                                placeholder="Apply All Date From"
                                                                isSmaller
                                                                showSelection={
                                                                    false
                                                                }
                                                                onChange={(
                                                                    date
                                                                ) => {
                                                                    Object.values(
                                                                        groupedClasses[
                                                                            grade
                                                                        ]
                                                                            .classes
                                                                    ).forEach(
                                                                        (
                                                                            classModel: any
                                                                        ) => {
                                                                            form.setValue(
                                                                                `classes[${classModel.index}].from`,
                                                                                date
                                                                            );
                                                                        }
                                                                    );
                                                                }}
                                                            />
                                                        </div>
                                                        <FreeDatePicker
                                                            control={
                                                                form.control
                                                            }
                                                            name={`_${grade}_to`}
                                                            hasLabel={false}
                                                            placeholder="Apply All Date To"
                                                            isSmaller
                                                            showSelection={
                                                                false
                                                            }
                                                            onChange={(
                                                                date
                                                            ) => {
                                                                Object.values(
                                                                    groupedClasses[
                                                                        grade
                                                                    ].classes
                                                                ).forEach(
                                                                    (
                                                                        classModel: any
                                                                    ) => {
                                                                        form.setValue(
                                                                            `classes[${classModel.index}].to`,
                                                                            date
                                                                        );
                                                                    }
                                                                );
                                                            }}
                                                        />
                                                    </>
                                                )}
                                            <ChevronDown
                                                size={20}
                                                className={clsx(
                                                    "cursor-pointer text-themeGreen5",
                                                    !isCollapsed && "rotate-180"
                                                )}
                                                onClick={() =>
                                                    toggleCollapse(grade)
                                                }
                                            />
                                        </div>

                                        {/* Collapsible Content */}
                                        <div
                                            className={clsx(
                                                "overflow-hidden border-x",
                                                isCollapsed
                                                    ? "max-h-0"
                                                    : "max-h-none"
                                            )}
                                        >
                                            {groupedClasses[grade].classes.map(
                                                (classModel) => (
                                                    <div
                                                        className={clsx(
                                                            "flex flex-wrap items-center justify-between gap-2 px-4 py-1",
                                                            "border-b"
                                                        )}
                                                        key={classModel.id}
                                                    >
                                                        <p>{classModel.name}</p>
                                                        <div className="ml-auto flex items-start gap-2">
                                                            <FreeDatePicker
                                                                control={
                                                                    form.control
                                                                }
                                                                name={`classes[${classModel.index}].from`}
                                                                hasLabel={false}
                                                                placeholder="Date From"
                                                                error={
                                                                    form
                                                                        .formState
                                                                        .errors
                                                                        ?.classes?.[
                                                                        classModel
                                                                            .index
                                                                    ]?.from
                                                                }
                                                                isDisabled={
                                                                    !hasPermit(
                                                                        _conduct_deadline_update
                                                                    )
                                                                }
                                                            />
                                                            <FreeDatePicker
                                                                control={
                                                                    form.control
                                                                }
                                                                name={`classes[${classModel.index}].to`}
                                                                hasLabel={false}
                                                                placeholder="Date To "
                                                                error={
                                                                    form
                                                                        .formState
                                                                        .errors
                                                                        ?.classes?.[
                                                                        classModel
                                                                            .index
                                                                    ]?.to
                                                                }
                                                                isDisabled={
                                                                    !hasPermit(
                                                                        _conduct_deadline_update
                                                                    )
                                                                }
                                                            />
                                                        </div>

                                                        <div className="mr-5"></div>
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )
                )}

                {hasPermit(_conduct_deadline_update) &&
                    !isLoading &&
                    !isEmpty(groupedClasses) && (
                        <Button
                            onClick={onSubmit}
                            className="ml-auto mr-10 mt-2"
                        >
                            Submit
                        </Button>
                    )}
            </div>
        </div>
    );
};

export default ConductDeadlineForm;
