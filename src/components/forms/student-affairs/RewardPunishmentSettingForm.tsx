import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { axiosInstance } from "@/lib/api";
import {
    CommonFormProps,
    meritDemeritSettingsAPI,
    rewardPunishmentCategoriesAPI,
    rewardPunishmentSettingsAPI,
    rewardPunishmentSubCategoriesAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { configForGetAll, showBackendFormError } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";

const RewardPunishmentSettingForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const [rewardPunishmentSetting, setRewardPunishmentSetting] =
        useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.meritDemeritSettings = res[0].data.data;
            _options.categories = res[1].data.data;
            _options.subCategories = res[2].data.data;

            setOptions(_options);

            if (res[3]) {
                setRewardPunishmentSetting(res[3].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(meritDemeritSettingsAPI, _config),
            axiosInstance.get(rewardPunishmentCategoriesAPI, _config),
            axiosInstance.get(rewardPunishmentSubCategoriesAPI, _config),
            props.id
                ? axiosInstance.get(
                      `${rewardPunishmentSettingsAPI}/${props.id}`,
                      {
                          headers: { "Accept-Language": locale },
                      }
                  )
                : null,
        ]);
    }, []);

    return activeLanguages &&
        options &&
        (props.isCreate || rewardPunishmentSetting) ? (
        <FormWrap
            activeLanguages={activeLanguages}
            options={options}
            rewardPunishmentSetting={rewardPunishmentSetting}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    activeLanguages: Array<Record<string, any>>;
    options: Record<string, any>;
    rewardPunishmentSetting: any;
};

const FormWrap = ({
    activeLanguages,
    options,
    rewardPunishmentSetting,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const locale = useLocale();

    const form = useForm({
        defaultValues: {
            merit_demerit_setting_ids:
                rewardPunishmentSetting?.merit_demerit_settings
                    ? rewardPunishmentSetting.merit_demerit_settings.map(
                          (setting) => setting.id
                      )
                    : [],
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            rewardPunishmentSetting?.translations?.name?.[
                                key
                            ] ?? "",
                    }),
                    {}
                ),
            category_id: rewardPunishmentSetting?.category?.id ?? "",
            sub_category_id: rewardPunishmentSetting?.sub_category?.id ?? "",
            average_exam_marks:
                rewardPunishmentSetting?.average_exam_marks ?? 0,
            conduct_marks: rewardPunishmentSetting?.conduct_marks ?? 0,
            display_in_report_card:
                rewardPunishmentSetting?.display_in_report_card ?? false,
            is_active: isCreate
                ? true
                : rewardPunishmentSetting?.is_active ?? false,
        },
    });

    const handleMeritDemeritChange = (selectedMeritDemeritSettings) => {
        let totalAverageMarks = 0;
        let totalConductMarks = 0;

        if (
            selectedMeritDemeritSettings &&
            selectedMeritDemeritSettings.length > 0
        ) {
            const meritDemeritSettingsData =
                options.meritDemeritSettings.filter((setting) =>
                    selectedMeritDemeritSettings.includes(setting.id)
                );

            totalAverageMarks = meritDemeritSettingsData.reduce(
                (sum, setting) => sum + (setting.average_exam_marks || 0),
                0
            );
            totalConductMarks = meritDemeritSettingsData.reduce(
                (sum, setting) => sum + (setting.conduct_marks || 0),
                0
            );

            const allMerit = meritDemeritSettingsData.every(
                (setting) => setting.type === "MERIT"
            );
            if (allMerit) {
                form.setValue("display_in_report_card", true);
            } else {
                form.setValue("display_in_report_card", false);
            }
        }

        if (
            !selectedMeritDemeritSettings ||
            selectedMeritDemeritSettings.length === 0
        ) {
            form.setValue("merit_demerit_setting_ids", []);
            form.setValue("average_exam_marks", 0);
            form.setValue("conduct_marks", 0);
        } else {
            form.setValue("average_exam_marks", totalAverageMarks);
            form.setValue("conduct_marks", totalConductMarks);
        }
    };

    const { axiosPost: createRewardPunishmentSetting, error: postError } =
        useAxios({
            api: rewardPunishmentSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateRewardPunishmentSetting, error: putError } =
        useAxios({
            api: rewardPunishmentSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createRewardPunishmentSetting(data);
            } else {
                updateRewardPunishmentSetting({
                    id: rewardPunishmentSetting.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Reward/ Punishment Setting
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <div className="lg:col-span-2">
                        <FormSelect
                            isMulti={true}
                            control={form.control}
                            name="merit_demerit_setting_ids"
                            label="Merit/ Demerit*"
                            options={options.meritDemeritSettings?.map(
                                (item) => ({
                                    id: item?.id,
                                    name: `${item?.name} (${item?.conduct_marks})`,
                                })
                            )}
                            onChange={(selected) =>
                                handleMeritDemeritChange(selected)
                            }
                        />
                    </div>

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}

                    <FormSelect
                        control={form.control}
                        name="category_id"
                        label="Category*"
                        options={options.categories}
                    />

                    <FormSelect
                        control={form.control}
                        name="sub_category_id"
                        label="Sub Category"
                        options={options.subCategories}
                    />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="average_exam_marks"
                        label="Exam Total Average Marks*"
                    />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="conduct_marks"
                        label="Conduct Marks*"
                    />

                    <FormCheckbox
                        control={form.control}
                        name="display_in_report_card"
                        label="Display in Report Card"
                        styleClass="mt-3 ml-1"
                        textStyleClass="text-gray-500 font-medium"
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="status"
                    />

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default RewardPunishmentSettingForm;
