import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, rewardPunishmentCategoriesAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const RewardPunishmentCategoryForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentCategory, axiosQuery: getRewardPunishmentCategory } =
        useAxios({
            api: rewardPunishmentCategoriesAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getRewardPunishmentCategory({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentCategory) ? (
        <FormWrap
            currentCategory={currentCategory}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    currentCategory: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentCategory,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentCategory?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createRewardPunishmentCategory, error: postError } =
        useAxios({
            api: rewardPunishmentCategoriesAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateRewardPunishmentCategory, error: putError } =
        useAxios({
            api: rewardPunishmentCategoriesAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createRewardPunishmentCategory(data);
            } else {
                updateRewardPunishmentCategory({
                    id: currentCategory.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Reward/ Punishment Category
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default RewardPunishmentCategoryForm;
