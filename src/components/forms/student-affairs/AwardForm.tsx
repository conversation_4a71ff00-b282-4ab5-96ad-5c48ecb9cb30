import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { awardAPI, CommonFormProps } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const AwardForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentRecord, axiosQuery: getAward } = useAxios({
        api: awardAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getAward({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentRecord) ? (
        <FormWrap
            currentRecord={currentRecord}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    activeLanguages: Array<Record<string, any>>;
    currentRecord: any;
};

const FormWrap = ({
    isCreate = false,
    currentRecord,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentRecord?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: currentRecord?.sequence ?? sequenceDefaultValue,
            is_active: isCreate ? true : currentRecord?.is_active ?? false,
        },
    });

    const { axiosPost: createAward, error: postError } = useAxios({
        api: awardAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateAward, error: putError } = useAxios({
        api: awardAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createAward(data);
            } else {
                updateAward({ id: currentRecord.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Award</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <FormInputInterger
                        control={form.control}
                        name="sequence"
                        label="sequence*"
                    />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="Status"
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default AwardForm;
