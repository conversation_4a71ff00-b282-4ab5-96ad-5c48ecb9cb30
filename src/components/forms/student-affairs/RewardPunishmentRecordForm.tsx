import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormSearchInput from "@/components/ui/FormSearchInput";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    CONFIRMED,
    DRAFT,
    POSTED,
    rewardPunishmentRecordsAPI,
    rewardPunishmentSettingsAPI,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    formatWithBrackets,
    optionUserLabel,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import clsx from "clsx";

const RewardPunishmentRecordForm = (props) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentData, axiosQuery: getRewardPunishmentRecord } =
        useAxios({
            api: rewardPunishmentRecordsAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getRewardPunishmentRecord({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentData,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const isEditable = isCreate || currentData?.status === DRAFT;
    const locale = useLocale();

    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<any>();

    const form = useForm({
        defaultValues: {
            _select_student: null,
            students: [],
            student_id: !isCreate ? currentData?.student?.id : "",
            date: isCreate ? new Date() : currentData?.date ?? "",
            reward_punishment_id: currentData?.reward_punishment?.id ?? "",
            average_exam_marks: currentData?.average_exam_marks ?? 0,
            conduct_marks: currentData?.conduct_marks ?? 0,
            display_in_report_card:
                currentData?.display_in_report_card ?? false,
            status: currentData?.status ?? DRAFT,
        },
    });

    function onAppendStudent(target) {
        const existingStudents = form.getValues("students");

        if (
            existingStudents.find(
                (student: any) => student?.id == target?.value
            )
        )
            return;

        if (target?.value) {
            appendStudent({
                id: target.value,
                name: target?.label ?? target?.name,
            });
        }
    }

    const { append: appendStudent, remove: removeStudent } = useFieldArray<any>(
        {
            control: form.control,
            name: "students",
        }
    );

    const { axiosQuery: getStudentById } = useAxios({
        api: studentAPI,
        locale,
        onSuccess(result) {
            setSelectedStudent(result?.data);
        },
    });

    const { axiosPost: createRewardPunishmentRecord, error: postError } =
        useAxios({
            api: rewardPunishmentRecordsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateRewardPunishmentRecord, error: putError } =
        useAxios({
            api: rewardPunishmentRecordsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.date = toYMD(data.date);
            if (isCreate) {
                data.student_ids = data.students.map((student) => student.id);
                createRewardPunishmentRecord(data);
            } else {
                updateRewardPunishmentRecord({ id: currentData.id, data });
            }
        })();
    }

    const { loadAsyncOptions: loadStudentAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
    });

    const { asyncOptions, setAsyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: rewardPunishmentSettingsAPI,
        params: { includes: ["meritDemeritSettings"] },
        optionFormatter: (item) => ({
            value: item?.id,
            label: `${item?.name} ${formatWithBrackets(item?.merit_demerit_settings?.[0]?.name)}`,
            data: {
                conduct_marks: item?.conduct_marks,
                average_exam_marks: item?.average_exam_marks,
                display_in_report_card: item?.display_in_report_card,
            },
        }),
    });

    const handleRewardPunishmentChange = (item) => {
        if (!item) {
            form.setValue("reward_punishment_id", "");
            form.setValue("average_exam_marks", 0);
            form.setValue("conduct_marks", 0);
            form.setValue("display_in_report_card", false);
            return;
        }

        form.setValue("average_exam_marks", item.data?.average_exam_marks);
        form.setValue("conduct_marks", item.data?.conduct_marks);
        form.setValue(
            "display_in_report_card",
            item.data?.display_in_report_card
        );
    };

    useEffect(() => {
        if (!isCreate && currentData?.reward_punishment?.name) {
            getStudentById({ id: currentData?.student?.id });
            loadAsyncOptions(
                currentData?.reward_punishment?.name,
                setAsyncOptions
            );
        }
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isEditable ? (isCreate ? "Create" : "Update") : ""} Reward/
                Punishment Record
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid-form">
                    {isCreate ? (
                        <div>
                            <FreeSelectAsync
                                control={form.control}
                                name="_select_student"
                                label="Students*"
                                placeholder="Select Students"
                                minWidth={300}
                                loadOptions={loadStudentAsyncOptions}
                                value={null}
                                onChange={(option) => {
                                    onAppendStudent(option);
                                }}
                            />

                            {form.watch("students")?.length > 0 && (
                                <div className="mt-2">
                                    {form
                                        .watch("students")
                                        .map((student: any, index) => (
                                            <div
                                                className="mt-1.5 flex gap-x-3"
                                                key={student?.value}
                                            >
                                                <X
                                                    size={20}
                                                    className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                                    onClick={() =>
                                                        removeStudent(index)
                                                    }
                                                />
                                                <div className="c-text-size">
                                                    {student?.name}
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            )}
                        </div>
                    ) : (
                        <FormSearchInput
                            name="student_id"
                            control={form.control}
                            label="Student*"
                            displayValue={optionUserLabel(
                                selectedStudent?.student_number,
                                selectedStudent?.translations?.name
                            )}
                            onClick={() =>
                                isEditable ? setOpenSearch(true) : null
                            }
                        />
                    )}

                    <DatePicker
                        control={form.control}
                        name="date"
                        label="date*"
                        isDisabled={!isEditable}
                    />

                    <div className="lg:col-span-2">
                        <FreeSelectAsync
                            isDisabled={!isEditable}
                            control={form.control}
                            name="reward_punishment_id"
                            label="Reward/Punishment*"
                            minWidth={300}
                            loadOptions={loadAsyncOptions}
                            value={asyncOptions.find(
                                (option) =>
                                    option.value ===
                                    form.getValues("reward_punishment_id")
                            )}
                            onChange={(option) => {
                                handleRewardPunishmentChange(option);
                            }}
                            error={form.formState.errors?.reward_punishment_id}
                        />
                    </div>
                    <FormInput
                        disabled={!isEditable}
                        control={form.control}
                        type="number"
                        name="average_exam_marks"
                        label="Exam Total Average Marks*"
                    />

                    <FormInput
                        disabled={!isEditable}
                        control={form.control}
                        type="number"
                        name="conduct_marks"
                        label="Conduct Marks*"
                    />

                    {!isCreate && (
                        <FormSelect
                            isDisabled={!isEditable}
                            control={form.control}
                            name="status"
                            isStringOptions={true}
                            options={[DRAFT, POSTED, CONFIRMED]}
                        />
                    )}

                    <FormCheckbox
                        isDisabled={!isEditable}
                        control={form.control}
                        name="display_in_report_card"
                        label="Display in Report Card"
                        styleClass={clsx(isCreate ? "mt-1" : "mt-7", "ml-1")}
                        textStyleClass="text-gray-500 font-medium"
                    />

                    {isEditable ? (
                        <div className="lg:col-span-2">
                            <Button
                                type="submit"
                                className="mb-5 ml-auto mt-20"
                            >
                                Submit
                            </Button>
                        </div>
                    ) : (
                        <div className="h-3"></div>
                    )}
                </form>
            </Form>

            <Modal
                open={!isCreate && openSearch}
                onOpenChange={setOpenSearch}
                size="large"
            >
                <StudentSearchEngine
                    setSelection={(student) => {
                        console.log(student);
                        if (student) {
                            form.setValue("student_id", student.id);
                            form.clearErrors("student_id");
                            setSelectedStudent(student);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </div>
    );
};

export default RewardPunishmentRecordForm;
