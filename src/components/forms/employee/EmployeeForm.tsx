import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import { axiosInstance } from "@/lib/api";
import {
    CommonFormProps,
    countryAPI,
    employeeAPI,
    employeeCategoriesAPI,
    employeeJobTitlesAPI,
    employeeSessionsAPI,
    genderOptions,
    GET_ALL_PARAMS,
    maritalStatusOptions,
    raceAPI,
    religionAPI,
    stateAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    configForGetAll,
    formatDate,
    isChineseCode,
    isMY,
    isProperPhoneNumber,
    isValueTrue,
    showBackendFormError,
    strStartCase,
    toYMD,
} from "@/lib/utils";
import { DatePicker } from "../../ui/DatePicker";
import FormDivider from "../../ui/FormDivider";
import FormFileInput from "../../ui/FormFileInput";
import FormSelect from "../../ui/FormSelect";
import { capitalize } from "lodash";

const EmployeeForm = (props: CommonFormProps & { isTeacher: boolean }) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const [employee, setEmployee] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.countries = res[0].data.data;
            _options.races = res[1].data.data;
            _options.religions = res[2].data.data;
            _options.jobTitles = res[3].data.data;
            _options.employeeCategories = res[4].data.data;
            _options.employeeSessions = res[5].data.data;
            setOptions(_options);
            if (res[6]) {
                setEmployee(res[6].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        const _configWithType = {
            params: { ...GET_ALL_PARAMS, is_teacher: props.isTeacher ? 1 : 0 },
            headers: { "Accept-Language": locale },
        };
        axiosMultipleQueries([
            axiosInstance.get(countryAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(employeeJobTitlesAPI, _config),
            axiosInstance.get(employeeCategoriesAPI, _config),
            axiosInstance.get(employeeSessionsAPI, _config),
            props.id
                ? axiosInstance.get(`${employeeAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return activeLanguages && options && (props.isCreate || employee) ? (
        <FormWrap
            employee={employee}
            activeLanguages={activeLanguages}
            options={options}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    employee: any;
    activeLanguages: Array<Record<string, any>>;
    options: Record<string, any>;
};

const FormWrap = ({
    isCreate = false,
    employee,
    activeLanguages,
    options,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: employee?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            badge_no: employee?.badge_no ?? "",
            email: employee?.email ?? "",
            personal_email: employee?.personal_email ?? "",
            phone_number: employee?.phone_number ?? "",
            job_title_id: employee?.job_title?.id ?? "",
            nric: employee?.nric ?? "",
            passport_number: employee?.passport_number ?? "",
            gender: employee?.gender ?? "",
            religion_id: employee?.religion?.id ?? "",
            race_id: employee?.race?.id ?? "",
            date_of_birth: employee?.date_of_birth ?? "",
            address: employee?.address ?? "",
            address_2: employee?.address_2 ?? "",
            postal_code: employee?.postal_code ?? "",
            city: employee?.city ?? "",
            state_id: employee?.state?.id ?? "",
            country_id: employee?.country?.id ?? "",
            is_hostel: isValueTrue(employee?.is_hostel) ? 1 : 0,
            epf_number: employee?.epf_number ?? "",
            employment_start_date: employee?.employment_start_date ?? "",
            highest_education: employee?.highest_education ?? "",
            highest_education_country_id:
                employee?.highest_education_country?.id ?? "",
            employment_type: employee?.employment_type ?? "",
            employee_category_id: employee?.employee_category?.id ?? "",
            employee_session_id: employee?.employee_session?.id ?? "",
            marriage_status: employee?.marriage_status ?? "",
        },
    });

    const { axiosMultipartPost: createEmployee, error: postError } = useAxios({
        api: employeeAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosMultipartPut: updateEmployee, error: putError } = useAxios({
        api: employeeAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const locale = useLocale();

    const {
        data: stateList,
        axiosQuery: getStateList,
        isLoading: isStatesLoading,
    } = useAxios({ api: stateAPI, locale });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (
                    !isProperPhoneNumber(
                        form,
                        "phone_number",
                        data.phone_number
                    )
                ) {
                    return;
                }
                if (data.date_of_birth) {
                    data.date_of_birth = toYMD(data.date_of_birth);
                }
                if (data.employment_start_date) {
                    data.employment_start_date = toYMD(
                        data.employment_start_date
                    );
                }

                if (data.photo) {
                    data.photo = data.photo?.[0];
                } else {
                    data.photo = null;
                }

                data.is_hostel = data.is_hostel ? 1 : 0;

                console.log("data", data);

                if (isCreate) {
                    createEmployee(data);
                } else {
                    updateEmployee({ id: employee.id, data });
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    useEffect(() => {
        if (employee?.country?.id) {
            getStateList({ params: { country_id: employee.country?.id } });
        }
        if (isCreate) {
            const defaultCountryId = options?.countries.find((country) =>
                isMY(country.name?.toLowerCase())
            )?.id;
            if (defaultCountryId) {
                form.setValue("country_id", defaultCountryId);
                getStateList({ params: { country_id: defaultCountryId } });
            }
        }
    }, []);

    return (
        <>
            <h2 className="mb-2">
                {isCreate ? t("Create ") : t("Update ")}
                {t("employee")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={t(`Name ( ${lang?.name} )`) + "*"}
                            isUpperCase={!isChineseCode(lang?.code)}
                        />
                    ))}
                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="badge_no"
                        label="badge number"
                    />

                    <FormInput control={form.control} name="epf_number" />

                    <FormSelect
                        control={form.control}
                        name="job_title_id"
                        label={t("job title") + "*"}
                        options={options.jobTitles}
                    />

                    <FormInputInterger
                        control={form.control}
                        name="nric"
                        label={t("NRIC") + "*"}
                        max={12}
                    />

                    <FormInput control={form.control} name="passport_number" />

                    <FormInput
                        control={form.control}
                        type="email"
                        name="email"
                        label={t("email") + "*"}
                    />

                    <FormInput
                        control={form.control}
                        type="email"
                        name="personal_email"
                    />

                    <FormPhoneInput
                        form={form}
                        name="phone_number"
                        label={t("phone number") + "*"}
                    />

                    <FormSelect
                        control={form.control}
                        name="gender"
                        label={t("gender") + "*"}
                        isSortByName={false}
                        options={genderOptions.map((gender) => ({
                            id: gender.id,
                            name: t(gender.name),
                        }))}
                    />

                    <FormSelect
                        control={form.control}
                        name="race_id"
                        label="race"
                        options={options.races}
                    />

                    <FormSelect
                        control={form.control}
                        name="religion_id"
                        label="religion"
                        options={options.religions}
                    />

                    <DatePicker
                        control={form.control}
                        name={"date_of_birth"}
                        label={t("date of birth") + "*"}
                    />

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="employment_type"
                        label={t("employment type") + "*"}
                        isSortByName={false}
                        options={[
                            "FULL_TIME",
                            "PART_TIME",
                            "CONTRACT",
                            "TEMPORARY",
                            "FREELANCE",
                            "UNKNOWN",
                        ].map((type) => ({
                            id: type,
                            name: strStartCase(t(type).replaceAll("_", " ")),
                        }))}
                    />

                    <FormSelect
                        control={form.control}
                        name="employee_category_id"
                        label={t("employee category") + "*"}
                        options={options.employeeCategories}
                    />

                    <FormInput
                        control={form.control}
                        name="highest_education"
                    />

                    <FormSelect
                        control={form.control}
                        name="highest_education_country_id"
                        label={t("highest education country")}
                        options={options.countries}
                    />

                    <FormSelect
                        control={form.control}
                        name="employee_session_id"
                        label={t("employee session")}
                        options={options.employeeSessions}
                    />

                    <FormSelect
                        control={form.control}
                        name="marriage_status"
                        label={t("marital status")}
                        options={maritalStatusOptions.map((status) => ({
                            id: status,
                            name: capitalize(t(status)),
                        }))}
                    />

                    <DatePicker
                        control={form.control}
                        name={"employment_start_date"}
                        label={t("employment start date") + "*"}
                    />

                    {employee?.employment_end_date && (
                        <div className="">
                            <div className="c-text-size mb-1.5 font-medium text-themeLabel">
                                {t("employment end date")}
                            </div>
                            <div className="c-text-input flex h-[42px] w-full items-center rounded-sm border border-gray-200 px-4">
                                {formatDate(employee?.employment_end_date)}
                            </div>
                        </div>
                    )}

                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="address"
                        label={t("address") + "*"}
                        isUpperCase={true}
                    />

                    <FormInput
                        control={form.control}
                        name="address_2"
                        label={t("address 2") + "*"}
                        isUpperCase={true}
                    />

                    <FormInput
                        control={form.control}
                        name="city"
                        isUpperCase={true}
                    />

                    <FormSelect
                        control={form.control}
                        name="country_id"
                        label={t("country")}
                        options={options.countries}
                        onChange={(val) => {
                            getStateList({ params: { country_id: val } });
                            form.setValue("state_id", "");
                        }}
                    />

                    <FormSelect
                        control={form.control}
                        name="state_id"
                        label={t("state")}
                        options={stateList}
                        isLoading={isStatesLoading}
                    />

                    <FormInputInterger
                        control={form.control}
                        name="postal_code"
                    />

                    <FormDivider />

                    <FormFileInput
                        name="photo"
                        currentFileUrl={employee?.photo}
                        register={form.register}
                        errors={form.formState.errors}
                        // clear={() => form.setValue("photo", null)}
                    />

                    <div className="mt-3 lg:ml-2 lg:mt-7">
                        <FormCheckbox
                            control={form.control}
                            name="is_hostel"
                            label={t("hostel applicant")}
                        />
                    </div>

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default EmployeeForm;
