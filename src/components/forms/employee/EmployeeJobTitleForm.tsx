import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, employeeJobTitlesAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const EmployeeJobTitleForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: jobTitle, axiosQuery: getJobTitle } = useAxios({
        api: employeeJobTitlesAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getJobTitle({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || jobTitle) ? (
        <FormWrap
            jobTitle={jobTitle}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    jobTitle: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    jobTitle,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: jobTitle?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    console.log("form values", form.getValues());

    const { axiosPost: createJobTitle, error: postError } = useAxios({
        api: employeeJobTitlesAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateJobTitle, error: putError } = useAxios({
        api: employeeJobTitlesAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createJobTitle(data);
            } else {
                updateJobTitle({ id: jobTitle.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("employee job title")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default EmployeeJobTitleForm;
