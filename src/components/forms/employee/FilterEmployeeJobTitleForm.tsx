import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFilterProps, DEFAULT_FILTER_PARAMS } from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterEmployeeJobTitleForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const t = useTranslations("common");
    const form = useForm({ defaultValues: { name: filter?.name ?? "" } });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />
                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterEmployeeJobTitleForm;
