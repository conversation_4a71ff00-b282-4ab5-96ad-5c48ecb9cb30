import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import {
    CommonFormProps,
    employeeAPI,
    employeeJobTitlesAPI,
    employeeReinstateAPI,
    employeeResignAPI,
    employeeTransferAPI,
    GET_ALL_PARAMS,
    REINSTATE,
    RESIGN,
    TRANSFER,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, toYMD } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";
import { capitalize } from "lodash";

const EmployeeStatusForm = (
    props: CommonFormProps & {
        actionType: typeof RESIGN | typeof REINSTATE | typeof TRANSFER | null;
    }
) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: employeeData, axiosQuery: getEmployeeData } = useAxios({
        api: employeeAPI,
        locale,
        onError: props.close,
    });

    const { data: jobTitleOptions, axiosQuery: getJobTitleOptions } = useAxios({
        api: employeeJobTitlesAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (props.id) {
            getEmployeeData({ id: props.id });
        }
        getJobTitleOptions({ params: GET_ALL_PARAMS });
    }, []);

    return activeLanguages && employeeData ? (
        <FormWrap
            employeeData={employeeData}
            jobTitleOptions={jobTitleOptions}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    employeeData: any;
    jobTitleOptions: any;
    activeLanguages: Array<Record<string, any>>;
    actionType: typeof RESIGN | typeof REINSTATE | typeof TRANSFER | null;
};

const FormWrap = ({
    employeeData,
    jobTitleOptions,
    refresh,
    close,
    actionType,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            effective_date: "",
            job_title_id: employeeData?.job_title?.id ?? "",
        },
    });

    const { axiosPost: handleEmployeeResign, error: postResignError } =
        useAxios({
            api: `${employeeResignAPI}/${employeeData.id}`,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPost: handleEmployeeReinstate, error: postReinstateError } =
        useAxios({
            api: `${employeeReinstateAPI}/${employeeData.id}`,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPost: handleEmployeeTransfer, error: postTransferError } =
        useAxios({
            api: `${employeeTransferAPI}/${employeeData.id}`,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.effective_date = toYMD(data.effective_date);

            if (actionType === RESIGN) {
                handleEmployeeResign(data);
            } else if (actionType === REINSTATE) {
                handleEmployeeReinstate(data);
            } else if (actionType === TRANSFER) {
                handleEmployeeTransfer(data);
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postResignError);
    }, [postResignError]);

    useEffect(() => {
        showBackendFormError(form, postReinstateError);
    }, [postReinstateError]);

    useEffect(() => {
        showBackendFormError(form, postTransferError);
    }, [postTransferError]);

    return (
        <div>
            {actionType === "TRANSFER" ? (
                <h2 className="mb-5 mt-2">
                    {t("Update ")}
                    {t("employee job title")}
                </h2>
            ) : (
                <h2 className="mb-5 mt-2">
                    {t("Update ")}
                    {t("employee work status")} -{" "}
                    <span className="font-semibold">
                        {t(capitalize(actionType ?? ""))}
                    </span>
                </h2>
            )}
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {actionType == TRANSFER && (
                        <FormSelect
                            control={form.control}
                            name="job_title_id"
                            label={t("job title")}
                            options={jobTitleOptions}
                        />
                    )}
                    <DatePicker
                        control={form.control}
                        name={"effective_date"}
                        label={t("effective date") + "*"}
                    />
                    <Button type="submit" className="ml-auto mt-10">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default EmployeeStatusForm;
