import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ActionDropdown from "@/components/ui/ActionDropdown";
import DataTable from "@/components/ui/DataTable";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import { examAPI, GET_ALL_PARAMS, subjectAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { displayCellValue } from "@/lib/utils";
import ExamResultSourceSubjectForm from "./ExamResultSourceSubjectForm";

const subjectColumns = [
    {
        key: "code",
    },
    {
        key: "name",
    },
    {
        key: "scoring_type",
    },
    {
        key: "number_of_components",
    },
];

const ExamGradingFrameworkUpdateForm = (props: {
    isCreate?: boolean;
    data?: any;
    onSave: (data: any) => void;
    close: () => void;
}) => {
    const locale = useLocale();

    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data: subjectOptions, axiosQuery: getSubjects } = useAxios({
        api: subjectAPI,
        onError: props.close,
    });

    const { data: examOptions, axiosQuery: getExams } = useAxios({
        api: examAPI,
        onError: props.close,
        locale,
    });

    useEffect(() => {
        getExams({
            params: GET_ALL_PARAMS,
        });
        getSubjects({
            params: GET_ALL_PARAMS,
        });
    }, []);
    return activeLanguages && subjectOptions && examOptions ? (
        <FormWrap
            activeLanguages={activeLanguages}
            subjectOptions={subjectOptions}
            examOptions={examOptions}
            {...props}
            currentData={props.data}
        />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({
    isCreate = false,
    activeLanguages,
    currentData,
    onSave,
    close,
    subjectOptions,
    examOptions,
}) => {
    const [openSubject, setOpenSubject] = useState(false);
    const [targetSubjectIndex, setTargetSubjectIndex] = useState<any>(null);
    const [targetDeleteSubjectIndex, setTargetDeleteSubjectIndex] =
        useState<any>(null);

    const resultSourceForm = useForm<any>({
        defaultValues: {
            code: currentData?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.name?.[key] ?? "",
                    }),
                    {}
                ),
            subjects: currentData?.subjects ?? [],
            exams: currentData?.exams ?? [],
        },
    });

    function definedSubjectData() {
        const subjects = resultSourceForm.getValues("subjects");
        return Array.isArray(subjects)
            ? subjects?.map((item) => ({
                  code: displayCellValue(item?.code),
                  name: getSubjectName(item?.code),
                  scoring_type: displayCellValue(item?.grading_type),
                  number_of_components: item?.components?.length,
              }))
            : [];
    }

    const { append: appendSubject, remove: removeSubject } = useFieldArray({
        control: resultSourceForm.control,
        name: "subjects",
    });

    function onAddSubject(data) {
        appendSubject(data);
        onSubmit();
    }

    function onCopySubject(index) {
        const data = resultSourceForm.getValues("subjects")[index];
        appendSubject(data);
        onSubmit();
    }

    function onUpdateSubject(data) {
        if (typeof data.index === "number") {
            resultSourceForm.setValue(`subjects[${data.index}]`, data);
            onSubmit();
        }
    }

    function onDeleteSubject() {
        removeSubject(targetDeleteSubjectIndex);
        setTargetDeleteSubjectIndex(null);
        onSubmit();
    }

    function getSubjectName(code) {
        return (
            subjectOptions.find((subject) => subject.code === code)?.name ?? "-"
        );
    }

    function onSubmit({ isSave = false } = {}) {
        resultSourceForm.handleSubmit((data: any) => {
            console.log(data);
            if (isCreate) {
                onSave(data);
            } else {
                onSave({ ...data, index: currentData?.index });
            }
        })();
        if (isSave) {
            close();
        }
    }

    useEffect(() => {
        // update form data when currentData changes after saved
        resultSourceForm.setValue("subjects", currentData?.subjects ?? []);
    }, [currentData]);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {t(isCreate ? "Add " : "Update ")}
                {t("Result Source")}
            </h2>
            <Form {...resultSourceForm}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit({ isSave: true });
                    }}
                    className="grid-form lg:min-w-[800px]"
                >
                    {activeLanguages?.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={resultSourceForm.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})`}
                        />
                    ))}

                    <FormInput control={resultSourceForm.control} name="code" />

                    <FormSelect
                        isMulti={true}
                        control={resultSourceForm.control}
                        name="exams"
                        options={examOptions.map((item) => ({
                            id: item?.code,
                            name: item?.code,
                        }))}
                    />

                    <div className="mt-2 border-t border-dashed pt-3 lg:col-span-2">
                        <div className="mb-2 flex flex-wrap items-center gap-3">
                            <h3>{t("Subjects")}</h3>
                            {!isCreate && (
                                <Button
                                    variant={"outline"}
                                    size={"smaller"}
                                    onClick={() => setOpenSubject(true)}
                                >
                                    {t("Add")}
                                </Button>
                            )}
                        </div>
                        {isCreate ? (
                            <p className="text-themeLabel">
                                {t(
                                    "Subjects need to be added after result source is created"
                                )}
                            </p>
                        ) : (
                            <DataTable
                                columns={subjectColumns}
                                data={definedSubjectData()}
                                actionMenu={({ cell }) => (
                                    <ActionDropdown>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetSubjectIndex(
                                                    cell.row.index
                                                );
                                            }}
                                        >
                                            {t("Edit / View")}
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                onCopySubject(cell.row.index);
                                            }}
                                        >
                                            {t("Copy")}
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setTargetDeleteSubjectIndex(
                                                    cell.row.index
                                                )
                                            }
                                        >
                                            {t("Delete")}
                                        </DropdownMenuItem>
                                    </ActionDropdown>
                                )}
                            />
                        )}
                    </div>

                    <Button
                        type="submit"
                        className="ml-auto mt-1 lg:col-span-2"
                    >
                        {t("Save")}
                    </Button>
                </form>
            </Form>

            {/* create subject */}
            <Modal
                open={openSubject}
                onOpenChange={setOpenSubject}
                size="medium"
            >
                <ExamResultSourceSubjectForm
                    isCreate={true}
                    onSave={onAddSubject}
                    close={() => setOpenSubject(false)}
                    subjectOptions={subjectOptions}
                />
            </Modal>

            {/* update subject */}
            <Modal
                open={targetSubjectIndex != null}
                onOpenChange={(isOpen: boolean) => {
                    if (!isOpen) setTargetSubjectIndex(null);
                }}
                size="medium"
            >
                <ExamResultSourceSubjectForm
                    data={{
                        ...resultSourceForm.watch("subjects")[
                            targetSubjectIndex
                        ],
                        index: targetSubjectIndex,
                    }}
                    onSave={onUpdateSubject}
                    close={() => setTargetSubjectIndex(null)}
                    subjectOptions={subjectOptions}
                />
            </Modal>

            {/* delete subject */}
            <Modal
                open={targetDeleteSubjectIndex != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetDeleteSubjectIndex(null);
                }}
            >
                <>
                    <p className="mt-3 font-medium">
                        {t("Are you sure you want to delete this?")}
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setTargetDeleteSubjectIndex(null)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button onClick={onDeleteSubject}>
                            {t("Confirm")}
                        </Button>
                    </DialogFooter>
                </>
            </Modal>
        </>
    );
};

export default ExamGradingFrameworkUpdateForm;
