import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ActionDropdown from "@/components/ui/ActionDropdown";
import DataTable from "@/components/ui/DataTable";
import FormInput from "@/components/ui/FormInput";
import FormSelectCreatable from "@/components/ui/FormSelectCreatable";
import Modal from "@/components/ui/Modal";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import { CommonFormProps, examGradingFrameworkAPI } from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { combinedNames, isValueTrue, showBackendFormError } from "@/lib/utils";
import ExamOutputSettingForm from "./ExamOutputSettingForm";
import ExamResultSourceForm from "./ExamResultSourceForm";

const resultSourceColumns = [
    {
        key: "code",
    },
    {
        key: "name",
    },
    {
        key: "number_of_subjects",
    },
    {
        key: "exams",
    },
];

const outputSettingColumns = [
    {
        key: "code",
    },
    {
        key: "name",
    },
    {
        key: "number_of_components",
    },
    {
        key: "is_final",
    },
];

const ExamGradingFrameworkUpdateForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const locale = useLocale();
    const { data: currentFramework, axiosQuery: getGradingFramework } =
        useAxios({
            api: examGradingFrameworkAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getGradingFramework({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentFramework) ? (
        <FormWrap
            currentFramework={currentFramework}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentFramework: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentFramework,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [promptReplace, setPromptReplace] = useState(false);
    const [openCreateResultSource, setOpenCreateResultSource] = useState(false);
    const [openCreateOutputSettings, setOpenCreateOutputSettings] =
        useState(false);

    const [targetResultSourceIndex, setTargetResultSourceIndex] =
        useState<any>(null);
    const [targetOutputSettingIndex, setTargetOutputSettingIndex] =
        useState<any>(null);

    const [targetDeleteResultSourceIndex, setTargetDeleteResultSourceIndex] =
        useState<any>(null);
    const [targetDeleteOutputSettingIndex, setTargetDeleteOutputSettingIndex] =
        useState<any>(null);

    const form: any = useForm({
        defaultValues: {
            code: currentFramework?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            currentFramework?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            is_active: isValueTrue(currentFramework?.is_active),
            replace_existing: false,
            configuration: {
                output_system_components:
                    currentFramework?.configuration?.output_system_components ??
                    [],
                result_sources:
                    currentFramework?.configuration?.result_sources ?? [],
                output: currentFramework?.configuration?.output ?? [],
            },
        },
    });

    const { append: appendResultSource, remove: removeResultSource } =
        useFieldArray({
            control: form.control,
            name: "configuration.result_sources",
        });

    const { append: appendOutputSetting, remove: removeOutputSetting } =
        useFieldArray({
            control: form.control,
            name: "configuration.output",
        });

    function defineResultSourceData() {
        const list = form.getValues("configuration.result_sources");
        return Array.isArray(list)
            ? list.map((item) => ({
                  code: item?.code ?? "-",
                  name: combinedNames(item?.name) ?? "-",
                  number_of_subjects: item?.subjects?.length,
                  exams: item?.exams?.join(", ") ?? "-",
              }))
            : [];
    }

    function defineOutputSettingData() {
        const list = form.getValues("configuration.output");
        return Array.isArray(list)
            ? list.map((item) => ({
                  code: item?.code ?? "-",
                  name: combinedNames(item?.name) ?? "-",
                  number_of_components: item?.components?.length,
                  is_final: t(item?.is_final ? "Yes" : "No"),
              }))
            : [];
    }

    const { axiosPut: updateGradingFramework, error: updateError } = useAxios({
        api: examGradingFrameworkAPI,
        onSuccess: (result) => {
            form.setValue("configuration", result.data?.configuration);
        },
        onError: () => {
            setOpenCreateResultSource(false);
            setOpenCreateOutputSettings(false);
        },
    });

    const { axiosPut: saveGradingFramework, error: saveError } = useAxios({
        api: examGradingFrameworkAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onAddResultSource(data) {
        appendResultSource(data);
        onSubmit();
    }

    function onCopyResultSource(index) {
        const data = form.getValues("configuration.result_sources")[index];
        appendResultSource(data);
        onSubmit();
    }

    function onUpdateResultSource(data) {
        if (typeof data.index === "number") {
            form.setValue(`configuration.result_sources[${data.index}]`, data);
            onSubmit();
        }
    }

    function onDeleteResultSource() {
        removeResultSource(targetDeleteResultSourceIndex);
        setTargetDeleteResultSourceIndex(null);
        onSubmit();
    }

    function onAddOutputSetting(data) {
        appendOutputSetting(data);
        onSubmit();
    }

    function onCopyOutputSetting(index) {
        const data = form.getValues("configuration.output")[index];
        appendOutputSetting(data);
        onSubmit();
    }

    function onUpdateOutputSetting(data) {
        if (typeof data.index === "number") {
            form.setValue(`configuration.output[${data.index}]`, data);
            onSubmit();
        }
    }

    function onDeleteOutputSetting() {
        removeOutputSetting(targetDeleteOutputSettingIndex);
        setTargetDeleteOutputSettingIndex(null);
        onSubmit();
    }

    const { initLoader } = useSubmit();

    function onSubmit({ isSave = false } = {}) {
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                console.log(data);

                data.configuration.grading_framework_code =
                    form.getValues("code");

                if (isSave) {
                    if (data.is_active) {
                        setPromptReplace(true);
                    } else {
                        saveGradingFramework({ id: currentFramework.id, data });
                    }
                } else {
                    updateGradingFramework({ id: currentFramework.id, data });
                }
            })
        );
    }

    function onReplaceAndSave() {
        form.setValue("replace_existing", true);
        setPromptReplace(false);

        const data = form.getValues();
        data.configuration.grading_framework_code = form.getValues("code");

        saveGradingFramework({
            id: currentFramework.id,
            data,
        });
    }

    function getSaveError(field) {
        const errors = saveError?.response?.data?.error?.[field];

        if (Array.isArray(errors)) {
            return errors.map((error, index) => <div key={index}>{error}</div>);
        }
        return `${errors}`;
    }

    useEffect(() => {
        showBackendFormError(form, saveError || updateError);
    }, [saveError, updateError]);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">{t("Exam Grading Framework")}</h2>
            <Form {...form}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit({ isSave: true });
                    }}
                    className="grid-form lg:min-w-[800px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="status"
                    />

                    <div className="mt-2 border-t border-dashed pt-3 lg:col-span-2">
                        <div className="mb-2.5 flex flex-wrap items-center gap-3">
                            <h3>{t("Result Source")}</h3>
                            {!isValueTrue(form.watch("is_active")) && (
                                <Button
                                    variant={"outline"}
                                    size={"smaller"}
                                    className="w-20"
                                    onClick={() =>
                                        setOpenCreateResultSource(true)
                                    }
                                >
                                    {t("Add")}
                                </Button>
                            )}
                        </div>
                        <DataTable
                            columns={resultSourceColumns}
                            data={defineResultSourceData()}
                            actionMenu={
                                !isValueTrue(form.watch("is_active")) &&
                                hasPermit("grading-framework-update")
                                    ? ({ cell }) => (
                                          <ActionDropdown>
                                              <DropdownMenuItem
                                                  className="c-text-size"
                                                  onClick={() => {
                                                      setTargetResultSourceIndex(
                                                          cell.row.index
                                                      );
                                                  }}
                                              >
                                                  {t("Edit / View")}
                                              </DropdownMenuItem>
                                              <DropdownMenuItem
                                                  className="c-text-size"
                                                  onClick={() => {
                                                      onCopyResultSource(
                                                          cell.row.index
                                                      );
                                                  }}
                                              >
                                                  {t("Copy")}
                                              </DropdownMenuItem>
                                              <DropdownMenuSeparator />
                                              <DropdownMenuItem
                                                  className="c-text-size text-red-600"
                                                  onClick={() =>
                                                      setTargetDeleteResultSourceIndex(
                                                          cell.row.index
                                                      )
                                                  }
                                              >
                                                  {t("Delete")}
                                              </DropdownMenuItem>
                                          </ActionDropdown>
                                      )
                                    : undefined
                            }
                        />
                        {form.formState.errors?.result_sources && (
                            <div className="warning-text">
                                {getSaveError("result_sources")}
                            </div>
                        )}

                        <div className="mt-6 border-t border-dashed pb-6 pt-4">
                            <FormSelectCreatable
                                control={form.control}
                                isMulti={true}
                                name={"configuration.output_system_components"}
                                label="Compulsory Output Component"
                                value={form.watch(
                                    "configuration.output_system_components"
                                )}
                            />
                            {form.formState.errors
                                ?.output_system_components && (
                                <div className="warning-text">
                                    {getSaveError("output_system_components")}
                                </div>
                            )}
                        </div>
                        <div className="mb-2.5 flex flex-wrap items-center gap-3">
                            <h3>{t("Output Settings")}</h3>
                            {!isValueTrue(form.watch("is_active")) && (
                                <Button
                                    variant={"outline"}
                                    size={"smaller"}
                                    className="w-20"
                                    onClick={() =>
                                        setOpenCreateOutputSettings(true)
                                    }
                                >
                                    {t("Add")}
                                </Button>
                            )}
                        </div>
                        <DataTable
                            columns={outputSettingColumns}
                            data={defineOutputSettingData()}
                            actionMenu={
                                !isValueTrue(form.watch("is_active")) &&
                                hasPermit("grading-framework-update")
                                    ? ({ cell }) => (
                                          <ActionDropdown>
                                              <DropdownMenuItem
                                                  className="c-text-size"
                                                  onClick={() => {
                                                      setTargetOutputSettingIndex(
                                                          cell.row.index
                                                      );
                                                  }}
                                              >
                                                  {t("Edit / View")}
                                              </DropdownMenuItem>
                                              <DropdownMenuItem
                                                  className="c-text-size"
                                                  onClick={() => {
                                                      onCopyOutputSetting(
                                                          cell.row.index
                                                      );
                                                  }}
                                              >
                                                  {t("Copy")}
                                              </DropdownMenuItem>
                                              <DropdownMenuSeparator />
                                              <DropdownMenuItem
                                                  className="c-text-size text-red-600"
                                                  onClick={() =>
                                                      setTargetDeleteOutputSettingIndex(
                                                          cell.row.index
                                                      )
                                                  }
                                              >
                                                  {t("Delete")}
                                              </DropdownMenuItem>
                                          </ActionDropdown>
                                      )
                                    : undefined
                            }
                        />
                        {form.formState.errors?.output && (
                            <div className="warning-text">
                                {getSaveError("output")}
                            </div>
                        )}
                    </div>

                    <Button
                        type="submit"
                        className="ml-auto mt-1 lg:col-span-2"
                    >
                        {t("Validate & Save")}
                    </Button>
                </form>
            </Form>

            {/* create result source */}
            <Modal
                open={openCreateResultSource}
                onOpenChange={setOpenCreateResultSource}
                size="medium"
            >
                <ExamResultSourceForm
                    isCreate={true}
                    onSave={onAddResultSource}
                    close={() => setOpenCreateResultSource(false)}
                />
            </Modal>

            {/* update result source */}
            <Modal
                open={targetResultSourceIndex != null}
                onOpenChange={(isOpen: boolean) => {
                    if (!isOpen) setTargetResultSourceIndex(null);
                }}
                size="medium"
            >
                <ExamResultSourceForm
                    data={{
                        ...form.watch("configuration.result_sources")[
                            targetResultSourceIndex
                        ],
                        index: targetResultSourceIndex,
                    }}
                    onSave={onUpdateResultSource}
                    close={() => setTargetResultSourceIndex(null)}
                />
            </Modal>

            {/* delete result source */}
            <Modal
                open={targetDeleteResultSourceIndex != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetDeleteResultSourceIndex(null);
                }}
            >
                <>
                    <p className="mt-3 font-medium">
                        {t("Are you sure you want to delete this?")}
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() =>
                                setTargetDeleteResultSourceIndex(null)
                            }
                        >
                            {t("Cancel")}
                        </Button>
                        <Button onClick={onDeleteResultSource}>
                            {t("Confirm")}
                        </Button>
                    </DialogFooter>
                </>
            </Modal>

            {/* create output settings */}
            <Modal
                open={openCreateOutputSettings}
                onOpenChange={setOpenCreateOutputSettings}
                size="medium"
            >
                <ExamOutputSettingForm
                    isCreate={true}
                    onSave={onAddOutputSetting}
                    close={() => setOpenCreateOutputSettings(false)}
                />
            </Modal>

            {/* update output settings */}
            <Modal
                open={targetOutputSettingIndex != null}
                onOpenChange={(isOpen: boolean) => {
                    if (!isOpen) setTargetOutputSettingIndex(null);
                }}
                size="medium"
            >
                <ExamOutputSettingForm
                    gradingFrameworkId={currentFramework.id}
                    data={{
                        ...form.watch("configuration.output")[
                            targetOutputSettingIndex
                        ],
                        index: targetOutputSettingIndex,
                    }}
                    onSave={onUpdateOutputSetting}
                    close={() => setTargetOutputSettingIndex(null)}
                />
            </Modal>

            {/* delete output settings */}
            <Modal
                open={targetDeleteOutputSettingIndex != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetDeleteOutputSettingIndex(null);
                }}
            >
                <>
                    <p className="mt-3 font-medium">
                        {t("Are you sure you want to delete this?")}
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() =>
                                setTargetDeleteOutputSettingIndex(null)
                            }
                        >
                            {t("Cancel")}
                        </Button>
                        <Button onClick={onDeleteOutputSetting}>
                            {t("Confirm")}
                        </Button>
                    </DialogFooter>
                </>
            </Modal>

            {/* prompt to replace_existing */}
            <Modal open={promptReplace} onOpenChange={setPromptReplace}>
                <p className="mt-6 font-medium">
                    {t(
                        "Do you want to replace the existing student grading framework?"
                    )}
                </p>
                <DialogFooter className={"mt-1"}>
                    <Button
                        variant="outline"
                        onClick={() => setPromptReplace(false)}
                    >
                        {t("Cancel")}
                    </Button>
                    <Button onClick={onReplaceAndSave}>{t("Confirm")}</Button>
                </DialogFooter>
            </Modal>
        </>
    );
};

export default ExamGradingFrameworkUpdateForm;
