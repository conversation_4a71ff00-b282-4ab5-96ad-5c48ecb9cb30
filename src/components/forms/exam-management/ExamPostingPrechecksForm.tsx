import { useLocale, useTranslations } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { capitalize, groupBy, isEmpty } from "lodash";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import FreeSelect from "@/components/ui/FreeSelect";
import {
    GET_ALL_PARAMS,
    semesterSettingAPI,
    examPostingPrechecksDashboardAPI,
    examPostingPrechecksManualRefreshAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import { combinedNames, showBackendFormError, strStartCase } from "@/lib/utils";

const ExamPostingPrechecksForm = () => {
    useCheckViewPermit("results-posting-header-view");
    const locale = useLocale();

    const [isLoading, setIsLoading] = useState(false);
    const [groupedResultSourceCodes, setGroupedResultSourceCodes] = useState(
        {}
    );
    const [collapsedSourceCodes, setCollapsedSourceCodes] = useState<string[]>(
        []
    );
    const [classSubjects, setClassSubjects] = useState<any[]>([]);
    const [expandedClasses, setExpandedClasses] = useState<
        Record<string, number[]>
    >({});

    const [subjectStudents, setSubjectStudents] = useState<any[]>([]);
    const [expandedSubjects, setExpandedSubjects] = useState<
        Record<number, number[]>
    >({});

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
            classes: [],
        },
    });

    const { data: semesterSettings, axiosQuery: getSemesterOptions } = useAxios(
        {
            api: semesterSettingAPI,
            locale,
            onSuccess: (response) => {
                const currentSemester = response.data.find(
                    (option) => option.is_current_semester === true
                );
                if (currentSemester) {
                    form.setValue("semester_setting_id", currentSemester.id);
                }
            },
        }
    );

    const { axiosQuery: getExamPostingPrechecksDataL0 } = useAxios({
        api: examPostingPrechecksDashboardAPI,
        locale,
        onSuccess: (res) => {
            setIsLoading(false);
            const grouped = {};

            res.data.forEach((item) => {
                const sourceCode = item.result_source_code;

                if (!grouped[sourceCode]) {
                    grouped[sourceCode] = [];
                }

                grouped[sourceCode].push({
                    ...item,
                });
            });

            setGroupedResultSourceCodes(grouped);
        },
    });

    const { axiosQuery: getExamPostingPrechecksDataL1 } = useAxios({
        api: examPostingPrechecksDashboardAPI,
        locale,
        onSuccess: (res) => {
            setIsLoading(false);
            setClassSubjects(res.data);
        },
    });

    const { axiosQuery: getExamPostingPrechecksDataL2 } = useAxios({
        api: examPostingPrechecksDashboardAPI,
        locale,
        onSuccess: (res) => {
            setSubjectStudents(res.data);
        },
    });

    const { axiosPost: refreshExamPostingPrechecks, error: postError } =
        useAxios({
            api: examPostingPrechecksManualRefreshAPI,
            onSuccess: () => {
                onSearchSemester();
            },
        });

    function onSearchSemester() {
        setIsLoading(true);
        setGroupedResultSourceCodes({});
        setExpandedClasses({});
        setExpandedSubjects({});
        getExamPostingPrechecksDataL0({
            params: {
                semester_setting_id: form.getValues().semester_setting_id,
                drill_down_level: 0,
            },
        });
    }

    const toggleCollapse = (code: string) => {
        setCollapsedSourceCodes((prev) =>
            prev.includes(code)
                ? prev.filter((item) => item !== code)
                : [...prev, code]
        );
    };

    const toggleExpandClass = (code: string, class_id: number) => {
        const isAlreadyExpanded = expandedClasses[code]?.includes(class_id);

        getExamPostingPrechecksDataL1({
            params: {
                semester_setting_id: form.getValues().semester_setting_id,
                drill_down_level: 1,
                result_source_code: code,
                class_id: class_id,
            },
        });

        setExpandedClasses((prev) => ({
            ...prev,
            [code]: isAlreadyExpanded ? [] : [class_id], // only one allowed
        }));

        setExpandedSubjects({});
    };

    const toggleExpandSubject = (
        code: string,
        class_id: number,
        subject_id: number
    ) => {
        const isAlreadyExpanded =
            expandedSubjects[class_id]?.includes(subject_id);

        getExamPostingPrechecksDataL2({
            params: {
                semester_setting_id: form.getValues().semester_setting_id,
                drill_down_level: 2,
                result_source_code: code,
                class_id: class_id,
                subject_id: subject_id,
            },
        });

        setExpandedSubjects((prev) => ({
            ...prev,
            [class_id]: isAlreadyExpanded ? [] : [subject_id], // only one allowed
        }));
    };

    const nestedSubjectsTable = (code: string, classModel: any) => {
        if (!classSubjects) return null;

        return (
            <div className="border-themeGreenLight ml-6 mt-2 border-l-4 pl-4">
                <div className="grid grid-cols-[1fr_2fr_1fr_1fr] gap-4 border bg-gray-200 px-4 py-2 text-sm font-semibold text-themeGreenDark">
                    <div>{t("Subject")}</div>
                    <div>{t("Teacher")}</div>
                    <div>{t("Status")}</div>
                    <div className="text-right capitalize">
                        {t("percentage")}
                    </div>
                </div>

                {classSubjects
                    .filter(
                        (subject) =>
                            String(subject.class_id) ===
                            String(classModel.class_id)
                    )
                    .map((subject) => (
                        <Fragment key={subject.subject_id}>
                            <div
                                key={subject.subject_id}
                                onClick={() =>
                                    toggleExpandSubject(
                                        code,
                                        classModel.class_id,
                                        subject.subject_id
                                    )
                                }
                                className="grid cursor-pointer grid-cols-[1fr_2fr_1fr_1fr] items-center gap-4 border-b px-4 py-2 text-sm leading-tight hover:bg-gray-50"
                            >
                                <div className="flex items-center gap-2">
                                    {expandedSubjects[
                                        classModel.class_id
                                    ]?.includes(subject.subject_id) ? (
                                        <ChevronUp className="h-4 w-4 text-themeLabel" />
                                    ) : (
                                        <ChevronDown className="h-4 w-4 text-themeLabel" />
                                    )}
                                    {subject.subject_name?.[locale] ??
                                        subject.subject_name?.en}
                                </div>
                                <div>
                                    {subject.teacher_names
                                        ?.map((t) =>
                                            t.en === t.zh
                                                ? t.en
                                                : combinedNames(t)
                                        )
                                        ?.join(", ") ?? "-"}
                                </div>
                                <div>
                                    {subject.completed_rows} /{" "}
                                    {subject.total_rows}
                                </div>
                                <div className="text-right">
                                    {subject.completed_percentage} %
                                </div>
                            </div>

                            {expandedSubjects[classModel.class_id]?.includes(
                                subject.subject_id
                            ) && nestedStudentsTable(subject)}
                        </Fragment>
                    ))}
            </div>
        );
    };

    const nestedStudentsTable = (subject: any) => {
        if (!subjectStudents) return null;

        const showPrimaryClass = subjectStudents.some(
            (s) =>
                s.subject_id === subject.subject_id &&
                s.class_name?.en !== s.primary_class_name?.en
        );

        const filteredStudents = subjectStudents
            .filter((s) => s.subject_id === subject.subject_id)
            .sort((a, b) => {
                if (showPrimaryClass) {
                    const primaryA = a.primary_class_name?.en || "";
                    const primaryB = b.primary_class_name?.en || "";
                    if (primaryA !== primaryB) {
                        return primaryA.localeCompare(primaryB);
                    }
                }
                return a.student_name.en.localeCompare(b.student_name.en);
            });

        return (
            <div className="border-themeGreenLight ml-8 mt-2 border-l-2 border-dashed pl-4">
                <div
                    className={`grid ${showPrimaryClass ? "grid-cols-[1fr_2fr_1fr_1fr_1fr]" : "grid-cols-[1fr_2fr_1fr_1fr]"} gap-4 rounded-t-md bg-gray-100 px-4 py-2 text-sm font-medium leading-tight text-themeGreenDark`}
                >
                    <div>{strStartCase(t("student number"))}</div>
                    <div>{strStartCase(t("student name"))}</div>
                    {showPrimaryClass && (
                        <div>{strStartCase(t("primary class"))}</div>
                    )}
                    <div>{strStartCase(t("status"))}</div>
                    <div className="text-right capitalize">{t("score")}</div>
                </div>

                {filteredStudents.map((student) => (
                    <div
                        key={student.student_id}
                        className={`grid ${showPrimaryClass ? "grid-cols-[1fr_2fr_1fr_1fr_1fr]" : "grid-cols-[1fr_2fr_1fr_1fr]"} gap-4 border-b px-4 py-2 text-sm leading-tight`}
                    >
                        <div>{student.student_number}</div>
                        <div>
                            {student.student_name?.en ===
                            student.student_name?.zh
                                ? student.student_name?.en
                                : combinedNames(student.student_name)}
                        </div>
                        {showPrimaryClass && (
                            <div>
                                {combinedNames(student.primary_class_name)}
                            </div>
                        )}
                        <div>
                            {student.input_value_status === 1 ? (
                                <span className="text-green-600">OK</span>
                            ) : (
                                <span className="text-yellow-500">
                                    {t("Pending")}
                                </span>
                            )}
                            {student?.is_exempted === 1 && (
                                <span className="text-yellow-500">
                                    {` (${t("Exempted")})`}
                                </span>
                            )}
                        </div>
                        <div className="text-right">
                            {student.input_value ?? "-"}
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    const refresh = () => {
        refreshExamPostingPrechecks({});
    };

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">{t("Exam Posting Pre-Checks")}</h2>
            <div
                className={clsx(
                    "grid max-w-[1000px]",
                    !isEmpty(groupedResultSourceCodes) &&
                        "overflow-auto lg:overflow-visible"
                )}
            >
                <div className="mb-4 flex items-end gap-3">
                    <div className="lg:min-w-[300px]">
                        <FreeSelect
                            control={form.control}
                            name="semester_setting_id"
                            label="semester"
                            options={semesterSettings}
                        />
                    </div>
                    <Button onClick={onSearchSemester} variant={"outline"}>
                        {t("Search")}
                    </Button>
                </div>

                {isLoading ? (
                    <div className="ml-2 text-[14px] text-themeLabel">
                        {t("Loading")}...
                    </div>
                ) : (
                    !isEmpty(groupedResultSourceCodes) && (
                        <div>
                            {Object.keys(groupedResultSourceCodes).map(
                                (code) => {
                                    const isCollapsed =
                                        collapsedSourceCodes.includes(code);

                                    return (
                                        <div key={code} className="mb-2">
                                            {/* Result source codes */}
                                            <div className="flex min-h-[46px] items-center justify-between gap-2 border border-themeGreen4 bg-themeGreen3 px-4 py-1 text-xl">
                                                <div
                                                    className="c-text-size cursor-pointer font-semibold text-themeGreenDark"
                                                    onClick={() =>
                                                        toggleCollapse(code)
                                                    }
                                                >
                                                    {code}
                                                </div>

                                                <ChevronDown
                                                    size={20}
                                                    className={clsx(
                                                        "cursor-pointer text-themeGreen5",
                                                        !isCollapsed &&
                                                            "rotate-180"
                                                    )}
                                                    onClick={() =>
                                                        toggleCollapse(code)
                                                    }
                                                />
                                            </div>

                                            {/* Classes */}
                                            <div
                                                className={clsx(
                                                    "overflow-hidden border-x p-3",
                                                    isCollapsed
                                                        ? "max-h-0 py-0"
                                                        : "max-h-none"
                                                )}
                                            >
                                                {Object.entries(
                                                    groupBy(
                                                        groupedResultSourceCodes[
                                                            code
                                                        ],
                                                        "class_type"
                                                    )
                                                ).map(
                                                    ([
                                                        classType,
                                                        classList,
                                                    ]) => (
                                                        <div
                                                            key={classType}
                                                            className="border-themeBorder mb-5 overflow-hidden rounded-md border"
                                                        >
                                                            <div className="grid grid-cols-3 gap-4 border-themeGreen bg-themeGreen4 px-4 py-2 text-sm font-semibold leading-tight text-themeGreenDark">
                                                                {capitalize(
                                                                    t(classType)
                                                                )}{" "}
                                                                {t("Classes")}
                                                            </div>

                                                            <div className="border-themeBorder bg-themeBgLight c-text-size thead-item grid grid-cols-3 gap-4 border-b border-t px-4 py-2 capitalize text-themeLabel">
                                                                <div>
                                                                    {t("class")}
                                                                </div>
                                                                <div>
                                                                    {t(
                                                                        "status"
                                                                    )}
                                                                </div>
                                                                <div className="text-right">
                                                                    {t(
                                                                        "percentage"
                                                                    )}
                                                                </div>
                                                            </div>

                                                            {classList.map(
                                                                (
                                                                    classModel,
                                                                    index
                                                                ) => (
                                                                    <React.Fragment
                                                                        key={
                                                                            classModel.class_id
                                                                        }
                                                                    >
                                                                        <div
                                                                            onClick={() =>
                                                                                toggleExpandClass(
                                                                                    code,
                                                                                    classModel.class_id
                                                                                )
                                                                            }
                                                                            className={clsx(
                                                                                "grid cursor-pointer grid-cols-3 items-center gap-4 px-4 py-2 text-sm leading-tight transition",
                                                                                index !==
                                                                                    classList.length -
                                                                                        1 &&
                                                                                    "border-b",
                                                                                "hover:bg-gray-50"
                                                                            )}
                                                                        >
                                                                            <div className="flex items-center gap-2">
                                                                                {expandedClasses[
                                                                                    code
                                                                                ]?.includes(
                                                                                    classModel.class_id
                                                                                ) ? (
                                                                                    <ChevronUp className="h-5 w-5 text-themeLabel" />
                                                                                ) : (
                                                                                    <ChevronDown className="h-5 w-5 text-themeLabel" />
                                                                                )}
                                                                                {classModel
                                                                                    .class_name
                                                                                    ?.en ===
                                                                                classModel
                                                                                    .class_name
                                                                                    ?.zh
                                                                                    ? classModel
                                                                                          .class_name
                                                                                          ?.en
                                                                                    : combinedNames(
                                                                                          classModel.class_name
                                                                                      )}
                                                                            </div>
                                                                            <div>
                                                                                {
                                                                                    classModel.completed_rows
                                                                                }{" "}
                                                                                /{" "}
                                                                                {
                                                                                    classModel.total_rows
                                                                                }
                                                                            </div>
                                                                            <div className="text-right text-sm leading-tight">
                                                                                {
                                                                                    classModel.completed_percentage
                                                                                }{" "}
                                                                                %
                                                                            </div>
                                                                        </div>

                                                                        {/*  Subject */}
                                                                        {expandedClasses[
                                                                            code
                                                                        ]?.includes(
                                                                            classModel.class_id
                                                                        ) &&
                                                                            classSubjects &&
                                                                            nestedSubjectsTable(
                                                                                code,
                                                                                classModel
                                                                            )}
                                                                    </React.Fragment>
                                                                )
                                                            )}
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    );
                                }
                            )}
                        </div>
                    )
                )}

                {!isLoading &&
                    Object.keys(groupedResultSourceCodes).length > 0 && (
                        <Button onClick={refresh} className="ml-auto mt-3">
                            {t("Refresh")}
                        </Button>
                    )}
            </div>
        </div>
    );
};

export default ExamPostingPrechecksForm;
