import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import ExamFormulaForm from "./ExamFormulaForm";
import { useLanguages } from "@/lib/store";
import { useTranslations } from "next-intl";

const ExamOutputComponentForm = (props: {
    gradingFrameworkId?: any;
    isCreate?: boolean;
    data?: any;
    onSave: (data: any) => void;
    close: () => void;
    subjectOptions: any;
    gradingOptions: any;
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    return activeLanguages ? (
        <FormWrap
            {...props}
            currentData={props.data}
            subjectOptions={props.subjectOptions}
            gradingOptions={props.gradingOptions}
            activeLanguages={activeLanguages}
        />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({
    gradingFrameworkId = null,
    isCreate = false,
    currentData,
    onSave,
    close,
    gradingOptions,
    subjectOptions,
    activeLanguages,
}) => {
    const t = useTranslations("common");

    const componentForm = useForm<any>({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.name?.[key] ?? "",
                    }),
                    {}
                ),
            code: currentData?.code ?? "",
            subject_code: currentData?.subject_code ?? "",
            grading_scheme_code: currentData?.grading_scheme_code ?? "",
            weightage_multiplier: currentData?.weightage_multiplier ?? "",
            calculate_rank: currentData?.calculate_rank ?? "",
            priority: currentData?.priority ?? "",
            total_formula: currentData?.total_formula ?? "",
            label_formula: currentData?.label_formula ?? "",
            output_type: currentData?.output_type ?? "",
        },
    });

    const [editFormula, setEditFormula] = useState<
        "total_formula" | "label_formula" | null
    >(null);

    function onUpdateTotalFormula(data) {
        componentForm.setValue("total_formula", data);
        onSubmit();
    }

    function onUpdateLabelFormula(data) {
        componentForm.setValue("label_formula", data);
        onSubmit();
    }

    function onSubmit({ isSave = false } = {}) {
        componentForm.handleSubmit((data: any) => {
            console.log(data);
            if (isCreate) {
                onSave(data);
            } else {
                onSave({ ...data, index: currentData?.index });
            }
        })();
        if (isSave) {
            close();
        }
    }

    function formatFormula(type: "total_formula" | "label_formula") {
        const formula = componentForm.getValues(type);
        if (formula) {
            return Array.isArray(formula)
                ? formula.map((item) => item?.label).join(" ")
                : `${formula}`;
        }
        return <span className="text-gray-400">{t("None")}</span>;
    }

    return (
        <>
            <h2 className="mb-2">
                {isCreate ? t("Add ") : t("Update ")}
                {t("Result Source Output Component")}
            </h2>
            <Form {...componentForm}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit({ isSave: true });
                    }}
                    className="grid-form lg:min-w-[800px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={componentForm.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})`}
                        />
                    ))}

                    <FormInput control={componentForm.control} name="code" />

                    <FormSelect
                        control={componentForm.control}
                        name="subject_code"
                        label="subject"
                        options={subjectOptions?.map((item) => ({
                            id: item?.code,
                            name: item?.name,
                        }))}
                    />

                    <FormSelect
                        control={componentForm.control}
                        name="grading_scheme_code"
                        options={gradingOptions?.map((item) => ({
                            id: item?.code,
                            name: item?.name,
                        }))}
                    />

                    <FormInputInterger
                        control={componentForm.control}
                        name="weightage_multiplier"
                    />

                    <FormInputInterger
                        control={componentForm.control}
                        name="priority"
                    />

                    <FormSelect
                        control={componentForm.control}
                        name="output_type"
                        isStringOptions={true}
                        options={["SCORE", "GRADE"]}
                    />

                    <div className="mt-1 lg:col-span-2 lg:ml-0.5">
                        <FormCheckbox
                            control={componentForm.control}
                            name="calculate_rank"
                        />
                    </div>

                    <div className="mt-1 border-t border-dashed pt-4 lg:col-span-2 lg:ml-0.5">
                        <div className="flex items-center gap-x-3">
                            <h3>{t("Total Formula")}</h3>
                            {!isCreate && (
                                <Button
                                    className="min-w-20 text-themeGreen"
                                    variant={"outline"}
                                    size={"smaller"}
                                    onClick={() =>
                                        setEditFormula("total_formula")
                                    }
                                >
                                    <span>{t("Edit")}</span>
                                </Button>
                            )}
                        </div>

                        {isCreate ? (
                            <p className="mt-2 text-themeLabel">
                                {t(
                                    "Total formula need to be added after output component is created"
                                )}
                            </p>
                        ) : (
                            <div className="c-text-size mt-3 rounded-md border border-dashed border-input px-3 py-2.5">
                                {formatFormula("total_formula")}
                            </div>
                        )}
                    </div>

                    <div className="mt-1 border-t border-dashed pt-4 lg:col-span-2 lg:ml-0.5">
                        <div className="flex items-center gap-x-3">
                            <h3>{t("Label Formula")}</h3>
                            {!isCreate && (
                                <Button
                                    className="min-w-20 text-themeGreen"
                                    variant={"outline"}
                                    size={"smaller"}
                                    onClick={() =>
                                        setEditFormula("label_formula")
                                    }
                                >
                                    <span>{t("Edit")}</span>
                                </Button>
                            )}
                        </div>

                        {isCreate ? (
                            <p className="mt-2 text-themeLabel">
                                {t(
                                    "Label formula need to be added after output component is created"
                                )}
                            </p>
                        ) : (
                            <div className="c-text-size mt-3 rounded-md border border-dashed border-input px-3 py-2.5">
                                {formatFormula("label_formula")}
                            </div>
                        )}
                    </div>

                    <Button
                        type="submit"
                        className="ml-auto mt-5 lg:col-span-2"
                    >
                        {t("Save")}
                    </Button>
                </form>
            </Form>

            <Modal
                open={editFormula != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setEditFormula(null);
                }}
                size="large"
            >
                <ExamFormulaForm
                    gradingFrameworkId={gradingFrameworkId}
                    data={
                        editFormula
                            ? componentForm.watch(editFormula)
                            : undefined
                    }
                    subjectOptions={subjectOptions}
                    onSave={
                        editFormula === "total_formula"
                            ? onUpdateTotalFormula
                            : editFormula === "label_formula"
                              ? onUpdateLabelFormula
                              : () => {}
                    }
                    close={() => setEditFormula(null)}
                />
            </Modal>
        </>
    );
};

export default ExamOutputComponentForm;
