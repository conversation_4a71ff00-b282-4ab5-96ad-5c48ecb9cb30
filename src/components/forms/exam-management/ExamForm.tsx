import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { format, parse } from "date-fns";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormTextarea from "@/components/ui/FormTextarea";
import { CommonFormProps, DATE_FORMAT, examAPI } from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, toUTC, toYMD } from "@/lib/utils";
import FormDivider from "@/components/ui/FormDivider";

const ExamForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const locale = useLocale();
    const { data: currentExam, axiosQuery: getExam } = useAxios({
        api: examAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getExam({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentExam) ? (
        <FormWrap
            currentExam={currentExam}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentExam: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentExam,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            code: currentExam?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentExam?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            description: currentExam?.description ?? "",
            start_date: currentExam?.start_date ?? "",
            end_date: currentExam?.end_date ?? "",
            results_entry_period_from: currentExam?.results_entry_period_from
                ? format(
                      currentExam?.results_entry_period_from,
                      DATE_FORMAT.YMD_HMS
                  )
                : null,
            results_entry_period_to: currentExam?.results_entry_period_to
                ? format(
                      currentExam?.results_entry_period_to,
                      DATE_FORMAT.YMD_HMS
                  )
                : null,
        },
    });

    const [dateRange, setDateRange] = useState<any>(null);

    const { axiosPost: createExam, error: postError } = useAxios({
        api: examAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateExam, error: putError } = useAxios({
        api: examAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (dateRange) {
                    data.start_date = toYMD(dateRange.startDate);
                    data.end_date = toYMD(dateRange.endDate);
                }

                if (data.results_entry_period_from) {
                    data.results_entry_period_from = toUTC(
                        data.results_entry_period_from
                    );
                }

                if (data.results_entry_period_to) {
                    data.results_entry_period_to = toUTC(
                        data.results_entry_period_to
                    );
                }

                if (isCreate) {
                    createExam(data);
                } else {
                    updateExam({ id: currentExam.id, data });
                }
            })
        );
    }

    function formatToDate(timeString?: string) {
        return timeString
            ? parse(timeString, "yyyy-MM-dd", new Date())
            : undefined;
    }

    useEffect(() => {
        if (currentExam?.start_date && currentExam?.end_date) {
            const currentDateRange = {
                startDate: formatToDate(currentExam?.start_date),
                endDate: formatToDate(currentExam?.end_date),
                key: "selection",
            };
            setDateRange(currentDateRange);
        }
    }, [currentExam?.start_date, currentExam?.end_date]);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {isCreate ? t("Create ") : t("Update ")}
                {t("exam")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid-form">
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <FormDivider />

                    <FormTextarea
                        control={form.control}
                        name="description"
                        rows={2}
                    />

                    <div className="lg:col-span-1">
                        <DateRangePicker
                            label={t("Exam Date Range") + "*"}
                            range={dateRange}
                            setRange={setDateRange}
                            error={
                                form.formState.errors?.start_date?.message ||
                                form.formState.errors?.end_date?.message
                            }
                        />
                    </div>

                    <DateTimePicker
                        control={form.control}
                        name={"results_entry_period_from"}
                        label={t("Result Key in Date (From)")}
                    />

                    <DateTimePicker
                        control={form.control}
                        name={"results_entry_period_to"}
                        label={t("Result Key in Date (To)")}
                    />

                    <div className="mt-4 lg:col-span-2">
                        <Button type="submit" className="ml-auto">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default ExamForm;
