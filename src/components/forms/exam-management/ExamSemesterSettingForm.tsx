import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import clsx from "clsx";
import { isEmpty } from "lodash";
import { ChevronDown } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import FreeDatePicker from "@/components/ui/FreeDatePicker";
import FreeSelect from "@/components/ui/FreeSelect";
import {
    GET_ALL_PARAMS,
    semesterSettingAPI,
    examSemesterSettingAPI,
    gradeAPI,
    examSemesterSettingCreateUpdateAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import { showBackendFormError, toYMD } from "@/lib/utils";

const _exam_semester_setting_create = "exam-semester-setting-create";

const categories = [
    { name: "Attendance", id: "ATTENDANCE" },
    { name: "Merit / Demerit", id: "MERIT_DEMERIT" },
    { name: "Mark Deduct", id: "MARK_DEDUCT" },
    { name: "<PERSON> Deduct (Retain)", id: "MARK_DEDUCT_RETAIN" },
    { name: "Off Campus", id: "OFF_CAMPUS" },
];

const ExamSemesterSettingForm = () => {
    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const [groupedCategories, setGroupedCategories] = useState({});
    const [collapsedCategories, setCollapsedCategories] = useState<string[]>(
        []
    );
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
            grades: [],
        },
    });

    function onSearchSemester() {
        setIsLoading(true);
        setGroupedCategories({});
        getExamSemesterSettings({
            params: {
                ...GET_ALL_PARAMS,
                semester_setting_id: form.getValues().semester_setting_id,
            },
        });
    }

    const { data: grades, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: semesterSettings, axiosQuery: getSemesterOptions } = useAxios(
        {
            api: semesterSettingAPI,
            locale,
            onSuccess: (response) => {
                const currentSemester = response.data.find(
                    (option) => option.is_current_semester === true
                );
                if (currentSemester) {
                    form.setValue("semester_setting_id", currentSemester.id);
                }
            },
        }
    );

    const { axiosQuery: getExamSemesterSettings } = useAxios({
        api: examSemesterSettingAPI,
        locale,
        onSuccess: (res) => {
            const categoryMap: Record<string, Record<string, any>> = {};

            const settingsMap = {};
            res.data.forEach((item) => {
                const key = `${item.category}_${item.grade.id}`;
                settingsMap[key] = item;
            });

            categories.forEach(({ id: categoryId }) => {
                categoryMap[categoryId] = {};

                grades?.forEach((grade) => {
                    const key = `${categoryId}_${grade.id}`;
                    const item = settingsMap[key];
                    const index = key;

                    if (item) {
                        form.setValue(`grades[${index}]`, {
                            id: item.id,
                            category: categoryId,
                            grade_id: grade.id,
                            from: item.from_date ?? "",
                            to: item.to_date ?? "",
                        });
                    } else {
                        form.setValue(`grades[${index}]`, {
                            id: undefined,
                            category: categoryId,
                            grade_id: grade.id,
                            from: "",
                            to: "",
                        });
                    }

                    categoryMap[categoryId][grade.id] = {
                        ...item,
                        grade_id: grade.id,
                        index,
                    };
                });
            });

            setGroupedCategories(categoryMap);
            setIsLoading(false);
        },
    });

    const { axiosPut: updateExamSemesterSetting, error: putError } = useAxios({
        api: examSemesterSettingCreateUpdateAPI,
    });

    const categoryNameMap = Object.fromEntries(
        categories.map(({ id, name }) => [id, name])
    );

    const toggleCollapse = (category: string) => {
        setCollapsedCategories((prev) =>
            prev.includes(category)
                ? prev.filter((item) => item !== category)
                : [...prev, category]
        );
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                const payload = {
                    semester_setting_id: form.getValues().semester_setting_id,
                    exam_semester_settings: Object.values(data.grades).map(
                        (item: any) => ({
                            category: item.category,
                            grade_id: item.grade_id,
                            from_date: toYMD(item.from),
                            to_date: toYMD(item.to),
                        })
                    ),
                };

                updateExamSemesterSetting({ data: payload });
            })
        );
    }

    function expandTabWithError(
        errors: any,
        gradeFieldArray: Record<string, any>
    ): Record<string, { from?: string; to?: string }> {
        const errorMap: Record<string, { from?: string; to?: string }> = {};
        const errorArray = errors?.exam_semester_settings ?? [];

        const keys = Object.keys(gradeFieldArray);

        keys.forEach((key, index) => {
            const errorItem = errorArray[index];
            if (!errorItem) return;

            if (errorItem.from_date?.message) {
                errorMap[key] = {
                    ...errorMap[key],
                    from: errorItem.from_date.message,
                };
            }

            if (errorItem.to_date?.message) {
                errorMap[key] = {
                    ...errorMap[key],
                    to: errorItem.to_date.message,
                };
            }
        });

        return errorMap;
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGrades({ params: GET_ALL_PARAMS });
    }, []);

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <div>
            <h2 className="mb-5">Exam Semester Settings</h2>
            <div className="grid max-w-[800px]">
                <div className="mb-4 flex items-end gap-3">
                    <div className="lg:min-w-[300px]">
                        <FreeSelect
                            control={form.control}
                            name="semester_setting_id"
                            label="semester"
                            options={semesterSettings}
                            isClearable={false}
                            onChange={() => setGroupedCategories({})}
                        />
                    </div>
                    <Button onClick={onSearchSemester} variant={"outline"}>
                        Search
                    </Button>
                </div>

                {isLoading ? (
                    <div className="ml-2 text-[14px] text-themeLabel">
                        Loading...
                    </div>
                ) : (
                    !isEmpty(groupedCategories) && (
                        <div>
                            {Object.entries(groupedCategories).map(
                                ([category, categoryGrades]) => {
                                    const isCollapsed =
                                        collapsedCategories.includes(category);
                                    return (
                                        <div key={category} className="mb-3">
                                            <div className="flex min-h-[46px] items-center justify-between gap-2 border border-themeGreen4 bg-themeGreen3 px-4 py-1 text-xl">
                                                <div
                                                    className="c-text-size cursor-pointer font-semibold text-themeGreenDark"
                                                    onClick={() =>
                                                        toggleCollapse(category)
                                                    }
                                                >
                                                    {categoryNameMap[
                                                        category
                                                    ] ?? category}
                                                </div>
                                                {hasPermit(
                                                    _exam_semester_setting_create
                                                ) &&
                                                    !isCollapsed && (
                                                        <>
                                                            <div className="ml-auto">
                                                                <FreeDatePicker
                                                                    control={
                                                                        form.control
                                                                    }
                                                                    name={`_${category}_from`}
                                                                    hasLabel={
                                                                        false
                                                                    }
                                                                    placeholder="Apply All Date From"
                                                                    isSmaller
                                                                    showSelection={
                                                                        false
                                                                    }
                                                                    onChange={(
                                                                        date
                                                                    ) => {
                                                                        Object.values(
                                                                            categoryGrades as Record<
                                                                                string,
                                                                                any
                                                                            >
                                                                        ).forEach(
                                                                            (
                                                                                gradeData: any
                                                                            ) => {
                                                                                form.setValue(
                                                                                    `grades[${gradeData.index}].from`,
                                                                                    date
                                                                                );
                                                                            }
                                                                        );
                                                                    }}
                                                                />
                                                            </div>
                                                            <FreeDatePicker
                                                                control={
                                                                    form.control
                                                                }
                                                                name={`_${category}_to`}
                                                                hasLabel={false}
                                                                placeholder="Apply All Date To"
                                                                isSmaller
                                                                showSelection={
                                                                    false
                                                                }
                                                                onChange={(
                                                                    date
                                                                ) => {
                                                                    Object.values(
                                                                        categoryGrades as Record<
                                                                            string,
                                                                            any
                                                                        >
                                                                    ).forEach(
                                                                        (
                                                                            gradeData: any
                                                                        ) => {
                                                                            form.setValue(
                                                                                `grades[${gradeData.index}].to`,
                                                                                date
                                                                            );
                                                                        }
                                                                    );
                                                                }}
                                                            />
                                                        </>
                                                    )}
                                                <ChevronDown
                                                    size={20}
                                                    className={clsx(
                                                        "cursor-pointer text-themeGreen5",
                                                        !isCollapsed &&
                                                            "rotate-180"
                                                    )}
                                                    onClick={() =>
                                                        toggleCollapse(category)
                                                    }
                                                />
                                            </div>

                                            {!isCollapsed && (
                                                <div className="border-x">
                                                    {grades?.map((grade) => {
                                                        const gradeName =
                                                            grade.translations
                                                                ?.name?.[
                                                                locale
                                                            ] ?? grade.name;
                                                        const gradeData =
                                                            groupedCategories[
                                                                category
                                                            ]?.[grade.id];
                                                        const index =
                                                            gradeData?.index ??
                                                            `${category}_${grade.id}`;

                                                        const gradesFieldArray =
                                                            form.watch(
                                                                "grades"
                                                            ) || [];

                                                        const gradeErrorMap =
                                                            expandTabWithError(
                                                                form.formState
                                                                    .errors,
                                                                gradesFieldArray
                                                            );

                                                        return (
                                                            <div
                                                                key={grade.id}
                                                                className="flex items-center justify-between border-b px-4 py-2"
                                                            >
                                                                <div>
                                                                    {gradeName}
                                                                </div>
                                                                <div className="flex gap-2">
                                                                    <FreeDatePicker
                                                                        control={
                                                                            form.control
                                                                        }
                                                                        name={`grades[${index}].from`}
                                                                        placeholder="From"
                                                                        error={
                                                                            gradeErrorMap[
                                                                                index
                                                                            ]
                                                                                ?.from
                                                                        }
                                                                        isDisabled={
                                                                            !hasPermit(
                                                                                _exam_semester_setting_create
                                                                            )
                                                                        }
                                                                    />
                                                                    <FreeDatePicker
                                                                        control={
                                                                            form.control
                                                                        }
                                                                        name={`grades[${index}].to`}
                                                                        placeholder="To"
                                                                        error={
                                                                            gradeErrorMap[
                                                                                index
                                                                            ]
                                                                                ?.from
                                                                        }
                                                                        isDisabled={
                                                                            !hasPermit(
                                                                                _exam_semester_setting_create
                                                                            )
                                                                        }
                                                                    />
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            )}
                                        </div>
                                    );
                                }
                            )}
                        </div>
                    )
                )}

                {hasPermit(_exam_semester_setting_create) &&
                    !isLoading &&
                    !isEmpty(groupedCategories) && (
                        <Button
                            onClick={onSubmit}
                            className="ml-auto mr-10 mt-2"
                        >
                            Submit
                        </Button>
                    )}
            </div>
        </div>
    );
};

export default ExamSemesterSettingForm;
