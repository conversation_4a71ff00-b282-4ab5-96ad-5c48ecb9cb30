import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { examAPI, examGradingFrameworkAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import { useTranslations } from "next-intl";

const ExamGradingFrameworkCreateForm = ({ onNext }) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    return activeLanguages ? (
        <FormWrap activeLanguages={activeLanguages} onNext={onNext} />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({ activeLanguages, onNext }) => {
    const form = useForm({
        defaultValues: {
            code: "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createExam, error: postError } = useAxios({
        api: examGradingFrameworkAPI,
        onSuccess: (result) => {
            onNext(result.data?.id);
        },
    });

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();
        form.handleSubmit((data: any) => {
            createExam(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {t("Create ")}
                {t("Exam Grading Framework")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Next")}
                    </Button>
                </form>
            </Form>
        </>
    );
};

export default ExamGradingFrameworkCreateForm;
