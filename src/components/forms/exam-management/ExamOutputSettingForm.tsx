import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ActionDropdown from "@/components/ui/ActionDropdown";
import DataTable from "@/components/ui/DataTable";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import Modal from "@/components/ui/Modal";
import { GET_ALL_PARAMS, gradingSchemeAPI, subjectAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { displayCellValue, isValueTrue } from "@/lib/utils";
import ExamOutputComponentForm from "./ExamOutputComponentForm";

const componentColumns = [
    {
        key: "code",
    },
    {
        key: "subject",
    },
    {
        key: "grading_scheme",
    },
];

const ExamOutputSettingForm = (props: {
    gradingFrameworkId?: any;
    isCreate?: boolean;
    data?: any;
    onSave: (data: any) => void;
    close: () => void;
}) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const { data: subjectOptions, axiosQuery: getSubjects } = useAxios({
        api: subjectAPI,
        onError: props.close,
        locale,
    });

    const { data: gradingOptions, axiosQuery: getGradingScheme } = useAxios({
        api: gradingSchemeAPI,
        onError: props.close,
        locale,
    });

    useEffect(() => {
        getSubjects({
            params: GET_ALL_PARAMS,
        });
        getGradingScheme({
            params: { ...GET_ALL_PARAMS, type: "EXAM" },
        });
    }, []);

    return activeLanguages && subjectOptions && gradingOptions ? (
        <FormWrap
            {...props}
            activeLanguages={activeLanguages}
            subjectOptions={subjectOptions}
            gradingOptions={gradingOptions}
            currentData={props.data}
        />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({
    gradingFrameworkId = null,
    activeLanguages,
    isCreate = false,
    currentData,
    onSave,
    close,
    subjectOptions,
    gradingOptions,
}) => {
    const [openComponent, setOpenComponent] = useState(false);
    const [targetComponentIndex, setTargetComponentIndex] = useState<any>(null);
    const [targetDeleteComponentIndex, setTargetDeleteComponentIndex] =
        useState<any>(null);

    const outputSettingForm = useForm<any>({
        defaultValues: {
            code: currentData?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.name?.[key] ?? "",
                    }),
                    {}
                ),
            report_name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.report_name?.[key] ?? "",
                    }),
                    {}
                ),
            service: currentData?.service ?? "",
            is_final: isValueTrue(currentData?.is_final),
            components: currentData?.components ?? [],
        },
    });

    function getSubjectName(code) {
        return (
            subjectOptions?.find((subject) => subject.code === code)?.name ??
            "-"
        );
    }

    function getGradingSchemeName(code) {
        return (
            gradingOptions?.find((grading) => grading.code === code)?.name ??
            "-"
        );
    }

    function definedComponentData() {
        const components = outputSettingForm.getValues("components");
        return Array.isArray(components)
            ? components?.map((item) => ({
                  code: displayCellValue(item?.code),
                  subject: getSubjectName(item?.subject_code),
                  grading_scheme: getGradingSchemeName(
                      item?.grading_scheme_code
                  ),
              }))
            : [];
    }

    const { append: appendComponent, remove: removeComponent } = useFieldArray({
        control: outputSettingForm.control,
        name: "components",
    });

    function onAddComponent(data) {
        appendComponent(data);
        onSubmit();
    }

    function onCopyComponent(index) {
        const data = outputSettingForm.getValues("components")[index];
        appendComponent(data);
        onSubmit();
    }

    function onUpdateComponent(data) {
        if (typeof data.index === "number") {
            outputSettingForm.setValue(`components[${data.index}]`, data);
            onSubmit();
        }
    }

    function onDeleteComponent() {
        removeComponent(targetDeleteComponentIndex);
        setTargetDeleteComponentIndex(null);
        onSubmit();
    }

    function onSubmit({ isSave = false } = {}) {
        outputSettingForm.handleSubmit((data: any) => {
            if (isCreate) {
                onSave(data);
            } else {
                onSave({ ...data, index: currentData?.index });
            }
        })();
        if (isSave) {
            close();
        }
    }

    useEffect(() => {
        // update form data when currentData changes after saved
        outputSettingForm.setValue("components", currentData?.components ?? []);
    }, [currentData]);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {isCreate ? t("Add ") : t("Update ")}
                {t("Result Source Output Setting")}
            </h2>
            <Form {...outputSettingForm}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit({ isSave: true });
                    }}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormInput
                        control={outputSettingForm.control}
                        name="code"
                    />

                    <div className="">
                        <FormInput
                            control={outputSettingForm.control}
                            name="service"
                        />
                        <div className="mt-1 text-[13px] text-themeLabel">
                            {t("Please get this value from system provider")}
                        </div>
                    </div>

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={outputSettingForm.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})`}
                        />
                    ))}

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={outputSettingForm.control}
                            name={`report_name[${lang?.code}]`}
                            label={`${t("Report Name")} (${t(lang?.name)})`}
                        />
                    ))}

                    <div className="mt-1 lg:col-span-2 lg:ml-0.5">
                        <FormCheckbox
                            control={outputSettingForm.control}
                            name="is_final"
                        />
                    </div>

                    <div className="border-t border-dashed pt-3 lg:col-span-2">
                        <div className="mb-2 flex flex-wrap items-center gap-3">
                            <h3>{t("Components")}</h3>
                            {!isCreate && (
                                <Button
                                    className="w-20"
                                    variant={"outline"}
                                    size={"smaller"}
                                    onClick={() => setOpenComponent(true)}
                                >
                                    {t("Add")}
                                </Button>
                            )}
                        </div>
                        {isCreate ? (
                            <p className="text-themeLabel">
                                {t(
                                    "Components need to be added after output setting is created"
                                )}
                            </p>
                        ) : (
                            <DataTable
                                columns={componentColumns}
                                data={definedComponentData()}
                                actionMenu={({ cell }) => (
                                    <ActionDropdown>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetComponentIndex(
                                                    cell.row.index
                                                );
                                            }}
                                        >
                                            {t("Edit / View")}
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                onCopyComponent(cell.row.index);
                                            }}
                                        >
                                            {t("Copy")}
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setTargetDeleteComponentIndex(
                                                    cell.row.index
                                                )
                                            }
                                        >
                                            {t("Delete")}
                                        </DropdownMenuItem>
                                    </ActionDropdown>
                                )}
                            />
                        )}
                    </div>

                    <Button
                        type="submit"
                        className="ml-auto mt-1 lg:col-span-2"
                    >
                        {t("Save")}
                    </Button>
                </form>
            </Form>

            {/* create component */}
            <Modal
                open={openComponent}
                onOpenChange={setOpenComponent}
                size="medium"
            >
                <ExamOutputComponentForm
                    isCreate={true}
                    onSave={onAddComponent}
                    close={() => setOpenComponent(false)}
                    subjectOptions={subjectOptions}
                    gradingOptions={gradingOptions}
                />
            </Modal>

            {/* update component */}
            <Modal
                open={targetComponentIndex != null}
                onOpenChange={(isOpen: boolean) => {
                    if (!isOpen) setTargetComponentIndex(null);
                }}
                size="medium"
            >
                <ExamOutputComponentForm
                    gradingFrameworkId={gradingFrameworkId}
                    data={{
                        ...outputSettingForm.watch("components")[
                            targetComponentIndex
                        ],
                        index: targetComponentIndex,
                    }}
                    onSave={onUpdateComponent}
                    close={() => setTargetComponentIndex(null)}
                    subjectOptions={subjectOptions}
                    gradingOptions={gradingOptions}
                />
            </Modal>

            {/* delete component */}
            <Modal
                open={targetDeleteComponentIndex != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetDeleteComponentIndex(null);
                }}
            >
                <>
                    <p className="mt-3 font-medium">
                        {t("Are you sure you want to delete?")}
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setTargetDeleteComponentIndex(null)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button onClick={onDeleteComponent}>
                            {t("Confirm")}
                        </Button>
                    </DialogFooter>
                </>
            </Modal>
        </>
    );
};

export default ExamOutputSettingForm;
