import { useEffect, useState } from "react";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import { Form } from "@/components/base-ui/form";
import ActionDropdown from "@/components/ui/ActionDropdown";
import DataTable from "@/components/ui/DataTable";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import { combinedNames, displayCellValue, isValueTrue } from "@/lib/utils";
import ExamResultSourceSubjectComponentForm from "./ExamResultSourceSubjectComponentForm";
import { useTranslations } from "next-intl";

const componentColumns = [
    {
        key: "code",
    },
    {
        key: "name",
    },
    {
        key: "weightage_percent",
        displayAs: "weightage percentage",
    },
];

const ExamResultSourceSubjectForm = (props: {
    isCreate?: boolean;
    data?: any;
    onSave: (data: any) => void;
    close: () => void;
    subjectOptions: any;
}) => {
    return <FormWrap {...props} currentData={props.data} />;
};

const FormWrap = ({
    isCreate = false,
    currentData,
    onSave,
    close,
    subjectOptions,
}) => {
    const [openComponent, setOpenComponent] = useState(false);
    const [targetComponentIndex, setTargetComponentIndex] = useState<any>(null);
    const [targetDeleteComponentIndex, setTargetDeleteComponentIndex] =
        useState<any>(null);

    const subjectForm = useForm<any>({
        defaultValues: {
            code: currentData?.code ?? "",
            grading_type: currentData?.grading_type ?? "",
            weightage_multiplier: currentData?.weightage_multiplier ?? "",
            is_exempted: isValueTrue(currentData?.is_exempted),
            components: currentData?.components ?? [],
        },
    });

    function definedComponentData() {
        const components = subjectForm.watch("components");
        return Array.isArray(components)
            ? components?.map((item) => ({
                  code: displayCellValue(item?.code),
                  name: combinedNames(item?.name) ?? "-",
                  weightage_percent: item?.weightage_percent ?? "-",
              }))
            : [];
    }

    const { append: appendComponent, remove: removeComponent } = useFieldArray({
        control: subjectForm.control,
        name: "components",
    });

    function onAddComponent(data) {
        appendComponent(data);
        onSubmit();
    }

    function onCopyComponent(index) {
        const data = subjectForm.getValues("components")[index];
        appendComponent(data);
        onSubmit();
    }

    function onUpdateComponent(data) {
        if (typeof data.index === "number") {
            subjectForm.setValue(`components[${data.index}]`, data);
            onSubmit();
        }
    }

    function onDeleteComponent() {
        removeComponent(targetDeleteComponentIndex);
        setTargetDeleteComponentIndex(null);
        onSubmit();
    }

    function onSubmit({ isSave = false } = {}) {
        subjectForm.handleSubmit((data: any) => {
            if (isCreate) {
                onSave(data);
            } else {
                onSave({ ...data, index: currentData?.index });
            }
        })();
        if (isSave) {
            close();
        }
    }

    useEffect(() => {
        // update form data when currentData changes after saved
        subjectForm.setValue("components", currentData?.components ?? []);
    }, [currentData]);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {t(isCreate ? "Add " : "Update ")}
                {t("Result Source Subject")}
            </h2>
            <Form {...subjectForm}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit({ isSave: true });
                    }}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormSelect
                        control={subjectForm.control}
                        name="code"
                        label="subject"
                        options={subjectOptions?.map((item) => ({
                            id: item?.code,
                            name: item?.name,
                        }))}
                    />

                    <FormSelect
                        control={subjectForm.control}
                        name="grading_type"
                        label="scoring type"
                        isStringOptions={true}
                        options={["SCORE", "GRADE"]}
                    />

                    <FormInputInterger
                        control={subjectForm.control}
                        name="weightage_multiplier"
                        label="weightage"
                    />

                    <div className="lg:ml-0.5 lg:mt-7">
                        <FormCheckbox
                            control={subjectForm.control}
                            name="is_exempted"
                        />
                    </div>

                    <div className="mt-2 border-t border-dashed pt-3 lg:col-span-2">
                        <div className="mb-2 flex flex-wrap items-center gap-3">
                            <h3>{t("Components")}</h3>
                            {!isCreate && (
                                <Button
                                    className="w-20"
                                    variant={"outline"}
                                    size={"smaller"}
                                    onClick={() => setOpenComponent(true)}
                                >
                                    {t("Add")}
                                </Button>
                            )}
                        </div>
                        {isCreate ? (
                            <p className="text-themeLabel">
                                {t(
                                    "Components need to be added after result source subject is created"
                                )}
                            </p>
                        ) : (
                            <DataTable
                                columns={componentColumns}
                                data={definedComponentData()}
                                actionMenu={({ cell }) => (
                                    <ActionDropdown>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                setTargetComponentIndex(
                                                    cell.row.index
                                                );
                                            }}
                                        >
                                            {t("Edit / View")}
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() => {
                                                onCopyComponent(cell.row.index);
                                            }}
                                        >
                                            {t("Copy")}
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setTargetDeleteComponentIndex(
                                                    cell.row.index
                                                )
                                            }
                                        >
                                            {t("Delete")}
                                        </DropdownMenuItem>
                                    </ActionDropdown>
                                )}
                            />
                        )}
                    </div>

                    <Button
                        type="submit"
                        className="ml-auto mt-1 lg:col-span-2"
                    >
                        {t("Save")}
                    </Button>
                </form>
            </Form>

            {/* create component */}
            <Modal
                open={openComponent}
                onOpenChange={setOpenComponent}
                size="medium"
            >
                <ExamResultSourceSubjectComponentForm
                    isCreate={true}
                    onSave={onAddComponent}
                    close={() => setOpenComponent(false)}
                />
            </Modal>

            {/* update component */}
            <Modal
                open={targetComponentIndex != null}
                onOpenChange={(isOpen: boolean) => {
                    if (!isOpen) setTargetComponentIndex(null);
                }}
                size="medium"
            >
                <ExamResultSourceSubjectComponentForm
                    data={{
                        ...subjectForm.watch("components")[
                            targetComponentIndex
                        ],
                        index: targetComponentIndex,
                    }}
                    onSave={onUpdateComponent}
                    close={() => setTargetComponentIndex(null)}
                />
            </Modal>

            {/* delete component */}
            <Modal
                open={targetDeleteComponentIndex != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetDeleteComponentIndex(null);
                }}
            >
                <>
                    <p className="mt-3 font-medium">
                        Are you sure you want to delete this component?
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setTargetDeleteComponentIndex(null)}
                        >
                            Cancel
                        </Button>
                        <Button onClick={onDeleteComponent}>Confirm</Button>
                    </DialogFooter>
                </>
            </Modal>
        </>
    );
};

export default ExamResultSourceSubjectForm;
