import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    CANCELLED,
    CommonFilterProps,
    COMPLETED,
    DEFAULT_FILTER_PARAMS,
    ERROR,
    GET_ALL_PARAMS,
    gradeAPI,
    IN_PROGRESS,
    PENDING,
    semesterSettingAPI,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useAxios } from "@/lib/hook";
import { useLocale, useTranslations } from "next-intl";

const FilterExamPostingStatusForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const t = useTranslations("common");
    const locale = useLocale();
    const [currentSemester, setCurrentSemester] = useState<any>();
    const form = useForm({
        defaultValues: {
            semester_setting_id: filter?.semester_setting_id ?? "",
            grade_id: filter?.grade_id ?? "",
            status: filter?.status ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            semester_setting_id: currentSemester?.id ?? null,
        });
        close();
    }

    const { data: semesterSettings, axiosQuery: getSemesterSettings } =
        useAxios({
            api: semesterSettingAPI,
            locale,
            onSuccess: (res) => {
                const currentSemester = res.data.find(
                    (semester) => semester.is_current_semester
                );

                if (currentSemester) {
                    form.setValue("semester_setting_id", currentSemester.id);
                    setCurrentSemester(currentSemester);
                }
            },
        });

    const { data: gradeOptions, axiosQuery: getGradeOptions } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    useEffect(() => {
        getSemesterSettings({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGradeOptions({ params: GET_ALL_PARAMS });
    }, []);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name="semester_setting_id"
                    label="semester"
                    options={semesterSettings}
                    isSortByName={false}
                />

                <FormSelect
                    control={form.control}
                    name="grade_id"
                    label="grade"
                    isSortByName={false}
                    options={gradeOptions}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[
                        IN_PROGRESS,
                        COMPLETED,
                        PENDING,
                        ERROR,
                        CANCELLED,
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterExamPostingStatusForm;
