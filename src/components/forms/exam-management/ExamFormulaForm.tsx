import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useRef, useState } from "react";
import clsx from "clsx";
import { Asterisk, Minus, Plus, Slash, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import FreeSelect from "@/components/ui/FreeSelect";
import {
    examFixedFormulaAPI,
    examGradingFrameworkAPI,
    examOutputFormulaAPI,
    examResultSourceFormulaAPI,
    GET_ALL_PARAMS,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import Modal from "@/components/ui/Modal";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import { toYMD } from "@/lib/utils";
import FreeInput from "@/components/ui/FreeInput";

function formatValueDisplay(item) {
    const size = 16;
    return item === "+" ? (
        <Plus size={size} />
    ) : item === "-" ? (
        <Minus size={size} />
    ) : item === "/" ? (
        <Slash size={size} />
    ) : item === "*" ? (
        <Asterisk size={size} />
    ) : (
        item
    );
}

const ExamFormulaForm = (props: {
    gradingFrameworkId: any;
    data?: any;
    onSave: (data: any) => void;
    subjectOptions: any;
    close: () => void;
}) => {
    const locale = useLocale();
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosQuery: getGradingFramework } = useAxios({
        api: `${examGradingFrameworkAPI}/${props.gradingFrameworkId}`,
        locale,
        onSuccess: (result) => {
            const _options: Record<string, any> = {};
            const gradingFrameWorksConfig = result.data?.configuration;

            function getUniqueList(array) {
                const codeList =
                    array
                        ?.map((item) => item?.code)
                        ?.filter((code) => code != null) ?? [];

                return [...new Set(codeList)];
            }

            const resultSources =
                getUniqueList(gradingFrameWorksConfig?.result_sources) ?? [];

            const outputs =
                getUniqueList(gradingFrameWorksConfig?.output) ?? [];

            const subjects =
                getUniqueList(
                    gradingFrameWorksConfig?.result_sources?.flatMap(
                        (item) => item?.subjects
                    )
                ) ?? [];

            const components =
                getUniqueList(
                    gradingFrameWorksConfig?.output?.flatMap(
                        (item) => item?.components
                    )
                ) ?? [];

            _options.resultSources = resultSources;
            _options.subjects = props.subjectOptions?.filter((option) =>
                subjects.includes(option?.code)
            );
            _options.outputs = outputs;
            _options.components = components;
            setOptions(_options);
        },
    });

    useEffect(() => {
        getGradingFramework();
    }, []);

    return options ? (
        <FormWrap {...props} currentData={props.data} options={options} />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({
    gradingFrameworkId,
    currentData,
    onSave,
    close,
    options,
}) => {
    const targetRef = useRef<any>({});

    const [formulaArray, setFormulaArray] = useState<any[]>([]);
    const [targetIndex, setTargetIndex] = useState(-1);
    const [formulaOptions, setFormulaOptions] = useState<any>();

    function getLabels() {
        const allOptions = Object.values(formulaOptions ?? {}).flat();

        const optionsMap = new Map(
            allOptions.map((item: any) => [item?.value, item?.label])
        );

        const labels = formulaArray?.map((formula) => {
            const label = optionsMap.get(formula);
            if (label) {
                return label;
            }
            const fixedFormula = formula.split("(")?.[0];
            const fixedFormulaLabel = optionsMap.get(fixedFormula);

            if (fixedFormulaLabel) {
                const args = formula
                    .match(/\((.*?)\)/)?.[0]
                    ?.replaceAll(`\"`, "");
                return fixedFormulaLabel + args;
            }
            return { text: formula, isNotFound: true };
        });

        return labels;
    }

    function removeFormula(index) {
        let newArray = [...formulaArray];
        newArray[index] = null;
        newArray = newArray.filter((item) => item !== null);
        setFormulaArray(newArray);
        setTimeout(() => {
            setTargetIndex(-1);
        }, 0);
    }

    function onSelect(value) {
        if (targetIndex === -1) {
            // append
            setFormulaArray([...formulaArray, value]);
        } else {
            // replace
            const newArray = [...formulaArray];
            newArray[targetIndex] = value;
            setFormulaArray(newArray);
        }
    }

    function updateFormulaOptions(array, type) {
        setFormulaOptions((prev) => ({
            ...prev,
            [type]: [...new Set([...(prev?.[type] ?? []), ...array])],
        }));
    }

    function onSubmit() {
        onSave(formulaArray.join(" "));
        close();
    }

    function handleClickOutside(event) {
        const clickedInside = Object.values(targetRef.current ?? {}).some(
            (ref: Record<string, any>) => ref && ref.contains(event.target)
        );
        if (!clickedInside) {
            setTargetIndex(-1);
        }
    }

    useEffect(() => {
        document.addEventListener("mousedown", handleClickOutside);

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [targetRef]);

    const t = useTranslations("common");

    return (
        <div className="">
            <h2 className="mb-5">{t("Edit Formula")}</h2>
            <h3>{t("Current Formula")}</h3>
            <div className="mb-4 mt-1.5 text-[15px] font-medium text-gray-700">
                {currentData}
            </div>
            <h3>{t("New Formula")}</h3>
            <div className="mt-3 flex min-h-[64px] flex-wrap gap-x-2 gap-y-3 rounded-md border border-dashed border-themeGreen p-3">
                {getLabels().map((formula, index) => (
                    <div
                        key={index}
                        ref={(el) => {
                            const value = formula?.isNotFound
                                ? formula.text
                                : formula;
                            targetRef.current[value] = el;
                        }}
                        className={clsx(
                            "c-text-size flex min-h-[38px] cursor-pointer items-center gap-x-2.5 rounded-md border border-input py-2 pl-3 pr-2 font-medium transition hover:border-themeGreen2",
                            formula?.isNotFound && "text-gray-400",
                            targetIndex === index &&
                                "border-themeGreen2 bg-white"
                        )}
                        style={{
                            boxShadow:
                                targetIndex === index
                                    ? "0 0 8px rgba(68, 182, 86, 0.5)"
                                    : "",
                        }}
                        onClick={() => {
                            setTargetIndex(index);
                        }}
                    >
                        {formula?.isNotFound
                            ? formula?.text
                            : formatValueDisplay(formula)}
                        <div
                            className="ml-auto flex h-5 w-5 items-center rounded-full border border-gray-300"
                            onClick={() => removeFormula(index)}
                        >
                            <X className="m-auto h-3 w-3 text-gray-400" />
                        </div>
                    </div>
                ))}
            </div>

            {gradingFrameworkId != null && (
                <div className="mt-5 grid gap-y-5">
                    <FixedFormulas
                        id={gradingFrameworkId}
                        targetRef={targetRef}
                        onSelect={onSelect}
                        updateFormulaOptions={(array) =>
                            updateFormulaOptions(array, "fixed")
                        }
                    />
                    <ResultSourceFormulas
                        id={gradingFrameworkId}
                        targetRef={targetRef}
                        options={options}
                        onSelect={onSelect}
                        updateFormulaOptions={(array) =>
                            updateFormulaOptions(array, "source")
                        }
                    />
                    <OutputFormulas
                        id={gradingFrameworkId}
                        targetRef={targetRef}
                        options={options}
                        onSelect={onSelect}
                        updateFormulaOptions={(array) =>
                            updateFormulaOptions(array, "output")
                        }
                    />
                </div>
            )}
            <Button onClick={onSubmit} className="ml-auto mt-8">
                {t("Save")}
            </Button>
        </div>
    );
};

function getUniqueList(array) {
    return Array.from(
        new Map(array?.map((item) => [item.value, item])).values()
    );
}

const FixedFormulas = ({ id, targetRef, onSelect, updateFormulaOptions }) => {
    const locale = useLocale();
    const [targetItem, setTargetItem] = useState<any>(null);

    const { data: fixedFormulas, axiosQuery: getFixedFormulas } = useAxios({
        api: examFixedFormulaAPI(id),
        locale,
        onSuccess: (result) => {
            updateFormulaOptions(getUniqueList(result.data));
        },
    });

    useEffect(() => {
        getFixedFormulas({
            params: GET_ALL_PARAMS,
        });
    }, []);

    function onClick(item) {
        console.log(item);
        if (item?.args?.length > 0) {
            setTargetItem(item);
        } else {
            onSelect(item?.value);
        }
    }

    const t = useTranslations("common");

    return (
        <>
            <div className="pt-3">
                <h3 className="mb-2">{t("Fixed Formulas")}</h3>
                <div className="flex flex-wrap gap-2">
                    {fixedFormulas?.length === 0 && (
                        <div className="c-text-size ml-1 pb-3 text-themeLabel">
                            {t("No data found")}
                        </div>
                    )}
                    {fixedFormulas?.length > 0 &&
                        getUniqueList(fixedFormulas)?.map(
                            (item: any, index) => (
                                <div
                                    key={item?.value + index}
                                    ref={(el) => {
                                        targetRef.current[item?.value] = el;
                                    }}
                                    className={"formula-button"}
                                    onClick={() => onClick(item)}
                                >
                                    {formatValueDisplay(item?.label)}
                                </div>
                            )
                        )}
                </div>
            </div>
            <Modal open={targetItem} onOpenChange={setTargetItem}>
                <FixedFormulaForm
                    item={targetItem}
                    onAdd={onSelect}
                    close={() => setTargetItem(null)}
                />
            </Modal>
        </>
    );
};

const FixedFormulaForm = ({ item, onAdd, close }) => {
    const _fromDate = "From Date";
    const _toDate = "To Date";

    const form = useForm({
        defaultValues: Object.fromEntries(
            item?.args?.map((key) => [key, ""]) ?? []
        ),
    });

    const [isDateRangeError, setIsDateRangeError] = useState(false);

    async function onSubmit(data) {
        if (data[_fromDate] && data[_toDate]) {
            if (data[_fromDate] > data[_toDate]) {
                setIsDateRangeError(true);
                return;
            }
        }

        data = Object.entries(data).map(([key, value]) => {
            const formattedValue = value instanceof Date ? toYMD(value) : value;
            return `\"${formattedValue}\"`;
        });

        const formatedValue = `${item.value}(${data?.join(",")})`;
        console.log(formatedValue);

        onAdd(formatedValue);
        close();
    }

    const t = useTranslations("common");

    return (
        <Form {...form}>
            <form
                className="grid min-h-16 gap-y-4"
                onSubmit={form.handleSubmit(onSubmit)}
            >
                <h3 className="ml-[1px]">{item?.label}</h3>

                {item?.args?.map((key, index) => {
                    if ([_fromDate, _toDate].includes(key)) {
                        return (
                            <div key={key + index}>
                                <DatePicker
                                    control={form.control}
                                    name={key}
                                    rules={{
                                        required: "This field is required",
                                    }}
                                    onChange={() =>
                                        isDateRangeError
                                            ? setIsDateRangeError(false)
                                            : null
                                    }
                                />
                                {isDateRangeError && (
                                    <div className="warning-text">
                                        {t(
                                            "From Date should be earlier than To Date"
                                        )}
                                    </div>
                                )}
                            </div>
                        );
                    }
                    return (
                        <FreeInput
                            key={index}
                            control={form.control}
                            name={key}
                            rules={{
                                required: "This field is required",
                            }}
                            error={form.formState.errors?.[key]}
                        />
                    );
                })}
                <Button type="submit" className="ml-auto mt-2">
                    {t("Add")}
                </Button>
            </form>
        </Form>
    );
};

const ResultSourceFormulas = ({
    id,
    targetRef,
    options,
    onSelect,
    updateFormulaOptions,
}) => {
    const locale = useLocale();

    const resultSourceFilterForm = useForm<any>({
        defaultValues: {
            result_source_code: "",
            subject_code: "",
        },
    });

    const { data: resultSourceFormulas, axiosQuery: getResultSourceFormulas } =
        useAxios({
            api: examResultSourceFormulaAPI(id),
            locale,
            onSuccess: (result) => {
                updateFormulaOptions(getUniqueList(result.data));
            },
        });

    function onFilter() {
        getResultSourceFormulas({
            params: {
                ...GET_ALL_PARAMS,
                ...resultSourceFilterForm.getValues(),
            },
        });
    }

    const t = useTranslations("common");

    return (
        <div className="border-t border-dashed pt-3">
            <h3 className="mb-2">{t("Result Source Formulas")}</h3>
            <div className="mb-3 flex max-w-[800px] flex-wrap items-center gap-3">
                <div className="flex-grow">
                    <FreeSelect
                        hasLabel={false}
                        control={resultSourceFilterForm.control}
                        name="result_source_code"
                        placeholder="Result Source"
                        options={options?.resultSources}
                        isStringOptions={true}
                        isStartCaseForStringOptions={false}
                    />
                </div>
                <div className="flex-grow">
                    <FreeSelect
                        hasLabel={false}
                        control={resultSourceFilterForm.control}
                        name="subject_code"
                        placeholder="Subject"
                        options={options?.subjects?.map((item) => ({
                            id: item?.code,
                            name: item?.name,
                        }))}
                    />
                </div>

                <Button
                    className="min-w-20 text-themeGreen"
                    variant={"outline"}
                    size={"smaller"}
                    onClick={onFilter}
                >
                    {t("Filter")}
                </Button>
            </div>

            <div className="flex flex-wrap gap-2">
                {resultSourceFormulas?.length === 0 && (
                    <div className="c-text-size ml-1 pb-3 text-themeLabel">
                        {t("No data found")}
                    </div>
                )}

                {resultSourceFormulas?.length > 0 &&
                    getUniqueList(resultSourceFormulas)?.map(
                        (item: any, index) => (
                            <div
                                key={item?.value + index}
                                ref={(el) => {
                                    targetRef.current[item?.value] = el;
                                }}
                                className={"formula-button"}
                                onClick={() => onSelect(item?.value)}
                            >
                                {item?.label}
                            </div>
                        )
                    )}
            </div>
        </div>
    );
};

const OutputFormulas = ({
    id,
    targetRef,
    options,
    onSelect,
    updateFormulaOptions,
}) => {
    const locale = useLocale();

    const outputFilterForm = useForm<any>({
        defaultValues: {
            output_code: "",
            component_code: "",
        },
    });

    const { data: outputFormulas, axiosQuery: getOutputFormulas } = useAxios({
        api: examOutputFormulaAPI(id),
        locale,
        onSuccess: (result) => {
            updateFormulaOptions(getUniqueList(result.data));
        },
    });

    function onFilter() {
        getOutputFormulas({
            params: { ...GET_ALL_PARAMS, ...outputFilterForm.getValues() },
        });
    }

    const t = useTranslations("common");

    return (
        <div className="border-t border-dashed pt-3">
            <h3 className="mb-2">{t("Output Formulas")}</h3>
            <div className="mb-3 flex max-w-[800px] flex-wrap items-center gap-3">
                <div className="flex-grow">
                    <FreeSelect
                        hasLabel={false}
                        control={outputFilterForm.control}
                        name="output_code"
                        placeholder="Output"
                        options={options?.outputs}
                        isStringOptions={true}
                        isStartCaseForStringOptions={false}
                    />
                </div>
                <div className="flex-grow">
                    <FreeSelect
                        hasLabel={false}
                        control={outputFilterForm.control}
                        name="component_code"
                        placeholder="Component"
                        options={options?.components}
                        isStringOptions={true}
                        isStartCaseForStringOptions={false}
                    />
                </div>

                <Button
                    className="min-w-20 text-themeGreen"
                    variant={"outline"}
                    size={"smaller"}
                    onClick={onFilter}
                >
                    {t("Filter")}
                </Button>
            </div>

            <div className="flex flex-wrap gap-2">
                {outputFormulas?.length === 0 && (
                    <div className="c-text-size ml-1 pb-3 text-themeLabel">
                        {t("No data found")}
                    </div>
                )}
                {outputFormulas?.length > 0 &&
                    getUniqueList(outputFormulas)?.map((item: any, index) => (
                        <div
                            key={item?.value + index}
                            ref={(el) => {
                                targetRef.current[item?.value] = el;
                            }}
                            className={"formula-button"}
                            onClick={() => onSelect(item?.value)}
                        >
                            {item?.label}
                        </div>
                    ))}
            </div>
        </div>
    );
};

export default ExamFormulaForm;
