import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { useLanguages } from "@/lib/store";
import { useTranslations } from "next-intl";

const ExamResultSourceSubjectComponentForm = (props: {
    isCreate?: boolean;
    data?: any;
    onSave: (data: any) => void;
    close: () => void;
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    return activeLanguages ? (
        <FormWrap
            activeLanguages={activeLanguages}
            {...props}
            currentData={props.data}
        />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({
    activeLanguages,
    isCreate = false,
    currentData,
    onSave,
    close,
}) => {
    const componentForm = useForm<any>({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.name?.[key] ?? "",
                    }),
                    {}
                ),
            code: currentData?.code ?? "",
            weightage_percent: currentData?.weightage_percent ?? "",
        },
    });

    function onSubmit({ isSave = false } = {}) {
        componentForm.handleSubmit((data: any) => {
            console.log(data);
            if (isCreate) {
                onSave(data);
            } else {
                onSave({ ...data, index: currentData?.index });
            }
        })();
        if (isSave) {
            close();
        }
    }

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {t(isCreate ? "Add " : "Update ")}
                {t("Result Source Subject Component")}
            </h2>
            <Form {...componentForm}>
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSubmit({ isSave: true });
                    }}
                    className="grid-form lg:min-w-[800px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={componentForm.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})`}
                        />
                    ))}

                    <FormInput control={componentForm.control} name="code" />

                    <FormInputInterger
                        control={componentForm.control}
                        name="weightage_percent"
                        label="weightage percentage"
                        isPercent={true}
                    />

                    <Button
                        type="submit"
                        className="ml-auto mt-1 lg:col-span-2"
                    >
                        {t("Save")}
                    </Button>
                </form>
            </Form>
        </>
    );
};

export default ExamResultSourceSubjectComponentForm;
