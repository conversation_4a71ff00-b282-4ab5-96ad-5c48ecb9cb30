import { useLocale } from "next-intl";
import { useEffect } from "react";
import DOMPurify from "isomorphic-dompurify";
import { capitalize } from "lodash";
import { Paperclip } from "lucide-react";
import Link from "next/link";
import InfoCard from "@/components/ui/InfoCard";
import { announcementAPI, DATE_FORMAT } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { displayDateTime, isTypeEmployee, optionUserLabel } from "@/lib/utils";

const ViewAnnouncement = ({ id, close }) => {
    const locale = useLocale();

    const { data: announcement, axiosQuery: getAnnouncement } = useAxios({
        api: announcementAPI,
        locale,
        onError: close,
    });

    useEffect(() => {
        getAnnouncement({ id });
    }, []);

    function formattedData(data) {
        const createdBy = data?.created_by?.userables?.find((userable) =>
            isTypeEmployee(userable?.user_type_description)
        );
        const approvedBy = data?.approved_by?.userables?.find((userable) =>
            isTypeEmployee(userable?.user_type_description)
        );
        return {
            title: data?.title,
            status: capitalize(data?.status),
            scheduled_time: displayDateTime(
                data?.scheduled_time,
                DATE_FORMAT.forDisplay
            ),
            sent_at: displayDateTime(data?.sent_at, DATE_FORMAT.forDisplay),
            created_at: displayDateTime(
                data?.created_at,
                DATE_FORMAT.forDisplay
            ),
            created_by: optionUserLabel(
                createdBy?.number,
                createdBy?.translations?.name
            ),
            approved_by: optionUserLabel(
                approvedBy?.number,
                approvedBy?.translations?.name
            ),
            recipients_reached: data?.recipient_count,
        };
    }

    return announcement ? (
        <div>
            <InfoCard
                cardStyleClass="w-full"
                title="Announcement Details"
                noBorder
                data={formattedData(announcement)}
            />
            <div className="mb-1 mt-2 border-t pt-2 text-[14px] font-medium text-themeLabel">
                Message
            </div>
            <div
                dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(announcement?.message),
                }}
            />

            {announcement.attachments?.length > 0 && (
                <div className="mb-1 mt-2 border-t pt-2 text-[14px] font-medium text-themeLabel">
                    Attachments
                </div>
            )}
            {announcement.attachments?.map((url, index) => (
                <Link
                    key={`${url} + ${index}`}
                    href={url}
                    className="mb-1.5 flex w-fit items-start gap-x-1.5"
                    target="_blank"
                >
                    <Paperclip size={16} className="text-gray-500" />
                    <div className="text-sm leading-none underline">
                        File {index + 1}
                    </div>
                </Link>
            ))}
        </div>
    ) : (
        <div className="h-10"></div>
    );
};

export default ViewAnnouncement;
