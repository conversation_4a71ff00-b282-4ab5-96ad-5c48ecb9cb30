import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { chunk, flatten } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import MultiTypeTargetPicker from "@/components/ui/PickersWithPaginatedTable/MultiTypeTargetPicker";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import {
    announcementGroupAPI,
    CommonFormProps,
    DEFAULT_FILTER_PARAMS,
    EMPLOYEE,
    STUDENT,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    isTypeStudent,
    isValueTrue,
    showBackendFormError,
    getChunkedUserableData,
} from "@/lib/utils";

const AnnouncementGroupForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: announcementGroupUserable, axiosQuery: getAnnouncementGroupUserable } =
        useAxios({
            api: `${announcementGroupAPI}/${props.id}/get-userables`,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getAnnouncementGroupUserable();
        }
    }, []);

    return props.isCreate || (props.currentTableData && announcementGroupUserable) ? (
        <FormWrap 
            announcementGroupUserable={announcementGroupUserable} 
            announcementGroup={props.currentTableData} 
            {...props} 
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    announcementGroupUserable: any;
    announcementGroup: any;
};

const FormWrap = ({
    isCreate = false,
    announcementGroupUserable,
    announcementGroup,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: announcementGroup?.name ?? "",
            is_active: isCreate
                ? true
                : isValueTrue(announcementGroup?.is_active),
            student_ids: [],
            employee_ids: [],
        },
    });

    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        getChunkedUserableData(announcementGroupUserable)
    );

    const { axiosPost: createAnnouncementGroup, error: postError } = useAxios({
        api: announcementGroupAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateAnnouncementGroup, error: putError } = useAxios({
        api: announcementGroupAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            flatten(targetsChunk).forEach((target) => {
                if (isTypeStudent(target.type)) {
                    data.student_ids.push(target.target_id);
                } else {
                    data.employee_ids.push(target.target_id);
                }
            });
            if (isCreate) {
                createAnnouncementGroup(data);
            } else {
                updateAnnouncementGroup({ id: announcementGroup.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Announcement Group
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormInput
                        control={form.control}
                        name={`name`}
                        label={`Name*`}
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name={"is_active"}
                        label="Status*"
                    />

                    <div className="mt-3 border-t border-dashed pt-5 lg:col-span-2">
                        <MultiTypeTargetPicker
                            title="Target List"
                            targetsChunk={targetsChunk}
                            setTargetsChunk={setTargetsChunk}
                            targetTypes={[STUDENT, EMPLOYEE]}
                        />
                    </div>

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default AnnouncementGroupForm;
