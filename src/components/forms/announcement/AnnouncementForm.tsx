import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { format } from "date-fns";
import { flatten } from "lodash";
import dynamic from "next/dynamic";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormMultipleFilesInput from "@/components/ui/FormMultipleFilesInput";
import FormSelect from "@/components/ui/FormSelect";
import MultiTypeTargetPicker from "@/components/ui/PickersWithPaginatedTable/MultiTypeTargetPicker";
import {
    announcementAPI,
    announcementGroupAPI,
    CommonFormProps,
    DATE_FORMAT,
    EMPLOYEE,
    GET_ALL_PARAMS,
    STUDENT,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { isTypeStudent, showBackendFormError, toUTC } from "@/lib/utils";

const RichTextEditor = dynamic(() => import("@/components/ui/RichTextEditor"), {
    ssr: false,
});

const AnnouncementForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: announcement, axiosQuery: getAnnouncement } = useAxios({
        api: announcementAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getAnnouncement({ id: props.id });
        }
    }, []);

    return props.isCreate || announcement ? (
        <FormWrap announcement={announcement} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    announcement: any;
};

const FormWrap = ({
    isCreate = false,
    announcement,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            title: "",
            message: "",
            status: "",
            scheduled_time: format(Date.now(), DATE_FORMAT.YMD_HMS),
            send_now: false,
            announcement_group_ids: [],
            student_as_recipient: true,
            guardian_as_recipient: true,
            student_ids: [],
            employee_ids: [],
            attachments: [],
        },
    });

    const [receiversChunk, setReceiversChunk] = useState<any[]>([]);

    const { axiosMultipartPost: createAnnouncement, error: postError } =
        useAxios({
            api: announcementAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const locale = useLocale();

    const { data: announcementGroups, axiosQuery: getAnnouncementGroups } =
        useAxios({
            api: announcementGroupAPI,
            locale,
        });

    useEffect(() => {
        getAnnouncementGroups({ params: { ...GET_ALL_PARAMS, is_active: 1 } });
    }, []);

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            flatten(receiversChunk).forEach((receiver) => {
                if (isTypeStudent(receiver.type)) {
                    data.student_ids.push(receiver.target_id);
                } else {
                    data.employee_ids.push(receiver.target_id);
                }
            });
            if (data.scheduled_time) {
                data.scheduled_time = toUTC(
                    data.scheduled_time,
                    DATE_FORMAT.YMD_HMS
                );
            }
            data.student_as_recipient = data.student_as_recipient ? 1 : 0;
            data.guardian_as_recipient = data.guardian_as_recipient ? 1 : 0;
            if (data.send_now) {
                delete data.scheduled_time;
            }
            data.send_now = data.send_now ? 1 : 0;
            console.log("data", data);
            createAnnouncement(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Announcement
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <div className="lg:col-span-2">
                        <FormInput
                            control={form.control}
                            name={`title`}
                            label={`Title*`}
                        />
                    </div>
                    <div>
                        {form.watch("send_now") ? (
                            <p className="ml-0.5 font-medium text-themeLabel">
                                Scheduled Time*
                            </p>
                        ) : (
                            <DateTimePicker
                                control={form.control}
                                name={"scheduled_time"}
                                label="Scheduled Time*"
                                disableDateBeforeToday={true}
                            />
                        )}
                        <FormCheckbox
                            control={form.control}
                            name={"send_now"}
                            label="Send Now"
                            styleClass="mt-2.5 ml-0.5"
                        />
                    </div>
                    <FormSelect
                        control={form.control}
                        isMulti
                        name={"announcement_group_ids"}
                        label={"Announcement Groups*"}
                        options={announcementGroups}
                    />
                    <div className="mt-2 border-t border-dashed lg:col-span-2"></div>

                    <FormCheckbox
                        control={form.control}
                        name={"student_as_recipient"}
                        label="Send to students"
                        styleClass="ml-0.5"
                    />
                    <FormCheckbox
                        control={form.control}
                        name={"guardian_as_recipient"}
                        label="Send to student's guardian"
                        styleClass="ml-0.5"
                    />

                    <div className="lg:col-span-2">
                        <MultiTypeTargetPicker
                            title="Receiver List"
                            targetsChunk={receiversChunk}
                            setTargetsChunk={setReceiversChunk}
                            targetTypes={[STUDENT, EMPLOYEE]}
                        />
                    </div>

                    <div className="border-t border-dashed pt-3 lg:col-span-2">
                        <Label className="label">Message*</Label>
                        <RichTextEditor
                            value={form.getValues("message")}
                            onChange={(value) =>
                                form.setValue("message", value)
                            }
                        />
                        <div className="pt-12 lg:pt-3">
                            <FormMultipleFilesInput
                                form={form}
                                name="attachments"
                                errors={form.formState.errors}
                            />
                        </div>
                    </div>

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-5">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default AnnouncementForm;
