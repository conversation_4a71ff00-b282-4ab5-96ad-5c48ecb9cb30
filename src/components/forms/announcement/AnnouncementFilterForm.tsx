import { useState } from "react";
import clsx from "clsx";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import Modal from "@/components/ui/Modal";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    EMPLOYEE,
    STUDENT,
} from "@/lib/constant";
import { optionUserLabel, toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterAnnouncementForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            title: filter?.title ?? "",
            message: filter?.message ?? "",
            status: filter?.status ?? "",
            scheduled_time: filter?.scheduled_time ?? null,
            sent_at: filter?.sent_at ?? null,
            created_by: filter?.created_by ?? "",
            sent_by: filter?.sent_by ?? "",
        },
    });
    const t = useTranslations("common");

    const [openSearchCreatedBy, setOpenSearchCreatedBy] = useState(false);
    const [openSearchSentBy, setOpenSearchSentBy] = useState(false);

    const [selectedCreatedBy, setSelectedCreatedBy] = useState<string | null>(
        filter.created_by_name ?? null
    );
    const [selectedSentBy, setSelectedSentBy] = useState<string | null>(
        filter.sent_by_name ?? null
    );

    function onSubmit(data: Record<string, any>) {
        data.scheduled_time = toYMD(data.scheduled_time);
        data.sent_at = toYMD(data.sent_at);
        data.created_by_name = selectedCreatedBy;
        data.sent_by_name = selectedSentBy;

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"title"} />
                <FormSelect
                    control={form.control}
                    options={[
                        {
                            id: "PENDING_APPROVAL",
                            name: "Pending Approval",
                        },
                        { id: "SCHEDULED", name: "Scheduled" },
                        { id: "SENDING", name: "Sending" },
                        { id: "SENT", name: "Sent" },
                        { id: "EXPIRED", name: "Expired" },
                        { id: "CANCELLED", name: "Cancelled" },
                    ]}
                    name={"status"}
                />
                <DatePicker name="scheduled_time" control={form.control} />
                <DatePicker name="sent_at" control={form.control} />

                <div>
                    <div className="c-text-size mb-1.5 font-medium capitalize text-themeLabel">
                        Created by
                    </div>
                    {
                        <div
                            className={clsx(
                                "c-text-size min-h-[42px] rounded-md border border-input px-4 py-2.5",
                                !selectedCreatedBy && "text-themeGray3"
                            )}
                            onClick={() => setOpenSearchCreatedBy(true)}
                        >
                            {selectedCreatedBy ?? "Select Employee"}
                        </div>
                    }
                </div>

                <div className="lg:col-span-2">
                    <FormInput control={form.control} name={"message"} />
                </div>

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>

                <Modal
                    open={openSearchCreatedBy}
                    onOpenChange={setOpenSearchCreatedBy}
                    size="large"
                >
                    <StaffSearchEngine
                        setSelection={(staff) => {
                            if (staff) {
                                const staffLabel = optionUserLabel(
                                    staff.employee_number,
                                    staff.translations.name
                                );
                                form.setValue("created_by", staff.id);
                                setSelectedCreatedBy(staffLabel);
                            }
                        }}
                        close={() => setOpenSearchCreatedBy(false)}
                    />
                </Modal>

                <Modal
                    open={openSearchSentBy}
                    onOpenChange={setOpenSearchSentBy}
                    size="large"
                >
                    <StaffSearchEngine
                        setSelection={(staff) => {
                            if (staff) {
                                const staffLabel = optionUserLabel(
                                    staff.employee_number,
                                    staff.translations.name
                                );
                                form.setValue("sent_by", staff.id);
                                setSelectedSentBy(staffLabel);
                            }
                        }}
                        close={() => setOpenSearchSentBy(false)}
                    />
                </Modal>
            </form>
        </Form>
    );
};

export default FilterAnnouncementForm;
