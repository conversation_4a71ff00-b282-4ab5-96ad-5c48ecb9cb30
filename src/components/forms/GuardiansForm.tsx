import { useEffect, useState } from "react";
import clsx from "clsx";
import { capitalize, isArray, isEmpty } from "lodash";
import { Check, ChevronDownCircle, X } from "lucide-react";
import { useFieldArray } from "react-hook-form";
import toast from "react-hot-toast";
import { isValidPhoneNumber } from "react-phone-number-input";
import Select from "react-select";
import { v4 as uuidv4 } from "uuid";
import {
    FATHER,
    MOTHER,
    GUARDIAN,
    selectStyles,
    maritalStatusOptions,
    guardianAPI,
    GET_ALL_PARAMS,
    userAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import {
    combinedNames,
    formatStringsForSelect,
    getInputErrorMessage,
    isChineseCode,
    isMY,
    isValueTrue,
} from "@/lib/utils";
import { But<PERSON> } from "../base-ui/button";
import FormCheckbox from "../ui/FormCheckbox";
import FormDivider from "../ui/FormDivider";
import FormInput from "../ui/FormInput";
import FormPhoneInput from "../ui/FormPhoneInput";
import FormSelect from "../ui/FormSelect";
import FormTextarea from "../ui/FormTextarea";
import Modal from "../ui/Modal";
import GuardianSearchEngine from "../ui/search-engines/GuardianSearchEngine";
import { useTranslations } from "next-intl";

const GuardiansForm = ({
    form,
    options,
    children,
    isHostel = false,
    isEnrollment = false,
}: {
    form: any;
    options: Record<string, any>;
    children?: React.ReactNode;
    isHostel?: boolean;
    isEnrollment?: boolean;
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const t = useTranslations("common");

    const guardianTypeOptions = [FATHER, MOTHER, GUARDIAN].map((type) => ({
        value: type,
        label: capitalize(t(type)),
    }));
    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "guardians",
    });

    const [openSearch, setOpenSearch] = useState(false);
    const [isOpenList, setIsOpenList] = useState<number[]>([]);
    const [selectedGuardianType, setSelectedGuardianType] = useState<any>();

    function canAdd() {
        if (!selectedGuardianType) return false;
        if ([FATHER, MOTHER].includes(selectedGuardianType)) {
            const hasAdded = fields.some(
                (person: any) => person.type === selectedGuardianType
            );
            if (hasAdded) {
                toast(
                    `Only one ${selectedGuardianType.toLowerCase()} is allowed to be added`
                );
                return false;
            }
        }
        return true;
    }
    function onAddNew() {
        if (!canAdd()) return;
        form.clearErrors("guardians");
        append({
            id: uuidv4(),
            type: selectedGuardianType,
            _isNew: true,
            name: activeLanguages
                ?.map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: "",
                    }),
                    {}
                ),
            nric: "",
            passport_number: "",
            email: "",
            phone_number: "",
            married_status: "",
            occupation: "",
            occupation_description: "",
            nationality_id:
                options?.countries.find((country) =>
                    isMY(country.name?.toLowerCase())
                )?.id ?? "",
            race_id: "",
            religion_id: "",
            education_id: "",
            is_primary: false,
            with_user_account: true,
            _with_user_account: false,
            is_direct_dependant: !isHostel,
        });
        setIsOpenList([fields.length]);
    }

    function onAddExisting(guardian) {
        console.log(guardian);
        if (!guardian) return;
        append(formatGuardianData(guardian));
        setIsOpenList([fields.length]);
    }

    function formatGuardianData(guardian) {
        return {
            id: guardian?.id ?? undefined,
            type: selectedGuardianType,
            name: guardian?.translations?.name ?? "",
            nric: guardian?.nric ?? "",
            passport_number: guardian?.passport_number ?? "",
            email: guardian?.email ?? "",
            phone_number: guardian?.phone_number ?? "",
            married_status: guardian?.married_status ?? "",
            occupation: guardian?.occupation ?? "",
            occupation_description: guardian?.occupation_description ?? "",

            nationality_id: guardian?.nationality?.id ?? "",
            race_id: guardian?.race?.id ?? "",
            religion_id: guardian?.religion?.id ?? "",
            education_id: guardian?.education?.id ?? "",
            is_primary: isValueTrue(guardian?.is_primary),
            with_user_account: isValueTrue(guardian?.with_user_account),
            _with_user_account: isValueTrue(guardian?.with_user_account),
            is_direct_dependant: isValueTrue(guardian?.is_direct_dependant),
        };
    }

    useEffect(() => {
        if (form.formState.errors?.guardians) {
            console.log(
                "form.formState.errors?.guardians",
                form.formState.errors?.guardians
            );
            if (isArray(form.formState.errors?.guardians)) {
                setIsOpenList(
                    form.formState.errors?.guardians
                        ?.map((error, index) => index)
                        .filter((i) => i !== undefined)
                );
            }
        }
    }, [form.formState.errors?.guardians]);

    return (
        <div className="lg:col-span-2">
            <div className="c-text-size mb-1 font-medium normal-case text-themeLabel">
                {t("Select a guardian type and add guardian")}
            </div>
            <div className="mb-4 mt-3 flex w-full flex-wrap items-center gap-2">
                <div className="lg:w-[300px]">
                    <Select
                        placeholder={t("Select Guardian Type")}
                        options={guardianTypeOptions}
                        value={
                            guardianTypeOptions?.find(
                                (option) =>
                                    option.value === selectedGuardianType
                            ) ?? null
                        }
                        onChange={(selectedOption) => {
                            setSelectedGuardianType(selectedOption?.value);
                        }}
                        styles={selectStyles}
                    />
                </div>
                <Button
                    size={"smaller"}
                    variant={"outline"}
                    className="flex items-center gap-x-1"
                    onClick={onAddNew}
                >
                    {t("Add ")}
                    {t("Guardian")}
                </Button>
                {/*<Button*/}
                {/*    size={"smaller"}*/}
                {/*    variant={"outline"}*/}
                {/*    className="flex items-center gap-x-1"*/}
                {/*    onClick={() => {*/}
                {/*        if (!canAdd()) return;*/}
                {/*        setOpenSearch(true);*/}
                {/*    }}*/}
                {/*>*/}
                {/*    <span>Add Existing Guardian</span>*/}
                {/*</Button>*/}
            </div>

            {form.formState.errors?.guardians?.message && (
                <div className="warning-text -mt-2">
                    {t(
                        `${getInputErrorMessage(form.formState.errors?.guardians?.message)}`
                    )}
                </div>
            )}
            {children}
            {form.watch("guardians").map((guardian: any, index) => {
                return (
                    <Form
                        key={guardian.id.toString() + index}
                        form={form}
                        currentGuardian={guardian}
                        index={index}
                        options={options}
                        isOpenList={isOpenList}
                        setIsOpenList={setIsOpenList}
                        remove={remove}
                        formatGuardianData={formatGuardianData}
                        isHostel={isHostel}
                        isEnrollment={isEnrollment}
                    />
                );
            })}
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <GuardianSearchEngine
                    setSelection={(guardian) => {
                        onAddExisting(guardian);
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </div>
    );
};

const Form = ({
    index,
    currentGuardian,
    form,
    options,
    isOpenList,
    setIsOpenList,
    remove,
    formatGuardianData,
    isHostel = false,
    isEnrollment = false,
}: {
    index: number;
    currentGuardian: any;
    form: any;
    options: Record<string, any>;
    isOpenList: number[];
    setIsOpenList: any;
    remove: any;
    formatGuardianData: any;
    isHostel?: boolean;
    isEnrollment?: boolean;
}) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const t = useTranslations("common");

    const isSuperAdmin = hasPermit("student-update");

    const viewOnly = isEnrollment
        ? false
        : isSuperAdmin
          ? false
          : isHostel
            ? false
            : true;

    const [hidden, setHidden] = useState(currentGuardian._isNew === true);
    const [linkedPhone, setLinkedPhone] = useState<any>(null);
    const [names, setNames] = useState<Record<string, any>>(
        currentGuardian?.name
    );

    const { axiosQuery: searchGuardian } = useAxios({
        api: guardianAPI,
        onSuccess(result) {
            setHidden(false);
            if (result.data.length > 0) {
                const guardian = result.data?.[0];
                const guardianData = formatGuardianData(guardian);
                Object.entries(guardianData).forEach(([key, value]) => {
                    form.setValue(`guardians[${index}].${key}`, value);
                });
                form.setValue(`guardians[${index}]._isNew`, false);
            }
        },
    });

    const { axiosQuery: searchGuardianByPhone } = useAxios({
        api: userAPI,
        noLoading: true,
        onSuccess(result) {
            if (result.data.length > 0) {
                setLinkedPhone(result.data?.[0]?.phone_number);
            } else {
                setLinkedPhone(null);
            }
        },
    });

    function onClickChevron() {
        if (isOpenList.includes(index)) {
            setIsOpenList(isOpenList.filter((i) => i !== index));
        } else {
            setIsOpenList([...isOpenList, index]);
        }
    }

    function onPhoneChange(value) {
        const guardian = form.watch("guardians")[index];
        const hasLinked = guardian._with_user_account;
        const willLink = guardian.with_user_account;

        if (!hasLinked || !willLink) {
            if (value && isValidPhoneNumber(value)) {
                searchGuardianByPhone({
                    params: {
                        phone_number: value,
                    },
                });
            } else {
                setLinkedPhone(null);
            }
        } else {
            setLinkedPhone(null);
        }
    }

    function onContinue() {
        const nric = form.watch("guardians")[index].nric;
        const passportNumber = form.watch("guardians")[index].passport_number;

        if (isEmpty(nric.trim()) && isEmpty(passportNumber.trim())) {
            setHidden(false);
            return;
        }
        searchGuardian({
            params: {
                nric,
                passport_number: passportNumber,
                ...GET_ALL_PARAMS,
            },
        });
    }

    useEffect(() => {
        if (form.formState.errors?.guardians?.[index]?.name) {
            setHidden(false);
        }
    }, [form.formState.errors?.guardians?.[index]?.name]);

    useEffect(() => {
        if (hidden === false && linkedPhone === null) {
            onPhoneChange(currentGuardian.phone_number);
        }
    }, [hidden]);

    return (
        <div className="mb-3 rounded-md border px-4 py-3">
            <div className="col-span-2 flex items-center justify-between">
                {!viewOnly && (
                    <X
                        size={20}
                        className="mt-0.5 cursor-pointer rounded-full border border-themeLabel bg-white p-0.5 text-themeLabel"
                        onClick={() => {
                            return remove(index);
                        }}
                    />
                )}
                <div className="c-text-size flex w-[calc(100%-44px)] flex-wrap items-center gap-x-1.5 px-3 text-gray-500">
                    <span className="font-medium">
                        {capitalize(t(currentGuardian.type))}
                    </span>
                    <span className="h-[3px] w-[3px] rounded-full bg-gray-400"></span>
                    <span>{combinedNames(names)}</span>
                    {form.watch(`guardians[${index}].is_primary`) && (
                        <span className="ml-1 rounded-sm border border-dotted border-themeLabel p-1 text-[10px] font-medium uppercase leading-none">
                            Primary
                        </span>
                    )}
                </div>
                <ChevronDownCircle
                    className={clsx(
                        "cursor-pointer text-themeGreen transition",
                        isOpenList.includes(index) && "rotate-180"
                    )}
                    onClick={onClickChevron}
                />
            </div>

            <div
                className={clsx(
                    "grid w-full gap-3 lg:grid-cols-2",
                    !isOpenList.includes(index) && "h-0 overflow-hidden"
                )}
            >
                <FormDivider />

                <FormInput
                    control={form.control}
                    name={`guardians[${index}].nric`}
                    label={"NRIC"}
                    max={12}
                    disabled={viewOnly}
                />
                <FormInput
                    control={form.control}
                    name={`guardians[${index}].passport_number`}
                    label="passport number"
                    disabled={viewOnly}
                />
                {hidden && (
                    <Button
                        size={"smaller"}
                        variant={"outline"}
                        className="flex items-center gap-x-1"
                        onClick={onContinue}
                    >
                        {t("Continue")}
                    </Button>
                )}
                {!hidden && (
                    <>
                        <FormDivider />

                        {isArray(activeLanguages) &&
                            activeLanguages.map((lang) => (
                                <FormInput
                                    key={"guardian" + lang?.code}
                                    control={form.control}
                                    name={`guardians[${index}].name[${lang?.code}]`}
                                    label={`${t("name")} (${t(lang?.name)})*`}
                                    isUpperCase={!isChineseCode(lang?.code)}
                                    onChange={(value) => {
                                        setNames({
                                            ...names,
                                            [lang?.code]: value,
                                        });
                                    }}
                                    disabled={viewOnly}
                                />
                            ))}
                        {form.formState.errors?.guardians?.[index]?.name && (
                            <div className="warning-text -mt-1.5 lg:col-span-2">
                                {t("Name is required")}
                            </div>
                        )}
                        <FormDivider />
                        <FormInput
                            control={form.control}
                            type="email"
                            name={`guardians[${index}].email`}
                            label="email"
                            disabled={viewOnly}
                        />
                        <FormPhoneInput
                            form={form}
                            name={`guardians[${index}].phone_number`}
                            label={t("phone number") + "*"}
                            onChange={onPhoneChange}
                            disabled={viewOnly}
                        />
                        {form.watch("guardians")[index].type === GUARDIAN && (
                            <FormInput
                                control={form.control}
                                name={`guardians[${index}].relation_to_student`}
                                label="relation_to_student"
                                disabled={viewOnly}
                            />
                        )}
                        <FormSelect
                            control={form.control}
                            name={`guardians[${index}].live_status`}
                            label="live status"
                            isStringOptions={true}
                            options={[
                                "NORMAL",
                                "SICK",
                                "PASSED_AWAY",
                                "UNKNOWN",
                            ]}
                            isDisabled={viewOnly}
                        />

                        <FormSelect
                            control={form.control}
                            name={`guardians[${index}].nationality_id`}
                            label="nationality"
                            options={options?.countries}
                            isDisabled={viewOnly}
                        />
                        <FormSelect
                            control={form.control}
                            name={`guardians[${index}].race_id`}
                            label="race"
                            options={options?.races}
                            isDisabled={viewOnly}
                        />
                        <FormSelect
                            control={form.control}
                            name={`guardians[${index}].religion_id`}
                            label="religion"
                            options={options?.religions}
                            isDisabled={viewOnly}
                        />
                        <FormSelect
                            control={form.control}
                            name={`guardians[${index}].married_status`}
                            label="marital status"
                            isStringOptions={true}
                            options={maritalStatusOptions}
                            isDisabled={viewOnly}
                        />
                        <FormSelect
                            control={form.control}
                            name={`guardians[${index}].education_id`}
                            label="education"
                            options={options?.educations}
                            isDisabled={viewOnly}
                        />
                        <FormInput
                            control={form.control}
                            name={`guardians[${index}].occupation`}
                            label={"occupation"}
                            disabled={viewOnly}
                        />
                        <FormTextarea
                            control={form.control}
                            name={`guardians[${index}].occupation_description`}
                            label={"occupation_description"}
                            disabled={viewOnly}
                        />
                        <div className="grid gap-1 lg:ml-1">
                            <div className="mt-3 lg:mt-7">
                                <FormCheckbox
                                    control={form.control}
                                    name={`guardians[${index}].with_user_account`}
                                    label={
                                        linkedPhone
                                            ? `link to existing user (${linkedPhone})`
                                            : `with_user_account`
                                    }
                                    isDisabled={viewOnly}
                                />
                            </div>
                            <FormCheckbox
                                control={form.control}
                                name={`guardians[${index}].is_primary`}
                                label={`is_primary`}
                                isDisabled={viewOnly}
                            />
                            {!isEnrollment &&
                                (!isSuperAdmin &&
                                isHostel &&
                                !currentGuardian.is_direct_dependant ? (
                                    <p className="flex flex-wrap items-center gap-1.5 font-medium">
                                        <Check
                                            className="text-themeGreen"
                                            size={20}
                                        />
                                        <span className="leading-none text-gray-600">
                                            Is Non Direct Dependant
                                        </span>
                                    </p>
                                ) : (
                                    <FormCheckbox
                                        control={form.control}
                                        name={`guardians[${index}].is_direct_dependant`}
                                        label={`is_direct_dependant`}
                                        isDisabled={viewOnly}
                                    />
                                ))}
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default GuardiansForm;
