import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { Plus, X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FreeInput from "@/components/ui/FreeInput";
import {
    CommonFormProps,
    gradingSchemeAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import StatusFormSwitch from "../ui/StatusFormSwitch";
import FreeTable from "../ui/FreeTable";

const GradingSchemeForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: currentData, axiosQuery: getGradingScheme } = useAxios({
        api: gradingSchemeAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getGradingScheme({
                params: { type: props.gradingType },
                id: props.id,
            });
        }
    }, []);

    return props.isCreate || currentData ? (
        <FormWrap currentData={currentData} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
};

const FormWrap = ({
    currentData,
    isCreate = false,
    refresh,
    close,
    gradingType,
}: FormWrapProps) => {
    const t = useTranslations("common");

    type GradingSchemeItem = {
        id?: string;
        name: string;
        display_as_name: string;
        from: string | number;
        to: string | number;
        extra_marks: number;
    };

    const form = useForm({
        defaultValues: {
            name: currentData?.name ?? "",
            code: currentData?.code ?? "",
            is_active: isCreate ? true : currentData?.is_active ?? false,
            grading_scheme_items: isCreate
                ? ([] as GradingSchemeItem[])
                : ((currentData?.grading_scheme_items ??
                      []) as GradingSchemeItem[]),
        },
    });

    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "grading_scheme_items",
    });

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [showTable, setShowTable] = useState<boolean>(false);

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "name",
                displayAs: `${t("name")}*`,
                hasSort: false,
                modify: (_, index) => {
                    return (
                        <FreeInput
                            isDecimal={false}
                            hasLabel={false}
                            control={form.control}
                            name={`grading_scheme_items[${index}].name`}
                            error={
                                form.formState.errors?.grading_scheme_items?.[
                                    index
                                ]?.name
                            }
                        />
                    );
                },
            },
            {
                key: "display_as_name",
                displayAs: `${t("display as")}*`,
                hasSort: false,
                modify: (_, index) => {
                    return (
                        <FreeInput
                            isDecimal={false}
                            hasLabel={false}
                            control={form.control}
                            name={`grading_scheme_items[${index}].display_as_name`}
                            error={
                                form.formState.errors?.grading_scheme_items?.[
                                    index
                                ]?.display_as_name
                            }
                        />
                    );
                },
            },
            {
                key: "from",
                displayAs: `${t("from")}*`,
                hasSort: false,
                modify: (_, index) => {
                    return (
                        <FreeInput
                            isDecimal={true}
                            hasLabel={false}
                            control={form.control}
                            name={`grading_scheme_items[${index}].from`}
                            error={
                                form.formState.errors?.grading_scheme_items?.[
                                    index
                                ]?.from
                            }
                        />
                    );
                },
            },
            {
                key: "to",
                displayAs: `${t("to")}*`,
                hasSort: false,
                modify: (_, index) => {
                    return (
                        <FreeInput
                            isDecimal={true}
                            hasLabel={false}
                            control={form.control}
                            name={`grading_scheme_items[${index}].to`}
                            error={
                                form.formState.errors?.grading_scheme_items?.[
                                    index
                                ]?.to
                            }
                        />
                    );
                },
            },
            {
                key: "extra_marks",
                displayAs: `${t("extra marks")}*`,
                hasSort: false,
                modify: (_, index) => {
                    return (
                        <FreeInput
                            isDecimal={true}
                            hasLabel={false}
                            control={form.control}
                            name={`grading_scheme_items[${index}].extra_marks`}
                            error={
                                form.formState.errors?.grading_scheme_items?.[
                                    index
                                ]?.extra_marks
                            }
                        />
                    );
                },
            },
            {
                key: "_",
                hasSort: false,
                modify: (_, index) => {
                    return (
                        <div className="flex h-full items-center">
                            <div
                                className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                                onClick={() => {
                                    remove(index);
                                }}
                            >
                                <X size={14} className="text-themeGreenDark" />
                            </div>
                        </div>
                    );
                },
            },
        ];
        setColumns(_columns);
    }

    const { axiosPost: createGradingScheme, error: postError } = useAxios({
        api: gradingSchemeAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateGradingScheme, error: putError } = useAxios({
        api: gradingSchemeAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const handleAddMore = () => {
        append({
            name: "",
            display_as_name: "",
            from: "",
            to: "",
            extra_marks: 0,
        });
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data: any) => {
                data.type = gradingType;

                console.log("submit data", data);

                if (isCreate) {
                    createGradingScheme(data);
                } else {
                    updateGradingScheme({ id: currentData.id, data });
                }
            })
        );
    }

    useEffect(() => {
        if (fields.length === 0) {
            setShowTable(false);
        } else if (!showTable && fields.length > 0) {
            defineColumn();
            setShowTable(true);
        }
    }, [fields.length]);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("grading scheme")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormInput
                        control={form.control}
                        name="name"
                        label={`${t("name")}*`}
                    />

                    <FormInput
                        control={form.control}
                        name="code"
                        label={`${t("code")}*`}
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="Status"
                    />

                    <div className="mt-3 border-t border-dashed pt-3 lg:col-span-2">
                        <div className="flex items-center gap-5">
                            <h3 className="ml-0.5 text-themeGreenDark">
                                {t("Grading Scheme List")}
                            </h3>
                            <Button
                                variant={"outline"}
                                size={"smaller"}
                                className="flex gap-x-1"
                                onClick={handleAddMore}
                            >
                                <Plus
                                    size={18}
                                    className="-ml-2 mt-1 text-themeGreen"
                                />{" "}
                                <span>{t("Add")}</span>
                            </Button>
                        </div>

                        {showTable && (
                            <div className="mb-5 mt-2 lg:col-span-2">
                                <FreeTable columns={columns} data={fields} />
                            </div>
                        )}

                        {form.formState.errors?.grading_scheme_items
                            ?.message && (
                            <p className="warning-text mb-3">
                                {`${form.formState.errors?.grading_scheme_items?.message}`}
                            </p>
                        )}
                        <Button type="submit" className="ml-auto mt-4">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default GradingSchemeForm;
