import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import { genderOptions, stateAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { DatePicker } from "../ui/DatePicker";
import FormDivider from "../ui/FormDivider";
import FormInput from "../ui/FormInput";
import FormInputInterger from "../ui/FormInputInterger";
import FormPhoneInput from "../ui/FormPhoneInput";
import FormSelect from "../ui/FormSelect";
import "react-phone-number-input/style.css";
import { capitalize } from "lodash";

type EnrollmentFormProfileSectionProps = {
    form: any;
    options: Record<string, any>;
    countryId: number;
};

const EnrollmentFormProfileSection = ({
    form,
    options,
    countryId,
}: EnrollmentFormProfileSectionProps) => {
    const locale = useLocale();
    const {
        data: stateList,
        axiosQuery: getStateList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        if (countryId) {
            getStateList({ params: { country_id: countryId } });
        }
    }, []);

    const t = useTranslations("common");

    return (
        <>
            <FormInput
                control={form.control}
                name="birth_cert_no"
                label="birth cert no*"
            />

            <FormInputInterger
                control={form.control}
                name="nric_no"
                label={"NRIC"}
            />

            <FormInput control={form.control} name="passport_no" />

            <FormSelect
                control={form.control}
                name="gender"
                isSortByName={false}
                options={genderOptions.map((option) => ({
                    id: option.id,
                    name: capitalize(t(option.name)),
                }))}
            />

            <FormSelect
                control={form.control}
                name="race_id"
                label="race*"
                options={options.races}
            />

            <FormSelect
                control={form.control}
                name="religion_id"
                label="religion*"
                options={options.religions}
            />

            <FormDivider />

            <FormSelect
                control={form.control}
                name="birthplace_id"
                label="birthplace*"
                options={options.countries}
            />

            <FormSelect
                control={form.control}
                name="nationality_id"
                label="nationality*"
                options={options.countries}
            />

            <DatePicker
                control={form.control}
                name={"date_of_birth"}
                label={"date of birth*"}
            />

            <FormDivider />

            <FormInput control={form.control} name="address" label="address*" />

            <FormInput control={form.control} name="city" label="City*" />

            <FormSelect
                control={form.control}
                name="country_id"
                label="Country*"
                options={options.countries}
                onChange={(val) => {
                    getStateList({ params: { country_id: val } });
                    form.setValue("state_id", "");
                }}
            />

            <FormSelect
                control={form.control}
                name="state_id"
                label="State*"
                options={stateList}
                isLoading={isStatesLoading}
            />

            <FormInput
                control={form.control}
                name="postal_code"
                label="postal code*"
            />

            <FormDivider />

            <FormInput
                control={form.control}
                name="email"
                type="email"
                label="email*"
            />

            <FormPhoneInput form={form} name="phone_no" />
        </>
    );
};

export default EnrollmentFormProfileSection;
