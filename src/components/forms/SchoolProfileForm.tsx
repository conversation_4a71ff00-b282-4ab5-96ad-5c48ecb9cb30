import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    countryAPI,
    GET_ALL_PARAMS,
    schoolProfileAPI,
    stateAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isProperPhoneNumber, showBackendFormError } from "@/lib/utils";
import Card from "../ui/Card";
import FormDivider from "../ui/FormDivider";
import FormFileInput from "../ui/FormFileInput";
import FormPhoneInput from "../ui/FormPhoneInput";
import FormSelect from "../ui/FormSelect";

const SchoolProfileForm = () => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data, axiosQuery: getSchoolProfile } = useAxios({
        api: schoolProfileAPI,
        locale,
    });

    useEffect(() => {
        getSchoolProfile();
    }, [locale]);

    return activeLanguages && data ? (
        <FormWrap
            data={data}
            activeLanguages={activeLanguages}
            refresh={getSchoolProfile}
        />
    ) : (
        <></>
    );
};

type FormWrapProps = {
    data: any;
    activeLanguages: Array<Record<string, any>>;
    refresh: () => void;
};

const FormWrap = ({ data, activeLanguages, refresh }: FormWrapProps) => {
    const t = useTranslations("common");
    const locale = useLocale();

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: data?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            code: data?.code ?? "",
            short_name: data?.short_name ?? "",
            address: data?.address ?? "",
            country_id: data?.country_id ?? "",
            state_id: data?.state_id ?? "",
            city: data?.city ?? "",
            postcode: data?.postcode ?? "",
            phone_1: data?.phone_1 ?? "",
            phone_2: data?.phone_2 ?? "",
            fax_1: data?.fax_1 ?? "",
            fax_2: data?.fax_2 ?? "",
            email: data?.email ?? "",
            url: data?.url ?? "",
            logo_url: data?.logo_url ?? "",
            principal_name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: data?.translations?.principal_name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const { axiosMultipartPost: createSchoolProfile, error: postError } =
        useAxios({
            api: schoolProfileAPI,
            onSuccess: () => {
                refresh();
            },
            toastMsg: t("Updated successfully"),
        });

    const {
        data: countriesList,
        axiosQuery: getCountriesList,
        isLoading: isCountriesLoading,
    } = useAxios({
        api: countryAPI,
        locale,
    });

    const {
        data: statesList,
        axiosQuery: getStatesList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    useEffect(() => {
        getCountriesList({ params: GET_ALL_PARAMS });
        getStatesList({
            params: { country_id: data?.country_id, ...GET_ALL_PARAMS },
        });
    }, []);

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                if (
                    !isProperPhoneNumber(form, "phone_1", data.phone_1) ||
                    !isProperPhoneNumber(form, "phone_2", data.phone_2) ||
                    !isProperPhoneNumber(form, "fax_1", data.fax_1) ||
                    !isProperPhoneNumber(form, "fax_2", data.fax_2)
                ) {
                    return;
                }
                if (data.logo) {
                    data.logo = data.logo?.[0];
                } else {
                    data.logo = null;
                }

                createSchoolProfile(data);
            })
        );
    }

    return (
        <Card styleClass="max-w-4xl mx-auto">
            <div className="lg:px-2">
                <h2 className="mb-7 pt-2">{t("School Profile")}</h2>
                <Form {...form}>
                    <form onSubmit={onSubmit} className="grid-form">
                        {activeLanguages.map((lang, index) => (
                            <FormInput
                                key={lang?.code || index}
                                control={form.control}
                                name={`name[${lang?.code}]`}
                                label={`${t("name")} (${t(lang?.name)})`}
                            />
                        ))}

                        <FormDivider />

                        <FormInput
                            control={form.control}
                            name="code"
                            label={t("code") + "*"}
                        />
                        <FormInput
                            control={form.control}
                            name="short_name"
                            label={t("short name") + "*"}
                        />

                        <FormDivider />

                        <FormInput
                            control={form.control}
                            name="address"
                            label={t("address") + "*"}
                        />
                        <FormInput
                            control={form.control}
                            name="city"
                            label={t("city") + "*"}
                        />

                        <FormSelect
                            control={form.control}
                            name="state_id"
                            label={t("state") + "*"}
                            options={statesList}
                            isLoading={isStatesLoading}
                        />

                        <FormSelect
                            control={form.control}
                            name="country_id"
                            label={t("country") + "*"}
                            options={countriesList}
                            isLoading={isCountriesLoading}
                            onChange={(val) => {
                                getStatesList({ params: { country_id: val } });
                                form.setValue("state_id", "");
                            }}
                        />

                        <FormInput
                            control={form.control}
                            name="postcode"
                            label={t("postcode") + "*"}
                        />

                        <FormDivider />

                        <FormPhoneInput
                            form={form}
                            name="phone_1"
                            label={t("phone number") + " 1"}
                        />

                        <FormPhoneInput
                            form={form}
                            name="phone_2"
                            label={t("phone number") + " 2"}
                        />

                        <FormPhoneInput
                            form={form}
                            name="fax_1"
                            label={t("fax number") + " 1"}
                        />

                        <FormPhoneInput
                            form={form}
                            name="fax_2"
                            label={t("fax number") + " 2"}
                        />

                        <FormDivider />

                        <FormInput
                            control={form.control}
                            name="email"
                            type="email"
                        />
                        <FormInput
                            control={form.control}
                            name="url"
                            label={t("website url")}
                            type="url"
                        />

                        <FormFileInput
                            name="logo"
                            label={t("school logo")}
                            currentFileUrl={data?.logo_url}
                            register={form.register}
                            errors={form.formState.errors}
                        />

                        <FormDivider />

                        {activeLanguages.map((lang, index) => (
                            <FormInput
                                key={lang?.code || index}
                                control={form.control}
                                name={`principal_name[${lang?.code}]`}
                                label={`${t("principal name")} (${t(lang?.name)})`}
                            />
                        ))}

                        <div className="lg:col-span-2">
                            <Button
                                type="submit"
                                className="ml-auto mt-2 block w-32"
                            >
                                {t("Save")}
                            </Button>
                        </div>
                    </form>
                </Form>
            </div>
        </Card>
    );
};

export default SchoolProfileForm;
