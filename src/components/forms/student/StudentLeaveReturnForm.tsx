import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormTextarea from "@/components/ui/FormTextarea";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    LEAVE,
    leaveReasonAPI,
    RETURN,
    semesterClassesAPI,
    semesterClassPrimaryDropdownFilter,
    semesterSettingAPI,
    studentAPI,
    studentLeaveAPI,
    studentReturnAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, toYMD } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";

const StudentLeaveReturnForm = (
    props: CommonFormProps & {
        actionType: typeof LEAVE | typeof RETURN | null;
    }
) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: studentData, axiosQuery: getStudentData } = useAxios({
        api: studentAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (props.id) {
            getStudentData({ id: props.id });
        }
    }, []);

    return activeLanguages && studentData ? (
        <FormWrap
            studentData={studentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    studentData: any;

    activeLanguages: Array<Record<string, any>>;
    actionType: typeof LEAVE | typeof RETURN | null;
};

const FormWrap = ({
    studentData,
    refresh,
    close,
    actionType,
}: FormWrapProps) => {
    const locale = useLocale();
    const form = useForm({
        defaultValues: {
            effective_date: "",
            leave_reason_id: "",
            remarks: "",
            semester_setting_id: "",
            semester_class_id: "",
        },
    });

    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });
    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { data: leaveReasonOptions, axiosQuery: getLeaveReasonOptions } =
        useAxios({
            api: leaveReasonAPI,
            locale,
        });

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getLeaveReasonOptions({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    sequence: "desc",
                },
            },
        });
    }, []);

    useEffect(() => {
        const semesterSettingId = form.watch("semester_setting_id");
        if (semesterSettingId) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassPrimaryDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    const { axiosPost: handleStudentLeave, error: postLeaveError } = useAxios({
        api: `${studentLeaveAPI}/${studentData.id}`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPost: handleStudentReturn, error: postReturnError } = useAxios(
        {
            api: `${studentReturnAPI}/${studentData.id}`,
            onSuccess: () => {
                close();
                refresh();
            },
        }
    );

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.effective_date = toYMD(data.effective_date);

            if (actionType === LEAVE) {
                delete data.semester_setting_id;
                delete data.semester_class_id;
                handleStudentLeave(data);
            } else if (actionType === RETURN) {
                delete data.leave_reason_id;
                delete data.remarks;
                handleStudentReturn(data);
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postLeaveError);
    }, [postLeaveError]);

    useEffect(() => {
        showBackendFormError(form, postReturnError);
    }, [postReturnError]);

    return (
        <div>
            <h2 className="mb-5">
                Update Student - {actionType ? capitalize(actionType) : ""}{" "}
                School
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <DatePicker
                        control={form.control}
                        name={"effective_date"}
                        label={"Effective Date*"}
                    />

                    {actionType == LEAVE && (
                        <>
                            <FormSelect
                                control={form.control}
                                name="leave_reason_id"
                                label="Leave Reason*"
                                options={leaveReasonOptions}
                                isSortByName={false}
                            />

                            <FormTextarea
                                control={form.control}
                                name="remarks"
                            />
                        </>
                    )}

                    {actionType == RETURN && (
                        <>
                            <FormSelect
                                control={form.control}
                                name="semester_setting_id"
                                label="semester*"
                                options={semesterOptions}
                                isSortByName={false}
                                onChange={() => {
                                    form.setValue("semester_class_id", "");
                                }}
                            />
                            <FormSelect
                                control={form.control}
                                name={"semester_class_id"}
                                label={"Class*"}
                                isDisabled={!form.watch("semester_setting_id")}
                                options={
                                    semesterClassOptions?.map((option) => ({
                                        id: option?.id,
                                        name: `${option?.class_model?.name}`,
                                    })) ?? []
                                }
                            />
                        </>
                    )}

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default StudentLeaveReturnForm;
