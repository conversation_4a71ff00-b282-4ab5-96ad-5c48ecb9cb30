import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    religionAPI,
    substituteRecordAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const SubstituteSingleRecordForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: substituteRecord, axiosQuery: getSubstituteRecord } =
        useAxios({
            api: substituteRecordAPI,
            locale,
            // onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getSubstituteRecord({ id: props.id });
        }
    }, []);

    return activeLanguages && substituteRecord ? (
        <FormWrap
            substituteRecord={substituteRecord}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = import("@/lib/constant").CommonFormProps & {
    substituteRecord: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    substituteRecord,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const sequenceDefaultValue = 1;

    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            substituteRecord?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            sequence: substituteRecord?.sequence ?? sequenceDefaultValue,
        },
    });

    const { axiosPut: updateReligion, error: putError } = useAxios({
        api: religionAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            updateReligion({ id: substituteRecord.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <div>
            <h2 className="mb-5">Update Substitute Record</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SubstituteSingleRecordForm;
