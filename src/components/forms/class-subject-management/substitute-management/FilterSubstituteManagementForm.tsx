import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { axiosInstance } from "@/lib/api";
import { useAxios } from "@/lib/hook";
import { optionUserLabel, toYMD } from "@/lib/utils";
import { Button } from "@/components/base-ui/button";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    employeeAPI,
    GET_ALL_PARAMS,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    subjectAPI,
} from "@/lib/constant";
import FormSelect from "@/components/ui/FormSelect";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import DateRangePicker from "@/components/ui/DateRangePicker";

const FilterSubstituteManagementForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const [requestors, setRequestors] = useState<any[]>([]);
    const [teachers, setTeachers] = useState<any[]>([]);
    const [requestorName, setRequestorName] = useState();
    const [substituteTeacherName, setSubstituteTeacherName] = useState();
    const [currentSemester, setCurrentSemester] = useState(null);
    const [substituteDateRange, setSubstituteDateRange] = useState<any>(null);

    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            substitute_date_from: filter?.substitute_date_from ?? "",
            substitute_date_to: filter?.substitute_date_to ?? "",
            requestor_id: filter?.requestor_id ?? undefined,
            substitute_teacher_id: filter?.substitute_teacher_id ?? undefined,
            semester_class_id: filter?.semester_class_id ?? undefined,
            subject_id: filter?.subject_id ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        form.clearErrors();
        const _filter: any = { ...filter, ...data, page: 1 };

        if (substituteDateRange?.startDate) {
            _filter.substitute_date_from = toYMD(substituteDateRange.startDate);
        }

        if (substituteDateRange?.endDate) {
            _filter.substitute_date_to = toYMD(substituteDateRange.endDate);
        }

        if (data.requestor_id && requestorName) {
            _filter.requestorName = requestorName;
        }

        if (data.substitute_teacher_id && substituteTeacherName) {
            _filter.substituteTeacherName = substituteTeacherName;
        }

        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }
    const locale = useLocale();

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                setCurrentSemester(currentSemester?.id);
            }
        },
    });

    const { data: classOptions, axiosQuery: getClassOptions } = useAxios({
        api: semesterClassesAPI,
        locale,
        onError: () => close(),
    });

    const { data: subjectOptions, axiosQuery: getSubjectOptions } = useAxios({
        api: subjectAPI,
        locale,
        onError: () => close(),
    });

    const { handleError: handleEmployeeError } = useAxios({
        api: employeeAPI,
    });
    const loadEmployeeOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(employeeAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: optionUserLabel(
                            item?.employee_number,
                            item?.translations?.name
                        ),
                        value: item.id,
                        name: item.name,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleEmployeeError(error);
                });
        } else {
            callback([]);
        }
    };

    useEffect(() => {
        getSemesterOptions({ params: GET_ALL_PARAMS });

        if (filter?.requestor_id && filter?.requestorName) {
            loadEmployeeOptions(filter.requestorName, setRequestors);
        }

        if (filter?.substitute_teacher_id && filter?.substituteTeacherName) {
            loadEmployeeOptions(filter.substituteTeacherName, setTeachers);
        }
    }, []);

    useEffect(() => {
        if (currentSemester) {
            getClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: currentSemester,
                },
            });
            getSubjectOptions({
                params: GET_ALL_PARAMS,
            });
        } else {
            if (!filter?.semester_class_id)
                form.setValue("semester_class_id", "");
        }
    }, [currentSemester, locale]);

    useEffect(() => {
        if (filter?.substitute_date_from || filter?.substitute_date_to) {
            setSubstituteDateRange((prev) => ({
                ...prev,
                key: "selection",
                startDate: filter?.substitute_date_from ?? "",
                endDate: filter?.substitute_date_to ?? "",
            }));
        }
    }, [filter?.substitute_date_from, filter?.substitute_date_to]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <div className="flex-grow">
                    <DateRangePicker
                        label={"substitute date"}
                        range={substituteDateRange}
                        setRange={setSubstituteDateRange}
                    />
                </div>

                <FormSelectAsync
                    control={form.control}
                    name="requestor_id"
                    label="Requestor"
                    loadOptions={loadEmployeeOptions}
                    value={requestors.find(
                        (option) => option.value === form.watch("requestor_id")
                    )}
                    onChange={(val) => setRequestorName(val?.name)}
                />

                <FormSelect
                    control={form.control}
                    name={"semester_class_id"}
                    label="class"
                    options={
                        classOptions?.map((option) => ({
                            id: option?.id,
                            name: option?.class_model?.name,
                        })) ?? []
                    }
                />

                <FormSelect
                    control={form.control}
                    name="subject_id"
                    label="subject"
                    options={subjectOptions}
                />

                <div className="lg:col-span-2 2xl:col-span-1">
                    <FormSelectAsync
                        control={form.control}
                        name="substitute_teacher_id"
                        label="Substitute Teacher"
                        loadOptions={loadEmployeeOptions}
                        value={teachers.find(
                            (option) =>
                                option.value ===
                                form.watch("substitute_teacher_id")
                        )}
                        onChange={(val) => setSubstituteTeacherName(val?.name)}
                    />
                </div>
                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSubstituteManagementForm;
