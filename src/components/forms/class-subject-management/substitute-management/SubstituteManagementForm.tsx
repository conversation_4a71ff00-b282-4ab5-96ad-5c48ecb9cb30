import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray } from "lodash";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import FreeInputDecimal from "@/components/ui/FreeInputDecimal";
import FreeTextArea from "@/components/ui/FreeTextArea";
import { axiosInstance } from "@/lib/api";
import {
    appCurrencySymbol,
    CommonFormProps,
    employeeAPI,
    substituteRecordAPI,
    TableColumnType,
    teacherDropdownFilter,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { optionUserLabel, showBackendFormError, toYMD } from "@/lib/utils";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import FreeTable from "@/components/ui/FreeTable";
import { Label } from "@/components/base-ui/label";

type SubstituteRecord = {
    timeslot_id: number;
    substitute_teacher_id: number | null;
    substitute_date: string;
    allowance: number;
    remarks?: string | null;
};

type FormValues = {
    requestor_id: number | null;
    substitute_date_from: Date | null;
    substitute_date_to: Date | null;
    sub_teacher_id: number | null;
    substitute_records: SubstituteRecord[];
};

type FormWrapProps = CommonFormProps;

const SubstituteManagementForm = (props: CommonFormProps) => {
    return <FormWrap {...props} />;
};

const FormWrap = ({ refresh, close }: FormWrapProps) => {
    const form = useForm<FormValues>({
        defaultValues: {
            requestor_id: null,
            substitute_date_from: null,
            substitute_date_to: null,
            sub_teacher_id: null,
            substitute_records: [],
        },
    });

    const { fields } = useFieldArray({
        control: form.control,
        name: "substitute_records",
    });

    const locale = useLocale();
    const t = useTranslations("common");
    const t_daysOfWeek = useTranslations("daysOfWeek");

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [employees, setEmployees] = useState<any[]>([]);
    const [teacherValue, setTeacherValue] = useState<{
        label: string;
        value: string | number;
        name?: string;
    } | null>(null);

    const [teachers, setTeachers] = useState<any[]>([]);
    const [substituteTeachers, setSubstituteTeachers] = useState<
        { label: string; value: string | number; name?: string }[]
    >([]);

    const [selection, setSelection] = useState<any[]>([]);
    const [clearSelectionCount, setClearSelectionCount] = useState(0);

    const [dateRange, setDateRange] = useState<any>(null);

    const _columns: TableColumnType[] = [
        {
            key: "date",
            modify: (value, cell) => (
                <span className="whitespace-nowrap text-sm">{value}</span>
            ),
        },
        {
            key: "period",
        },
        {
            key: "class",
        },
        {
            key: "subject",
        },
        {
            key: "substitute_teacher_id",
            displayAs: t("Substitute Teacher") + "*",
            hasSort: false,
            modify: (_, index) => {
                const subTeacherId = form.watch(
                    `substitute_records.${index}.substitute_teacher_id` as const
                );

                return (
                    <span className="z-50 whitespace-nowrap text-sm">
                        <FreeSelectAsync
                            hasLabel={false}
                            control={form.control}
                            name={`substitute_records[${index}].substitute_teacher_id`}
                            loadOptions={loadTeacherOptions}
                            value={
                                substituteTeachers?.find(
                                    (option) => option?.value == subTeacherId
                                ) || null
                            }
                            onChange={(val) => {
                                if (val) {
                                    setSubstituteTeachers((prevTeachers) => {
                                        if (
                                            !prevTeachers.some(
                                                (teacher) =>
                                                    teacher?.value ===
                                                    val?.value
                                            )
                                        ) {
                                            return [...prevTeachers, val];
                                        }
                                        return prevTeachers;
                                    });
                                }
                                form.setValue(
                                    `substitute_records.${index}.substitute_teacher_id`,
                                    val?.value || null
                                );
                            }}
                            error={
                                form.formState.errors?.substitute_records?.[
                                    index
                                ]?.substitute_teacher_id
                            }
                        />
                    </span>
                );
            },
        },
        {
            key: "allowance",
            displayAs: `${t("allowance")} (${appCurrencySymbol})*`,
            modify: (_, index) => {
                return (
                    <FreeInputDecimal
                        hasLabel={false}
                        control={form.control}
                        name={`substitute_records.${index}.allowance`}
                        error={
                            form.formState.errors?.substitute_records?.[index]
                                ?.allowance
                        }
                    />
                );
            },
        },
        {
            key: "remarks",
            modify: (_, index) => {
                return (
                    <FreeTextArea
                        control={form.control}
                        name={`substitute_records.${index}.remarks`}
                        error={
                            form.formState.errors?.substitute_records?.[index]
                                ?.remarks
                        }
                    />
                );
            },
        },
    ];

    const { handleError: handleEmployeeError } = useAxios({ api: employeeAPI });

    const loadEmployeeOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(employeeAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                        ...teacherDropdownFilter,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: optionUserLabel(
                            item?.employee_number,
                            item?.translations?.name
                        ),
                        value: item.id,
                        name: item.name,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleEmployeeError(error);
                });
        } else {
            callback([]);
        }
    };

    const loadTeacherOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(employeeAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                        ...teacherDropdownFilter,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: optionUserLabel(
                            item?.employee_number,
                            item?.translations?.name
                        ),
                        value: item.id,
                        name: item.name,
                    }));

                    callback(options);
                })
                .catch((error) => {
                    handleEmployeeError(error);
                });
        } else {
            callback([]);
        }
    };

    const applyAll = () => {
        const selectedTeacher = form.watch("sub_teacher_id");
        if (!selectedTeacher) return;

        const updatedRecords = form
            .getValues("substitute_records")
            .map((record) => ({
                ...record,
                substitute_teacher_id: selectedTeacher,
            }));

        form.setValue("substitute_records", updatedRecords);

        const isTeacherExists = substituteTeachers.some(
            (teacher) => teacher.value === teacherValue?.value
        );

        if (!isTeacherExists && teacherValue) {
            setSubstituteTeachers([...substituteTeachers, teacherValue]);
        }
    };

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        initLoader(
            form.handleSubmit((data: any) => {
                let missingRequiredField = false;

                const requiredFields = ["substitute_teacher_id", "allowance"];
                selection.forEach((index: number) => {
                    requiredFields.forEach((key) => {
                        const value = data.substitute_records?.[index]?.[key];
                        if (!value) {
                            missingRequiredField = true;
                            form.setError(
                                `substitute_records.${index}.${key}` as `substitute_records.${number}.${keyof SubstituteRecord}`,
                                {
                                    type: "manual",
                                    message: "This field is required",
                                }
                            );
                        }
                    });
                });

                if (missingRequiredField) return;

                const filteredRecords = data.substitute_records
                    .filter((_, index) => selection.includes(index))
                    .map((record) => ({
                        ...record,
                        substitute_date: toYMD(record.substitute_date),
                    }));

                const payload = {
                    substitute_records: filteredRecords,
                };
                createSubstitute(payload);
            })
        );
    }

    const { axiosQuery: getRequestorSubstituteRecord } = useAxios({
        api: `${substituteRecordAPI}/${form.getValues("requestor_id")}`,
        locale,
        onError: close,
        onSuccess: (result) => {
            const substituteRecord = result?.data;

            form.setValue("substitute_records", []);
            setColumns([]);
            setClearSelectionCount((prev) => prev + 1);
            setSubstituteTeachers([]);

            if (substituteRecord) {
                const formattedRecords = isArray(substituteRecord)
                    ? substituteRecord.map((item, index) => ({
                          id: index,
                          substitute_record_id: item?.id ?? null,
                          date: `${item?.substitute_date} (${t_daysOfWeek(capitalize(item?.day))})`,
                          period: item?.period,
                          class: item?.class_name?.[locale],
                          subject: item?.subject_name?.[locale],

                          timeslot_id: item?.timeslot_id,
                          substitute_date: item?.substitute_date ?? "",
                          allowance: item?.allowance ?? 4,
                          remarks: item?.remarks ?? "",
                          substitute_teacher_id:
                              item?.substitute_teacher?.id ?? null,
                      }))
                    : [];

                const teacherOptions = substituteRecord
                    .filter(({ substitute_teacher }) => substitute_teacher?.id)
                    .map(({ substitute_teacher }) => ({
                        label: optionUserLabel(
                            substitute_teacher?.employee_number,
                            substitute_teacher?.translations?.name
                        ),
                        value: substitute_teacher?.id,
                        name: substitute_teacher?.name,
                    }));

                setSubstituteTeachers([...teacherOptions]);
                form.setValue("substitute_records", formattedRecords);
            }
        },
    });

    const { axiosPost: createSubstitute, error: postError } = useAxios({
        api: `${substituteRecordAPI}/${form.getValues("requestor_id")}`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    useEffect(() => {
        if (
            !form.watch("requestor_id") ||
            !form.watch("substitute_date_from") ||
            !form.watch("substitute_date_to")
        )
            return;
        getRequestorSubstituteRecord({
            params: {
                substitute_date_from: toYMD(form.watch("substitute_date_from")),
                substitute_date_to: toYMD(form.watch("substitute_date_to")),
                includes: ["substitute_teacher", "requestor"],
            },
        });
    }, [
        form.watch("requestor_id"),
        form.watch("substitute_date_from"),
        form.watch("substitute_date_to"),
    ]);

    useEffect(() => {
        if (dateRange) {
            form.setValue("substitute_date_from", dateRange?.startDate);
            form.setValue("substitute_date_to", dateRange?.endDate);
        }
    }, [dateRange]);

    useEffect(() => {
        setColumns(_columns);
    }, [fields, substituteTeachers]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-5">{t("Bulk Assign Substitute Teachers")}</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid-form">
                    <FormSelectAsync
                        control={form.control}
                        name="requestor_id"
                        label="Requestor"
                        loadOptions={loadEmployeeOptions}
                        value={employees.find(
                            (option) =>
                                option.value === form.watch("requestor_id")
                        )}
                    />
                    <div className="lg:col-span-1">
                        <DateRangePicker
                            label={t("Substitute Date Range") + "*"}
                            range={dateRange}
                            setRange={setDateRange}
                            error={
                                form.formState.errors?.substitute_date_from
                                    ?.message ||
                                form.formState.errors?.substitute_date_to
                                    ?.message
                            }
                        />
                    </div>

                    <div className="border-t border-dashed lg:col-span-2">
                        {fields.length > 0 && (
                            <>
                                <div className="mt-4 flex max-w-[700px] items-end gap-4 lg:col-span-1">
                                    <div className="min-w-[300px] flex-1">
                                        <FormSelectAsync
                                            control={form.control}
                                            name="sub_teacher_id"
                                            label="Substitute Teacher"
                                            loadOptions={loadTeacherOptions}
                                            value={teachers.find(
                                                (option) =>
                                                    option.value ===
                                                    form.watch("sub_teacher_id")
                                            )}
                                            onChange={(val) => {
                                                setTeacherValue(val);
                                            }}
                                        />
                                    </div>

                                    <Button
                                        variant={"outline"}
                                        onClick={applyAll}
                                    >
                                        {t("Apply All")}
                                    </Button>
                                </div>

                                <div className="my-5 grid gap-y-2.5">
                                    <div className="">
                                        <Label>{t("Records")}*</Label>
                                        {form.formState.errors
                                            ?.substitute_records?.message && (
                                            <p className="warning-text">
                                                {`${form.formState.errors?.substitute_records?.message}`}
                                            </p>
                                        )}
                                    </div>
                                    <FreeTable
                                        selection={selection}
                                        setSelection={setSelection}
                                        clearSelectionCount={
                                            clearSelectionCount
                                        }
                                        columns={columns}
                                        data={fields}
                                    />
                                </div>
                            </>
                        )}

                        <Button
                            type="submit"
                            className="mb-32 ml-auto mt-8"
                            disabled={selection.length === 0}
                        >
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default SubstituteManagementForm;
