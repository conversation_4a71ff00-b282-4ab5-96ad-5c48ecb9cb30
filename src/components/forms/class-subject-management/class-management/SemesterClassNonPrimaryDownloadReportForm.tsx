import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    ENGLISH,
    SOCIETY,
    semesterSettingAPI,
    semesterClassesAPI,
    semesterClassDropdownFilter,
    semesterClassReportByStudentInNonPrimaryClassAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const SemesterClassNonPrimaryDownloadReportForm = ({
    classId,
    semesterId,
    isSelectSemester = false,
    semesterClassName,
    classType,
}: {
    classType: typeof ENGLISH | typeof SOCIETY;
    classId?: any;
    semesterId?: any;
    isSelectSemester?: boolean;
    semesterClassName?: string;
}) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            export_type: PDF,
            semester_setting_id: semesterId ? semesterId : "",
            semester_class_ids: classId ? [classId] : [],
        },
    });

    const { data: semesterOptions, axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    function fetchSemesterClassOptions(type?: string) {
        getSemesterClassOptions({
            params: {
                ...GET_ALL_PARAMS,
                ...semesterClassDropdownFilter(locale),
                semester_setting_id: form.watch("semester_setting_id"),
                class_type: classType,
            },
        });
    }

    const { axiosQuery: downloadByStudentsInNonPrimaryClassReports } = useAxios(
        {
            api: semesterClassReportByStudentInNonPrimaryClassAPI,
            locale,
            onSuccess(result) {
                downloadFile(result.data.url);
            },
        }
    );

    const { axiosQuery: viewByStudentsInNonPrimaryClassReports } = useAxios({
        api: semesterClassReportByStudentInNonPrimaryClassAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    function getReport(isView: boolean) {
        form.clearErrors();
        form.handleSubmit((data) => {
            data.class_type = classType;

            let hasError = false;
            const requiredFields = [
                "report_language",
                "export_type",
                ...(isSelectSemester ? ["semester_setting_id"] : []),
            ];
            requiredFields.forEach((key) => {
                if (!data[key]) {
                    hasError = true;
                    form.setError(key, {
                        type: "manual",
                        message: "This field is required",
                    });
                }
            });
            if (hasError) return;

            isView
                ? viewByStudentsInNonPrimaryClassReports({ params: data })
                : downloadByStudentsInNonPrimaryClassReports({ params: data });
        })();
    }

    function onViewReport() {
        getReport(true);
    }

    function onDownloadReport(e) {
        e.preventDefault();
        getReport(false);
    }

    useEffect(() => {
        if (isSelectSemester) {
            form.setValue("semester_class_ids", []);

            const semesterSettingId = form.watch("semester_setting_id");
            if (semesterSettingId) {
                fetchSemesterClassOptions();
            }
        }
    }, [isSelectSemester, form.watch("semester_setting_id")]);

    useEffect(() => {
        if (isSelectSemester)
            getSemesterOptions({
                params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
            });
    }, []);

    return (
        <Form {...form}>
            <form className="grid gap-y-3" onSubmit={onDownloadReport}>
                <h3 className="mb-2">
                    {t("Download Report")}
                    {semesterClassName ? ` - ${semesterClassName}` : ""}
                </h3>

                {isSelectSemester && (
                    <>
                        <FormSelect
                            control={form.control}
                            name={"semester_setting_id"}
                            label={t("semester") + "*"}
                            options={semesterOptions}
                            isSortByName={false}
                        />

                        <FormSelect
                            control={form.control}
                            isMulti={true}
                            name={"semester_class_ids"}
                            label="semester classes"
                            isDisabled={
                                !classType || !form.watch("semester_setting_id")
                            }
                            options={
                                semesterClassOptions?.map((option) => ({
                                    id: option?.id,
                                    name: `${option?.class_model?.name}`,
                                })) ?? []
                            }
                        />
                    </>
                )}

                <FormSelect
                    control={form.control}
                    name="report_language"
                    label={t("report language") + "*"}
                    options={activeLanguages?.map((language) => ({
                        id: language?.code,
                        name: t(language?.name),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="export_type"
                    label={t("export type") + "*"}
                    options={[
                        { id: EXCEL, name: "Excel" },
                        { id: PDF, name: "PDF" },
                    ]}
                />
                <div className="mt-3 flex items-center justify-end gap-2">
                    {form.watch("export_type") === PDF && (
                        <Button variant={"outline"} onClick={onViewReport}>
                            {t("View Report")}
                        </Button>
                    )}
                    <Button type="submit">{t("Download Report")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default SemesterClassNonPrimaryDownloadReportForm;
