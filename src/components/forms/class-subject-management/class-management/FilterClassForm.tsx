import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    classStreamOptions,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    gradeAPI,
    SOCIETY,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../../base-ui/button";

const FilterClassForm = ({
    type,
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            type: type,
            name: filter?.name ?? "",
            stream: filter?.stream ?? "",
            grade_id: filter?.grade_id ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }
    const locale = useLocale();

    const { data: gradeOptions, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    useEffect(() => {
        getGrades({
            params: {
                ...GET_ALL_PARAMS,
                order_by: {
                    name: {
                        [locale]: "asc",
                    },
                },
            },
        });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name={"stream"}
                    isSortByName={false}
                    options={classStreamOptions.map((option) => ({
                        ...option,
                        name: t(option?.name),
                    }))}
                />

                {type !== SOCIETY && (
                    <FormSelect
                        control={form.control}
                        name={"grade_id"}
                        label="grade"
                        options={gradeOptions}
                        isSortByName={false}
                    />
                )}

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterClassForm;
