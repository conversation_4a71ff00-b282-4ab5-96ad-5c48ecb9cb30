import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { chunk, flatten, indexOf, orderBy } from "lodash";
import { X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormSelect from "@/components/ui/FormSelect";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import FormlessDatePicker from "@/components/ui/FormlessDatePicker";
import FreeDatePicker from "@/components/ui/FreeDatePicker";
import InfoCard from "@/components/ui/InfoCard";
import Modal from "@/components/ui/Modal";
import PaginatedTableFilter from "@/components/ui/PaginatedTableFilter";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import { axiosInstance } from "@/lib/api";
import {
    assignClassStudentsAPI,
    assignClassSubjectsAPI,
    CHUNKED_FILTER_PARAMS,
    CommonFormProps,
    employeeAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    semesterClassesAPI,
    SOCIETY,
    STUDENT,
    subjectAPI,
    teacherDropdownFilter,
} from "@/lib/constant";
import { useAxios, usePaginatedTable, useSubmit } from "@/lib/hook";
import {
    combinedNames,
    getSubjectTypesForClassType,
    optionUserLabel,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";

const SemesterClassSettingForm = ({ classType, id, refresh, close }) => {
    const locale = useLocale();

    const { data: currentSemesterClass, axiosQuery: getSemesterClass } =
        useAxios({
            api: semesterClassesAPI,
            locale,
            onError: () => close(),
        });

    const { data: subjects, axiosQuery: getSubjects } = useAxios({
        api: subjectAPI,
        locale,
        onError: () => close(),
    });

    useEffect(() => {
        getSemesterClass({
            id,
            params: {
                includes: [
                    "semesterSetting",
                    "classModel",
                    "homeroomTeacher",
                    "latestPrimaryClassBySemesterSettings",
                    "studentClasses",
                ],
                active_student_class_only: 1,
                student_classes: {
                    is_active: 1,
                },
            },
        });
        getSubjects({
            params: {
                ...GET_ALL_PARAMS,
                types: getSubjectTypesForClassType(classType),
            },
        });
    }, []);

    return subjects && currentSemesterClass ? (
        <FormWrap
            classType={classType}
            currentSemesterClass={currentSemesterClass}
            subjects={subjects}
            refresh={refresh}
            close={close}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    classType: string;
    currentSemesterClass: any;
    subjects: any;
};

const FormWrap = ({
    classType,
    currentSemesterClass,
    subjects,
    refresh,
    close,
}: FormWrapProps) => {
    const locale = useLocale();

    const form = useForm<any>({
        defaultValues: {
            is_active: currentSemesterClass?.is_active ?? false,
            class_id: currentSemesterClass?.id,
            homeroom_teacher_id:
                currentSemesterClass?.homeroom_teacher?.id ?? "",
            subject_ids: currentSemesterClass?.subject_ids ?? [],
            students: orderBy(
                currentSemesterClass?.student_classes?.map((item) => ({
                    id: item?.student?.id,
                    student_number: item?.student?.student_number,
                    name: combinedNames(item?.student?.translations?.name),
                    primary_class: combinedNames(
                        item?.student?.primary_class?.class
                    ),
                    is_active: item?.is_active ?? false,
                    class_enter_date: item?.class_enter_date ?? null,
                })) ?? [],
                ["primary_class", "name"],
                ["asc", "asc"]
            ),
        },
    });

    const _showPrimaryClass = classType !== PRIMARY;
    const _perPage =
        classType === SOCIETY ? 50 : CHUNKED_FILTER_PARAMS.per_page;

    const [teachers, setTeachers] = useState<any[]>([]);
    const [selectedTeacherPhoto, setSelectedTeacherPhoto] = useState<any>();
    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [hasEnterDateError, setHasEnterDateError] = useState(false);
    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        chunk(form.getValues().students, _perPage)
    );

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
        resetClonedFilter,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
        showPrimaryClass: _showPrimaryClass,
        perPage: _perPage,
    });

    const t = useTranslations("common");

    const { append: appendStudent, remove: removeStudent } = useFieldArray({
        control: form.control,
        name: "students",
    });

    function onAddStudent(selection) {
        const currentIds = form
            .getValues()
            .students.map((student) => student.id.toString());

        const newStudents = selection
            .filter((student) => !currentIds.includes(student.id.toString()))
            .map((student) => ({
                id: student?.id,
                student_number: "student?.student_number",
                name: combinedNames(student?.translations?.name),
                is_active: student?.is_active ?? false,
                class_enter_date: null,
            }));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        onAdd(selection);
    }

    const studentColumns = [
        {
            key: "_",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            removeStudent(index);
                            onRemove(cell.row.original?.id);
                        }}
                    />
                );
            },
        },
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
        },
        {
            key: "primary_class",
            hasSort: true,
        },
        {
            key: "class_enter_date",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="h-full">
                        <FreeDatePicker
                            align="end"
                            control={form.control}
                            name={`students[${index}].class_enter_date`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.class_enter_date
                            }
                        />
                    </div>
                );
            },
        },
    ];

    // step 1
    const { axiosPut: updateTeacherAndStatus, error: updateTeacherError } =
        useAxios({
            api: semesterClassesAPI,
            toastMsg: t("Teacher guardian assigned successfully"),
            onSuccess: () => {
                refresh();
                assignSubjects({
                    data: {
                        semester_class_id: currentSemesterClass.id,
                        subject_ids: form.getValues().subject_ids,
                    },
                });
            },
        });

    // step 2
    const { axiosPut: assignSubjects, error: assignSubjectsError } = useAxios({
        api: assignClassSubjectsAPI,
        toastMsg: t("Subjects assigned successfully"),
        onSuccess: (result) => {
            const classSubjectIds = result.data.map((item) => item.id);
            assignStudents({
                data: {
                    class_subject_ids: classSubjectIds,
                    semester_class_id: currentSemesterClass?.id,
                    semester_setting_id:
                        currentSemesterClass.semester_setting?.id,
                    students: form.getValues().students?.map((student) => ({
                        ...student,
                        class_enter_date: toYMD(student?.class_enter_date),
                    })),
                },
            });
        },
    });

    // step 3
    const { axiosPut: assignStudents, error: assignStudentsError } = useAxios({
        api: assignClassStudentsAPI,
        toastMsg: t("Students assigned successfully"),
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { handleError: handleEmployeeError } = useAxios({ api: employeeAPI });

    const { axiosQuery: getTeacher } = useAxios({
        api: employeeAPI,
        onSuccess: (result) => setSelectedTeacherPhoto(result?.data?.photo),
    });

    const loadTeacherOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(employeeAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                        ...teacherDropdownFilter,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: optionUserLabel(
                            item?.employee_number,
                            item?.translations?.name
                        ),
                        value: item?.id,
                        photo: item?.photo,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleEmployeeError(error);
                });
        } else {
            callback([]);
        }
    };

    function onApplyAllDate(date) {
        form.setValue(
            "students",
            form.getValues().students.map((student) => ({
                ...student,
                class_enter_date: date,
            }))
        );
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data) => {
                updateTeacherAndStatus({
                    id: currentSemesterClass.id,
                    data: {
                        homeroom_teacher_id: data.homeroom_teacher_id,
                        is_active: data.is_active,
                    },
                });
            })
        );
    }

    useEffect(() => {
        if (currentSemesterClass?.homeroom_teacher?.id) {
            getTeacher({ id: currentSemesterClass?.homeroom_teacher?.id });
        }
        loadTeacherOptions(
            currentSemesterClass?.homeroom_teacher?.name,
            setTeachers
        );
    }, []);

    useEffect(() => {
        showBackendFormError(
            form,
            updateTeacherError || assignSubjectsError || assignStudentsError
        );

        const studentsErrors = form.formState.errors?.students;
        const _hasEnterDateError =
            Array.isArray(studentsErrors) &&
            !!studentsErrors?.find((student) => student?.class_enter_date);
        setHasEnterDateError(_hasEnterDateError);
    }, [updateTeacherError, assignSubjectsError, assignStudentsError]);

    return (
        <div className="lg:min-w-[800px]">
            <h2 className="mb-4">{t("Semester Class Settings")}</h2>

            <InfoCard
                data={{
                    semester: currentSemesterClass?.semester_setting?.name,
                    class: currentSemesterClass?.class_model?.name,
                }}
            />

            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-5 grid gap-y-5">
                    <FormCheckbox control={form.control} name="is_active" />

                    <div className="z-30 max-w-[500px]">
                        <FormSelectAsync
                            control={form.control}
                            name="homeroom_teacher_id"
                            label="teacher guardian"
                            loadOptions={loadTeacherOptions}
                            value={teachers.find(
                                (option) =>
                                    option.value ===
                                    form.watch("homeroom_teacher_id")
                            )}
                            onChange={(selected) => {
                                setSelectedTeacherPhoto(selected?.photo);
                            }}
                        />
                        {selectedTeacherPhoto && (
                            <img
                                src={selectedTeacherPhoto}
                                className="mt-2 h-auto w-40 rounded-sm"
                            />
                        )}
                    </div>

                    <div className="z-40 max-w-[500px]">
                        <FormSelect
                            control={form.control}
                            name="subject_ids"
                            label="subjects"
                            options={subjects}
                            isMulti={true}
                        />
                    </div>

                    <div className="min-h-[260px]">
                        <div className="flex gap-x-2">
                            <div className="c-text-size mb-2 font-medium capitalize text-themeLabel">
                                {t("students")}
                            </div>
                            {targetsChunk?.length > 0 && (
                                <div className="mt-0.5 text-xs text-gray-500">
                                    <span className="font-semibold">
                                        {flatten(targetsChunk)?.length}
                                    </span>{" "}
                                    {t("in total")}
                                </div>
                            )}
                        </div>
                        <div className="relative z-20 mb-4 flex items-center gap-3">
                            <Button
                                variant={"outline"}
                                size={"smaller"}
                                onClick={() => setOpenStudentSearch(true)}
                            >
                                {t("Select Students")}
                            </Button>
                            <FormlessDatePicker
                                isSmaller
                                onSelect={(date) => onApplyAllDate(date)}
                                placeholder={t("Apply All Class Enter Date")}
                            />
                            {targetsChunk?.length > 0 && (
                                <PaginatedTableFilter
                                    type={STUDENT}
                                    filter={filter}
                                    setFilter={setFilter}
                                    reset={resetClonedFilter}
                                />
                            )}
                        </div>
                        {form.formState.errors?.students?.message && (
                            <p className="warning-text mb-2">
                                {`${form.formState.errors?.students?.message}`}
                            </p>
                        )}
                        {hasEnterDateError && (
                            <p className="warning-text mb-2">
                                {t(
                                    "Please make sure all class enter dates are filled"
                                )}
                            </p>
                        )}
                        {targetsChunk?.length > 0 && pagination && (
                            <DataTable
                                hasPicker
                                isSmaller
                                columns={
                                    classType === PRIMARY
                                        ? studentColumns.filter(
                                              (col) =>
                                                  col.key != "primary_class"
                                          )
                                        : studentColumns
                                }
                                data={
                                    (clonedFilteredChunk ?? targetsChunk)[
                                        pagination?.current_page - 1
                                    ]
                                }
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                hasPerPage={false}
                                sorted={filter?.order_by}
                                sort={onSort}
                            />
                        )}
                    </div>

                    <Modal
                        open={openStudentSearch}
                        onOpenChange={setOpenStudentSearch}
                        size="large"
                    >
                        <StudentSearchEngine
                            isMultiSelect={true}
                            otherFilterParams={{
                                semester_setting_id:
                                    currentSemesterClass?.semester_setting?.id,
                                semester_class_ids: [currentSemesterClass?.id],
                                ...(_showPrimaryClass
                                    ? {
                                          response: "FULL",
                                          includes: [
                                              "currentSemesterPrimaryClass.semesterClass.classModel",
                                          ],
                                      }
                                    : {}),
                            }}
                            setSelection={(selection) =>
                                onAddStudent(selection)
                            }
                            close={() => setOpenStudentSearch(false)}
                        />
                    </Modal>

                    <Button
                        type="submit"
                        className="mb-5 ml-auto min-w-[100px]"
                    >
                        {t("Save")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SemesterClassSettingForm;
