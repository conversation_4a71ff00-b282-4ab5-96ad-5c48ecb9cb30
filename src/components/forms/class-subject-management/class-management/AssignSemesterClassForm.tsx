import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import ClassSearchEngine from "@/components/ui/search-engines/ClassSearchEngine";
import {
    assignSemesterClassesAPI,
    ClassTypes,
    GET_ALL_PARAMS,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import { capitalize } from "lodash";

const AssignSemesterClassForm = (props) => {
    const locale = useLocale();

    const { data: semesterSettings, axiosQuery: getSemesterSettings } =
        useAxios({
            api: semesterSettingAPI,
            locale,
            onError: () => close(),
        });

    useEffect(() => {
        getSemesterSettings({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    return semesterSettings ? (
        <FormWrap semesterSettings={semesterSettings} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = {
    classType: ClassTypes;
    semesterSettings: any;
    refresh: () => void;
    close: () => void;
};

const FormWrap = ({
    classType,
    semesterSettings,
    refresh,
    close,
}: FormWrapProps) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [openClassSearch, setOpenClassSearch] = useState(false);
    const [selectedClasses, setSelectedClasses] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            semester_setting_id: "",
            classes: [],
        },
    });

    const { axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
        onSuccess(result) {
            setSelectedClasses(
                result?.data?.map((item) => item?.class_model) ?? []
            );
        },
    });

    useEffect(() => {
        const semesterSettingId = form.watch("semester_setting_id");
        if (semesterSettingId) {
            getSemesterClasses({
                params: {
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                    class_type: classType,
                },
            });
        }
    }, [form.watch("semester_setting_id")]);

    const { axiosPut: assignSemesterClasses, error: putError } = useAxios({
        api: assignSemesterClassesAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onAdd(selection) {
        const selectedClassesIds = selectedClasses.map((item) =>
            item.id.toString()
        );
        const newClasses = selection.filter(
            (item) => !selectedClassesIds.includes(item.id.toString())
        );
        setSelectedClasses([...selectedClasses, ...newClasses]);
    }

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data) => {
            data.classes = selectedClasses.map((item) => ({
                id: item.id,
                is_active: true,
            }));
            console.log("data", data);
            assignSemesterClasses({ data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">{t("Assign Class to Semester")}</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <div className="max-w-[500px]">
                        <FormSelect
                            control={form.control}
                            name="semester_setting_id"
                            label={t("semester") + "*"}
                            options={semesterSettings}
                            isSortByName={false}
                            onChange={() => {
                                setSelectedClasses([]);
                            }}
                        />
                    </div>

                    <div>
                        <div className="mb-2.5 flex items-center justify-between gap-x-5">
                            <div className="mt-2 flex items-center gap-x-2 capitalize">
                                <h3>{t("classes")}</h3>
                                <div className="text-[14px] text-gray-500">
                                    {t("Total of")}{" "}
                                    <span className="font-semibold">
                                        {selectedClasses.length}
                                    </span>{" "}
                                </div>
                            </div>
                            <Button
                                variant={"outline"}
                                size={"smaller"}
                                onClick={() => setOpenClassSearch(true)}
                            >
                                {t("Add ")}
                                {capitalize(t("classes"))}
                            </Button>
                        </div>

                        {form.formState.errors?.classes?.message && (
                            <p className="warning-text mb-2">
                                (
                                {t(
                                    `${form.formState.errors?.classes?.message}`
                                )}
                                )
                            </p>
                        )}

                        <div className="lg:min-w-[800px]">
                            <DataTable
                                isSmaller={true}
                                columns={[
                                    {
                                        key: "_",
                                        modify: (_, cell) => {
                                            const id = cell?.row?.original?.id;
                                            return (
                                                <X
                                                    size={20}
                                                    className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                                    onClick={() => {
                                                        setSelectedClasses(
                                                            [
                                                                ...selectedClasses,
                                                            ].filter(
                                                                (item) =>
                                                                    item.id !==
                                                                    id
                                                            )
                                                        );
                                                    }}
                                                />
                                            );
                                        },
                                    },
                                    ...activeLanguages!.map((lang) => ({
                                        key: lang.code,
                                        displayAs: `${t("class")} ( ${t(lang?.name)} )`,
                                    })),
                                    {
                                        key: "type",
                                        modify: (value) => t(value),
                                    },
                                    { key: "grade" },
                                ]}
                                data={selectedClasses.map((item) => ({
                                    id: item?.id,
                                    ...item?.translations?.name,
                                    type: item?.type,
                                    grade: item?.grade?.name,
                                }))}
                            />
                        </div>
                    </div>

                    <Modal
                        open={openClassSearch}
                        onOpenChange={setOpenClassSearch}
                        size="large"
                    >
                        <ClassSearchEngine
                            isMultiSelect={true}
                            setSelection={onAdd}
                            close={() => setOpenClassSearch(false)}
                            otherFilterParams={{ type: classType }}
                        />
                    </Modal>
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default AssignSemesterClassForm;
