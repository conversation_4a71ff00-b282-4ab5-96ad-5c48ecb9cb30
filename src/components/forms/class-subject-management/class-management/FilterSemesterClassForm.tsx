import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import {
    classesAPI,
    ClassTypes,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    employeeAPI,
    GET_ALL_PARAMS,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../../base-ui/button";
import { useAsyncSelect } from "@/lib/async-select-hook";

const FilterSemesterClassForm = ({
    classType,
    currentSemesterId,
    filter,
    setFilter,
    close,
}: CommonFilterProps & { classType: ClassTypes; currentSemesterId }) => {
    const form = useForm({
        defaultValues: {
            class_type: classType,
            name: filter?.name ?? "",
            semester_setting_id:
                filter?.semester_setting_id ?? currentSemesterId ?? "",
            class_id: filter?.class_id ?? "",
            homeroom_teacher_id: filter?.homeroom_teacher_id ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });
    const t = useTranslations("common");

    const [teacherName, setTeacherName] = useState();

    const { asyncOptions, setAsyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: employeeAPI,
    });

    function onSubmit(data: Record<string, any>) {
        const _filter: any = { ...filter, ...data, page: 1 };
        if (data.homeroom_teacher_id && teacherName) {
            _filter.teacherName = teacherName;
        }
        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
            semester_setting_id: currentSemesterId,
        });
        close();
    }

    const locale = useLocale();

    const { data: semesterOptions, axiosQuery: getSemesters } = useAxios({
        api: semesterSettingAPI,
        locale,
    });

    const { data: classOptions, axiosQuery: getClasses } = useAxios({
        api: classesAPI,
        locale,
    });

    useEffect(() => {
        getSemesters({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (filter?.homeroom_teacher_id && filter?.teacherName) {
            loadAsyncOptions(filter.teacherName, setAsyncOptions);
        }
    }, []);

    useEffect(() => {
        getClasses({
            params: {
                ...GET_ALL_PARAMS,
                type: classType,
                semester_setting_id: form.watch("semester_setting_id"),
            },
        });
    }, [form.watch("semester_setting_id"), locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name={"semester_setting_id"}
                    label={"semester"}
                    options={semesterOptions}
                    isSortByName={false}
                    isClearable={false}
                    onChange={() => form.setValue("class_id", "")}
                />

                <FormSelect
                    control={form.control}
                    name={"class_id"}
                    label="class"
                    options={classOptions}
                    isDisabled={!form.watch("semester_setting_id")}
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: t("Active"),
                            id: "1",
                        },
                        {
                            name: t("Inactive"),
                            id: "0",
                        },
                    ]}
                />

                <div className="lg:col-span-2 2xl:col-span-1">
                    <FormSelectAsync
                        control={form.control}
                        name="homeroom_teacher_id"
                        label={t("teacher guardian")}
                        loadOptions={loadAsyncOptions}
                        value={asyncOptions.find(
                            (option) =>
                                option.value ===
                                form.watch("homeroom_teacher_id")
                        )}
                        onChange={(val) => setTeacherName(val?.name)}
                    />
                </div>

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSemesterClassForm;
