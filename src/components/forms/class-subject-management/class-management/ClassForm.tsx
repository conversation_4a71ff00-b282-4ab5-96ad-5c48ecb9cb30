import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import { axiosInstance } from "@/lib/api";
import {
    ADVANCED_1,
    ADVANCED_2,
    ADVANCED_3,
    classesAPI,
    classStreamOptions,
    ClassTypes,
    CommonFormProps,
    ELEMENTARY,
    ENGLISH,
    gradeAPI,
    INTERMEDIATE,
    PRE_INTERMEDIATE,
    PRIMARY,
    semesterSettingAPI,
    SOCIETY,
    STARTER,
    UPPER_INTERMEDIATE,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    configForGetAll,
    showBackendFormError,
    strStartCase,
} from "@/lib/utils";

const ClassForm = (props: CommonFormProps & { classType: ClassTypes }) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [currentClass, setCurrentClass] = useState(null);
    const [options, setOptions] = useState<Record<string, any>>();

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _grades = res[0].data.data;
            const _semesters = res[1].data.data;
            setOptions({ ...options, grades: _grades, semesters: _semesters });

            if (res[2]) {
                setCurrentClass(res[2].data.data);
            }
        },
        onError: () => props.close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(gradeAPI, _config),
            axiosInstance.get(semesterSettingAPI, _config),
            props.id
                ? axiosInstance.get(`${classesAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return activeLanguages && options && (props.isCreate || currentClass) ? (
        <FormWrap
            currentClass={currentClass}
            options={options}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    classType: ClassTypes;
    currentClass: any;
    options: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    classType,
    isCreate = false,
    currentClass,
    options,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            type: classType,
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentClass?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            code: currentClass?.code || "",
            stream: currentClass?.stream ?? "",
            grade_id: currentClass?.grade?.id ?? "",
            english_level: currentClass?.english_level ?? "",
            is_active: true,
        },
    });

    const { axiosPost: createClass, error: postError } = useAxios({
        api: classesAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateClass, error: putError } = useAxios({
        api: classesAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data) => {
            console.log("data", data);
            if (data.type == SOCIETY) {
                delete data.grade_id;
                delete data.english_level;
            }
            if (data.type !== ENGLISH) {
                delete data.english_level;
            }
            if (isCreate) {
                const newData = { ...data };
                createClass(newData);
            } else {
                updateClass({ id: currentClass.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {strStartCase(`${t("class")} (${t(classType)}`)})
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[600px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />

                    {form.watch("type") == ENGLISH && (
                        <FormSelect
                            control={form.control}
                            name="english_level"
                            label={t("english level") + "*"}
                            isStringOptions={true}
                            isSortByName={false}
                            options={[
                                STARTER,
                                ELEMENTARY,
                                PRE_INTERMEDIATE,
                                INTERMEDIATE,
                                UPPER_INTERMEDIATE,
                                ADVANCED_1,
                                ADVANCED_2,
                                ADVANCED_3,
                            ]}
                        />
                    )}

                    {form.watch("type") !== SOCIETY && (
                        <FormSelect
                            control={form.control}
                            name="grade_id"
                            label={
                                form.watch("type") === PRIMARY
                                    ? `${t("grade")}*`
                                    : t("grade")
                            }
                            options={options.grades}
                        />
                    )}

                    <FormSelect
                        control={form.control}
                        name="stream"
                        label={t("stream") + "*"}
                        isSortByName={false}
                        options={classStreamOptions.map((option) => ({
                            ...option,
                            name: t(option?.name),
                        }))}
                    />

                    <div className="mt-24 lg:col-span-2">
                        <Button type="submit" className="ml-auto">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default ClassForm;
