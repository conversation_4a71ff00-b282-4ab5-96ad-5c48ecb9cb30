import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    semesterClassReportsStudentContactAPI,
    semesterClassReportsHomeroomTeacherAPI,
    semesterSettingAPI,
    semesterClassReportsStudentDetailAPI,
    semesterClassReportsByStudentsInClassAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const _teacher_guardian = "Teacher_Guardian";
const _student_contacts = "Student_Contacts";
const _student_details = "Student_Details";
const _by_students_in_class = "By_Students_In_Class";

const SemesterClassDownloadReportForm = ({
    id,
    isSelectSemester = false,
    semesterClassName,
}: {
    id?: any;
    isSelectSemester?: boolean;
    semesterClassName?: string;
}) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            report_type: "",
            export_type: PDF,
            semester_setting_id: "",
            semester_class_id: id ?? "",
        },
    });

    const { data: semesterOptions, axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { axiosQuery: downloadStudentContactsReports } = useAxios({
        api: semesterClassReportsStudentContactAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: downloadStudentDetailsReports } = useAxios({
        api: semesterClassReportsStudentDetailAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: downloadHomeroomTeacherReports } = useAxios({
        api: semesterClassReportsHomeroomTeacherAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: downloadByStudentsInClassReports } = useAxios({
        api: semesterClassReportsByStudentsInClassAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: viewStudentContactsReports } = useAxios({
        api: semesterClassReportsStudentContactAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: viewStudentDetailsReports } = useAxios({
        api: semesterClassReportsStudentDetailAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: viewHomeroomTeacherReports } = useAxios({
        api: semesterClassReportsHomeroomTeacherAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    const { axiosQuery: viewByStudentsInClassReports } = useAxios({
        api: semesterClassReportsByStudentsInClassAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    function getReport(isView: boolean) {
        form.clearErrors();
        form.handleSubmit((data) => {
            console.log(data);
            if (isSelectSemester) {
                delete data.semester_class_id;
            } else {
                delete data.semester_setting_id;
            }
            let hasError = false;
            const requiredFields = [
                "report_type",
                "report_language",
                "export_type",
                ...(isSelectSemester ? ["semester_setting_id"] : []),
            ];
            requiredFields.forEach((key) => {
                if (!data[key]) {
                    hasError = true;
                    form.setError(key, {
                        type: "manual",
                        message: t("This field is required"),
                    });
                }
            });
            if (hasError) return;

            if (data.report_type === _student_contacts) {
                delete data.report_type;
                isView
                    ? viewStudentContactsReports({ params: data })
                    : downloadStudentContactsReports({ params: data });
            } else if (data.report_type === _student_details) {
                delete data.report_type;
                isView
                    ? viewStudentDetailsReports({ params: data })
                    : downloadStudentDetailsReports({ params: data });
            } else if (data.report_type === _teacher_guardian) {
                delete data.report_type;
                isView
                    ? viewHomeroomTeacherReports({ params: data })
                    : downloadHomeroomTeacherReports({ params: data });
            } else if (data.report_type === _by_students_in_class) {
                delete data.report_type;
                isView
                    ? viewByStudentsInClassReports({ params: data })
                    : downloadByStudentsInClassReports({ params: data });
            }
        })();
    }

    function onViewReport() {
        getReport(true);
    }

    function onDownloadReport(e) {
        e.preventDefault();
        getReport(false);
    }

    useEffect(() => {
        if (isSelectSemester)
            getSemesterOptions({
                params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
            });
    }, []);

    return (
        <Form {...form}>
            <form className="grid gap-y-3" onSubmit={onDownloadReport}>
                <h3 className="mb-2">
                    {t("Download Report")}
                    {semesterClassName ? ` - ${semesterClassName}` : ""}
                </h3>

                {isSelectSemester && (
                    <FormSelect
                        control={form.control}
                        name={"semester_setting_id"}
                        label={t("semester") + "*"}
                        options={semesterOptions}
                        isSortByName={false}
                    />
                )}

                <FormSelect
                    control={form.control}
                    name="report_type"
                    label={t("report type") + "*"}
                    options={[
                        _student_contacts,
                        ...(isSelectSemester
                            ? [_teacher_guardian]
                            : [_student_details, _by_students_in_class]),
                    ]}
                    isStringOptions
                />

                <FormSelect
                    control={form.control}
                    name="report_language"
                    label={t("report language") + "*"}
                    options={activeLanguages?.map((language) => ({
                        id: language?.code,
                        name: t(language?.name),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="export_type"
                    label={t("export type") + "*"}
                    options={[
                        { id: EXCEL, name: "Excel" },
                        { id: PDF, name: "PDF" },
                    ]}
                />
                <div className="mt-3 flex items-center justify-end gap-2">
                    {form.watch("export_type") === PDF && (
                        <Button variant={"outline"} onClick={onViewReport}>
                            {t("View Report")}
                        </Button>
                    )}
                    <Button type="submit">{t("Download Report")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default SemesterClassDownloadReportForm;
