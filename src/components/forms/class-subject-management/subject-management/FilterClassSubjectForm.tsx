import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    classSubjectsAPIFilter,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    SOCIETY,
    subjectAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../../base-ui/button";

const FilterClassSubjectForm = ({
    type,
    filter,
    setFilter,
    close,
    currentSemesterId,
}: CommonFilterProps & { currentSemesterId: any }) => {
    const form = useForm({
        defaultValues: {
            semester_setting_id: filter?.semester_setting_id ?? null,
            semester_class_id: filter?.semester_class_id ?? "",
            subject_id: filter?.subject_id ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            ...classSubjectsAPIFilter,
            per_page: filter?.per_page,
            semester_setting_id: currentSemesterId,
        });
        close();
    }

    const locale = useLocale();

    const { data: semesterOptions, axiosQuery: getSemesters } = useAxios({
        api: semesterSettingAPI,
        locale,
    });

    const { data: semesterClasses, axiosQuery: getSemesterClasses } = useAxios({
        api: semesterClassesAPI,
        locale,
    });

    const { data: subjects, axiosQuery: getSubjects } = useAxios({
        api: subjectAPI,
        locale,
    });

    useEffect(() => {
        getSubjects({ params: GET_ALL_PARAMS });
        getSemesters({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, [locale]);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    class_type: type,
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        }
    }, [form.watch("semester_setting_id"), locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name={"semester_setting_id"}
                    label="semester"
                    options={semesterOptions}
                    isSortByName={false}
                    onChange={() => form.setValue("semester_class_id", "")}
                />

                <FormSelect
                    control={form.control}
                    name={"semester_class_id"}
                    label="class"
                    options={
                        semesterClasses?.map((option) => ({
                            id: option?.id,
                            name:
                                (type === SOCIETY
                                    ? `${option?.class_model?.code} - `
                                    : "") + `${option?.class_model?.name}`,
                        })) ?? []
                    }
                    isDisabled={!form.watch("semester_setting_id")}
                />

                <FormSelect
                    control={form.control}
                    name={"subject_id"}
                    label="subject"
                    options={subjects}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterClassSubjectForm;
