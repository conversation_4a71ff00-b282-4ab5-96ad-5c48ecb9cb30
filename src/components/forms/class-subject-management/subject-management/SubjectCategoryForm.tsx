import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, employeeJobTitlesAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const SubjectCategoryForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: subjectCategory, axiosQuery: getSubject } = useAxios({
        api: employeeJobTitlesAPI, // subject category API
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getSubject({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || subjectCategory) ? (
        <FormWrap
            subjectCategory={subjectCategory}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    subjectCategory: any;

    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    subjectCategory,

    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: subjectCategory?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    console.log("form values", form.getValues());

    const { axiosPost: createSubjectCategory, error: postError } = useAxios({
        api: employeeJobTitlesAPI, // subject category API
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSubjectCategory, error: putError } = useAxios({
        api: employeeJobTitlesAPI, // subject category API
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createSubjectCategory(data);
            } else {
                updateSubjectCategory({ id: subjectCategory.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Subject Category
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}`}
                        />
                    ))}

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SubjectCategoryForm;
