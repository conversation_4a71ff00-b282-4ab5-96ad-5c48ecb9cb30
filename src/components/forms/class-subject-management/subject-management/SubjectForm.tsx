import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    COCURRICULUM,
    CommonFormProps,
    ELECTIVE,
    GET_ALL_PARAMS,
    MAJOR,
    societyAPI,
    subjectAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, strStartCase } from "@/lib/utils";

const SubjectForm = (props: CommonFormProps & { type: string }) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentSubject, axiosQuery: getSubject } = useAxios({
        api: subjectAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getSubject({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentSubject) ? (
        <FormWrap
            currentSubject={currentSubject}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: any;
    currentSubject: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    type,
    isCreate = false,
    currentSubject,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            type: type,
            club_id: currentSubject?.club?.id ?? null,
            code: currentSubject?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentSubject?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const locale = useLocale();

    const {
        data: societyOptions,
        axiosQuery: getSocieties,
        isLoading: isLoadingSocieties,
    } = useAxios({
        api: societyAPI,
        locale,
    });

    useEffect(() => {
        getSocieties({ params: GET_ALL_PARAMS });
    }, []);

    const { axiosPost: createSubject, error: postError } = useAxios({
        api: subjectAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSubject, error: putError } = useAxios({
        api: subjectAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (data.type !== COCURRICULUM) {
                delete data.club_id;
            }

            console.log("data", data);

            if (isCreate) {
                createSubject(data);
            } else {
                updateSubject({ id: currentSubject.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {strStartCase(t("subject"))}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {form.watch("type") === "COCURRICULUM" && (
                        <FormSelect
                            control={form.control}
                            name="club_id"
                            label={t("Society/Club") + "*"}
                            options={societyOptions}
                            isLoading={isLoadingSocieties}
                        />
                    )}

                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SubjectForm;
