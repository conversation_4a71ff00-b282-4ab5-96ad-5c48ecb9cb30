import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { flatten, orderBy } from "lodash";
import { X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Checkbox } from "@/components/base-ui/checkbox";
import { Form } from "@/components/base-ui/form";
import FormSearchInput from "@/components/ui/FormSearchInput";
import InfoCard from "@/components/ui/InfoCard";
import Modal from "@/components/ui/Modal";
import StudentsPicker from "@/components/ui/PickersWithPaginatedTable/StudentsPicker";
import TrainerSearchEngine from "@/components/ui/search-engines/TrainerSearchEngine";
import {
    classSubjectAPI,
    COCURRICULUM,
    CommonFormProps,
    employeeAPI,
    SOCIETY,
    WORKING,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    getChunkedStudentData,
    optionUserLabel,
    showBackendFormError,
} from "@/lib/utils";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";

const ClassSubjectSettingsForm = ({ classType, id, refresh, close }) => {
    const locale = useLocale();

    const { data: currentClassSubject, axiosQuery: getClassSubject } = useAxios(
        {
            api: classSubjectAPI,
            locale,
            onError: () => close(),
        }
    );

    useEffect(() => {
        getClassSubject({ id });
    }, []);

    return currentClassSubject ? (
        <FormWrap
            classType={classType}
            currentClassSubject={currentClassSubject}
            refresh={refresh}
            close={close}
        />
    ) : (
        <div className="h-10"></div>
    );
};

const PRIMARY = "PRIMARY";
const SECONDARY = "SECONDARY";

type FormWrapProps = CommonFormProps & {
    classType: string;
    currentClassSubject: any;
};

const FormWrap = ({
    classType,
    currentClassSubject,
    refresh,
    close,
}: FormWrapProps) => {
    const _showPrimaryClass = classType !== PRIMARY;

    const form = useForm<any>({
        defaultValues: {
            number_of_period_per_week:
                currentClassSubject?.number_of_period_per_week,
            teachers:
                currentClassSubject?.teachers
                    ?.sort((a, b) =>
                        a?.type === "PRIMARY"
                            ? -1
                            : b?.type === "PRIMARY"
                              ? 1
                              : 0
                    )
                    ?.map((item) => ({
                        id: item?.teacher?.id,
                        type: item?.type,
                        name: optionUserLabel(
                            item?.teacher?.employee_number,
                            item?.teacher?.translations?.name
                        ),
                    })) ?? [],
            _select_teacher: null,
            trainers:
                currentClassSubject?.trainers.map((item) => ({
                    id: item?.id,
                    name: combinedNames(item?.translations?.name),
                })) ?? [],
        },
    });

    const { loadAsyncOptions } = useAsyncSelect({
        api: employeeAPI,
        params: {
            status: WORKING,
            is_active: 1,
            ...(classType === SOCIETY ? {} : { is_teacher: 1 }),
        },
    });

    function onAppendTeacher(target) {
        const existingTeachers = form.getValues("teachers");

        if (
            existingTeachers.find(
                (teacher) => teacher?.employee_id == target?.value
            )
        )
            return;

        const hasPrimaryTicked =
            existingTeachers.find((teacher) => teacher?.type === PRIMARY) !==
            undefined;

        appendTeacher({
            type:
                hasPrimaryTicked && target?.type === PRIMARY
                    ? SECONDARY
                    : target?.type ?? SECONDARY,
            id: target?.value,
            name: target?.label ?? target?.name,
        });
    }

    const { append: appendTeacher, remove: removeTeacher } = useFieldArray({
        control: form.control,
        name: "teachers",
    });

    const { append: appendTrainer, remove: removeTrainer } = useFieldArray({
        control: form.control,
        name: "trainers",
    });

    const [openSearchTrainer, setOpenSearchTrainer] = useState(false);

    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        getChunkedStudentData(currentClassSubject?.students ?? [], true)
    );

    function onSetPrimaryTeacher(teacher, index) {
        const teachers = form.getValues("teachers");

        if (teachers.length > 1 && teacher?.type === SECONDARY) {
            // uncheck others
            const checkedIndex = teachers.findIndex(
                (teacher) => teacher?.type === PRIMARY
            );

            if (checkedIndex === index) return;

            if (checkedIndex > -1) {
                form.setValue(`teachers[${checkedIndex}].type`, SECONDARY);
            }
        }

        form.setValue(
            `teachers[${index}].type`,
            teacher?.type === PRIMARY ? SECONDARY : PRIMARY
        );
    }

    const { axiosPut: updateClassSubject, error: putError } = useAxios({
        api: classSubjectAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data) => {
            console.log("data", data);
            const newData = {
                ...data,
                student_ids:
                    flatten(targetsChunk)?.map((student) => student?.id) || [],
            };

            const trainerIds =
                data.trainers?.map((trainer) => trainer.id) || [];

            newData.trainer_ids = trainerIds;

            delete newData.trainers;

            console.log("newData", newData);

            updateClassSubject({
                id: currentClassSubject.id,
                data: newData,
            });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-4">{t("Class Subject Settings")}</h2>
            <InfoCard
                data={{
                    semester:
                        currentClassSubject?.semester_class?.semester_setting
                            ?.name,
                    class: currentClassSubject?.semester_class?.class_model
                        ?.name,
                    subject: currentClassSubject?.subject?.name,
                    society: currentClassSubject?.subject?.club?.name ?? "-",
                }}
            />

            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="mt-5 grid gap-y-5 lg:min-w-[800px]"
                >
                    <div className="max-w-[400px]">
                        <FormInputInterger
                            control={form.control}
                            name="number_of_period_per_week"
                        />
                    </div>
                    <div>
                        <div className="max-w-[400px]">
                            <FreeSelectAsync
                                control={form.control}
                                name="_select_teacher"
                                label="Teachers"
                                placeholder="Select Teacher"
                                minWidth={300}
                                loadOptions={loadAsyncOptions}
                                value={null}
                                onChange={(option) => {
                                    console.log(option);
                                    onAppendTeacher(option);
                                }}
                            />
                        </div>

                        {form.watch("teachers")?.length > 0 && (
                            <>
                                <div className="mb-2.5 mt-3 text-[13px] text-gray-500">
                                    {t(
                                        "Set a primary teacher by ticking the checkbox"
                                    )}
                                </div>
                                <div className="ml-0.5 grid gap-y-2.5">
                                    {form
                                        .watch("teachers")
                                        .map((teacher, index) => (
                                            <div
                                                key={teacher?.id}
                                                className="flex items-center gap-x-2"
                                            >
                                                <Checkbox
                                                    checked={
                                                        form.watch(
                                                            `teachers[${index}].type`
                                                        ) === PRIMARY
                                                    }
                                                    onCheckedChange={() =>
                                                        onSetPrimaryTeacher(
                                                            teacher,
                                                            index
                                                        )
                                                    }
                                                />

                                                <div className="c-text-size">
                                                    {teacher?.name}
                                                </div>
                                                <X
                                                    size={20}
                                                    className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                                    onClick={() =>
                                                        removeTeacher(index)
                                                    }
                                                />
                                            </div>
                                        ))}
                                </div>
                            </>
                        )}

                        {currentClassSubject?.subject?.type == COCURRICULUM && (
                            <FormSearchInput
                                name="trainers"
                                control={form.control}
                                label="Coaches"
                                onClick={() => setOpenSearchTrainer(true)}
                                displayValue={
                                    form.watch("trainers")?.length > 0
                                        ? `${form.watch("trainers").length} trainer${form.watch("trainers").length > 1 ? "s" : ""} selected`
                                        : ""
                                }
                                styleClass="max-w-[400px] mt-4"
                            />
                        )}

                        {form.watch("trainers")?.length > 0 && (
                            <div className="mb-1 ml-0.5 mt-3 grid gap-y-2.5">
                                {form
                                    .watch("trainers")
                                    .map((trainer, index) => (
                                        <div
                                            key={trainer?.id}
                                            className="flex items-center gap-x-2"
                                        >
                                            <div className="c-text-size">
                                                {trainer?.name}
                                            </div>
                                            <X
                                                size={20}
                                                className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                                onClick={() =>
                                                    removeTrainer(index)
                                                }
                                            />
                                        </div>
                                    ))}
                            </div>
                        )}
                    </div>

                    <StudentsPicker
                        targetsChunk={targetsChunk}
                        setTargetsChunk={setTargetsChunk}
                        errorMessage={form.formState.errors?.students?.message}
                        otherFilterParams={{
                            semester_setting_id:
                                currentClassSubject?.semester_class
                                    ?.semester_setting?.id,
                            semester_class_id:
                                currentClassSubject?.semester_class?.id,
                            only_active_class: 1,
                            ...(_showPrimaryClass
                                ? {
                                      response: "FULL",
                                      includes: [
                                          "currentSemesterPrimaryClass.semesterClass.classModel",
                                      ],
                                  }
                                : {}),
                        }}
                        showPrimaryClass={_showPrimaryClass}
                    />

                    <Button type="submit" className="ml-auto">
                        {t("Save")}
                    </Button>
                </form>
            </Form>

            <Modal
                open={openSearchTrainer}
                onOpenChange={setOpenSearchTrainer}
                size="large"
            >
                <TrainerSearchEngine
                    isMultiSelect
                    setSelection={(trainers) => {
                        if (trainers) {
                            const currentIds = form
                                .watch("trainers")
                                ?.map((trainer: any) => trainer?.id.toString());
                            const newTrainers = trainers
                                .filter(
                                    (trainer: any) =>
                                        !currentIds.includes(
                                            trainer.id.toString()
                                        )
                                )
                                .map((trainer) => ({
                                    id: trainer.id,
                                    name: combinedNames(
                                        trainer?.translations?.name
                                    ),
                                }));
                            appendTrainer(newTrainers);
                            form.clearErrors("trainers");
                        }
                    }}
                    close={() => setOpenSearchTrainer(false)}
                />
            </Modal>
        </div>
    );
};

export default ClassSubjectSettingsForm;
