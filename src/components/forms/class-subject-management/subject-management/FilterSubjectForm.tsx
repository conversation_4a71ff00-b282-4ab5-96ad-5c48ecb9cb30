import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    COCURRICULUM,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    ELECTIVE,
    GET_ALL_PARAMS,
    MAJOR,
    societyAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../../base-ui/button";

const FilterSubjectForm = ({
    type,
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            code: filter?.code ?? "",
            type: type,
            club_id: filter?.club_id ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const locale = useLocale();

    const {
        data: societyOptions,
        axiosQuery: getSocietyOptions,
        isLoading,
    } = useAxios({
        api: societyAPI,
        locale,
        onError: close,
    });

    useEffect(() => {
        getSocietyOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"code"} />

                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name="club_id"
                    label="club"
                    options={societyOptions}
                    isLoading={isLoading}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSubjectForm;
