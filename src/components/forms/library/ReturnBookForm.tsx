import { useEffect, useState } from "react";
import { isNumber } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import FreeCheckbox from "@/components/ui/FreeCheckbox";
import FreeInput from "@/components/ui/FreeInput";
import FreeSelect from "@/components/ui/FreeSelect";
import FreeTextArea from "@/components/ui/FreeTextArea";
import Modal from "@/components/ui/Modal";
import { axiosInstance } from "@/lib/api";
import {
    appCurrencySymbol,
    BORROWED,
    libraryBooksAPI,
    libraryReturnBookAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLoading } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import FreeInputDecimal from "@/components/ui/FreeInputDecimal";
import { useTranslations } from "next-intl";
import clsx from "clsx";

const ReturnBookForm = ({ books, refresh, close }) => {
    const setLoading = useLoading((state) => state.setLoading);

    const form = useForm<any>({
        defaultValues: {
            payment_method: "",
            book_loans: books.map((book) => ({
                id: book?.id,
                penalty_overdue_amount: book?.overdue_amount ?? 0,
                penalty_lost_amount: 0,
                is_lost: false,
                remarks: "",
            })),
        },
    });

    const [totalPayable, setTotalPayable] = useState(0);
    const [showPaymentMethodWarning, setShowPaymentMethodWarning] =
        useState(false);
    const [promptConfirmReturn, setPromptConfirmReturn] = useState(false);

    function getTotalPayable() {
        const currentBooks = form.getValues("book_loans");
        const total = currentBooks.reduce(
            (acc, book) =>
                acc +
                Number(book.penalty_overdue_amount) +
                Number(book.penalty_lost_amount),
            0
        );
        setTotalPayable(total);
    }

    useEffect(() => {
        getTotalPayable();
    }, []);

    const { handleError } = useAxios({});

    function onIsLostChange(isChecked, index, bookId) {
        if (isChecked) {
            setLoading(true);
            axiosInstance
                .get(`${libraryBooksAPI}/${bookId}`)
                .then((res) => {
                    const book = res.data.data;
                    const penaltyAmount = book?.lost_penalty_value;
                    form.setValue(
                        `book_loans[${index}].penalty_lost_amount`,
                        penaltyAmount ?? 0
                    );
                    getTotalPayable();
                })
                .catch((error) => {
                    handleError(error);
                })
                .finally(() => setLoading(false));
        } else {
            form.setValue(`book_loans[${index}].penalty_lost_amount`, 0);
            getTotalPayable();
        }
    }

    const { axiosPost: returnBook, error } = useAxios({
        api: libraryReturnBookAPI,
        toastMsg: "Books returned successfully",
        onSuccess(result) {
            refresh();
            close();
        },
    });

    function onSubmit() {
        setPromptConfirmReturn(false);
        form.clearErrors();
        setShowPaymentMethodWarning(false);
        form.handleSubmit((data) => {
            if (totalPayable > 0 && !data.payment_method) {
                setShowPaymentMethodWarning(true);
                return;
            }
            data.book_loans = data.book_loans.map((book) => {
                const overdueAmount = Number(book.penalty_overdue_amount);
                const lostAmount = Number(book.penalty_lost_amount);
                return {
                    ...book,
                    penalty_overdue_amount: overdueAmount,
                    penalty_lost_amount: lostAmount,
                    penalty_paid_amount: overdueAmount + lostAmount,
                };
            });
            returnBook(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, error);
    }, [error]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">{t("Return Books")}</h2>
            <div className="c-table-wrap">
                <table className="c-table smaller w-full lg:min-w-[1000px]">
                    <thead>
                        <tr className="capitalize">
                            <th>barcode number(条码)</th>
                            <th className="min-w-[100px]">{t("book title")}</th>
                            <th>{t("borrow date")}</th>
                            <th>{t("due date")}</th>
                            <th>{t("due aging")}</th>
                            <th>
                                {t("overdue amount")} ({appCurrencySymbol})
                            </th>
                            <th className="whitespace-nowrap">
                                {t("is lost")}
                            </th>
                            <th>
                                {t("penalty lost amount")} ({appCurrencySymbol})
                            </th>
                            <th>{t("status")}</th>
                            <th>{t("remark")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {books.map((item, index) => (
                            <tr key={item.id}>
                                <td>{item.book_number}</td>
                                <td>{item.book_name}</td>
                                <td className="whitespace-nowrap">
                                    {item.borrow_date}
                                </td>
                                <td className="whitespace-nowrap">
                                    {item.current_due_date}
                                </td>
                                <td className="text-center">
                                    {item.due_aging}
                                </td>
                                <td className="text-center">
                                    <div className="w-[100px]">
                                        <FreeInputDecimal
                                            hasLabel={false}
                                            control={form.control}
                                            name={`book_loans[${index}].penalty_overdue_amount`}
                                            onChange={getTotalPayable}
                                            error={
                                                form.formState.errors
                                                    ?.book_loans?.[index]
                                                    ?.penalty_overdue_amount
                                            }
                                        />
                                    </div>
                                </td>
                                <td>
                                    <FreeCheckbox
                                        hasLabel={false}
                                        control={form.control}
                                        name={`book_loans[${index}].is_lost`}
                                        onChange={(value) =>
                                            onIsLostChange(
                                                value,
                                                index,
                                                item.book_id
                                            )
                                        }
                                    />
                                </td>
                                <td>
                                    <div className="w-[100px]">
                                        <FreeInputDecimal
                                            hasLabel={false}
                                            control={form.control}
                                            name={`book_loans[${index}].penalty_lost_amount`}
                                            onChange={getTotalPayable}
                                            error={
                                                form.formState.errors
                                                    ?.book_loans?.[index]
                                                    ?.penalty_lost_amount
                                            }
                                        />
                                    </div>
                                </td>
                                <td
                                    className={clsx(
                                        item.status === BORROWED &&
                                            "text-orange-400"
                                    )}
                                >
                                    {t(item.status)}
                                </td>
                                <td>
                                    <FreeTextArea
                                        control={form.control}
                                        name={`book_loans[${index}].remarks`}
                                        error={
                                            form.formState.errors?.book_loans?.[
                                                index
                                            ]?.remarks
                                        }
                                    />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="mt-5 flex flex-col items-end">
                <div className="flex">
                    {isNumber(totalPayable) && totalPayable > 0 && (
                        <div className="mr-5 mt-0.5 min-w-[140px] border-r border-dashed pr-5">
                            <FreeSelect
                                control={form.control}
                                name="payment_method"
                                isStringOptions
                                options={["WALLET", "CASH"]}
                            />
                            {showPaymentMethodWarning && (
                                <p className="warning-text">
                                    {t("This field is required")}
                                </p>
                            )}
                        </div>
                    )}
                    <div className="pb-9">
                        <div>
                            <p className="mr-2 font-medium text-themeLabel">
                                {t("Total Payable Amount")}
                            </p>
                            <div className="ml-0.5 mr-2 flex gap-x-1">
                                <span className="mt-0.5 text-sm">
                                    {appCurrencySymbol}
                                </span>
                                <span className="text-lg font-medium">
                                    {totalPayable}
                                </span>
                            </div>
                        </div>
                        <Button
                            className="mt-2"
                            onClick={() => setPromptConfirmReturn(true)}
                        >
                            {t("Return Books")}
                            {totalPayable > 0 && t(" & Pay Penalty")}
                        </Button>
                    </div>
                </div>
            </div>
            <Modal
                open={promptConfirmReturn}
                onOpenChange={setPromptConfirmReturn}
            >
                <p className="mt-3 font-medium">
                    {t("Are you sure you want to proceed?")}
                </p>
                <DialogFooter className={"mt-2"}>
                    <Button
                        variant="outline"
                        onClick={() => setPromptConfirmReturn(false)}
                    >
                        {t("Cancel")}
                    </Button>
                    <Button onClick={onSubmit}>{t("Confirm")}</Button>
                </DialogFooter>
            </Modal>
        </div>
    );
};

export default ReturnBookForm;
