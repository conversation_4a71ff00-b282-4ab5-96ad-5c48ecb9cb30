import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import FreeDatePicker from "@/components/ui/FreeDatePicker";
import Modal from "@/components/ui/Modal";
import { BORROWED, librayExtendBorrowAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError, toYMD } from "@/lib/utils";
import { useTranslations } from "next-intl";
import clsx from "clsx";

const ExtendBorrowForm = ({ books, refresh, close }) => {
    console.log(books);
    const form = useForm<any>({
        defaultValues: {
            book_loans: books.map((book) => ({
                id: book?.id,
                due_date: "",
            })),
        },
    });

    const [promptConfirmExtend, setPromptConfirmExtend] = useState(false);

    const { axiosPost: extendBorrow, error } = useAxios({
        api: librayExtendBorrowAPI,
        toastMsg: "Borrow period extended successfully",
        onSuccess(result) {
            refresh();
            close();
        },
    });

    function onSubmit() {
        setPromptConfirmExtend(false);
        form.clearErrors();
        form.handleSubmit((data) => {
            data.book_loans = data.book_loans.map((book) => ({
                id: book.id,
                due_date: toYMD(book.due_date),
            }));
            console.log(data);
            extendBorrow(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, error);
    }, [error]);

    const t = useTranslations("common");

    return (
        <div className="pb-[100px]">
            <h2 className="mb-5">{t("Extend Borrow Period")}</h2>
            <div className="c-table-wrap">
                <table className="c-table smaller w-full lg:min-w-[1000px]">
                    <thead>
                        <tr className="capitalize">
                            <th>barcode number(条码)</th>
                            <th className="min-w-[100px]">{t("book title")}</th>
                            <th>{t("borrow date")}</th>
                            <th>{t("due date")}</th>
                            <th>{t("due aging")}</th>
                            <th>{t("extended date")}</th>
                            <th>{t("status")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {books.map((item, index) => (
                            <tr key={item.id}>
                                <td>{item.book_number}</td>
                                <td>{item.book_name}</td>
                                <td className="whitespace-nowrap">
                                    {item.borrow_date}
                                </td>
                                <td className="whitespace-nowrap">
                                    {item.current_due_date}
                                </td>
                                <td className="text-center">
                                    {item.due_aging}
                                </td>

                                <td>
                                    <FreeDatePicker
                                        control={form.control}
                                        name={`book_loans[${index}].due_date`}
                                        error={
                                            form.formState.errors?.book_loans?.[
                                                index
                                            ]?.due_date
                                        }
                                    />
                                </td>

                                <td
                                    className={clsx(
                                        item.status === BORROWED &&
                                            "text-orange-400"
                                    )}
                                >
                                    {t(item.status)}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <Button
                className="ml-auto mt-5"
                onClick={() => setPromptConfirmExtend(true)}
            >
                {t("Submit")}
            </Button>
            <Modal
                open={promptConfirmExtend}
                onOpenChange={setPromptConfirmExtend}
            >
                <p className="mt-3 font-medium">
                    {t("Are you sure you want to proceed?")}
                </p>
                <DialogFooter className={"mt-2"}>
                    <Button
                        variant="outline"
                        onClick={() => setPromptConfirmExtend(false)}
                    >
                        {t("Cancel")}
                    </Button>
                    <Button onClick={onSubmit}>{t("Confirm")}</Button>
                </DialogFooter>
            </Modal>
        </div>
    );
};

export default ExtendBorrowForm;
