import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    bookClassificationAPI,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import FormSelect from "../../ui/FormSelect";

const FilterBookSubClassificationForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            book_classification_id: filter?.book_classification_id ?? null,
            name: filter?.name ?? "",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const locale = useLocale();

    const { data: bookClassificationList, axiosQuery: getBookClassification } =
        useAxios({
            api: bookClassificationAPI,
            locale,
        });

    useEffect(() => {
        getBookClassification({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name="book_classification_id"
                    label={t("book classification") + "*"}
                    options={bookClassificationList}
                />

                <FormInput control={form.control} name={"name"} />
                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterBookSubClassificationForm;
