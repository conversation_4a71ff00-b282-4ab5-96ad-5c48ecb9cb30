import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormFileInput from "@/components/ui/FormFileInput";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FormSelect from "@/components/ui/FormSelect";
import { axiosInstance } from "@/lib/api";
import {
    CommonFormProps,
    configAPI,
    countryAPI,
    genderOptions,
    GET_ALL_PARAMS,
    libraryMembersAPI,
    OTHERS,
    raceAPI,
    religionAPI,
    STAFF,
    stateAPI,
    STUDENT,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    configForGetAll,
    isProperPhoneNumber,
    isValueTrue,
    showBackendFormError,
    studentInfoDisplay,
    toYMD,
} from "@/lib/utils";
import FormCheckbox from "../../ui/FormCheckbox";
import FormDivider from "../../ui/FormDivider";
import InfoDisplay from "../../ui/InfoDisplay";
import StaffSearchEngine from "../../ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "../../ui/search-engines/StudentSearchEngine";

type MemberType = typeof STUDENT | typeof STAFF | typeof OTHERS;

const LibraryMemberForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);

    const [member, setMember] = useState<Record<string, any> | null>(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);
    const [defaultBorrowLimit, setDefaultBorrowLimit] = useState<number>();

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.countries = res[0].data.data;
            _options.races = res[1].data.data;
            _options.religions = res[2].data.data;
            const config = res[3].data.data;
            const _defaultBorrowLimit = config.find(
                (item) => item.key === "LIBRARY_BORROW_LIMIT_OTHER"
            )?.value;
            setDefaultBorrowLimit(_defaultBorrowLimit);
            setOptions(_options);
            if (res[4]) {
                setMember(res[4].data.data);
            }
        },
        onError: () => props.close(),
    });

    const locale = useLocale();

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(countryAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(configAPI, _config),
            props.id
                ? axiosInstance.get(`${libraryMembersAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return activeLanguages && options && (props.isCreate || member) ? (
        <FormWrap
            member={member}
            activeLanguages={activeLanguages}
            options={options}
            defaultBorrowLimit={defaultBorrowLimit}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    member: any;
    options: any;
    defaultBorrowLimit: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    member,
    options,
    activeLanguages,
    defaultBorrowLimit,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: member?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            type: OTHERS,
            card_number: member?.card_number ?? "",
            member_number: member?.member_number ?? "",
            register_date: isCreate ? new Date() : member?.register_date ?? "",
            valid_from: isCreate ? new Date() : member?.valid_from ?? "",
            valid_to: member?.valid_to ?? "",
            gender: member?.gender ?? "",
            nric: member?.nric ?? "",
            passport_number: member?.passport_number ?? "",
            date_of_birth: member?.date_of_birth ?? "",
            race_id: member?.race?.id ?? "",
            religion_id: member?.religion?.id ?? "",
            address: member?.address ?? "",
            postcode: member?.postcode ?? "",
            city: member?.city ?? "",
            state_id: member?.state?.id ?? "",
            country_id: member?.country?.id ?? "",
            phone_number: member?.phone_number ?? "",
            email: member?.email ?? "",

            borrow_limit: member?.borrow_limit ?? defaultBorrowLimit ?? "",
            is_active: isCreate ? true : isValueTrue(member?.is_active),
            is_librarian: isValueTrue(member?.is_librarian),
        },
    });

    const [memberType, setMemberType] = useState<MemberType | null>(OTHERS);
    // const [memberType, setMemberType] = useState<MemberType | null>(
    //     member?.type ?? null
    // );
    const [selected, setSelected] = useState<Record<string, any> | null>(
        member ?? null
    );

    // useEffect(() => {
    //     if (memberType === OTHERS) {
    //         if (!member) return;
    //         Object.entries(member).map(([key, value]) =>
    //             form.setValue(key, value)
    //         );
    //     }
    // }, [memberType]);

    const { axiosMultipartPost: createMember, error: postError } = useAxios({
        api: libraryMembersAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosMultipartPut: updateMember, error: putError } = useAxios({
        api: libraryMembersAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSelectMemberType(type) {
        setMemberType(type);
        setSelected(null);
        form.reset();
    }
    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();
        initLoader(
            form.handleSubmit((data: any) => {
                // data.type = memberType;
                // if (memberType === STUDENT || memberType === STAFF) {
                //     data.user_id = selected?.id;
                // }

                if (
                    !isProperPhoneNumber(
                        form,
                        "phone_number",
                        data.phone_number
                    )
                ) {
                    return;
                }

                if (data.photo) {
                    data.photo = data.photo?.[0];
                } else {
                    data.photo = null;
                }

                data.register_date = toYMD(data.register_date);
                data.valid_from = toYMD(data.valid_from);
                data.valid_to = toYMD(data.valid_to);
                data.date_of_birth = toYMD(data.date_of_birth);

                data.is_active = isValueTrue(data.is_active) ? 1 : 0;
                data.is_librarian = isValueTrue(data.is_librarian) ? 1 : 0;

                if (isCreate) {
                    createMember(data);
                } else {
                    updateMember({ id: member.id, data });
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div className="lg:min-w-[800px]">
            <h2 className="mb-5">
                {t(isCreate ? "Create Other " : "Update ")}
                {capitalize(t("member"))}
            </h2>
            <div className="flex flex-col gap-y-2">
                {/* <p className="mb-2 font-medium text-themeLabel">
                    Select Member Type
                </p> */}

                {/* <div className="flex gap-x-2 lg:gap-x-3">
                    {[STUDENT, STAFF, OTHERS].map((type) => (
                        <RaisedButton
                            key={type}
                            name={startCase(type)}
                            isSelected={type === memberType}
                            onClick={() => onSelectMemberType(type)}
                        />
                    ))}
                </div> */}

                {memberType === STUDENT && (
                    <>
                        <StudentSearchEngine
                            setSelection={(val) => {
                                setSelected(val);
                            }}
                            close={() => setMemberType(null)}
                            reset={() => setSelected(null)}
                        />
                    </>
                )}

                {memberType === STAFF && (
                    <>
                        <StaffSearchEngine
                            setSelection={(val) => {
                                setSelected(val);
                            }}
                            close={() => setMemberType(null)}
                            reset={() => setSelected(null)}
                        />
                    </>
                )}

                {/* TODO: update when api ready */}
                {selected && memberType === STUDENT && (
                    <InfoDisplay
                        title={`Selected ${STUDENT} Details`}
                        data={[studentInfoDisplay(selected)]}
                    />
                )}

                {/* TODO: update when api ready */}
                {selected && memberType === STAFF && (
                    <InfoDisplay
                        title={`${STAFF} Details`}
                        data={[
                            {
                                name: selected?.user?.name,
                                employee_number: selected?.employee_number,
                                gender: capitalize(selected?.gender),
                            },
                        ]}
                    />
                )}

                <Form {...form}>
                    <form className="flex flex-col gap-y-2" onSubmit={onSubmit}>
                        {/* {memberType === OTHERS && ( */}
                        <div className="grid-form">
                            {/* <FormDivider /> */}

                            <OtherMemberProfileForm
                                form={form}
                                options={options}
                                countryId={member?.country?.id}
                                activeLanguages={activeLanguages}
                                member={selected}
                            />
                        </div>
                        {/* )} */}

                        <FormDivider />
                        <div className="grid items-center gap-x-3 lg:grid-cols-2">
                            <FormInputInterger
                                control={form.control}
                                name="borrow_limit"
                                label={t("borrow limit") + "*"}
                                type="number"
                            />
                            <div>
                                {member?.type === STUDENT && (
                                    <FormCheckbox
                                        control={form.control}
                                        name="is_librarian"
                                        styleClass="mt-3"
                                    />
                                )}
                                <FormCheckbox
                                    control={form.control}
                                    name={t("is_active")}
                                    styleClass="mt-3"
                                />
                            </div>
                        </div>

                        <Button
                            type="submit"
                            className="mb-3 ml-auto mt-3"
                            // disabled={
                            //     memberType === null ||
                            //     (memberType !== OTHERS &&
                            //         selected === null)
                            // }
                        >
                            {t("Submit")}
                        </Button>
                    </form>
                </Form>
            </div>
        </div>
    );
};

type OtherMemberProfileFormProps = {
    form: any;
    options: Record<string, any>;
    countryId: number;
    activeLanguages: any;
    member: any;
};

const OtherMemberProfileForm = ({
    form,
    options,
    countryId,
    activeLanguages,
    member,
}: OtherMemberProfileFormProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const {
        data: stateList,
        axiosQuery: getStateList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        if (countryId) {
            getStateList({
                params: { ...GET_ALL_PARAMS, country_id: countryId },
            });
        }
    }, []);

    return (
        <>
            <FormInputInterger
                control={form.control}
                name="card_number"
                label={t("card number") + "*"}
                max={10}
            />
            <FormInput
                control={form.control}
                name="member_number"
                label={t("member number") + "*"}
                isUpperCase={true}
            />

            <DatePicker
                control={form.control}
                name="valid_from"
                label={t("valid from") + "*"}
            />
            <DatePicker
                control={form.control}
                name="valid_to"
                label={t("valid to") + "*"}
            />
            <DatePicker
                control={form.control}
                name="register_date"
                label={t("register date") + "*"}
            />

            <FormDivider />

            {activeLanguages.map((lang, index) => (
                <FormInput
                    key={lang?.code || index}
                    control={form.control}
                    name={`name[${lang?.code}]`}
                    label={`${t("name")} (${t(lang?.name)})*`}
                />
            ))}

            <FormDivider />

            <FormInputInterger
                control={form.control}
                name="nric"
                label={t("NRIC") + "*"}
            />

            <FormInput control={form.control} name="passport_number" />

            <FormSelect
                control={form.control}
                name="gender"
                label={t("gender") + "*"}
                isSortByName={false}
                options={genderOptions.map((option) => ({
                    id: option.id,
                    name: capitalize(t(option.name)),
                }))}
            />

            <FormSelect
                control={form.control}
                name="race_id"
                label={t("race")}
                options={options.races}
            />

            <FormSelect
                control={form.control}
                name="religion_id"
                label={t("religion")}
                options={options.religions}
            />

            <DatePicker
                control={form.control}
                name="date_of_birth"
                label={t("date of birth") + "*"}
            />

            <FormDivider />

            <FormInput control={form.control} name="address" />

            <FormInput control={form.control} name="city" />

            <FormSelect
                control={form.control}
                name="country_id"
                label={t("country")}
                options={options.countries}
                onChange={(val) => {
                    getStateList({ params: { country_id: val } });
                    form.setValue("state_id", "");
                }}
            />

            <FormSelect
                control={form.control}
                name="state_id"
                label={t("state")}
                options={stateList}
                isLoading={isStatesLoading}
            />

            <FormInput control={form.control} name="postcode" />

            <FormDivider />

            <FormInput control={form.control} name="email" />

            <FormPhoneInput form={form} name="phone_number" />

            <FormDivider />

            <FormFileInput
                name="photo"
                currentFileUrl={member?.photo}
                register={form.register}
                errors={form.formState.errors}
            />
        </>
    );
};

export default LibraryMemberForm;
