import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import { libraryBooksAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

type RecoverLostBookPromptProps = {
    id: number | null;
    refresh: () => void;
    close: () => void;
};

const RecoverLostBookPrompt = ({
    id,
    close,
    refresh,
}: RecoverLostBookPromptProps) => {
    const form = useForm();

    const { axiosPost: recoverLostBook, error: postError } = useAxios({
        api: `${libraryBooksAPI}/${id}/recover`,
        onSuccess: () => {
            close();
            refresh();
        },
        toastMsg: "Book recovered successfully",
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit(() => {
            if (id) {
                recoverLostBook({ id });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <>
            <p className="mt-3 font-medium">
                Are you sure you want to update the status of this book from
                Lost to Available?
            </p>
            <DialogFooter className={"mt-2"}>
                <Button variant="outline" onClick={close}>
                    Cancel
                </Button>
                <Button onClick={onSubmit}>Confirm</Button>
            </DialogFooter>
        </>
    );
};

export default RecoverLostBookPrompt;
