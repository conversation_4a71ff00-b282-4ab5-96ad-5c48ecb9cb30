import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, bookCategoryAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, strStartCase } from "@/lib/utils";

const BookCategoryForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: bookCategory, axiosQuery: getBookCategory } = useAxios({
        api: bookCategoryAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getBookCategory({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || bookCategory) ? (
        <FormWrap
            bookCategory={bookCategory}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    bookCategory: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    bookCategory,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: bookCategory?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            code: bookCategory?.code ?? "",
        },
    });

    const { axiosPost: createBookCategory, error: postError } = useAxios({
        api: bookCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateBookCategory, error: putError } = useAxios({
        api: bookCategoryAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createBookCategory(data);
            } else {
                updateBookCategory({ id: bookCategory.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {strStartCase(t("book category"))}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />
                    {activeLanguages
                        .filter((lang) => lang?.is_active)
                        .map((lang, index) => (
                            <FormInput
                                key={lang?.code || index}
                                control={form.control}
                                name={`name[${lang?.code}]`}
                                label={`${t("name")} (${t(lang?.name)})*`}
                            />
                        ))}
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default BookCategoryForm;
