import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    bookClassificationAPI,
    bookSubClassficationAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, strStartCase } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";

const BookSubClassificationForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: bookSubClassification, axiosQuery: getBookSubClassfication } =
        useAxios({
            api: bookSubClassficationAPI,
            locale,
            onError: props.close,
        });

    const {
        data: bookClassificationList,
        axiosQuery: getBookClassificationList,
    } = useAxios({
        api: bookClassificationAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getBookClassificationList({ params: GET_ALL_PARAMS });

        if (!props.isCreate && props.id) {
            getBookSubClassfication({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || bookSubClassification) ? (
        <FormWrap
            bookClassificationList={bookClassificationList}
            bookSubClassification={bookSubClassification}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    bookClassificationList: any;
    bookSubClassification: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    bookClassificationList,
    bookSubClassification,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            book_classification_id:
                bookSubClassification?.book_classification?.id ?? null,
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            bookSubClassification?.translations?.name?.[key] ??
                            "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createBookSubClassification, error: postError } =
        useAxios({
            api: bookSubClassficationAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateBookSubClassification, error: putError } = useAxios(
        {
            api: bookSubClassficationAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        }
    );

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createBookSubClassification(data);
            } else {
                updateBookSubClassification({
                    id: bookSubClassification.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {strStartCase(t("book subclassification"))}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name={"book_classification_id"}
                        label={t("book classification") + "*"}
                        options={bookClassificationList?.map((option) => ({
                            id: option?.id,
                            name: `${option?.code} ${option?.name}`,
                        }))}
                    />

                    {activeLanguages
                        .filter((lang) => lang?.is_active)
                        .map((lang, index) => (
                            <FormInput
                                key={lang?.code || index}
                                control={form.control}
                                name={`name[${lang?.code}]`}
                                label={`${t("name")} (${t(lang?.name)})*`}
                            />
                        ))}
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default BookSubClassificationForm;
