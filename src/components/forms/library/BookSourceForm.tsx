import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, bookSourceAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError, strStartCase } from "@/lib/utils";

const BookSourceForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: bookSource, axiosQuery: getBookSource } = useAxios({
        api: bookSourceAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getBookSource({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || bookSource) ? (
        <FormWrap
            bookSource={bookSource}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    bookSource: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    bookSource,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: bookSource?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createBookSource, error: postError } = useAxios({
        api: bookSourceAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateBookSource, error: putError } = useAxios({
        api: bookSourceAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createBookSource(data);
            } else {
                updateBookSource({ id: bookSource.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {t(isCreate ? "Create " : "Update ")}
                {strStartCase(t("book source"))}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages
                        .filter((lang) => lang?.is_active)
                        .map((lang, index) => (
                            <FormInput
                                key={lang?.code || index}
                                control={form.control}
                                name={`name[${lang?.code}]`}
                                label={`${t("name")} (${t(lang?.name)})*`}
                            />
                        ))}
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default BookSourceForm;
