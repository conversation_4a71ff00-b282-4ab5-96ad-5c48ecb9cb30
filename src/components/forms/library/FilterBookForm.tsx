import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import { axiosInstance } from "@/lib/api";
import {
    authorAPI,
    bookSubClassficationAPI,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    libraryBooksAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";

const FilterBookForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const [authorOptions, setAuthorOptions] = useState<any[]>([]);
    const [asyncValue, setAsyncValue] = useState<Record<string, any> | null>(
        null
    );

    const { loadAsyncOptions } = useAsyncSelect({
        api: libraryBooksAPI,
        useTitle: true,
        optionFormatter: (option) => ({
            value: option?.title,
            label: option?.title,
        }),
    });

    const form = useForm({
        defaultValues: {
            title: filter?.title ?? "",
            isbn: filter?.isbn ?? "",
            book_no: filter?.book_no ?? "",
            call_no: filter?.call_no ?? "",
            author_ids: filter?.author_ids ?? [],
            book_sub_classification_id:
                filter?.book_sub_classification_id ?? "",
        },
    });

    const {
        data: bookSubclassifications,
        axiosQuery: getBookSubclassification,
    } = useAxios({
        api: bookSubClassficationAPI,
        locale,
    });

    const fetchAuthorsByIds = async (ids) => {
        if (ids.length > 0) {
            try {
                const fetchedAuthors = await Promise.all(
                    ids.map(async (id) => {
                        const response = await axiosInstance.get(
                            `${authorAPI}/${id}`
                        );
                        return {
                            label: response.data.data.name,
                            value: response.data.data.id,
                        };
                    })
                );
                setAuthorOptions(fetchedAuthors);
            } catch (error) {
                handleError(error);
            }
        }
    };

    const handleAuthorChange = async (selectedAuthors) => {
        if (selectedAuthors && selectedAuthors.length > 0) {
            fetchAuthorsByIds(selectedAuthors);
        } else {
            setAuthorOptions([]);
            form.setValue("author_ids", []);
        }
    };

    const { handleError } = useAxios({ api: authorAPI });

    const loadAuthorOptions = (inputValue, callback) => {
        if (inputValue?.trim()?.length > 0) {
            axiosInstance
                .get(authorAPI, {
                    params: {
                        name: inputValue,
                        per_page: 50,
                    },
                    headers: { "Accept-Language": locale },
                })
                .then((res) => {
                    const options = res.data.data.map((item) => ({
                        label: item.name,
                        value: item.id,
                    }));
                    callback(options);
                })
                .catch((error) => {
                    handleError(error);
                });
        } else {
            callback([]);
        }
    };

    useEffect(() => {
        const authorIds = filter?.author_ids ?? [];
        if (authorIds.length > 0) {
            fetchAuthorsByIds(authorIds);
        }
    }, [filter?.author_ids]);

    useEffect(() => {
        form.setValue("title", filter?.title ?? "");
    }, [filter?.title]);

    useEffect(() => {
        getBookSubclassification({
            params: { ...GET_ALL_PARAMS },
        });
    }, [locale]);

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset({ title: "" });
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
        setAsyncValue(null);
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <div className="lg:col-span-2">
                    <FreeSelectAsync
                        control={form.control}
                        name="title"
                        label="quick_search"
                        placeholder={t("Type to search book title")}
                        loadOptions={loadAsyncOptions}
                        value={asyncValue}
                        onChange={(option) => {
                            form.setValue("title", option?.value ?? "");
                            setAsyncValue(option);
                            setFilter({
                                ...filter,
                                page: 1,
                                title: option?.value ?? undefined,
                            });
                        }}
                    />
                </div>
                <FormInput
                    control={form.control}
                    name={"isbn"}
                    label={"ISBN"}
                />

                <FormInput
                    control={form.control}
                    name={"book_no"}
                    label="Barcode Number (条码)"
                />

                <FormInput
                    control={form.control}
                    name={"call_no"}
                    label="Book Number (书号)"
                />

                <FormSelectAsync
                    control={form.control}
                    name="author_ids"
                    label={t("authors")}
                    isMulti={true}
                    loadOptions={loadAuthorOptions}
                    value={authorOptions}
                    onChange={handleAuthorChange}
                />

                <FormSelect
                    control={form.control}
                    name={"book_sub_classification_id"}
                    label={t("book subclassification")}
                    options={bookSubclassifications}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterBookForm;
