import { useLocale, useTranslations } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FormSelectAsyncCreatableMulti from "@/components/ui/FormSelectAsyncCreatableMulti";
import FormTextarea from "@/components/ui/FormTextarea";
import { axiosInstance } from "@/lib/api";
import {
    CommonFormProps,
    EMPLOYEE,
    LIBRARIAN,
    LoanSettingType,
    OTHERS,
    STUDENT,
    authorAPI,
    bookCategoryAPI,
    bookClassificationAPI,
    bookLanguagesAPI,
    bookSourceAPI,
    bookSubClassficationAPI,
    configAPI,
    isbnAPI,
    libraryBooksAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { configForGetAll, showBackendFormError, toYMD } from "@/lib/utils";
import Tabs from "../../ui/Tabs";

const IconBarcodeScanner = "/icons/icon-barcode-scanner.svg";

const LibraryBookForm = (props: CommonFormProps) => {
    const [book, setBook] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.bookCategories = res[0].data.data;
            _options.bookSources = res[1].data.data;
            _options.bookClassifications = res[2].data.data;
            _options.bookSubClassifications = res[3].data.data;
            _options.bookLanguages = res[4].data.data;

            const configs = res[5].data.data;
            const loanPeriodDaysData = {};
            configs.forEach((config) => {
                if (config.key.startsWith("LOAN_PERIOD_DAY_")) {
                    const role = config.key.replace("LOAN_PERIOD_DAY_", "");
                    if (config.value !== undefined) {
                        loanPeriodDaysData[role] = config.value;
                    }
                }
            });
            _options.loanPeriodDays = loanPeriodDaysData;

            setOptions(_options);
            if (res[6]) {
                setBook(res[6].data.data);
            }
        },
        onError: () => props.close(),
    });

    const locale = useLocale();

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(bookCategoryAPI, _config),
            axiosInstance.get(bookSourceAPI, _config),
            axiosInstance.get(bookClassificationAPI, _config),
            axiosInstance.get(bookSubClassficationAPI, _config),
            axiosInstance.get(bookLanguagesAPI, _config),
            axiosInstance.get(configAPI, _config),
            props.id
                ? axiosInstance.get(`${libraryBooksAPI}/${props.id}`, {
                      headers: { "Accept-Language": locale },
                  })
                : null,
        ]);
    }, []);

    return options && (props.isCreate || book) ? (
        <FormWrap book={book} options={options} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    book: any;
    options: Record<string, any>;
};

const FormWrap = ({
    isCreate = false,
    book,
    options,
    refresh,
    close,
}: FormWrapProps) => {
    const transformLoanSettings = (loanSettingsArray) => {
        return loanSettingsArray.reduce((acc, item) => {
            acc[item.type] = {
                loan_period_day: item.loan_period_day,
                loan_extension_day: item.loan_extension_day, // TO BE REMOVED
                can_borrow: item.can_borrow,
            };
            return acc;
        }, {});
    };

    const getDefaultLoanSettings = (options) => {
        return {
            EMPLOYEE: {
                loan_period_day: options?.loanPeriodDays?.EMPLOYEE ?? 0,
                loan_extension_day: 1, // TO BE REMOVED
                can_borrow: true,
            },
            STUDENT: {
                loan_period_day: options?.loanPeriodDays?.STUDENT ?? 0,
                loan_extension_day: 1, // TO BE REMOVED
                can_borrow: true,
            },
            LIBRARIAN: {
                loan_period_day: options?.loanPeriodDays?.LIBRARIAN ?? 0,
                loan_extension_day: 1, // TO BE REMOVED
                can_borrow: true,
            },
            OTHERS: {
                loan_period_day: options?.loanPeriodDays?.OTHER ?? 0,
                loan_extension_day: 1, // TO BE REMOVED
                can_borrow: true,
            },
        };
    };

    const loanSettings = book?.loan_settings
        ? transformLoanSettings(book.loan_settings)
        : getDefaultLoanSettings(options);

    const form = useForm({
        defaultValues: {
            isbn: book?.isbn ?? "",
            entry_date: isCreate ? new Date() : book?.entry_date ?? null,
            condition: book?.condition ?? "",
            book_language_id: book?.book_language?.id ?? "",
            book_no: book?.book_no ?? "",
            title: book?.title ?? "",
            book_category_id: book?.book_category?.id ?? "",
            book_source_id: book?.book_source?.id ?? "",
            book_classification_id: book?.book_classification?.id ?? "",
            book_sub_classification_id: book?.book_sub_classification?.id ?? "",
            series: book?.series ?? "",
            edition: book?.edition ?? "",
            topic: book?.topic ?? "",
            call_no: book?.call_no ?? "",
            location_1: book?.location_1 ?? "",
            location_2: book?.location_2 ?? "",
            publisher: book?.publisher ?? "",
            publisher_place: book?.publisher_place ?? "",
            published_date: book?.published_date ?? "",
            binding: book?.binding ?? "HARD_COVER",
            book_size: book?.book_size ?? "",
            book_page: book?.book_page ?? "",
            words: book?.words ?? "",
            purchase_value: book?.purchase_value ?? "",
            lost_penalty_value: book?.lost_penalty_value ?? "",
            cdrom: book?.cdrom ?? 0,
            authors: book?.authors ?? [],
            remark: book?.remark ?? "",
            loan_settings: loanSettings,
        },
    });

    const locale = useLocale();

    const { axiosQuery: getBookInfo } = useAxios({
        api: isbnAPI,
        locale,
        onSuccess: (result) => {
            const info = result.data;
            form.setValue("title", info?.title ?? "");
            form.setValue("publisher", info?.publisher ?? "");
            form.setValue("published_date", info?.published_date ?? "");
            form.setValue("book_size", info?.book_size ?? "");
            form.setValue("book_page", info?.book_page ?? "");
            form.setValue("edition", info?.edition ?? "");
            form.setValue(
                "authors",
                info?.authors.map((author, index) => ({
                    id: index,
                    name: author,
                })) ?? []
            );
        },
    });

    const { axiosPost: createBook, error: postError } = useAxios({
        api: libraryBooksAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateBook, error: putError } = useAxios({
        api: libraryBooksAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function retrieveBookInfo() {
        getBookInfo({ id: form.getValues("isbn") });
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (data.published_date) {
                    data.published_date = toYMD(data.published_date);
                }

                if (data.authors) {
                    data.authors = data.authors
                        .filter(
                            (author) =>
                                author?.name && author.name.trim() !== ""
                        )
                        .map((author) => author.name);
                }

                data.cdrom = data.cdrom ? 1 : 0;

                if (data.loan_settings) {
                    Object.keys(data.loan_settings).forEach((key) => {
                        data.loan_settings[key].can_borrow = data.loan_settings[
                            key
                        ].can_borrow
                            ? 1
                            : 0;
                    });
                }

                console.log("data", data);

                if (isCreate) {
                    createBook(data);
                } else {
                    updateBook({ id: book.id, data });
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    const [loanSettingsType, setLoanSettingsType] =
        useState<LoanSettingType>(STUDENT);

    const t = useTranslations("common");

    return (
        <>
            <h2 className="mb-2">
                {t(isCreate ? "Create " : "Update ")}
                {t("book")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormInput
                        control={form.control}
                        name="isbn"
                        label="ISBN"
                        suffixIcon={IconBarcodeScanner}
                    />
                    <Button
                        type="button"
                        variant={"outline"}
                        onClick={retrieveBookInfo}
                        className="max-h-0 self-end"
                        disabled={form.getValues("isbn") === ""}
                    >
                        {t("Retrieve Information")}
                    </Button>
                    <FormDivider />

                    <FormInput
                        control={form.control}
                        name="book_no"
                        label="Barcode Number (条码)*"
                    />

                    <FormInput
                        control={form.control}
                        name="call_no"
                        label="Book Number (书号)*"
                    />

                    <FormInput
                        control={form.control}
                        name="title"
                        label={t("title") + "*"}
                    />

                    <FormSelect
                        control={form.control}
                        name="book_category_id"
                        label={t("book category") + "*"}
                        options={options.bookCategories}
                    />

                    <FormSelect
                        control={form.control}
                        name="book_source_id"
                        label={t("book source") + "*"}
                        options={options.bookSources}
                    />

                    <FormSelect
                        control={form.control}
                        name="book_classification_id"
                        label={t("book classification") + "*"}
                        options={options.bookClassifications}
                    />

                    <FormSelect
                        control={form.control}
                        name="book_sub_classification_id"
                        label={t("book subclassification") + "*"}
                        options={options.bookSubClassifications.map((item) => ({
                            id: item?.id,
                            name: `${item?.code ?? ""} ${item?.name}`,
                        }))}
                    />

                    <FormSelect
                        control={form.control}
                        name="book_language_id"
                        label={t("language")}
                        options={options.bookLanguages}
                    />

                    <FormDivider />
                    {/* 
                    <FormInput control={form.control} name="series" />
                    <FormInput control={form.control} name="edition" />
                    <FormInput control={form.control} name="topic" /> */}

                    <FormInput
                        control={form.control}
                        name="location_1"
                        label={t("location") + " 1"}
                    />
                    <FormInput
                        control={form.control}
                        name="location_2"
                        label={t("location") + " 2"}
                    />

                    <FormDivider />

                    <div className="max-w-xl lg:col-span-2">
                        <FormSelectAsyncCreatableMulti
                            control={form.control}
                            name="authors"
                            api={authorAPI}
                            idField="id"
                            labelField="name"
                            label={t("authors") + "*"}
                        />
                    </div>

                    <FormDivider />

                    <FormInput control={form.control} name="publisher" />
                    <FormInput control={form.control} name="publisher_place" />

                    <DatePicker
                        control={form.control}
                        name={"published_date"}
                    />

                    <FormSelect
                        control={form.control}
                        name="condition"
                        isStringOptions={true}
                        options={["GOOD", "DAMAGE", "MISSING", "WRITE_OFF"]}
                    />

                    <DatePicker control={form.control} name="entry_date" />

                    <FormDivider />

                    <FormSelect
                        control={form.control}
                        name="binding"
                        isStringOptions={true}
                        options={["HARD_COVER", "SOFT_COVER"]}
                    />

                    <FormInput control={form.control} name="book_size" />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="book_page"
                    />
                    <FormInput
                        control={form.control}
                        type="number"
                        name="words"
                    />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="purchase_value"
                        label={t("purchase value") + "*"}
                        onChange={(value) => {
                            form.clearErrors("lost_penalty_value");
                            form.setValue("lost_penalty_value", value * 2);
                        }}
                    />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="lost_penalty_value"
                        label={t("lost penalty value") + "*"}
                    />

                    <FormTextarea control={form.control} name="remark" />

                    <FormCheckbox
                        control={form.control}
                        name="cdrom"
                        label={t("CD Rom")}
                        styleClass="mt-3 ml-1"
                        textStyleClass="text-gray-500 font-medium"
                    />

                    <FormDivider />

                    <div className="lg:col-span-2">
                        <h3 className="mb-5 ml-0.5 text-themeGreenDark">
                            {t("Loan Settings")}
                        </h3>

                        <Tabs
                            list={[STUDENT, LIBRARIAN, EMPLOYEE, OTHERS]}
                            selected={loanSettingsType}
                            setSelected={setLoanSettingsType}
                        />
                        <div className="grid gap-x-3 gap-y-3.5 lg:grid-cols-2">
                            {[STUDENT, LIBRARIAN, EMPLOYEE, OTHERS].map(
                                (type) => {
                                    const targetType = type;
                                    return loanSettingsType === type ? (
                                        <Fragment key={type}>
                                            <FormInput
                                                control={form.control}
                                                type="number"
                                                name={`loan_settings.${targetType}.loan_period_day`}
                                                label={
                                                    t("loan period days") + "*"
                                                }
                                            />

                                            <FormCheckbox
                                                control={form.control}
                                                name={`loan_settings.${targetType}.can_borrow`}
                                                label={t("can borrow")}
                                                styleClass="mt-3 ml-1"
                                                textStyleClass="text-gray-500 font-medium"
                                            />
                                        </Fragment>
                                    ) : (
                                        <Fragment key={type}></Fragment>
                                    );
                                }
                            )}
                        </div>
                    </div>

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default LibraryBookForm;
