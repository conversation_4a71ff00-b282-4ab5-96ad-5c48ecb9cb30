import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { GET_ALL_PARAMS, semesterYearsAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import FormDivider from "../ui/FormDivider";
import FormInputInterger from "../ui/FormInputInterger";

const SemesterYearForm = ({ close }: { close: () => void }) => {
    const t = useTranslations("common");
    const locale = useLocale();

    const { data: semesterYears, axiosQuery: getSemesterYears } = useAxios({
        api: semesterYearsAPI,
        locale,
    });

    useEffect(() => {
        getSemesterYears({ params: GET_ALL_PARAMS });
    }, []);

    const form = useForm({
        defaultValues: {
            year: "",
        },
    });

    const { axiosPost: createSemesterYear, error: postError } = useAxios({
        api: semesterYearsAPI,
        onSuccess: () => {
            form.setValue("year", "");
            getSemesterYears();
            close();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            createSemesterYear(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-1">{t("Add Semester Year")}</h2>
            {semesterYears?.length > 0 && (
                <div>
                    <div className="c-text-size mb-2.5 mt-3 font-semibold text-gray-500">
                        {t("Semester Years List")}
                    </div>
                    <div className="grid max-w-[140px] grid-cols-2 gap-1 px-1 pb-2">
                        {semesterYears
                            .sort((a, b) => a.year - b.year)
                            .map((i, index) => (
                                <div key={index} className="c-text-size">
                                    {i?.year}
                                </div>
                            ))}
                    </div>
                    <FormDivider hasColSpan={false} />
                </div>
            )}

            <Form {...form}>
                <form onSubmit={onSubmit} className="mt-4 grid gap-y-5">
                    <FormInputInterger
                        control={form.control}
                        name={"year"}
                        label={t("semester year") + "*"}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SemesterYearForm;
