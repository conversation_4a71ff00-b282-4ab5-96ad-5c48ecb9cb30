import React, { useEffect, useState } from "react";
import { isArray, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import FreeInput from "@/components/ui/FreeInput";
import {
    DATE_FORMAT,
    HOME,
    hostelArrivalDepartureAPI,
    OUTING,
    TableColumnType,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    combinedNamesCell,
    isObjectType,
    toUTC,
} from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { Textarea } from "../../base-ui/textarea";
import DataTable from "../../ui/DataTable";
import FreeDateTimePicker from "../../ui/FreeDateTimePicker";
import FreeSelect from "../../ui/FreeSelect";
import FreeTextArea from "../../ui/FreeTextArea";
import { useTranslations } from "next-intl";

const StudentBulkMarkDepartureForm = ({
    students,
    close,
    refresh,
}: {
    students: any[];
    close: () => void;
    refresh: () => void;
}) => {
    const t = useTranslations("common");

    const form = useForm<any>({
        defaultValues: {
            students: students.map((student) => ({
                id: student?.id,
                hostel_room_bed_id:
                    student?.active_hostel_bed_assignment?.bed?.id,
                guardian_id: "",
                type: "",
                reason: "",
                check_out_datetime: new Date(),
            })),
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "student_number",
        },
        {
            key: "student_name",
            modify: (value) => combinedNamesCell(value),
        },
        {
            key: "type",
            displayAs: `${t("type")}*`,
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="h-full min-w-[150px]">
                        <FreeSelect
                            hasLabel={false}
                            control={form.control}
                            name={`students[${index}].type`}
                            isStringOptions={true}
                            options={[HOME, OUTING]}
                            error={
                                form.formState.errors?.students?.[index]?.type
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "guardian_id",
            displayAs: `${t("signing guardian")}`,
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="h-full min-w-[130px]">
                        <FreeSelect
                            hasLabel={false}
                            control={form.control}
                            name={`students[${index}].guardian_id`}
                            options={
                                students?.[index]?.guardians?.map(
                                    (guardian) => ({
                                        ...guardian,
                                        name: combinedNames(
                                            guardian?.translations?.name
                                        ),
                                    })
                                ) ?? []
                            }
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.guardian_id
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "card_no",
            displayAs: `${t("Card number")}`,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="flex h-full items-center">
                        {form.watch(`students[${index}].type`) == OUTING ? (
                            <FreeInput
                                hasLabel={false}
                                control={form.control}
                                name={`students[${index}].card_no`}
                                error={
                                    form.formState.errors?.students?.[index]
                                        ?.card_no
                                }
                            />
                        ) : (
                            <span>-</span>
                        )}
                    </div>
                );
            },
        },
        {
            key: "check_out_datetime",
            displayAs: `${t("check out date time")}*`,
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="h-full">
                        <FreeDateTimePicker
                            control={form.control}
                            name={`students[${index}].check_out_datetime`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.check_out_datetime
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "remark",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="h-full">
                        <FreeTextArea
                            control={form.control}
                            name={`students[${index}].reason`}
                            error={
                                form.formState.errors?.students?.[index]?.reason
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "_",
            hasSort: false,
            modify: (_, cell) => {
                const id = cell.row.original.id;
                return (
                    <div className="h-full">
                        <div
                            className="ml-2 mt-5 w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                            onClick={() => remove(id)}
                        >
                            <X size={14} className="text-themeGreenDark" />
                        </div>
                    </div>
                );
            },
        },
    ];

    const [data, setData] = useState(definedData());
    const [reasonForAll, setReasonForAll] = useState("");

    function definedData() {
        return isArray(students) ? getDefinedData(students) : [];
    }

    function getDefinedData(students) {
        return students.map((student) => ({
            id: student?.id,
            student_number: student?.student_number,
            student_name: Object.values(student?.translations?.name ?? {}),
        }));
    }

    function remove(id) {
        const currentValues = form.getValues();
        const newStudentsValues = currentValues.students.filter(
            (student) => student.id.toString() !== id.toString()
        );
        form.setValue("students", newStudentsValues);

        const newStudentsIds = newStudentsValues.map((student) =>
            student.id.toString()
        );
        const newStudents = students.filter((student) =>
            newStudentsIds.includes(student.id.toString())
        );

        setData(getDefinedData(newStudents));
    }

    function applyAll() {
        data.forEach((_, index) => {
            form.setValue(`students[${index}].reason`, reasonForAll);
        });
    }

    const { axiosPost: bulkMark, error: postError } = useAxios({
        api: hostelArrivalDepartureAPI,
        toastMsg: "Departure marked successfully",
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit() {
        form.clearErrors();
        form.handleSubmit((data) => {
            const newStudentsData = data.students.map((student) => {
                if (student.check_out_datetime) {
                    const newDatetime = toUTC(
                        student.check_out_datetime,
                        DATE_FORMAT.YMD_HMS
                    );
                    student.check_out_datetime = newDatetime;
                }
                return student;
            });
            const newStudentsDataIds = newStudentsData.map(
                (student) => student.id
            );
            const studentsWithoutBed = students.filter(
                (student) =>
                    newStudentsDataIds.includes(student.id) &&
                    student.active_hostel_bed_assignment == null
            );
            if (studentsWithoutBed.length > 0) {
                const studentNames = studentsWithoutBed
                    .map((student) => student?.name)
                    .join(", ");
                const errorMsg =
                    studentNames +
                    ` do${studentsWithoutBed.length == 1 ? "es" : ""} not have bed assigned`;
                toast(errorMsg);
                return;
            }

            bulkMark({ students: newStudentsData });
        })();
    }

    useEffect(() => {
        const error: any = postError;
        if (error && error?.response?.status === 422) {
            const errorData = error?.response?.data?.error;
            if (isObjectType(errorData)) {
                Object.entries(errorData).forEach(
                    ([key, list]: [string, Array<any>]) => {
                        if (key.includes(".id") && key.includes("students")) {
                            const index = key.split(".")?.[1];
                            const studentId = data[index]?.id;
                            const studentName = students.find(
                                (student) => student.id == studentId
                            )?.name;
                            toast(
                                `${studentName} has already checked out from the hostel.`
                            );
                        }
                        form?.setError(
                            key,
                            {
                                type: "custom",
                                message: list?.join(" "),
                            },
                            { shouldFocus: true }
                        );
                    }
                );
            }
        }
    }, [postError]);

    return (
        <div className="mt-2 flex flex-col gap-y-4 pb-64">
            <h2 className="">
                {t("Bulk Mark ")}
                {t("Departure")}
            </h2>

            <div className="flex items-start gap-x-4 pb-1">
                <Textarea
                    placeholder={t("Reason")}
                    value={reasonForAll}
                    onChange={(e) => setReasonForAll(e.target.value)}
                />
                <Button variant={"outline"} onClick={applyAll}>
                    {t("Apply All")}
                </Button>
            </div>

            <div className="grid min-w-[700px] gap-y-5 lg:min-w-[1100px]">
                <DataTable
                    columns={columns}
                    data={data}
                    styleClass="lg:overflow-visible"
                />
                <div className="mt-5 flex justify-end gap-x-3">
                    <Button onClick={onSubmit} disabled={isEmpty(data)}>
                        {t("Submit")}
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default StudentBulkMarkDepartureForm;
