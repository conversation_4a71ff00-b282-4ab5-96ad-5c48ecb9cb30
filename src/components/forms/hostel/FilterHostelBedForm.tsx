import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFilterProps,
    GET_ALL_PARAMS,
    hostelBlocksAPI,
    hostelRoomsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import FormSelect from "../../ui/FormSelect";

const FilterHostelBedForm = ({
    filter,
    setFilter,
    close,
    type,
}: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            hostel_block_id: filter?.hostel_block_id ?? null,
            hostel_room_id: filter?.hostel_room_id ?? null,
            name: filter?.name ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            per_page: filter?.per_page,
            page: 1,
        });
        close();
    }

    const { data: hostelBlocks, axiosQuery: getHostelBlocks } = useAxios({
        api: hostelBlocksAPI,
        locale,
    });

    const { data: hostelRoomOptions, axiosQuery: getHostelRoomOptions } =
        useAxios({
            api: hostelRoomsAPI,
            locale,
        });

    useEffect(() => {
        if (form.watch("hostel_block_id")) {
            getHostelRoomOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    hostel_block_id: form.watch("hostel_block_id"),
                    hostel_block_type: type,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [form.watch("hostel_block_id")]);

    useEffect(() => {
        getHostelBlocks({
            params: {
                ...GET_ALL_PARAMS,
                type,
            },
        });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name={"hostel_block_id"}
                    label="block"
                    options={hostelBlocks}
                    onChange={() => {
                        form.setValue("hostel_room_id", "");
                    }}
                />

                <FormSelect
                    control={form.control}
                    name={"hostel_room_id"}
                    label="room"
                    isDisabled={!form.watch("hostel_block_id")}
                    options={hostelRoomOptions}
                />

                <FormInput
                    control={form.control}
                    name={"name"}
                    label={`${t("bed ")}${t("name")}`}
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    isSortByName={false}
                    options={[
                        {
                            name: t("Active"),
                            id: "1",
                        },
                        {
                            name: t("Inactive"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelBedForm;
