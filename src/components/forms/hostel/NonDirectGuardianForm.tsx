import {
    combinedNames,
    configForGetAll,
    isValueTrue,
    replaceAll,
    showBackendFormError,
} from "@/lib/utils";
import GuardiansForm from "../GuardiansForm";
import { useForm } from "react-hook-form";
import {
    countryAPI,
    raceAPI,
    gradeAPI,
    religionAPI,
    educationAPI,
    GUARDIAN,
    schoolAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    healthConcernAPI,
    studentAPI,
} from "@/lib/constant";
import { capitalize, isEmpty, orderBy } from "lodash";
import { Fragment, useEffect, useState } from "react";
import { useAxios, useSubmit } from "@/lib/hook";
import { axiosInstance } from "@/lib/api";
import { useLocale, useTranslations } from "next-intl";
import { Form } from "@/components/base-ui/form";
import { Button } from "@/components/base-ui/button";
import { ChevronDownCircle } from "lucide-react";
import clsx from "clsx";
import FormDivider from "@/components/ui/FormDivider";

const NonDirectGuardianForm = ({ id, close }) => {
    const locale = useLocale();

    const [student, setStudent] = useState(null);
    const [options, setOptions] = useState<Record<string, any> | null>(null);

    const { axiosMultipleQueries } = useAxios({
        onSuccess: (res) => {
            const _options: Record<string, any> = {};
            _options.countries = res[0].data.data;
            _options.races = res[1].data.data;
            _options.religions = res[2].data.data;
            _options.grades = res[3].data.data.sort(
                (a, b) => b.sequence - a.sequence
            );
            _options.educations = res[4].data.data;
            _options.primarySchools = res[5].data.data;
            _options.healthConcerns = res[6].data.data.sort(
                (a, b) => b.sequence - a.sequence
            );
            setOptions(_options);
            if (res[7]) {
                setStudent(res[7].data.data);
            }
        },
        onError: () => close(),
    });

    useEffect(() => {
        const _config = configForGetAll(locale);
        axiosMultipleQueries([
            axiosInstance.get(countryAPI, _config),
            axiosInstance.get(raceAPI, _config),
            axiosInstance.get(religionAPI, _config),
            axiosInstance.get(gradeAPI, _config),
            axiosInstance.get(educationAPI, _config),
            axiosInstance.get(schoolAPI, {
                params: { level: PRIMARY, ...GET_ALL_PARAMS },
                headers: { "Accept-Language": locale },
            }),
            axiosInstance.get(healthConcernAPI, _config),
            axiosInstance.get(`${studentAPI}/${id}`, {
                headers: { "Accept-Language": locale },
                params: {
                    response: "FULL",
                    includes: [
                        "admissionGrade",
                        "nationality",
                        "race",
                        "religion",
                        "state",
                        "country",
                        "healthConcern",
                        "primarySchool",
                        "guardians",
                        "guardians.country",
                        "guardians.race",
                        "guardians.religion",
                        "guardians.education",
                    ],
                },
            }),
        ]);
    }, []);

    return options ? (
        <FormWrap options={options} student={student} close={close} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = {
    student: any;
    options: Record<string, any>;
    close: () => void;
};

const FormWrap = ({ options, student, close }: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm<any>({
        defaultValues: {
            guardians: orderBy(
                student?.guardians
                    .filter((guardian) => !guardian.is_direct_dependant)
                    ?.map((guardian) => ({
                        id: guardian?.id ?? undefined,
                        type: guardian?.type ?? "",
                        name: guardian?.translations?.name ?? "",
                        nric: guardian?.nric ?? "",
                        passport_number: guardian?.passport_number ?? "",
                        email: guardian?.email ?? "",
                        live_status: guardian?.live_status ?? "",
                        phone_number: guardian?.phone_number ?? "",
                        married_status: guardian?.married_status ?? "",
                        occupation: guardian?.occupation ?? "",
                        occupation_description:
                            guardian?.occupation_description ?? "",

                        nationality_id: guardian?.nationality?.id ?? "",
                        race_id: guardian?.race?.id ?? "",
                        religion_id: guardian?.religion?.id ?? "",
                        education_id: guardian?.education?.id ?? "",
                        is_primary: isValueTrue(guardian?.is_primary),
                        with_user_account: isValueTrue(
                            guardian?.has_user_account
                        ),
                        _with_user_account: isValueTrue(
                            guardian?.has_user_account
                        ),
                        is_direct_dependant: isValueTrue(
                            guardian?.is_direct_dependant
                        ),
                    })) ?? [],
                [(guardian) => (guardian.type === GUARDIAN ? 1 : 0), "type"],
                ["asc", "asc"]
            ),
        },
    });

    const { axiosPut: updateGuardian, error: putError } = useAxios({
        api: `/admin/hostels/students`,
        onSuccess: () => {
            close();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data) => {
                const filteredGuardians: any[] = [];

                [...data.guardians].forEach((guardian) => {
                    if (guardian.is_direct_dependant) return;

                    if (guardian._isNew) {
                        delete guardian.id;
                        delete guardian._isNew;
                    }
                    const filledNames = Object.values(guardian.name).filter(
                        (name: any) => name
                    );
                    if (guardian.name && filledNames.length === 0) {
                        delete guardian.name;
                    }
                    guardian.with_user_account = guardian.with_user_account
                        ? 1
                        : 0;

                    delete guardian._with_user_account;

                    guardian.is_direct_dependant = guardian.is_direct_dependant
                        ? 1
                        : 0;

                    filteredGuardians.push({
                        ...guardian,
                        is_primary: guardian.is_primary ? 1 : 0,
                    });
                });

                data.guardians = filteredGuardians;

                updateGuardian({ id: student.id, data });
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const [isOpenList, setIsOpenList] = useState<number[]>([]);

    const locale = useLocale();

    function formatGuardianData(guardian) {
        return {
            nric: guardian?.nric,
            passport_number: guardian?.passport_number,
            email: guardian?.email,
            phone_number: guardian?.phone_number,
            marital_status: capitalize(guardian?.married_status),
            occupation: guardian?.occupation,
            occupation_description: guardian?.occupation_description,
            nationality: guardian?.nationality?.translations?.name?.[locale],
            race: guardian?.race?.translations?.name?.[locale],
            religion: guardian?.religion?.translations?.name?.[locale],
            education: guardian?.education?.translations?.name?.[locale],
            is_primary: isValueTrue(guardian?.is_primary) ? "Yes" : "No",
            has_user_account: isValueTrue(guardian?.has_user_account)
                ? "Yes"
                : "No",
            is_direct_dependant: isValueTrue(guardian?.is_direct_dependant)
                ? "Yes"
                : "No",
            live_status: guardian?.live_status ?? "-",
        };
    }

    return options ? (
        <Form {...form}>
            <form
                onSubmit={onSubmit}
                className="min-h-[70vh] pb-3 lg:min-w-[800px]"
            >
                <h2 className="mb-4">{`${t("Edit ")}${t("Guardian")}`}</h2>
                <GuardiansForm form={form} options={options} isHostel={true}>
                    <>
                        {student?.guardians
                            ?.filter((guardian) => guardian.is_direct_dependant)
                            .map((person, index) => {
                                const guardianData = formatGuardianData(person);

                                function onClickChevron() {
                                    if (isOpenList.includes(index)) {
                                        setIsOpenList(
                                            isOpenList.filter(
                                                (i) => i !== index
                                            )
                                        );
                                    } else {
                                        setIsOpenList([...isOpenList, index]);
                                    }
                                }

                                return (
                                    <div
                                        key={index}
                                        className="mb-3 rounded-md border px-4 py-3"
                                    >
                                        <div className="col-span-2 flex items-center justify-between">
                                            <div className="c-text-size flex w-[calc(100%-44px)] flex-wrap items-center gap-x-1.5 text-gray-500">
                                                <span className="font-medium capitalize">
                                                    {t("type")}:{" "}
                                                    {t(person?.type)}
                                                </span>
                                                <span className="h-[3px] w-[3px] rounded-full bg-gray-400"></span>
                                                <span>
                                                    {combinedNames(
                                                        person?.translations
                                                            ?.name
                                                    )}
                                                </span>
                                                {person?.is_primary && (
                                                    <span className="ml-1 rounded-sm border border-dotted border-themeLabel p-1 text-[10px] font-medium uppercase leading-none">
                                                        {t("Primary")}
                                                    </span>
                                                )}
                                            </div>
                                            <ChevronDownCircle
                                                className={clsx(
                                                    "text-themeGreen transition",
                                                    isOpenList.includes(
                                                        index
                                                    ) && "rotate-180"
                                                )}
                                                onClick={onClickChevron}
                                            />
                                        </div>

                                        <div
                                            className={clsx(
                                                "grid w-full gap-3",
                                                !isOpenList.includes(index) &&
                                                    "h-0 overflow-hidden"
                                            )}
                                        >
                                            <FormDivider />

                                            {Object.entries(guardianData).map(
                                                ([key, value], index) => (
                                                    <Fragment key={key}>
                                                        <div className="flex flex-col lg:grid-cols-2">
                                                            <p className="text-[14px] font-medium capitalize text-themeLabel">
                                                                {t(
                                                                    replaceAll(
                                                                        key ===
                                                                            "nric"
                                                                            ? "NRIC"
                                                                            : key,
                                                                        "_",
                                                                        " "
                                                                    )
                                                                )}
                                                            </p>
                                                            <p
                                                                className={
                                                                    "text-themeText font-medium"
                                                                }
                                                            >
                                                                {isEmpty(
                                                                    value?.toString()
                                                                )
                                                                    ? "-"
                                                                    : t(value)}
                                                            </p>
                                                        </div>
                                                    </Fragment>
                                                )
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                    </>
                </GuardiansForm>
                <Button type="submit" className="ml-auto mt-8">
                    {t("Submit")}
                </Button>
            </form>
        </Form>
    ) : (
        <div className="h-20"></div>
    );
};

export default NonDirectGuardianForm;
