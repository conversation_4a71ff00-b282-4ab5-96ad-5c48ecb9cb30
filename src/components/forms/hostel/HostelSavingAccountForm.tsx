import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import { DatePicker } from "@/components/ui/DatePicker";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import {
    CommonFormProps,
    DEPOSIT,
    GET_ALL_PARAMS,
    WITHDRAWAL,
    appCurrencySymbol,
    bankAPI,
    hostelSavingAccountDepositAPI,
    hostelSavingAccountWithdawAPI,
    withdrawalReasonsAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { combinedNames, showBackendFormError, toYMD } from "@/lib/utils";

const HostelSavingAccountForm = (
    props: CommonFormProps & {
        type: typeof DEPOSIT | typeof WITHDRAWAL;
        studentData: any;
    }
) => {
    const locale = useLocale();
    const [withdrawalReasonOptions, setWithdrawalReasonOptions] = useState([]);

    const { data: bankOptions, axiosQuery: getBankOptions } = useAxios({
        api: bankAPI,
        onError: close,
    });

    const { data, axiosQuery: getWithdrawalReasonOptions } = useAxios({
        api: withdrawalReasonsAPI,
        locale,
        onError: close,
        onSuccess(res) {
            const sortedData = res.data
                .sort((a, b) => b.sequence - a.sequence)
                .map((item) => ({
                    ...item,
                    name: capitalize(item.name),
                }));
            setWithdrawalReasonOptions(sortedData);
        },
    });

    useEffect(() => {
        getBankOptions({ params: GET_ALL_PARAMS });
        getWithdrawalReasonOptions({ params: GET_ALL_PARAMS });
    }, []);

    return props.studentData && props.type ? (
        <FormWrap
            {...props}
            studentData={props.studentData}
            type={props.type}
            bankOptions={bankOptions}
            withdrawalReasonOptions={withdrawalReasonOptions}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: typeof DEPOSIT | typeof WITHDRAWAL;
    studentData: any;
    bankOptions: any;
    withdrawalReasonOptions: any;
};

const FormWrap = ({
    studentData,
    type,
    bankOptions,
    withdrawalReasonOptions,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const paymentOptions = ["CASH", "BANK"];
    // type === WITHDRAWAL
    //     ? ["CASH", "BANK", "CHEQUE"]
    //     : ["CASH", "BANK", "CHEQUE", "CREDIT_CARD"];

    const form = useForm({
        defaultValues: {
            student_id: studentData.id,
            payment_method_code: "",
            reference_no: "",
            bank_id: "",
            transaction_date: new Date(),
            amount: 0.0,
            withdrawal_reason_id: "",
            remarks: "",
        },
    });

    const { axiosPost: createDepositRecord, error: depositPostError } =
        useAxios({
            api: hostelSavingAccountDepositAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPost: createWithdrawRecord, error: withdrawPostError } =
        useAxios({
            api: hostelSavingAccountWithdawAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                data.student_id = studentData.id;

                if (data.transaction_date) {
                    data.transaction_date = toYMD(data.transaction_date);
                }

                if (data.payment_method_code !== "BANK") {
                    delete data.bank_id;
                }

                if (type == "DEPOSIT") {
                    createDepositRecord(data);
                } else {
                    createWithdrawRecord(data);
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, depositPostError);
    }, [depositPostError]);

    useEffect(() => {
        showBackendFormError(form, withdrawPostError);
    }, [withdrawPostError]);

    return (
        <>
            <h2>{capitalize(type)}</h2>
            <div className="grid gap-3 px-1 lg:grid-cols-2">
                <div>
                    <Label>{t("Document Type")}</Label>
                    <p>{type == DEPOSIT ? "ADVANCED INVOICE" : "INVOICE"}</p>
                </div>
                <div>
                    <Label>{t("Transaction Date")}</Label>
                    <p>{toYMD(new Date())}</p>
                </div>

                <div>
                    <Label>{t("Bill To")}</Label>
                    <p>{combinedNames(studentData?.translations?.name)}</p>
                </div>

                <div>
                    <Label>{t("Bill To Address")}</Label>
                    <p>{studentData?.address}</p>
                </div>
                <FormDivider />
            </div>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormSelect
                        control={form.control}
                        name="payment_method_code"
                        label={`${t("payment method")}*`}
                        isStringOptions={true}
                        options={paymentOptions}
                    />

                    <DatePicker
                        control={form.control}
                        name={"transaction_date"}
                        label={`${t("transaction date")}*`}
                    />

                    <FormInput
                        control={form.control}
                        name="reference_no"
                        label={
                            form.watch("payment_method_code") == "BANK"
                                ? `${t("reference no")}*`
                                : t("reference no")
                        }
                    />

                    {form.watch("payment_method_code") == "BANK" && (
                        <>
                            <FormSelect
                                control={form.control}
                                name="bank_id"
                                label={`${t("bank")}*`}
                                options={bankOptions}
                            />
                        </>
                    )}

                    <FormDivider />

                    <FormInput
                        control={form.control}
                        type="number"
                        name="amount"
                        label={`${t("Amount")} (${appCurrencySymbol})*`}
                    />

                    {type == WITHDRAWAL && (
                        <FormSelect
                            control={form.control}
                            name="withdrawal_reason_id"
                            label={`${t("withdrawal reason")}*`}
                            options={withdrawalReasonOptions}
                            isSortByName={false}
                        />
                    )}

                    <FormTextarea control={form.control} name="remarks" />

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            {t("Submit")}
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default HostelSavingAccountForm;
