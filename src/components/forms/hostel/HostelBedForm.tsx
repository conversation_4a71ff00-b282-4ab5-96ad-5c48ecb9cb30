import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    hostelBedsAPI,
    hostelBlocksAPI,
    hostelRoomsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";
import StatusFormSwitch from "../../ui/StatusFormSwitch";

const HostelBedForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: hostelBed, axiosQuery: getHostelBed } = useAxios({
        api: hostelBedsAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getHostelBed({ id: props.id });
        }
    }, []);

    return props.isCreate || hostelBed ? (
        <FormWrap hostelBed={hostelBed} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    hostelBed: any;
};

const FormWrap = ({
    hostelBed,
    isCreate = false,
    refresh,
    close,
    hostelType,
}: FormWrapProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            hostel_block_id: hostelBed?.hostel_room?.hostel_block?.id ?? "",
            hostel_room_id: hostelBed?.hostel_room?.id ?? null,
            name: hostelBed?.name ?? "",
            is_active: isCreate ? true : hostelBed?.is_active ?? false,
        },
    });

    const { axiosPost: createHostelBed, error: postError } = useAxios({
        api: hostelBedsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateHostelBed, error: putError } = useAxios({
        api: hostelBedsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { data: hostelBlocks, axiosQuery: getHostelBlocks } = useAxios({
        api: hostelBlocksAPI,
        locale,
    });

    const { data: hostelRoomOptions, axiosQuery: getHostelRoomOptions } =
        useAxios({
            api: hostelRoomsAPI,
            locale,
        });

    useEffect(() => {
        if (form.watch("hostel_block_id")) {
            getHostelRoomOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    hostel_block_id: form.watch("hostel_block_id"),
                    hostel_block_type: hostelType,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [form.watch("hostel_block_id")]);

    useEffect(() => {
        getHostelBlocks({
            params: { ...GET_ALL_PARAMS, type: hostelType },
        });
    }, []);

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.type = hostelType;
            if (isCreate) {
                createHostelBed(data);
            } else {
                updateHostelBed({ id: hostelBed.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("bed")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name={"hostel_block_id"}
                        label={t("block") + "*"}
                        options={hostelBlocks}
                        onChange={() => form.setValue("hostel_room_id", null)}
                    />
                    <FormSelect
                        control={form.control}
                        name={"hostel_room_id"}
                        label={t("room") + "*"}
                        isDisabled={!form.watch("hostel_block_id")}
                        options={hostelRoomOptions}
                    />
                    <FormInput
                        control={form.control}
                        name="name"
                        label={`${t("bed ")}${t("name")}*`}
                    />
                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="Status"
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default HostelBedForm;
