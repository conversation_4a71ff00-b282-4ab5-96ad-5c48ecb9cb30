import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import { CommonFormProps, hostelBlocksAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const HostelBlockForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: hostelBlock, axiosQuery: getHostelBlock } = useAxios({
        api: hostelBlocksAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getHostelBlock({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || hostelBlock) ? (
        <FormWrap
            hostelBlock={hostelBlock}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    hostelBlock: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    hostelBlock,
    isCreate = false,
    activeLanguages,
    refresh,
    close,
    hostelType,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm<any>({
        defaultValues: {
            code: isCreate ? "" : hostelBlock?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: hostelBlock?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const { axiosPost: createHostelBlock, error: postError } = useAxios({
        api: hostelBlocksAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateHostelBlock, error: putError } = useAxios({
        api: hostelBlocksAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.type = hostelType;
            if (isCreate) {
                createHostelBlock(data);
            } else {
                updateHostelBlock({ id: hostelBlock.id, data });
            }
        })();
    }
    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("block")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                        disabled={!isCreate}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`${t("name")} (${t(lang?.name)})*`}
                        />
                    ))}

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default HostelBlockForm;
