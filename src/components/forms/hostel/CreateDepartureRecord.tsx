import { useEffect, useState } from "react";
import React from "react";
import { useForm, useWatch } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import FormTextarea from "@/components/ui/FormTextarea";
import FreeDateTimePicker from "@/components/ui/FreeDateTimePicker";
import {
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    HOME,
    hostelArrivalDepartureAPI,
    OUTING,
    studentAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    convertDateTime,
    formatWithBrackets,
    isTypeEmployee,
    optionUserLabel,
    showBackendFormError,
    toUTC,
} from "@/lib/utils";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import InfoCard from "@/components/ui/InfoCard";
import DataTable from "@/components/ui/DataTable";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "@/components/base-ui/dropdown-menu";
import ActionDropdown from "@/components/ui/ActionDropdown";
import { isArray } from "lodash";
import { useLocale, useTranslations } from "next-intl";
import { useUserProfile } from "@/lib/store";
import DeletePrompt from "@/components/ui/DeletePrompt";
import Modal from "@/components/ui/Modal";
import ArrivalDepartureForm from "./ArrivalDepartureForm";

const CreateDepartureRecordForm = () => {
    const locale = useLocale();
    const t = useTranslations("common");
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const { loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
        showGuardians: true,
        params: {
            response: "FULL",
            is_hostel: 1,
            has_active_bed: 1,
            is_checked_out: 0,
            includes: [
                "activeHostelBedAssignments.bed.hostelRoom.hostelBlock",
                "guardians",
            ],
        },
    });

    const [student, setStudent] = useState<any>();
    const [studentPhoto, setStudentPhoto] = useState<any>();
    const [studentGuardians, setStudentGuardians] = useState<any>();
    const [studentHostelInfo, setStudentHostelInfo] = useState<any>();

    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
    });
    const [pagination, setPagination] = useState();
    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const form = useForm<any>({
        defaultValues: {
            students: [
                {
                    id: "",
                    guardian_id: "",
                    hostel_room_bed_id: "",
                    type: HOME,
                    reason: "",
                    check_out_datetime: new Date(),
                    card_no: "",
                },
            ],
        },
    });

    const studentsWatch = useWatch({
        control: form.control,
        name: "students",
    });

    const { axiosPost: createRecord, error: postError } = useAxios({
        api: hostelArrivalDepartureAPI,
        onSuccess: () => {
            form.reset();
            form.setValue("students[0].check_out_datetime", new Date(), {
                shouldDirty: true,
            });
            setStudent(null);
            setStudentPhoto(null);
            setStudentGuardians(null);
            setStudentHostelInfo(null);
            fetchInOutRecords();
        },
    });

    const { data, axiosQuery: getInOutRecords } = useAxios({
        api: hostelArrivalDepartureAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function fetchInOutRecords(isRefresh = true) {
        getInOutRecords({
            params: {
                order_by: { id: "desc" },
                ...filter,
                ...(isRefresh ? { page: 1 } : {}),
                student_id: student?.value,
                includes: [
                    "bed.hostelRoom.hostelBlock",
                    "checkOutBy.userables",
                    "checkInBy.userables",
                ],
            },
        });
    }

    function sortGuardians(guardians) {
        const order = {
            FATHER: 1,
            MOTHER: 2,
            GUARDIAN: 3,
        };
        return guardians?.sort((a, b) => order[a.type] - order[b.type]);
    }

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.students[0].check_out_datetime = toUTC(
                data.students[0].check_out_datetime,
                DATE_FORMAT.YMD_HMS
            );
            createRecord(data);
        })();
    }

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "bed",
                modify: (value) => <div className="min-w-16">{value}</div>,
            },
            {
                key: "student_number",
            },

            {
                key: "check_out_datetime",
                displayAs: "departure date time",
                hasSort: true,
                modify: (value) => {
                    return <span className="text-[14px]">{value ?? "--"}</span>;
                },
            },
            {
                key: "check_out_by",
                displayAs: "checked out by",
                modify: (value) => {
                    return <span className="text-[14px]">{value ?? "-"}</span>;
                },
            },
            {
                key: "check_in_datetime",
                hasSort: true,
                displayAs: "arrival date time",
                modify: (value) => {
                    return <span className="text-[14px]">{value ?? "--"}</span>;
                },
            },
            {
                key: "check_in_by",
                displayAs: "checked in by",
                modify: (value) => {
                    return <span className="text-[14px]">{value ?? "--"}</span>;
                },
            },
            {
                key: "type",
                modify: (value) => t(value),
            },
            {
                key: "card_no",
                displayAs: "card number",
                modify: (value) => {
                    return <span>{value ?? "-"}</span>;
                },
            },
            {
                key: "remarks",
                modify: (value) => {
                    return (
                        <span className="text-sm leading-tight">
                            {value ?? "-"}
                        </span>
                    );
                },
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => {
                  const checkedInBy = item?.check_in_by?.userables?.find(
                      (userable) =>
                          isTypeEmployee(userable?.user_type_description)
                  );
                  const checkedOutBy = item?.check_out_by?.userables?.find(
                      (userable) =>
                          isTypeEmployee(userable?.user_type_description)
                  );

                  return {
                      id: item?.id,
                      block: item.hostel_room_bed?.hostel_room?.hostel_block
                          ?.name,
                      room: item.hostel_room_bed?.hostel_room?.name,
                      bed: item.hostel_room_bed?.name,
                      student_name: Object.values(
                          item?.student?.translations?.name ?? {}
                      ),
                      student_number: item.student?.student_number,
                      check_in_datetime: item.check_in_datetime
                          ? convertDateTime(item.check_in_datetime)
                          : null,
                      check_in_by: optionUserLabel(
                          checkedInBy?.number,
                          checkedInBy?.translations?.name
                      ),
                      check_out_datetime: convertDateTime(
                          item.check_out_datetime
                      ),
                      check_out_by: optionUserLabel(
                          checkedOutBy?.number,
                          checkedOutBy?.translations?.name
                      ),
                      remarks: item?.reason,
                      type: item?.type,
                      card_no: item?.card_no,
                  };
              })
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function closeForm() {
        setTargetId(null);
    }

    useEffect(() => {
        if (student) {
            fetchInOutRecords();
        }
    }, [student]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <Card styleClass="max-w-screen-2xl mx-auto">
            <div className="pb-7 lg:px-2">
                <h2 className="mb-5 pt-2 capitalize">
                    {t("create departure record")}
                </h2>
                {studentPhoto && (
                    <div className="mb-4 flex flex-wrap items-end gap-5">
                        <img
                            src={studentPhoto}
                            className="h-auto w-40 rounded-sm"
                        />
                        <div className="mb-2">
                            <InfoCard
                                noBorder={true}
                                data={{
                                    room: studentHostelInfo?.bed?.hostel_room
                                        ?.name,
                                    block: studentHostelInfo?.bed?.hostel_room
                                        ?.hostel_block?.name,
                                    bed: studentHostelInfo?.bed?.name,
                                }}
                            />
                        </div>
                    </div>
                )}
                <Form {...form}>
                    <form onSubmit={onSubmit} className="grid-form">
                        <FreeSelectAsync
                            control={form.control}
                            name="students[0].id"
                            label={`${t("student")}*`}
                            placeholder={"Type to search student"}
                            minWidth={300}
                            loadOptions={loadAsyncOptions}
                            value={student}
                            onChange={(option) => {
                                setStudent(option);
                                setStudentPhoto(option?.photo);
                                setStudentGuardians(option?.guardians);
                                setStudentHostelInfo(
                                    option?.activeHostelBedAssignments
                                );
                                form.setValue(
                                    "students[0].hostel_room_bed_id",
                                    option?.activeHostelBedAssignments?.bed?.id
                                );
                            }}
                            error={form.formState.errors?.students?.[0]?.id}
                        />
                        <FormSelect
                            control={form.control}
                            name={`students[0].guardian_id`}
                            label={t("signing guardian")}
                            isSortByName={false}
                            options={sortGuardians(studentGuardians)?.map(
                                (guardian) => ({
                                    id: guardian?.id,
                                    name: `${formatWithBrackets(guardian?.type)} ${combinedNames(
                                        guardian?.translations?.name
                                    )} ${formatWithBrackets(guardian?.nric)}`,
                                })
                            )}
                            isDisabled={!studentGuardians}
                        />

                        <FormSelect
                            control={form.control}
                            name={`students[0].type`}
                            label={t("type") + "*"}
                            isStringOptions={true}
                            options={[HOME, OUTING]}
                        />
                        <FreeDateTimePicker
                            hasLabel={true}
                            label={t("checkout date time") + "*"}
                            control={form.control}
                            name={`students[0].check_out_datetime`}
                            error={
                                form.formState.errors?.students?.[0]
                                    ?.check_out_datetime
                            }
                        />
                        {studentsWatch?.[0].type == OUTING && (
                            <FormInput
                                control={form.control}
                                name={`students[0].card_no`}
                                label={t("Card number") + "*"}
                            />
                        )}

                        <div className="lg:col-span-2">
                            <FormTextarea
                                control={form.control}
                                name={`students[0].reason`}
                                label="remarks"
                            />
                        </div>

                        <div className="pb-5 lg:col-span-2">
                            <Button type="submit" className="ml-auto mt-1 w-32">
                                {t("Submit")}
                            </Button>
                        </div>
                    </form>
                </Form>

                {student && (
                    <div className="border-t border-dashed pt-3">
                        <div className="table-page-top">
                            <h2 className="mr-auto capitalize">
                                {t("arrival records")}
                            </h2>
                        </div>

                        <DataTable
                            columns={columns}
                            data={definedData(data)}
                            pagination={pagination}
                            setPagination={setPagination}
                            changePage={(arg) =>
                                setFilter({ ...filter, ...arg })
                            }
                            sorted={filter?.order_by}
                            sort={onSort}
                            actionMenu={({ cell }) => (
                                <ActionDropdown>
                                    {hasPermit(
                                        "hostel-in-out-record-update"
                                    ) && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {t("Edit Remarks")}
                                        </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    {hasPermit(
                                        "hostel-in-out-record-delete"
                                    ) && (
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setTargetDeleteId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            {t("Delete")}
                                        </DropdownMenuItem>
                                    )}
                                </ActionDropdown>
                            )}
                        />
                    </div>
                )}
            </div>
            {/* update */}
            <Modal open={targetId} onOpenChange={setTargetId}>
                <ArrivalDepartureForm
                    id={targetId}
                    refresh={fetchInOutRecords}
                    close={closeForm}
                />
            </Modal>

            {/* delete */}
            <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                <DeletePrompt
                    id={targetDeleteId}
                    api={hostelArrivalDepartureAPI}
                    close={() => setTargetDeleteId(null)}
                    refresh={fetchInOutRecords}
                />
            </Modal>
        </Card>
    );
};

export default CreateDepartureRecordForm;
