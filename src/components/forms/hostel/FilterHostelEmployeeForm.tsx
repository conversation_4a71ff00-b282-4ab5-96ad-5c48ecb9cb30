import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    EMPLOYEE,
    GET_ALL_PARAMS,
    hostelBedsAPI,
    hostelBlocksAPI,
    hostelEmployeeAPIFilter,
    hostelRoomsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";

const FilterHostelEmployeeForm = ({
    filter,
    setFilter,
    close,
    isHostel,
}: CommonFilterProps & { isHostel?: boolean }) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            name: filter?.name ?? undefined,
            block_id: filter?.block_id ?? undefined,
            room_id: filter?.room_id ?? undefined,
            bed_id: filter?.bed_id ?? undefined,
            has_active_bed: filter?.has_active_bed ?? undefined,
        },
    });

    const { data: blocks, axiosQuery: getBlocks } = useAxios({
        api: hostelBlocksAPI,
        locale,
    });

    const { data: hostelRoomOptions, axiosQuery: getHostelRoomOptions } =
        useAxios({
            api: hostelRoomsAPI,
            locale,
        });

    const { data: hostelBedOptions, axiosQuery: getHostelBedOptions } =
        useAxios({
            api: hostelBedsAPI,
            locale,
        });

    useEffect(() => {
        form.setValue("room_id", undefined);
        form.setValue("bed_id", undefined);

        if (form.watch("block_id")) {
            getHostelRoomOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    hostel_block_id: form.watch("block_id"),
                    hostel_block_type: EMPLOYEE,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [form.watch("block_id")]);

    useEffect(() => {
        form.setValue("bed_id", undefined);

        if (form.watch("room_id")) {
            getHostelBedOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    hostel_room_id: form.watch("room_id"),
                    hostel_block_type: EMPLOYEE,
                    order_by: {
                        name: "asc",
                    },
                },
            });
        }
    }, [form.watch("room_id")]);

    useEffect(() => {
        getBlocks({
            params: {
                ...GET_ALL_PARAMS,
                type: EMPLOYEE,
            },
        });
    }, [locale]);

    function onSubmit(data: Record<string, any>) {
        const _filter: any = { ...filter, ...data, page: 1 };

        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...(isHostel ? hostelEmployeeAPIFilter : {}),
            per_page: filter?.per_page,
            page: 1,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name={"block_id"}
                    label="block"
                    options={blocks}
                />

                <FormSelect
                    control={form.control}
                    name={"room_id"}
                    label="room"
                    isDisabled={!form.watch("block_id")}
                    options={hostelRoomOptions}
                />

                <FormSelect
                    control={form.control}
                    name={"bed_id"}
                    label="bed"
                    isDisabled={!form.watch("room_id")}
                    options={hostelBedOptions}
                />

                <FormSelect
                    control={form.control}
                    name={"has_active_bed"}
                    label={"has bed"}
                    isSortByName={false}
                    options={[
                        {
                            name: t("Yes"),
                            id: "1",
                        },
                        {
                            name: t("No"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelEmployeeForm;
