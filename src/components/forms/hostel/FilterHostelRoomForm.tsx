import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    BOTH,
    CommonFilterProps,
    FEMALE,
    GET_ALL_PARAMS,
    MALE,
    hostelBlocksAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import FormSelect from "../../ui/FormSelect";

const FilterHostelRoomForm = ({
    filter,
    setFilter,
    close,
    type,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            hostel_block_id: filter?.hostel_block_id ?? null,
            name: filter?.name ?? "",
            gender: filter?.gender ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            page: 1,
            per_page: filter?.per_page,
        });
        close();
    }

    const locale = useLocale();
    const t = useTranslations("common");

    const { data: hostelBlockList, axiosQuery: getHostelBlockList } = useAxios({
        api: hostelBlocksAPI,
        locale,
    });

    useEffect(() => {
        getHostelBlockList({
            params: {
                ...GET_ALL_PARAMS,
                type,
            },
        });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name="hostel_block_id"
                    label="block"
                    options={hostelBlockList}
                />
                <FormInput
                    control={form.control}
                    name={"name"}
                    label={`${t("room ")}${t("name")}`}
                />
                <FormSelect
                    control={form.control}
                    name="gender"
                    isSortByName={false}
                    options={[
                        { id: MALE, name: t("Male") },
                        { id: FEMALE, name: t("Female") },
                        { id: BOTH, name: t("Both") },
                    ]}
                />
                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    isSortByName={false}
                    options={[
                        {
                            name: t("Active"),
                            id: "1",
                        },
                        {
                            name: t("Inactive"),
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelRoomForm;
