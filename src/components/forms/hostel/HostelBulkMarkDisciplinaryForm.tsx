import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { isArray, isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import { DropdownMenuItem } from "@/components/base-ui/dropdown-menu";
import InfoCard from "@/components/ui/InfoCard";
import {
    employeeAPI,
    hostelMeritDemeritSettingsAPI,
    hostelRewardPunishmentSettingsAPI,
    hostelDisciplinaryRecordsAPI,
    TableColumnType,
    GET_ALL_PARAMS,
    hostelPersonInChargeAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    combinedNames,
    optionUserLabel,
    showBackendFormError,
    toUTC,
    toYMD,
} from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { Label } from "../../base-ui/label";
import { Textarea } from "../../base-ui/textarea";
import ActionDropdown from "../../ui/ActionDropdown";
import DataTable from "../../ui/DataTable";
import FormDivider from "../../ui/FormDivider";
import FreeDatePicker from "../../ui/FreeDatePicker";
import FreeSelect from "../../ui/FreeSelect";
import FreeTextArea from "../../ui/FreeTextArea";
import Modal from "../../ui/Modal";
import SelectedPersonPhoto from "./SelectedPersonPhoto";

const HostelBulkMarkDisciplinaryForm = (props) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: meritsDemerits, axiosQuery: getMeritDemeritSettings } =
        useAxios({
            api: hostelMeritDemeritSettingsAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        getMeritDemeritSettings({ params: GET_ALL_PARAMS });
    }, []);

    return meritsDemerits && activeLanguages ? (
        <FormWrap
            {...props}
            meritsDemerits={meritsDemerits}
            activeLanguages={activeLanguages}
        />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({ students, close, refresh, meritsDemerits }) => {
    const t = useTranslations("common");
    const [remarkForAll, setRemarkForAll] = useState("");
    const [viewData, setViewData] = useState<any>(null);
    const [markToAdd, setMarkToAdd] = useState<any>(null);

    const form = useForm<any>({
        defaultValues: {
            person_in_charge_id: "",
            hostel_reward_punishment_setting_id: "",
            date: new Date(),
            records: students.map((student) => ({
                student_id: student?.id,
                remark: "",
            })),
        },
    });

    const columns: TableColumnType[] = [
        {
            key: "student_number",
            hasSort: false,
        },
        {
            key: "student_name",
            hasSort: false,
        },
        {
            key: "mark_before",
            displayAs: t("mark (before)"),
            hasSort: false,
            modify: (value) => value,
        },
        {
            key: "mark_after",
            displayAs: t("mark (after)"),
            hasSort: false,
            modify: (value) => {
                return value;
            },
        },
        {
            key: "remark",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="h-full">
                        <FreeTextArea
                            control={form.control}
                            name={`records[${index}].remark`}
                            error={
                                form.formState.errors?.records?.[index]?.remark
                            }
                        />
                    </div>
                );
            },
        },
        // TODO:add select guardian
    ];

    function getMarkAfter(markBefore) {
        if (markToAdd) {
            return Number(markBefore) + markToAdd;
        }
        return "-";
    }

    function definedData() {
        const year = toUTC(new Date())?.split("-")[0];
        return isArray(students)
            ? students.map((student) => {
                  const markBefore =
                      student?.hostel_reward_punishment_points?.find(
                          (item) => item?.year.toString() === year?.toString()
                      )?.points;

                  return {
                      id: student?.id,
                      student_number: student?.student_number,
                      student_name: combinedNames(student?.translations?.name),
                      mark_before: markBefore,
                      mark_after: getMarkAfter(markBefore),
                  };
              })
            : [];
    }

    function applyAll() {
        students.forEach((_, index) => {
            form.setValue(`records[${index}].remark`, remarkForAll);
        });
    }

    const locale = useLocale();

    const { data: personOptions, axiosQuery: getPersonOptions } = useAxios({
        api: hostelPersonInChargeAPI,
        locale,
    });

    const {
        data: hostelRewardPunishmentOptions,
        axiosQuery: getHostelRewardPunishment,
        isLoading: isHostelRewardPunishmentLoading,
    } = useAxios({
        api: hostelRewardPunishmentSettingsAPI,
        locale,
    });

    const { axiosPost: bulkMark, error: postError } = useAxios({
        api: hostelDisciplinaryRecordsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (data.date) {
                data.date = toYMD(data.date);
            }
            delete data.merit_demerit;
            console.log("data", data);
            bulkMark(data);
        })();
    }

    useEffect(() => {
        getPersonOptions({ params: GET_ALL_PARAMS });
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div className="mt-1 flex flex-col gap-y-3">
            <h2 className="">
                {t("Bulk Mark ")}
                {t("Disciplinary")}
            </h2>

            <div className="relative z-10 mt-2 grid gap-y-4 lg:max-w-[500px]">
                <div>
                    <FreeSelect
                        control={form.control}
                        name="person_in_charge_id"
                        label={`${t("person in charge")}*`}
                        options={personOptions?.map((item) => ({
                            id: item?.employee?.id,
                            name: optionUserLabel(
                                item?.employee?.employee_number,
                                item?.employee?.translations?.name
                            ),
                        }))}
                        error={form.formState.errors?.person_in_charge_id}
                    />
                    <SelectedPersonPhoto
                        person={personOptions?.find(
                            (person) =>
                                person?.employee?.id ==
                                form.watch("person_in_charge_id")
                        )}
                    />
                </div>
                <FreeSelect
                    control={form.control}
                    name="merit_demerit"
                    label={`${t("Merit/ Demerit")}*`}
                    options={meritsDemerits}
                    onChange={(val) => {
                        setMarkToAdd(null);
                        getHostelRewardPunishment({
                            params: {
                                hostel_merit_demerit_setting_id: val,
                            },
                        });
                        form.setValue(
                            "hostel_reward_punishment_setting_id",
                            ""
                        );
                    }}
                />

                <FreeSelect
                    control={form.control}
                    name="hostel_reward_punishment_setting_id"
                    label={`${t("hostel ")}${t("reward/punishment")}*`}
                    isLoading={isHostelRewardPunishmentLoading}
                    options={hostelRewardPunishmentOptions?.map((item) => ({
                        id: item?.id,
                        name: `${item?.name} (${item?.points})`,
                    }))}
                    isDisabled={isEmpty(hostelRewardPunishmentOptions)}
                    error={
                        form.formState.errors
                            ?.hostel_reward_punishment_setting_id
                    }
                    onChange={(val) => {
                        const _markToAdd = hostelRewardPunishmentOptions.find(
                            (item) => item.id == val
                        )?.points;
                        setMarkToAdd(_markToAdd);
                    }}
                />

                <FreeDatePicker
                    control={form.control}
                    name="date"
                    label={`${t("date")}*`}
                    hasLabel={true}
                    error={form.formState.errors?.date}
                />
            </div>

            <FormDivider />

            <div>
                <Label className={"label"}>{t("remarks for all")}</Label>
                <div className="flex items-start gap-x-4 pb-1">
                    <Textarea
                        className="textarea-h-sm lg:max-w-[500px]"
                        value={remarkForAll}
                        onChange={(e) => setRemarkForAll(e.target.value)}
                    />
                    <Button variant={"outline"} onClick={applyAll}>
                        {t("Apply All")}
                    </Button>
                </div>
            </div>

            <div className="mt-2 grid min-w-[700px] gap-y-5 lg:min-w-[900px]">
                <DataTable
                    columns={columns}
                    data={definedData()}
                    actionMenu={({ cell }) => (
                        <>
                            <ActionDropdown>
                                <DropdownMenuItem
                                    className="c-text-size"
                                    onClick={() => {
                                        const { id, ...rest } =
                                            cell.row.original;
                                        setViewData(rest);
                                    }}
                                >
                                    {t("View")}
                                </DropdownMenuItem>
                            </ActionDropdown>
                        </>
                    )}
                />
                <div className="flex justify-end gap-x-3">
                    <Button onClick={onSubmit}>{t("Submit")}</Button>
                </div>
            </div>
            <Modal open={viewData} onOpenChange={setViewData}>
                <InfoCard noBorder title="Student Info" data={viewData} />
            </Modal>
        </div>
    );
};

export default HostelBulkMarkDisciplinaryForm;
