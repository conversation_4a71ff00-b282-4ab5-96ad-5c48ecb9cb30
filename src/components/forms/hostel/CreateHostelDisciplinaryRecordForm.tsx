import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { isArray, isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import InfoCard from "@/components/ui/InfoCard";
import {
    hostelDisciplinaryRecordsAPI,
    DATE_FORMAT,
    FULL,
    studentAPI,
    TableColumnType,
    hostelRewardPunishmentSettingsAPI,
    hostelMeritDemeritSettingsAPI,
    GET_ALL_PARAMS,
    EMPLOYEE,
    hostelPersonInChargeAPI,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import {
    combinedNames,
    combineSemesterClass,
    getUserableType,
    optionUserLabel,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import FormTextarea from "../../ui/FormTextarea";
import ActionDropdown from "../../ui/ActionDropdown";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "../../base-ui/dropdown-menu";
import { useUserProfile } from "../../../lib/store";
import Modal from "../../ui/Modal";
import DeletePrompt from "../../ui/DeletePrompt";
import HostelDisciplinaryRecordForm from "./HostelDisciplinaryRecordForm";
import FormSelect from "@/components/ui/FormSelect";
import { DatePicker } from "@/components/ui/DatePicker";
import { Label } from "@/components/base-ui/label";
import { format } from "date-fns";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import SelectedPersonPhoto from "./SelectedPersonPhoto";
import TableFilterBtn from "@/components/ui/TableFilterBtn";
import FilterHostelDisciplinaryRecordForm from "./FilterHostelDisciplinaryRecordForm";
import FilterChevronButton from "@/components/ui/FilterChevronButton";
import FilterFormWrapper from "@/components/ui/FilterFormWrapper";

const CreateHostelDisciplinaryRecordForm = () => {
    useCheckViewPermit("hostel-reward-punishment-record-create");
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const userProfile = useUserProfile((state) => state.userProfile);
    const locale = useLocale();
    const t = useTranslations("common");
    const year = new Date().getFullYear();

    const form = useForm<any>({
        defaultValues: {
            student_number: "",
            person_in_charge_id: "",
            merit_demerit_setting_id: "",
            hostel_reward_punishment_setting_id: "",
            date: new Date(),
            records: [
                {
                    student_id: "",
                    remark: "",
                },
            ],
        },
    });

    const { asyncOptions, setAsyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
        params: {
            response: FULL,
            is_hostel: 1,
            includes: [
                "activeHostelBedAssignments.bed.hostelRoom.hostelBlock",
                "currentSemesterPrimaryClass.semesterSetting",
                "currentSemesterPrimaryClass.semesterClass.classModel",
            ],
            fields: ["hostel_reward_punishment_points"],
        },
        optionFormatter: (item) => ({
            value: item?.id,
            photo: item?.photo,
            name: combinedNames(item?.translations?.name),
            student_number: item?.student_number,
            class: combineSemesterClass(item?.current_primary_class),
            label: optionUserLabel(
                item?.student_number,
                item?.translations?.name
            ),
            hostel_info: item?.active_hostel_bed_assignment,
            hostel_reward_punishment_points:
                item?.hostel_reward_punishment_points?.find(
                    (item) => item?.year.toString() === year?.toString()
                )?.points,
        }),
    });

    const { data: personOptions, axiosQuery: getPersonOptions } = useAxios({
        api: hostelPersonInChargeAPI,
        locale,
    });

    function reset() {
        form.clearErrors();
        form.reset();
        form.setValue("records", [
            {
                student_id: "",
                remark: "",
            },
        ]);
        setMarkToAdd(null);
        setDefaultPIC();
    }

    const { data: meritDemerits, axiosQuery: getMeritDemerits } = useAxios({
        api: hostelMeritDemeritSettingsAPI,
        locale,
    });

    const { axiosPost: createDisciplinaryRecord, error: postError } = useAxios({
        api: hostelDisciplinaryRecordsAPI,
        onSuccess: () => {
            loadAsyncOptions(selectedStudent?.student_number, setAsyncOptions);
            fetchDisciplinaryRecords();
            reset();
            setMarkBefore(Number(markBefore) + markToAdd);
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.records[0].student_id = selectedStudent?.value;
            data.date = toYMD(data.date);
            createDisciplinaryRecord(data);
        })();
    }

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        page: 1,
        per_page: 10,
    });
    const [pagination, setPagination] = useState();
    const [openFilter, setOpenFilter] = useState(false);
    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const [selectedStudent, setSelectedStudent] = useState<any>();
    const [markBefore, setMarkBefore] = useState<any>("-");
    const [markToAdd, setMarkToAdd] = useState<any>(null);

    const { data, axiosQuery: getStudentDisciplinaryRecord } = useAxios({
        api: hostelDisciplinaryRecordsAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function closeForm() {
        setTargetId(null);
    }

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "year",
            },
            {
                key: "date",
                modify: (value, cell) => (
                    <span className="whitespace-nowrap text-sm">
                        {format(value, DATE_FORMAT.DMY)}
                    </span>
                ),
                hasSort: true,
            },
            {
                key: "hostel_merit_demerit",
                displayAs: "Merit/ Demerit",
            },
            {
                key: "hostel_reward_punishment",
                displayAs: "Reward/ Punishment",
            },
            {
                key: "points",
            },
            {
                key: "person_in_charge",
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  student_number: item?.student?.student_number,
                  student_name: Object.values(
                      item?.student?.translations?.name ?? {}
                  ),
                  hostel_merit_demerit:
                      item?.hostel_reward_punishment_setting
                          ?.hostel_merit_demerit_setting?.name,
                  hostel_reward_punishment:
                      item?.hostel_reward_punishment_setting?.name,
                  year: item?.date?.split("-")[0],
                  date: item?.date,
                  person_in_charge: item?.person_in_charge?.name ?? "-",
                  remark: item?.remark,
                  points: item?.hostel_reward_punishment_setting.points,
              }))
            : [];
    }

    const {
        data: hostelRewardPunishmentOptions,
        axiosQuery: getHostelRewardPunishment,
    } = useAxios({
        api: hostelRewardPunishmentSettingsAPI,
        locale,
    });

    function getMarkAfter(markBefore) {
        if (selectedStudent && markToAdd) {
            return Number(markBefore) + markToAdd;
        }
        return "-";
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function fetchDisciplinaryRecords(isRefresh = false) {
        getStudentDisciplinaryRecord({
            params: {
                order_by: { id: "desc" },
                ...filter,
                ...(isRefresh ? { page: 1 } : {}),
                student_id: selectedStudent?.value,
            },
        });
    }

    function setDefaultPIC() {
        const user = userProfile?.userables?.find(
            (item) => item?.userable_type === getUserableType(EMPLOYEE)
        );
        if (user) {
            const personInCharge = personOptions.find(
                (person) => person?.employee?.id == user.userable_id
            );
            form.setValue("person_in_charge_id", personInCharge?.employee?.id);
        }
    }

    useEffect(() => {
        getMeritDemerits({ params: GET_ALL_PARAMS });
        getPersonOptions({ params: GET_ALL_PARAMS });
    }, []);

    useEffect(() => {
        const latestValue = asyncOptions.find(
            (option) =>
                option.student_number === form.getValues("student_number")
        );

        if (latestValue) {
            setSelectedStudent(latestValue);
            setMarkBefore(latestValue?.hostel_reward_punishment_points);
        }
    }, [asyncOptions]);

    useEffect(() => {
        if (selectedStudent) {
            setMarkBefore(selectedStudent?.hostel_reward_punishment_points);
            fetchDisciplinaryRecords();
        }
    }, [selectedStudent, filter]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <Card styleClass="max-w-screen-2xl mx-auto">
            <div className="pb-7 lg:px-2">
                <h2 className="mb-5 pt-2 capitalize">
                    {t("create disciplinary record")}
                </h2>
                <div className="flex items-center gap-x-3">
                    <FreeSelectAsync
                        control={form.control}
                        name="student_number"
                        placeholder={"Type to search student"}
                        minWidth={300}
                        hasLabel={false}
                        loadOptions={loadAsyncOptions}
                        value={selectedStudent}
                        onChange={(option) => {
                            reset();
                            setDefaultPIC();
                            setMarkToAdd(null);
                            setSelectedStudent(option);
                            setMarkBefore(
                                option?.hostel_reward_punishment_points
                            );
                            form.setValue(
                                "student_number",
                                option?.student_number
                            );
                        }}
                    />
                </div>

                {selectedStudent?.photo && (
                    <>
                        <div className="mb-3 pt-3">
                            {selectedStudent?.photo && (
                                <div className="mb-4 flex flex-wrap items-end gap-5">
                                    <img
                                        src={selectedStudent?.photo}
                                        className="h-auto w-40 rounded-sm"
                                    />
                                    <div className="mb-2">
                                        <InfoCard
                                            noBorder={true}
                                            data={{
                                                student_number:
                                                    selectedStudent?.student_number,
                                                name: selectedStudent?.name,
                                                class: selectedStudent?.class,
                                                block: selectedStudent
                                                    ?.hostel_info?.bed
                                                    ?.hostel_room?.hostel_block
                                                    ?.name,
                                                room: selectedStudent
                                                    ?.hostel_info?.bed
                                                    ?.hostel_room?.name,
                                                bed: selectedStudent
                                                    ?.hostel_info?.bed?.name,
                                            }}
                                        />
                                    </div>
                                </div>
                            )}
                            <Form {...form}>
                                <form
                                    onSubmit={onSubmit}
                                    className="grid-form mt-5 border-dashed pt-4"
                                >
                                    <div>
                                        <FormSelect
                                            control={form.control}
                                            name="person_in_charge_id"
                                            label={`${t("person in charge")}*`}
                                            options={personOptions?.map(
                                                (item) => ({
                                                    id: item?.employee?.id,
                                                    name: optionUserLabel(
                                                        item?.employee
                                                            ?.employee_number,
                                                        item?.employee
                                                            ?.translations?.name
                                                    ),
                                                })
                                            )}
                                        />
                                        <SelectedPersonPhoto
                                            person={personOptions?.find(
                                                (person) =>
                                                    person?.employee?.id ==
                                                    form.watch(
                                                        "person_in_charge_id"
                                                    )
                                            )}
                                        />
                                    </div>
                                    <DatePicker
                                        control={form.control}
                                        name="date"
                                        label={`${t("date")}*`}
                                    />

                                    <FormSelect
                                        control={form.control}
                                        name={"merit_demerit_setting_id"}
                                        label={`${t("merit/demerit")}*`}
                                        options={meritDemerits}
                                        onChange={(val) => {
                                            setMarkToAdd(null);
                                            getHostelRewardPunishment({
                                                params: {
                                                    hostel_merit_demerit_setting_id:
                                                        val,
                                                },
                                            });
                                            form.setValue(
                                                "hostel_reward_punishment_setting_id",
                                                ""
                                            );
                                        }}
                                    />

                                    <FormSelect
                                        control={form.control}
                                        name={
                                            "hostel_reward_punishment_setting_id"
                                        }
                                        label={`${t("reward/punishment")}*`}
                                        options={hostelRewardPunishmentOptions?.map(
                                            (item) => ({
                                                id: item?.id,
                                                name: `${item?.name} (${item?.points})`,
                                            })
                                        )}
                                        isDisabled={
                                            !form.watch(
                                                "merit_demerit_setting_id"
                                            )
                                        }
                                        onChange={(val) => {
                                            const _markToAdd =
                                                hostelRewardPunishmentOptions.find(
                                                    (item) => item.id == val
                                                )?.points;
                                            setMarkToAdd(_markToAdd);
                                        }}
                                    />

                                    <FormTextarea
                                        control={form.control}
                                        name="records.0.remark"
                                        label="remark"
                                    />

                                    <div className="rounded-md bg-themeGreen3 p-4 lg:flex lg:items-start lg:gap-4">
                                        <div className="capitalize lg:border-r lg:pr-4">
                                            <Label className="text-themeGreen2 opacity-70">
                                                {t("mark (before)")}
                                            </Label>
                                            <div>{markBefore ?? "-"}</div>
                                        </div>

                                        <div className="capitalize">
                                            <Label className="text-themeGreen2 opacity-70">
                                                {t("mark (after)")}
                                            </Label>
                                            <div>
                                                {getMarkAfter(markBefore) ??
                                                    "-"}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="pb-5 lg:col-span-2">
                                        <Button
                                            type="submit"
                                            className="ml-auto mt-1 w-32"
                                        >
                                            {t("Submit")}
                                        </Button>
                                    </div>
                                </form>
                            </Form>
                        </div>

                        <div className="pt-1">
                            <div className="table-page-top">
                                <h2 className="mr-auto capitalize">
                                    {t("disciplinary records")}
                                </h2>

                                {/* <TableFilterBtn
                                    filter={filter}
                                    onClick={() => setOpenFilter(true)}
                                /> */}
                                <FilterChevronButton />
                            </div>

                            <FilterFormWrapper>
                                <FilterHostelDisciplinaryRecordForm
                                    filter={filter}
                                    setFilter={setFilter}
                                    close={() => setOpenFilter(false)}
                                />
                            </FilterFormWrapper>

                            <DataTable
                                columns={columns}
                                data={definedData(data)}
                                pagination={pagination}
                                setPagination={setPagination}
                                changePage={(arg) =>
                                    setFilter({ ...filter, ...arg })
                                }
                                sorted={filter?.order_by}
                                sort={onSort}
                                actionMenu={({ cell }) => (
                                    <ActionDropdown>
                                        {hasPermit(
                                            "hostel-reward-punishment-record-update"
                                        ) && (
                                            <DropdownMenuItem
                                                className="c-text-size"
                                                onClick={() =>
                                                    setTargetId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                {t("Edit / View")}
                                            </DropdownMenuItem>
                                        )}
                                        <DropdownMenuSeparator />
                                        {hasPermit(
                                            "hostel-reward-punishment-record-delete"
                                        ) && (
                                            <DropdownMenuItem
                                                className="c-text-size text-red-600"
                                                onClick={() =>
                                                    setTargetDeleteId(
                                                        cell.row.original.id
                                                    )
                                                }
                                            >
                                                {t("Delete")}
                                            </DropdownMenuItem>
                                        )}
                                    </ActionDropdown>
                                )}
                            />
                        </div>
                    </>
                )}

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId}>
                    <HostelDisciplinaryRecordForm
                        id={targetId}
                        refresh={() => {
                            loadAsyncOptions(
                                selectedStudent?.student_number,
                                setAsyncOptions
                            );
                        }}
                        close={closeForm}
                        hideStudentInfo={true}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={hostelDisciplinaryRecordsAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={() => {
                            loadAsyncOptions(
                                selectedStudent?.student_number,
                                setAsyncOptions
                            );
                        }}
                    />
                </Modal>

                {/* filter */}
                <Modal open={openFilter} onOpenChange={setOpenFilter}>
                    <FilterHostelDisciplinaryRecordForm
                        filter={filter}
                        setFilter={setFilter}
                        close={() => setOpenFilter(false)}
                    />
                </Modal>
            </div>
        </Card>
    );
};

export default CreateHostelDisciplinaryRecordForm;
