import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import { CommonFilterProps } from "@/lib/constant";
import { toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterHostelSavingAccountForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const today = new Date();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            period_from: filter?.period_from ?? "",
            period_to: filter?.period_to ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        data.period_from = toYMD(data.period_from);
        data.period_to = toYMD(data.period_to);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onReset() {
        form.reset();
        setFilter({
            period_from: toYMD(oneYearAgo),
            period_to: toYMD(today),
            page: 1,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">{t("Filter")}</h2>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="grid gap-y-5"
            >
                <DatePicker control={form.control} name="period_from" />

                <DatePicker control={form.control} name="period_to" />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onReset}>
                        {t("Reset")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelSavingAccountForm;
