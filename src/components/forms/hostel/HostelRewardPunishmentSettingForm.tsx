import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    hostelMeritDemeritSettingsAPI,
    hostelRewardPunishmentSettingsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";

const HostelRewardPunishmentSettingForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: currentData, axiosQuery: getHostelRewardPunishmentSetting } =
        useAxios({
            api: hostelRewardPunishmentSettingsAPI,
            locale,
            onError: props.close,
        });

    const {
        data: hostelMeritDemeritSettingList,
        axiosQuery: getHostelMeritDemeritSetting,
    } = useAxios({
        api: hostelMeritDemeritSettingsAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getHostelMeritDemeritSetting({ params: GET_ALL_PARAMS });
        if (!props.isCreate && props.id) {
            getHostelRewardPunishmentSetting({ id: props.id });
        }
    }, []);

    return props.isCreate || currentData ? (
        <FormWrap
            currentData={currentData}
            hostelMeritDemeritSettingList={hostelMeritDemeritSettingList}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    hostelMeritDemeritSettingList: any;
};

const FormWrap = ({
    currentData,
    hostelMeritDemeritSettingList,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            hostel_merit_demerit_setting_id:
                currentData?.hostel_merit_demerit_setting?.id ?? null,
            name: currentData?.name ?? "",
            code: currentData?.code ?? "",
            points: currentData?.points ?? "",
        },
    });
    const t = useTranslations("common");

    const { axiosPost: createHostelRewardPunishmentSetting, error: postError } =
        useAxios({
            api: hostelRewardPunishmentSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateHostelRewardPunishmentSetting, error: putError } =
        useAxios({
            api: hostelRewardPunishmentSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createHostelRewardPunishmentSetting(data);
            } else {
                updateHostelRewardPunishmentSetting({
                    id: currentData.id,
                    data,
                });
            }
        })();
    }
    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("reward/punishment setting")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name="hostel_merit_demerit_setting_id"
                        label={t("Merit/ Demerit") + "*"}
                        options={hostelMeritDemeritSettingList}
                    />

                    <FormInput
                        control={form.control}
                        name="code"
                        label={t("code") + "*"}
                    />

                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                    />

                    <FormInput
                        control={form.control}
                        name="points"
                        type="number"
                        label={t("points") + "*"}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default HostelRewardPunishmentSettingForm;
