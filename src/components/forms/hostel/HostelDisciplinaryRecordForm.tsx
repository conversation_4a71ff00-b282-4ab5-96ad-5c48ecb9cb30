import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import FormSearchInput from "@/components/ui/FormSearchInput";
import Modal from "@/components/ui/Modal";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    CommonFormProps,
    EMPLOYEE,
    employeeAPI,
    GET_ALL_PARAMS,
    hostelDisciplinaryRecordsAPI,
    hostelMeritDemeritSettingsAPI,
    hostelPersonInChargeAPI,
    hostelRewardPunishmentSettingsAPI,
    hostelStudentAPIFilter,
    STUDENT,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    optionUserLabel,
    showBackendFormError,
    toUTC,
    toYMD,
} from "@/lib/utils";
import { DatePicker } from "../../ui/DatePicker";
import FormSelect from "../../ui/FormSelect";
import FormTextarea from "../../ui/FormTextarea";
import SelectedPersonPhoto from "./SelectedPersonPhoto";
import clsx from "clsx";

const HostelDisciplinaryRecordForm = (
    props: CommonFormProps & { hideStudentInfo?: boolean }
) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: meritDemerits, axiosQuery: getMeritDemerits } = useAxios({
        api: hostelMeritDemeritSettingsAPI,
        locale,
        onError: props.close,
    });

    const { data: currentRecord, axiosQuery: getHostelRewardPunishmentRecord } =
        useAxios({
            api: hostelDisciplinaryRecordsAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        getMeritDemerits({ params: GET_ALL_PARAMS });

        if (!props.isCreate && props.id) {
            getHostelRewardPunishmentRecord({ id: props.id });
        }
    }, []);

    return activeLanguages &&
        meritDemerits &&
        (props.isCreate || currentRecord) ? (
        <FormWrap
            currentRecord={currentRecord}
            activeLanguages={activeLanguages}
            meritDemerits={meritDemerits}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentRecord: any;
    activeLanguages: Array<Record<string, any>>;
    meritDemerits: Record<string, any>;
    hideStudentInfo?: boolean;
};

const FormWrap = ({
    currentRecord,
    isCreate = false,
    meritDemerits,
    hideStudentInfo = false,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            person_in_charge_id: currentRecord?.person_in_charge?.id ?? "",
            merit_demerit_setting_id:
                currentRecord?.hostel_reward_punishment_setting
                    ?.hostel_merit_demerit_setting?.id ?? "",
            hostel_reward_punishment_setting_id:
                currentRecord?.hostel_reward_punishment_setting?.id ?? "",
            date: currentRecord?.date ?? new Date(),
            records: [
                {
                    student_id: currentRecord?.student?.id ?? "",
                    remark: currentRecord?.remark ?? "",
                },
            ],
        },
    });

    const [openSearch, setOpenSearch] = useState<
        typeof STUDENT | typeof EMPLOYEE | null
    >(null);
    const [selectedPerson, setSelectedPerson] = useState<any>();
    const [selectedStudent, setSelectedStudent] = useState<any>();
    const [markBefore, setMarkBefore] = useState<any>("-");
    const [markToAdd, setMarkToAdd] = useState<any>(null);

    const locale = useLocale();

    const { axiosQuery: getEmployeeById } = useAxios({
        api: employeeAPI,
        locale,
        onSuccess(result) {
            setSelectedPerson(result?.data);
        },
    });

    const { data: personOptions, axiosQuery: getPersonOptions } = useAxios({
        api: hostelPersonInChargeAPI,
        locale,
    });

    const { axiosQuery: getStudentById } = useAxios({
        api: studentAPI,
        locale,
        onSuccess(result) {
            setSelectedStudent(result?.data);
        },
    });

    const {
        data: hostelRewardPunishmentOptions,
        axiosQuery: getHostelRewardPunishment,
    } = useAxios({
        api: hostelRewardPunishmentSettingsAPI,
        locale,
    });

    const { axiosPost: createHostelDisciplinaryRecord, error: postError } =
        useAxios({
            api: hostelDisciplinaryRecordsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPatch: updateHostelDisciplinaryRecord, error: putError } =
        useAxios({
            api: hostelDisciplinaryRecordsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function getMarkAfter(markBefore) {
        if (selectedStudent && markToAdd) {
            return Number(markBefore) + markToAdd;
        }
        return "-";
    }

    useEffect(() => {
        if (currentRecord) {
            getEmployeeById({ id: currentRecord?.person_in_charge?.id });
            getStudentById({
                id: currentRecord?.student?.id,
                params: { ...hostelStudentAPIFilter },
            });
        }

        const currentMeritDemeritId =
            currentRecord?.hostel_reward_punishment_setting
                ?.hostel_merit_demerit_setting?.id;

        if (currentMeritDemeritId) {
            getHostelRewardPunishment({
                params: {
                    hostel_merit_demerit_setting_id: currentMeritDemeritId,
                },
            });
        }
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    useEffect(() => {
        const year = toUTC(new Date())?.split("-")[0];
        if (selectedStudent) {
            const studentCurrentMark =
                selectedStudent?.hostel_reward_punishment_points?.find(
                    (item) => item?.year.toString() === year?.toString()
                )?.points;

            setMarkBefore(studentCurrentMark);
        }
    }, [selectedStudent]);

    useEffect(() => {
        getPersonOptions({ params: GET_ALL_PARAMS });
    }, []);

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.date = toYMD(data.date);
            if (isCreate) {
                createHostelDisciplinaryRecord(data);
            } else {
                updateHostelDisciplinaryRecord({
                    ...data,
                    records: [{ ...data.records[0], id: currentRecord.id }],
                });
            }
        })();
    }

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("disciplinary record")}
            </h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className={clsx(!hideStudentInfo && "grid-form gap-x-5")}
                >
                    <div
                        className={clsx(
                            hideStudentInfo
                                ? "grid gap-y-3"
                                : "flex flex-col gap-3"
                        )}
                    >
                        <DatePicker
                            control={form.control}
                            name="date"
                            label={`${t("date")}*`}
                        />

                        <div>
                            <FormSelect
                                control={form.control}
                                name="person_in_charge_id"
                                label={`${t("person in charge")}*`}
                                options={personOptions?.map((item) => ({
                                    id: item?.employee?.id,
                                    name: optionUserLabel(
                                        item?.employee?.employee_number,
                                        item?.employee?.translations?.name
                                    ),
                                }))}
                            />
                            <SelectedPersonPhoto
                                person={personOptions?.find(
                                    (person) =>
                                        person?.employee?.id ==
                                        form.watch("person_in_charge_id")
                                )}
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name={"merit_demerit_setting_id"}
                            label={`${t("merit/demerit")}*`}
                            options={meritDemerits}
                            onChange={(val) => {
                                setMarkToAdd(null);
                                getHostelRewardPunishment({
                                    params: {
                                        hostel_merit_demerit_setting_id: val,
                                    },
                                });
                                form.setValue(
                                    "hostel_reward_punishment_setting_id",
                                    ""
                                );
                            }}
                        />

                        <FormSelect
                            control={form.control}
                            name={"hostel_reward_punishment_setting_id"}
                            label={`${t("reward/punishment")}*`}
                            options={hostelRewardPunishmentOptions?.map(
                                (item) => ({
                                    id: item?.id,
                                    name: `${item?.name} (${item?.points})`,
                                })
                            )}
                            isDisabled={isEmpty(hostelRewardPunishmentOptions)}
                            onChange={(val) => {
                                const _markToAdd =
                                    hostelRewardPunishmentOptions.find(
                                        (item) => item.id == val
                                    )?.points;
                                setMarkToAdd(_markToAdd);
                            }}
                        />

                        <FormTextarea
                            control={form.control}
                            name="records.0.remark"
                            label="remark"
                        />

                        {hideStudentInfo && (
                            <Button type="submit" className="ml-auto mt-1">
                                {t("Submit")}
                            </Button>
                        )}
                    </div>

                    {!hideStudentInfo && (
                        <div className="flex flex-col gap-3">
                            <>
                                <div>
                                    <FormSearchInput
                                        name="records.0.student_id"
                                        control={form.control}
                                        label={`${t("student")}*`}
                                        displayValue={optionUserLabel(
                                            selectedStudent?.student_number,
                                            selectedStudent?.translations?.name
                                        )}
                                        onClick={() => setOpenSearch(STUDENT)}
                                    />
                                    {selectedStudent?.photo && (
                                        <img
                                            src={selectedStudent.photo}
                                            className="mt-2 h-auto w-40 rounded-sm"
                                        />
                                    )}
                                </div>

                                <div className="rounded-md bg-themeGreen3 px-4 py-2 capitalize lg:flex lg:items-start lg:gap-4">
                                    <div className="lg:border-r lg:pr-4">
                                        <Label className="text-themeGreen2 opacity-70">
                                            {t("mark (before)")}
                                        </Label>
                                        <div>{markBefore ?? "-"}</div>
                                    </div>

                                    <div>
                                        <Label className="text-themeGreen2 opacity-70">
                                            {t("mark (after)")}
                                        </Label>
                                        <div>
                                            {getMarkAfter(markBefore) ?? "-"}
                                        </div>
                                    </div>
                                </div>
                            </>

                            <Button type="submit" className="ml-auto mt-1">
                                {t("Submit")}
                            </Button>
                        </div>
                    )}
                </form>
            </Form>

            <Modal
                open={openSearch === STUDENT}
                onOpenChange={setOpenSearch}
                size="large"
            >
                <StudentSearchEngine
                    isHostel={true}
                    hasActiveBed={true}
                    setSelection={(student) => {
                        if (student) {
                            form.setValue("records.0.student_id", student.id);
                            form.clearErrors("records.0.student_id");
                            setSelectedStudent(student);
                        }
                    }}
                    close={() => setOpenSearch(null)}
                />
            </Modal>

            <Modal
                open={openSearch === EMPLOYEE}
                onOpenChange={setOpenSearch}
                size="large"
            >
                <StaffSearchEngine
                    setSelection={(person) => {
                        if (person) {
                            form.setValue("person_in_charge_id", person.id);
                            form.clearErrors("person_in_charge_id");
                            setSelectedPerson(person);
                        }
                    }}
                    close={() => setOpenSearch(null)}
                />
            </Modal>
        </div>
    );
};

export default HostelDisciplinaryRecordForm;
