import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    hostelPersonInChargeAPI,
    hostelRewardPunishmentSettingsAPI,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { optionUserLabel, toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { DatePicker } from "../../ui/DatePicker";
import FormSelect from "../../ui/FormSelect";
import SelectedPersonPhoto from "./SelectedPersonPhoto";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";

const FilterHostelDisciplinaryRecordForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const [asyncValue, setAsyncValue] = useState<Record<string, any> | null>(
        null
    );

    const { loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
        params: {
            is_hostel: 1,
        },
    });

    const form = useForm({
        defaultValues: {
            student_id: filter?.student_id ?? "",
            person_in_charge_id: filter?.person_in_charge_id ?? "",
            date: filter?.date ?? "",
            hostel_reward_punishment_setting_id:
                filter?.hostel_reward_punishment_setting_id ?? null,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        const _filter: any = { ...filter, ...data, page: 1 };

        if (data.date) {
            _filter.date = toYMD(data.date);
        }
        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        setAsyncValue(null);
        close();
    }

    const locale = useLocale();

    const { data: personOptions, axiosQuery: getPersonOptions } = useAxios({
        api: hostelPersonInChargeAPI,
        locale,
    });

    const {
        data: rewardPunishmentOptions,
        axiosQuery: getHostelRewardPunishmentSettings,
    } = useAxios({
        api: hostelRewardPunishmentSettingsAPI,
        locale,
    });

    useEffect(() => {
        getHostelRewardPunishmentSettings({ params: GET_ALL_PARAMS });
        getPersonOptions({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <div className="lg:col-span-2">
                    <FreeSelectAsync
                        control={form.control}
                        name="student_id"
                        label="quick_search"
                        placeholder="Type to search student"
                        loadOptions={loadAsyncOptions}
                        value={asyncValue}
                        onChange={(option) => {
                            form.setValue("student_id", option?.value ?? "");
                            setAsyncValue(option);
                            setFilter({
                                ...filter,
                                page: 1,
                                student_id: option?.value ?? undefined,
                            });
                        }}
                    />
                </div>
                <div className="lg:col-span-2">
                    <FormSelect
                        control={form.control}
                        name="person_in_charge_id"
                        label={t("person in charge")}
                        options={personOptions?.map((item) => ({
                            id: item?.employee?.id,
                            name: optionUserLabel(
                                item?.employee?.employee_number,
                                item?.employee?.translations?.name
                            ),
                        }))}
                    />
                    <SelectedPersonPhoto
                        person={personOptions?.find(
                            (person) =>
                                person?.employee?.id ==
                                form.watch("person_in_charge_id")
                        )}
                    />
                </div>

                <div className="lg:col-span-2">
                    <FormSelect
                        control={form.control}
                        name="hostel_reward_punishment_setting_id"
                        label={t("Reward/ Punishment")}
                        options={rewardPunishmentOptions?.map((item) => ({
                            id: item?.id,
                            name: `${item?.name} (${item?.points})`,
                        }))}
                    />
                </div>

                <DatePicker control={form.control} name="date" />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelDisciplinaryRecordForm;
