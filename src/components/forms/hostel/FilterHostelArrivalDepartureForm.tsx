import React, { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import { CommonFilterProps, HOME, OUTING, studentAPI } from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";

const FilterHostelArrivalDepartureForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const [asyncValue, setAsyncValue] = useState<Record<string, any> | null>(
        null
    );

    const { loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        isStudentNumber: true,
        useCommonSearch: true,
        params: {
            is_hostel: 1,
        },
    });

    const form = useForm({
        defaultValues: {
            student_number: filter?.student_number ?? undefined,
            type: filter?.type ?? undefined,
            check_in_datetime:
                filter?.check_in_datetime === ""
                    ? "DEPARTED"
                    : filter?.check_in_datetime === "ARRIVED"
                      ? "ARRIVED"
                      : "BOTH",
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        if (isEmpty(data.student_number)) {
            delete data.student_number;
        }
        if (isEmpty(data.student_name)) {
            delete data.student_name;
        }
        if (data.check_in_datetime === "BOTH") {
            data.check_in_datetime = undefined;
        }
        if (data.check_in_datetime === "DEPARTED") {
            data.check_in_datetime = "";
        }
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            page: 1,
            per_page: filter?.per_page,
            order_by: {
                check_in_datetime: "desc",
            },
            includes: filter.includes ?? ["bed.hostelRoom.hostelBlock"],
        });
        close();
        setAsyncValue(null);
    }

    useEffect(() => {
        form.setValue("student_number", filter?.student_number ?? "");
    }, [filter?.student_number]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <div className="lg:col-span-2">
                    <FreeSelectAsync
                        control={form.control}
                        name="student_number"
                        label="quick_search"
                        placeholder="Type to search student"
                        loadOptions={loadAsyncOptions}
                        value={asyncValue}
                        onChange={(option) => {
                            form.setValue(
                                "student_number",
                                option?.value ?? ""
                            );
                            setAsyncValue(option);
                            setFilter({
                                ...filter,
                                page: 1,
                                student_number: option?.value ?? undefined,
                            });
                        }}
                    />
                </div>
                <FormSelect
                    control={form.control}
                    name={"type"}
                    isStringOptions={true}
                    options={[HOME, OUTING]}
                />

                <FormSelect
                    control={form.control}
                    name={"check_in_datetime"}
                    label={t("departed/arrived")}
                    isStringOptions={true}
                    options={["BOTH", "DEPARTED", "ARRIVED"]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelArrivalDepartureForm;
