import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormTextarea from "@/components/ui/FormTextarea";
import { CommonFormProps, hostelArrivalDepartureAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const ArrivalDepartureForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: record, axiosQuery: getRecord } = useAxios({
        api: hostelArrivalDepartureAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (props.id) {
            getRecord({ id: props.id });
        }
    }, []);

    return record ? (
        <FormWrap record={record} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    record: any;
};

const FormWrap = ({ record, refresh, close }: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            check_out_datetime: record?.check_out_datetime ?? "",
            reason: record?.reason ?? "",
        },
    });

    const { axiosPut: updateRecord, error: putError } = useAxios({
        api: hostelArrivalDepartureAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            updateRecord({ id: record.id, data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <div>
            <h2 className="mb-5 capitalize">{t("update record")}</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormTextarea
                        control={form.control}
                        name={`reason`}
                        label="remarks"
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default ArrivalDepartureForm;
