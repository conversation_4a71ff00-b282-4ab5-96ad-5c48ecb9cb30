import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFormProps,
    DEMERIT,
    MERIT,
    hostelMeritDemeritSettingsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";
import FormRadioGroup from "../../ui/FormRadioGroup";
import { capitalize } from "lodash";

const HostelMeritDemeritSettingForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentData, axiosQuery: getHostelMeritDemeritSetting } =
        useAxios({
            api: hostelMeritDemeritSettingsAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getHostelMeritDemeritSetting({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentData,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            type: currentData?.type ?? "",
            name: currentData?.name ?? "",
        },
    });

    const { axiosPost: createHostelMeritDemeritSetting, error: postError } =
        useAxios({
            api: hostelMeritDemeritSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateHostelMeritDemeritSetting, error: putError } =
        useAxios({
            api: hostelMeritDemeritSettingsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createHostelMeritDemeritSetting(data);
            } else {
                updateHostelMeritDemeritSetting({
                    id: currentData.id,
                    data,
                });
            }
        })();
    }
    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("merit/demerit setting")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormRadioGroup
                        control={form.control}
                        name="type"
                        isHorizontal={true}
                        options={[
                            { id: MERIT, name: capitalize(t(MERIT)) },
                            { id: DEMERIT, name: capitalize(t(DEMERIT)) },
                        ]}
                        label={t("type") + "*"}
                    />
                    <FormInput
                        control={form.control}
                        name="name"
                        label={t("name") + "*"}
                    />
                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default HostelMeritDemeritSettingForm;
