import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import {
    BOTH,
    CommonFormProps,
    FEMALE,
    GET_ALL_PARAMS,
    MALE,
    genderOptions,
    hostelBlocksAPI,
    hostelRoomsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import FormSelect from "../../ui/FormSelect";
import FormTextarea from "../../ui/FormTextarea";
import StatusFormSwitch from "../../ui/StatusFormSwitch";

const HostelRoomForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const { data: hostelRoom, axiosQuery: getHostelRoom } = useAxios({
        api: hostelRoomsAPI,
        locale,
        onError: props.close,
    });

    const { data: hostelBlockList, axiosQuery: getHostelBlockList } = useAxios({
        api: hostelBlocksAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getHostelBlockList({
            params: { ...GET_ALL_PARAMS, type: props.hostelType },
        });

        if (!props.isCreate && props.id) {
            getHostelRoom({ id: props.id });
        }
    }, []);

    return props.isCreate || hostelRoom ? (
        <FormWrap
            hostelBlockList={hostelBlockList}
            hostelRoom={hostelRoom}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    hostelRoom: any;
    hostelBlockList: any;
};

const FormWrap = ({
    hostelRoom,
    isCreate = false,
    hostelBlockList,
    refresh,
    close,
    hostelType,
}: FormWrapProps) => {
    const t = useTranslations("common");
    const form = useForm({
        defaultValues: {
            hostel_block_id: hostelRoom?.hostel_block?.id ?? "",
            name: hostelRoom?.name ?? "",
            gender: hostelRoom?.gender ?? "",
            capacity: hostelRoom?.capacity ?? "",
            is_active: isCreate ? true : hostelRoom?.is_active ?? false,
            remarks: hostelRoom?.remarks ?? "",
        },
    });

    console.log("form values", form.getValues());

    const { axiosPost: createHostelRoom, error: postError } = useAxios({
        api: hostelRoomsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateHostelRoom, error: putError } = useAxios({
        api: hostelRoomsAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.type = hostelType;
            if (isCreate) {
                createHostelRoom(data);
            } else {
                updateHostelRoom({ id: hostelRoom.id, data });
            }
        })();
    }
    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? t("Create ") : t("Update ")}
                {t("room")}
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name="hostel_block_id"
                        label={`${t("block")}*`}
                        options={hostelBlockList}
                    />
                    <FormInput
                        control={form.control}
                        name="name"
                        label={`${t("room ")}${t("name")}*`}
                    />
                    <FormSelect
                        control={form.control}
                        name="gender"
                        label={`${t("gender")}*`}
                        isSortByName={false}
                        options={[
                            { id: MALE, name: t("Male") },
                            { id: FEMALE, name: t("Female") },
                            { id: BOTH, name: t("Both") },
                        ]}
                    />
                    <FormInputInterger control={form.control} name="capacity" />
                    <FormTextarea control={form.control} name="remarks" />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label={`${t("status")}`}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        {t("Submit")}
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default HostelRoomForm;
