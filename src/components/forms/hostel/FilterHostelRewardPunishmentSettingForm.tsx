import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    hostelMeritDemeritSettingsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";
import FormSelect from "../../ui/FormSelect";

const FilterHostelRewardPunishmentSettingForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            hostel_merit_demerit_setting_id:
                filter?.hostel_merit_demerit_setting_id ?? null,
            code: filter?.code ?? "",
            name: filter?.name ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const {
        data: hostelMeritDemeritSettingList,
        axiosQuery: getHostelMeritDemeritSetting,
    } = useAxios({
        api: hostelMeritDemeritSettingsAPI,
        locale,
    });

    useEffect(() => {
        getHostelMeritDemeritSetting({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name="hostel_merit_demerit_setting_id"
                    label={t("Merit/ Demerit")}
                    options={hostelMeritDemeritSettingList}
                />
                <FormInput control={form.control} name="code" />

                <FormInput control={form.control} name="name" />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterHostelRewardPunishmentSettingForm;
