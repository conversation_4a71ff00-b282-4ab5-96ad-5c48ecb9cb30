import React from "react";
import { But<PERSON> } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import { useAxios } from "@/lib/hook";
import { useTranslations } from "next-intl";

type HostelTransactionChangeStatusPromptProps = {
    api: string;
    id: number | string | null;
    refresh: () => void;
    close: () => void;
};

const HostelTransactionChangeStatusPrompt = ({
    id,
    api,
    close,
    refresh,
}: HostelTransactionChangeStatusPromptProps) => {
    const t = useTranslations("common");
    const { axiosPost: updateTransactionStatus, error: postError } = useAxios({
        api: api,
        toastMsg: t("Voided successfully"),
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit() {
        updateTransactionStatus(
            { billing_document_id: id },
            { showErrorInToast: true }
        );
    }

    return (
        <>
            <p className="mt-3 font-medium">
                {t("Are you sure you want to void this transaction?")}
            </p>
            <DialogFooter className={"mt-2"}>
                <Button variant="outline" onClick={close}>
                    {t("Cancel")}
                </Button>
                <Button onClick={onSubmit}>{t("Confirm")}</Button>
            </DialogFooter>
        </>
    );
};

export default HostelTransactionChangeStatusPrompt;
