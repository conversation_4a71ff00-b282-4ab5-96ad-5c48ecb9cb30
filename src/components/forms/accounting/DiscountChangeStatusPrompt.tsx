import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import { discountConfirmAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

type DiscountChangeStatusPromptProps = {
    selectedDiscounts: number[];
    refresh: () => void;
    close: () => void;
};

const DiscountChangeStatusPrompt = ({
    selectedDiscounts,
    close,
    refresh,
}: DiscountChangeStatusPromptProps) => {
    const form = useForm();

    const { axiosPost: bulkUpdateStatus, error: putError } = useAxios({
        api: discountConfirmAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.discount_ids = selectedDiscounts;
            bulkUpdateStatus(data);
            close();
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <>
            <p className="mt-3 font-medium">
                Are you sure you want to confirm and activate these discount?
            </p>
            <DialogFooter className={"mt-2"}>
                <Button variant="outline" onClick={close}>
                    Cancel
                </Button>
                <Button onClick={onSubmit}>Confirm</Button>
            </DialogFooter>
        </>
    );
};

export default DiscountChangeStatusPrompt;
