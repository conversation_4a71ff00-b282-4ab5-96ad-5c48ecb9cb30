import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { chunk, indexOf, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FreeDatePicker from "@/components/ui/FreeDatePicker";
import Modal from "@/components/ui/Modal";
import PaginatedTableFilter from "@/components/ui/PaginatedTableFilter";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    CHUNKED_FILTER_PARAMS,
    CommonFormProps,
    DATE_FORMAT,
    scholarshipAPI,
    STUDENT,
} from "@/lib/constant";
import { useAxios, usePaginatedTable, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    combinedNames,
    isValueTrue,
    showBackendFormError,
    toMyZonedTime,
    toUTC,
    toYMD,
} from "@/lib/utils";

const ScholarshipForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentScholarshipData, axiosQuery: getScholarship } =
        useAxios({
            api: scholarshipAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getScholarship({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentScholarshipData) ? (
        <FormWrap
            currentScholarshipData={currentScholarshipData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentScholarshipData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentScholarshipData,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const form = useForm<any>({
        defaultValues: {
            name: activeLanguages!
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            currentScholarshipData?.translations?.name?.[key] ??
                            "",
                    }),
                    {}
                ),
            award_body: currentScholarshipData?.award_body ?? "",
            application_open_at:
                toMyZonedTime(currentScholarshipData?.application_open_at) ??
                null,
            application_close_at:
                toMyZonedTime(currentScholarshipData?.application_close_at) ??
                null,
            description: currentScholarshipData?.description ?? "",
            is_internal: isValueTrue(currentScholarshipData?.is_internal),
            is_active: isValueTrue(currentScholarshipData?.is_active),
            students:
                currentScholarshipData?.awards?.map((item) => ({
                    id: item?.id,
                    student_number: item?.student?.student_number,
                    name: combinedNames(item?.student?.translations?.name),

                    student_id: item?.student?.id ?? null,
                    effective_from: item?.effective_from ?? null,
                    effective_to: item?.effective_to ?? null,
                })) ?? [],
        },
    });

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [targetsChunk, setTargetsChunk] = useState<any[]>(
        chunk(form.getValues().students, CHUNKED_FILTER_PARAMS.per_page)
    );

    const [dateRange, setDateRange] = useState<any>(null);

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        clonedFilteredChunk,
        resetClonedFilter,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    const { append: appendStudent, remove: removeStudent } = useFieldArray({
        control: form.control,
        name: "students",
    });

    const studentColumns = [
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
            modify: (value, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="grid min-w-[200px] gap-y-1">
                        <span>{value}</span>
                        {form.formState.errors?.students?.[index]
                            ?.student_id && (
                            <span className="text-xs text-destructive">
                                Invalid student
                            </span>
                        )}
                    </div>
                );
            },
        },
        {
            key: "effective_from",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );

                return (
                    <div className="h-full">
                        <FreeDatePicker
                            control={form.control}
                            name={`students[${index}].effective_from`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.effective_from
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "effective_to",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="h-full">
                        <FreeDatePicker
                            control={form.control}
                            name={`students[${index}].effective_to`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.effective_to
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "_",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            removeStudent(index);
                            onRemove(cell.row.original?.id);
                        }}
                    />
                );
            },
        },
    ];

    function onAddStudent(selection) {
        const currentStudentNumbers = form
            .getValues()
            .students.map((student) => student.student_number);

        const newStudents = selection
            .filter(
                (student) =>
                    !currentStudentNumbers.includes(
                        student.student_number.toString()
                    )
            )
            .map((student) => ({
                id: student?.id,
                student_id: student?.id,
                student_number: student?.student_number,
                name: combinedNames(student?.translations?.name),
                effective_from: dateRange?.startDate,
                effective_to: dateRange?.endDate,
            }));

        const updatedRecords = form.getValues("students");
        setTargetsChunk(chunk(updatedRecords, CHUNKED_FILTER_PARAMS.per_page));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        onAdd(newStudents);
    }

    const { axiosPost: createScholarship, error: postError } = useAxios({
        api: scholarshipAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateScholarship, error: putError } = useAxios({
        api: scholarshipAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (data.application_open_at) {
                    data.application_open_at = toUTC(
                        data.application_open_at,
                        DATE_FORMAT.YMD_HMS
                    );
                }

                if (data.application_close_at) {
                    data.application_close_at = toUTC(
                        data.application_close_at,
                        DATE_FORMAT.YMD_HMS
                    );
                }

                data.is_active = data.is_active ? 1 : 0;
                data.is_internal = data.is_internal ? 1 : 0;

                data.students?.forEach((student: any) => {
                    student.effective_from = toYMD(student.effective_from);
                    student.effective_to = toYMD(student.effective_to);

                    if (!isCreate) {
                        const existingStudentIds =
                            currentScholarshipData.awards?.map(
                                (award: any) => award.student.id
                            ) || [];

                        if (!existingStudentIds.includes(student.student_id)) {
                            student.id = null;
                        }
                    }
                });

                if (isCreate) {
                    createScholarship(data);
                } else {
                    updateScholarship({ id: currentScholarshipData.id, data });
                }
            })
        );
    }

    useEffect(() => {
        const students = form.getValues().students;
        setTargetsChunk(chunk(students, CHUNKED_FILTER_PARAMS.per_page));
    }, [form.watch("students")]);

    useEffect(() => {
        if (dateRange)
            form.setValue(
                "students",
                form.getValues().students.map((student) => ({
                    ...student,
                    effective_from: dateRange?.startDate,
                    effective_to: dateRange?.endDate,
                }))
            );
    }, [dateRange]);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <>
            <h2 className="mb-2">
                {isCreate ? "Create" : "Update"} Scholarship
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="lg:min-w-[800px]">
                    <div className="grid-form pb-2">
                        {activeLanguages?.map((lang, index) => (
                            <FormInput
                                key={lang?.code || index}
                                control={form.control}
                                name={`name[${lang?.code}]`}
                                label={`Name in ${lang?.name}*`}
                            />
                        ))}

                        <FormInput
                            control={form.control}
                            name="award_body"
                            label="Award Body*"
                        />

                        <FormInput control={form.control} name="description" />

                        <DateTimePicker
                            control={form.control}
                            name="application_open_at"
                        />

                        <DateTimePicker
                            control={form.control}
                            name="application_close_at"
                        />

                        <div className="flex items-end justify-between lg:col-span-2">
                            <StatusFormSwitch
                                control={form.control}
                                name={"is_active"}
                                label="Status"
                            />
                            <FormCheckbox
                                control={form.control}
                                name="is_internal"
                                styleClass="items-end"
                                textStyleClass="items-end mb-0"
                            />
                        </div>
                    </div>

                    <FormDivider />

                    <div className="z-10 flex items-center gap-3 pb-3 pt-3">
                        <Button
                            variant={"outline"}
                            size={"smaller"}
                            onClick={() => setOpenStudentSearch(true)}
                        >
                            Select Students
                        </Button>
                        <div className="flex max-w-fit items-center">
                            <DateRangePicker
                                range={dateRange}
                                setRange={setDateRange}
                                isSmaller={true}
                                showSelection={false}
                                placeholder="Apply All Effective Date"
                            />
                        </div>

                        {targetsChunk?.length > 0 && (
                            <PaginatedTableFilter
                                type={STUDENT}
                                filter={filter}
                                setFilter={setFilter}
                                reset={resetClonedFilter}
                            />
                        )}
                    </div>

                    {targetsChunk?.length > 0 && pagination && (
                        <DataTable
                            isSmaller
                            columns={studentColumns}
                            data={
                                (clonedFilteredChunk ?? targetsChunk)[
                                    pagination?.current_page - 1
                                ]
                            }
                            pagination={pagination}
                            setPagination={setPagination}
                            changePage={(arg) =>
                                setFilter({ ...filter, ...arg })
                            }
                            hasPerPage={false}
                            sorted={filter?.order_by}
                            sort={onSort}
                            styleClass="overflow-visible"
                        />
                    )}

                    <div className="mt-6 flex justify-end gap-x-3">
                        <Button
                            type="submit"
                            disabled={isEmpty(form.getValues("students"))}
                        >
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>

            {/* add students */}
            <Modal
                open={openStudentSearch}
                onOpenChange={setOpenStudentSearch}
                size="large"
            >
                <StudentSearchEngine
                    isMultiSelect={true}
                    setSelection={(selection) => onAddStudent(selection)}
                    close={() => setOpenStudentSearch(false)}
                />
            </Modal>
        </>
    );
};

export default ScholarshipForm;
