import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    uomAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";

const FilterAccountingProductForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");
    const [uomOptions, setUomOptions] = useState<any[]>([]);

    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            code: filter?.code ?? "",
            category: filter?.category ?? "",
            sub_category_1: filter?.sub_category_1 ?? "",
            sub_category_2: filter?.sub_category_2 ?? "",
            uom_code: filter?.uom_code ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });

    const { axiosQuery: getUomOptions } = useAxios({
        api: uomAPI,
        locale,
        onSuccess: (response) => {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.translations.name?.[locale],
            }));
            setUomOptions(result);
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    useEffect(() => {
        getUomOptions({ params: { ...GET_ALL_PARAMS, is_active: 1 } });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"code"} />
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name="category"
                    isStringOptions={true}
                    options={[
                        "SCHOOL_FEES",
                        "HOSTEL_FEES",
                        "ENROLLMENT",
                        "OTHERS",
                    ]}
                />

                <FormInput control={form.control} name="sub_category_1" />
                <FormInput control={form.control} name="sub_category_2" />
                <FormSelect
                    control={form.control}
                    name="uom_code"
                    label="UOM"
                    options={uomOptions}
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: "Active",
                            id: "1",
                        },
                        {
                            name: "Inactive",
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterAccountingProductForm;
