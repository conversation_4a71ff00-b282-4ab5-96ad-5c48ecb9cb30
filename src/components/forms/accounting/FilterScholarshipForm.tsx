import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
} from "@/lib/constant";
import { toMyZonedTime, toUTC } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterScholarshipForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? undefined,
            application_open_at:
                toMyZonedTime(filter?.application_open_at) ?? undefined,
            application_close_at:
                toMyZonedTime(filter?.application_close_at) ?? undefined,
            is_active: filter?.is_active ?? undefined,
            is_internal: filter?.is_internal ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        if (data.application_open_at) {
            if (!data.application_close_at) {
                form.setError("application_close_at", {
                    type: "custom",
                    message:
                        "Application Close At is necessary when Application Open At is selected to filter by date range.",
                });
                return;
            }
            data.application_open_at = toUTC(
                data.application_open_at,
                DATE_FORMAT.YMD_HMS
            );
        }

        if (data.application_close_at) {
            if (!data.application_open_at) {
                form.setError("application_open_at", {
                    type: "custom",
                    message:
                        "Application Open At is necessary when Application Close At is selected to filter by date range.",
                });
                return;
            }
            data.application_close_at = toUTC(
                data.application_close_at,
                DATE_FORMAT.YMD_HMS
            );
        }

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <DateTimePicker
                    control={form.control}
                    name={"application_open_at"}
                    label="Application Open At"
                />

                <DateTimePicker
                    control={form.control}
                    name={"application_close_at"}
                    label="Application Close At"
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    isSortByName={false}
                    options={[
                        {
                            name: "Active",
                            id: "1",
                        },
                        {
                            name: "Inactive",
                            id: "0",
                        },
                    ]}
                />

                <FormSelect
                    control={form.control}
                    name="is_internal"
                    label="Internal"
                    isSortByName={false}
                    options={[
                        {
                            name: "Yes",
                            id: "1",
                        },
                        {
                            name: "No",
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterScholarshipForm;
