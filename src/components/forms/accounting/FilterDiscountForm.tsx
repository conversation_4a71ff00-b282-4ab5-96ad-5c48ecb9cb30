import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import FreeMonthPicker from "@/components/ui/FreeMonthPicker";
import {
    CommonFilterProps,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    FIXED,
    GET_ALL_PARAMS,
    glAccountAPI,
    PERCENT,
    scholarshipAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    convertDateToYearMonth,
    convertYearMonthToDate,
    toYMD,
} from "@/lib/utils";
import { Button } from "../../base-ui/button";

const FilterDiscountForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            basis: filter?.basis ?? undefined,
            effective_from: filter?.effective_from
                ? convertDateToYearMonth(filter?.effective_from)
                : undefined,
            effective_to: filter?.effective_to
                ? convertDateToYearMonth(filter?.effective_to)
                : undefined,
            gl_account_code: filter?.gl_account_code ?? undefined,
            source_type: filter?.source_type ?? undefined,
            scholarship_id: filter?.scholarship_id ?? undefined,
            source_id: filter?.source_id ?? undefined,
        },
    });
    const t = useTranslations("common");

    const locale = useLocale();
    const [glAccountOptions, setGlAccountOptions] = useState<any[]>([]);
    const [scholarshipStudentOptions, setScholarshipStudentOptions] = useState<
        any[]
    >([]);

    const { axiosQuery: getGlAccountOptions } = useAxios({
        api: glAccountAPI,
        locale,
        onSuccess: (response) => {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.label,
            }));
            setGlAccountOptions(result);
        },
        onError: close,
    });

    const { axiosQuery: getScholarshipStudents } = useAxios({
        api: scholarshipAPI,
        locale,
        onSuccess: (result) => {
            const students = result?.data?.awards?.map((item) => ({
                id: item?.id,
                name: `${combinedNames(item?.student?.translations?.name)} (${item?.student?.student_number})`,
            }));
            setScholarshipStudentOptions(students);
        },
    });

    const { data: scholarshipOptions, axiosQuery: getScholarshipOptions } =
        useAxios({
            api: scholarshipAPI,
            locale,
        });

    function onSubmit(data: Record<string, any>) {
        if (data.effective_from) {
            if (!data.effective_to) {
                form.setError("effective_to", {
                    type: "custom",
                    message:
                        "Effective Date (To) is necessary to filter effective date range.",
                });
                return;
            }

            data.effective_from = convertYearMonthToDate(
                data.effective_from,
                DATE_FORMAT.YMD
            );
        }

        if (data.effective_to) {
            if (!data.effective_from) {
                form.setError("effective_from", {
                    type: "custom",
                    message:
                        "Effective Date (From) is necessary to filter effective date range.",
                });
                return;
            }

            data.effective_to = convertYearMonthToDate(
                data.effective_to,
                DATE_FORMAT.YMD,
                true
            );
        }

        if (data.source_type) {
            if (data.source_type === "App\\Models\\ScholarshipAward") {
                if (!data.source_id) {
                    form.setError("source_id", {
                        type: "custom",
                        message:
                            "Scholarship Source is necessary when Source Type is present.",
                    });
                    return;
                }
            }
        }

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    useEffect(() => {
        if (form.watch("scholarship_id")) {
            getScholarshipStudents({ id: form.watch("scholarship_id") });
        }
    }, [form.watch("scholarship_id"), locale]);

    useEffect(() => {
        getGlAccountOptions({ params: { ...GET_ALL_PARAMS, is_active: 1 } });
        getScholarshipOptions({
            params: { ...GET_ALL_PARAMS },
        });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name="basis"
                    label="basis type"
                    options={[
                        {
                            id: FIXED,
                            name: "Fixed Amount",
                        },
                        {
                            id: PERCENT,
                            name: "Percentage",
                        },
                    ]}
                />

                <FreeMonthPicker
                    control={form.control}
                    name={"effective_from"}
                    label="Effective Date (From)"
                    error={form?.formState?.errors?.effective_from}
                />

                <FreeMonthPicker
                    control={form.control}
                    name={"effective_to"}
                    label="Effective Date (To)"
                    error={form?.formState?.errors?.effective_to}
                />

                <FormSelect
                    control={form.control}
                    name="gl_account_code"
                    options={glAccountOptions}
                />

                <FormSelect
                    control={form.control}
                    name="source_type"
                    options={[
                        {
                            id: "App\\Models\\ScholarshipAward",
                            name: "Scholarship",
                        },
                    ]}
                />

                {form.watch("source_type") ===
                    "App\\Models\\ScholarshipAward" && (
                    <FormSelect
                        control={form.control}
                        name="scholarship_id"
                        label="Scholarship"
                        options={scholarshipOptions}
                    />
                )}

                {form.watch("scholarship_id") && (
                    <FormSelect
                        control={form.control}
                        name="source_id"
                        label="Scholarship Award Student"
                        options={scholarshipStudentOptions}
                    />
                )}

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterDiscountForm;
