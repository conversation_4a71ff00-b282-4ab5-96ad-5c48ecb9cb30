import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { CommonFilterProps, PAID, UNPAID } from "@/lib/constant";
import { toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormSelect from "@/components/ui/FormSelect";
import { useTranslations } from "next-intl";

const FilterUnpaidItemForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            period_from: filter?.period_from ?? "",
            period_to: filter?.period_to ?? "",
            status: filter?.status ?? UNPAID,
        },
    });

    const [dateRange, setDateRange] = useState<any>(null);

    function onSubmit(data: Record<string, any>) {
        data.period_from = toYMD(dateRange?.startDate);
        data.period_to = toYMD(dateRange?.endDate);

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onReset() {
        form.reset();
        setFilter({
            period_from: "",
            period_to: "",
            status: UNPAID,
            page: 1,
            per_page: filter?.per_page,
        });
        close();
    }

    useEffect(() => {
        if (filter?.period_from || filter?.period_to) {
            setDateRange({
                key: "selection",
                startDate: filter?.period_from ?? "",
                endDate: filter?.period_to ?? "",
            });
        }
    }, [filter?.period_from, filter?.period_to]);

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="grid gap-y-5"
            >
                <FormSelect
                    control={form.control}
                    name="status"
                    options={[
                        { id: UNPAID, name: t(UNPAID) },
                        { id: PAID, name: t(PAID) },
                    ]}
                    isClearable={false}
                />

                <div className="flex-grow">
                    <DateRangePicker
                        label={"period"}
                        range={dateRange}
                        setRange={setDateRange}
                    />
                </div>

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onReset}>
                        {t("Reset")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterUnpaidItemForm;
