import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { isEmpty, uniqueId } from "lodash";
import { Plus, X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import FreeInput from "@/components/ui/FreeInput";
import FreeMonthPicker from "@/components/ui/FreeMonthPicker";
import {
    accountingProductAPI,
    currencyAPI,
    DATE_FORMAT,
    GET_ALL_PARAMS,
    ONE_TIME,
    TableColumnType,
    MONTHLY,
    monthPickerLang,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { convertDateToYearMonth, convertYearMonthToDate } from "@/lib/utils";

type RecurringSettingProps = {
    description: string;
    billing_date: { year: number; month: number };
};

type CreateFeeFormProps = {
    isCreate?: boolean;
    feeData?: any;
    close: () => void;
    submitFeeForm: (data: any) => void;
};

const CreateFeeForm = ({
    isCreate = false,
    feeData,
    close,
    submitFeeForm,
}: CreateFeeFormProps) => {
    return (
        <FormWrap
            isCreate={isCreate}
            feeData={feeData}
            submitFeeForm={submitFeeForm}
        />
    );
};

type FormWrapProps = {
    isCreate?: boolean;
    feeData: any;
    submitFeeForm: any;
};

type FormData = {
    product_id: string;
    name: string;
    currency_code: string;
    unit_price: string;
    quantity: number;
    apply_at: Date;
    occurrence: string;
    recurring_settings: RecurringSettingProps[];
};

const FormWrap = ({ isCreate, feeData, submitFeeForm }: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            product_id: "",
            name: "",
            currency_code: "MYR",
            unit_price: "",
            quantity: 1,
            apply_at: new Date(),
            occurrence: ONE_TIME,
            recurring_settings: [],
        },
    });

    const locale = useLocale();
    const [columns, setColumns] = useState<TableColumnType[]>([]);
    const [isInit, setIsInit] = useState(false);
    const [currencyOptions, setCurrencyOptions] = useState<any[]>([]);

    const { fields, append, remove } = useFieldArray<FormData>({
        control: form.control,
        name: "recurring_settings",
    });

    const {
        data: accountingProductOptions,
        axiosQuery: getAccountingProductOptions,
    } = useAxios({
        api: accountingProductAPI,
        locale,
        onError: close,
    });

    const { axiosQuery: getCurrencyOptions } = useAxios({
        api: currencyAPI,
        locale,
        onSuccess: (response) => {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.name,
            }));
            setCurrencyOptions(result);
        },
        onError: close,
    });

    const handleProductChange = (selectedProductId) => {
        if (!selectedProductId) {
            form.setValue("unit_price", "");
        } else {
            const productData = accountingProductOptions.find(
                (setting) => selectedProductId == setting.id
            );
            const productName = productData?.translations?.name?.[locale];

            form.setValue("name", productName);
            form.setValue("unit_price", productData.unit_price);

            const applyAt = form.watch("apply_at");

            const updatedRows = form.watch("recurring_settings").map((row) => {
                const billingDate = convertDateToYearMonth(
                    row.billing_date || applyAt
                );

                return {
                    ...row,
                    description: generateDescription(productName, billingDate),
                    billing_date: billingDate,
                };
            });

            form.setValue("recurring_settings", updatedRows, {
                shouldDirty: true,
            });
        }
    };

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "number",
                displayAs: "No.",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    return (
                        <span className="text-center text-[14px]">
                            {index + 1}
                        </span>
                    );
                },
            },
            {
                key: "description",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    return (
                        <FreeInput
                            isDecimal={false}
                            hasLabel={false}
                            control={form.control}
                            name={`recurring_settings[${index}].description`}
                            error={
                                form.formState.errors?.recurring_settings?.[
                                    index
                                ]
                            }
                            onChange={() =>
                                setTimeout(() => {
                                    form.setFocus(
                                        `recurring_settings[${index}].description`
                                    );
                                }, 1)
                            }
                        />
                    );
                },
            },
            {
                key: "billing_date",
                modify: (_, cell) => {
                    const index = cell?.row?.index;
                    const applyAt = new Date(form.watch("apply_at"));
                    const firstDayOfMonth = new Date(
                        applyAt.getFullYear(),
                        applyAt.getMonth(),
                        1
                    );

                    return (
                        <div className="h-full min-w-[150px]">
                            <FreeMonthPicker
                                hasLabel={false}
                                control={form.control}
                                name={`recurring_settings[${index}].billing_date`}
                                error={
                                    form.formState.errors?.recurring_settings?.[
                                        index
                                    ]?.billing_date
                                }
                                disableDateBefore={firstDayOfMonth}
                            />
                        </div>
                    );
                },
            },
            {
                key: "_",
                modify: (_, cell) => {
                    const index = cell.row.index;
                    return (
                        <div className="flex h-full items-center">
                            <div
                                className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                                onClick={() => {
                                    remove(index);
                                    form.setValue("occurrence", "");
                                }}
                            >
                                <X size={14} className="text-themeGreenDark" />
                            </div>
                        </div>
                    );
                },
            },
        ];
        setColumns(_columns);
    }

    const getBillingDate = (applyAt) => {
        const applyDate = new Date(applyAt);
        const year = applyDate.getFullYear();
        const month = applyDate.getMonth() + 1;

        return { year, month };
    };

    const generateDescription = (name, billingDate) => {
        return `${name || ""} ${monthPickerLang.months[billingDate.month - 1]} ${billingDate.year}`;
    };

    const handleAddMore = () => {
        form.setValue("occurrence", "");

        const applyAt = form.getValues("apply_at");
        const billingDate = applyAt
            ? getBillingDate(applyAt)
            : {
                  year: new Date().getFullYear(),
                  month: new Date().getMonth() + 1,
              };

        append({
            description: "",
            billing_date: billingDate,
        });
    };

    const handleClearAll = () => {
        form.setValue("recurring_settings", []);
        form.setValue("occurrence", "");
        remove();
    };

    function onChangeFeeName(name) {
        const applyAt = form.watch("apply_at");

        const updatedRows = form.watch("recurring_settings").map((row) => {
            const billingDate = convertDateToYearMonth(
                row.billing_date || applyAt
            );

            return {
                ...row,
                description: generateDescription(name, billingDate),
                billing_date: billingDate,
            };
        });

        form.setValue("recurring_settings", updatedRows, { shouldDirty: true });
    }

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                let hasError = false;
                const requiredFields: (keyof FormData)[] = [
                    "name",
                    "product_id",
                    "currency_code",
                    "unit_price",
                    "quantity",
                    "apply_at",
                    "recurring_settings",
                ];

                function setFieldError(field: keyof FormData) {
                    hasError = true;
                    form.setError(field, {
                        type: "manual",
                        message: "This field is required",
                    });
                }

                requiredFields.forEach((key) => {
                    if (key === "recurring_settings") {
                        if (form.getValues(key).length < 1) {
                            setFieldError(key);
                        }
                    } else if (!data[key]) {
                        setFieldError(key);
                    }
                });

                if (hasError) return;

                const formattedData = {
                    ...data,
                    id: Number(feeData ? feeData?.id : uniqueId()),
                    recurring_settings: data.recurring_settings.map(
                        (setting) => ({
                            ...setting,
                            billing_date: convertYearMonthToDate(
                                setting.billing_date,
                                DATE_FORMAT.YMD
                            ),
                        })
                    ),
                };

                submitFeeForm(formattedData);
            })
        );
    }

    useEffect(() => {
        if (feeData) {
            const recurringSettings = feeData.recurring_settings.map(
                (setting) => {
                    const billingDate = convertDateToYearMonth(
                        setting.billing_date
                    );
                    return {
                        description: setting.description,
                        billing_date: billingDate,
                    };
                }
            );

            form.reset({
                product_id: feeData.product_id,
                name: feeData.name,
                currency_code: feeData.currency_code || "MYR",
                unit_price: feeData.unit_price,
                quantity: feeData.quantity,
                apply_at: feeData.apply_at,
                occurrence: feeData.occurrence || "",
                recurring_settings: recurringSettings,
            });
        }
    }, [feeData]);

    useEffect(() => {
        if (!isCreate && !isInit) {
            setIsInit(true);
            return;
        }
        const occurrence = form.watch("occurrence");
        const applyAt = form.watch("apply_at");
        const name = form.watch("name");

        if (!applyAt) return;

        const billingDate = getBillingDate(applyAt);

        if (occurrence == ONE_TIME) {
            form.setValue("recurring_settings", [
                {
                    description: "",
                    billing_date: billingDate,
                },
            ]);
        }

        if (occurrence === MONTHLY) {
            form.setValue("recurring_settings", [
                {
                    description: generateDescription(name, billingDate),
                    billing_date: billingDate,
                },
            ]);
            const initialDate = new Date(applyAt);

            for (let i = 1; i < 12; i++) {
                const nextDate = new Date(initialDate);
                nextDate.setMonth(initialDate.getMonth() + i);
                const nextBillingDate = getBillingDate(nextDate);
                append({
                    description: generateDescription(name, nextBillingDate),
                    billing_date: nextBillingDate,
                });
            }
        }
    }, [form.watch("occurrence")]);

    useEffect(() => {
        defineColumn();
    }, []);

    useEffect(() => {
        getCurrencyOptions({ params: { ...GET_ALL_PARAMS, is_active: 1 } });
        getAccountingProductOptions({
            params: { ...GET_ALL_PARAMS, is_active: 1 },
        });
    }, []);

    return (
        <>
            <h2 className="mb-2">Add Fee</h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <FormSelect
                        control={form.control}
                        name="product_id"
                        label="Product*"
                        options={accountingProductOptions}
                        onChange={(selected) => handleProductChange(selected)}
                    />

                    <FormInput
                        control={form.control}
                        name="name"
                        label="Fee Name*"
                        onChange={onChangeFeeName}
                    />

                    <FormSelect
                        control={form.control}
                        name="currency_code"
                        label="Currency*"
                        options={currencyOptions}
                    />

                    <FormInputDecimal
                        control={form.control}
                        name="unit_price"
                        label="Unit Price*"
                    />

                    <FormInputInterger
                        control={form.control}
                        name="quantity"
                        label="Quantity*"
                        min={1}
                    />

                    <DateTimePicker
                        control={form.control}
                        name={"apply_at"}
                        label="Apply At*"
                        disableDateBeforeToday={true}
                    />

                    <FormSelect
                        control={form.control}
                        name="occurrence"
                        isSortByName={false}
                        options={[
                            { id: ONE_TIME, name: "One Time" },
                            { id: MONTHLY, name: "Monthly (12 months)" },
                        ]}
                    />

                    <div className="mt-3 border-t border-dashed pt-3 lg:col-span-2">
                        <div className="flex items-center gap-5">
                            <h3 className="ml-0.5 text-themeGreenDark">
                                Recurring Payment List*
                            </h3>
                            <div className="ml-auto flex justify-end gap-x-3">
                                <Button
                                    variant={"outline"}
                                    size={"smaller"}
                                    className="flex gap-x-1 pl-4"
                                    onClick={handleAddMore}
                                >
                                    <Plus
                                        size={18}
                                        className="-ml-2 text-themeGreen"
                                    />{" "}
                                    <span>Add</span>
                                </Button>

                                <Button
                                    variant={"outlineGray"}
                                    size={"smaller"}
                                    className="flex gap-x-1 pl-4 text-gray-500"
                                    onClick={handleClearAll}
                                >
                                    <X
                                        size={18}
                                        className="-ml-2 text-gray-500"
                                    />{" "}
                                    <span>Clear All</span>
                                </Button>
                            </div>
                        </div>

                        <div className="mb-5 mt-3 lg:col-span-2">
                            <DataTable
                                columns={columns}
                                data={fields.map((field, index) => ({
                                    ...field,
                                    index,
                                }))}
                            />
                            {form.formState.errors?.recurring_settings
                                ?.message && (
                                <p className="warning-text mb-3 mt-2">
                                    {`${form.formState.errors?.recurring_settings?.message}`}
                                </p>
                            )}
                        </div>
                        <Button type="submit" className="ml-auto mt-4">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default CreateFeeForm;
