import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    accountingProductAPI,
    APPLYING,
    CommonFilterProps,
    COMPLETED,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    NEW,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { toUTC } from "@/lib/utils";
import { Button } from "../../base-ui/button";

const FilterFeeForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            name: filter?.name ?? undefined,
            product_id: filter?.product_id ?? undefined,
            apply_at_from: filter?.apply_at_from ?? undefined,
            apply_at_to: filter?.apply_at_to ?? undefined,
            status: filter?.status ?? undefined,
        },
    });

    const {
        data: accountingProductOptions,
        axiosQuery: getAccountingProductOptions,
    } = useAxios({
        api: accountingProductAPI,
        locale,
    });

    function onSubmit(data: Record<string, any>) {
        if (data.apply_at_from) {
            if (!data.apply_at_to) {
                form.setError("apply_at_to", {
                    type: "custom",
                    message:
                        "Apply at (To) is necessary when Apply at (From) is selected to filter by date range.",
                });
                return;
            }
            data.apply_at_from = toUTC(data.apply_at_from, DATE_FORMAT.YMD_HMS);
        }

        if (data.apply_at_to) {
            if (!data.apply_at_from) {
                form.setError("apply_at_from", {
                    type: "custom",
                    message:
                        "Apply at (From) is necessary when Apply at (To) is selected to filter by date range.",
                });
                return;
            }
            data.apply_at_to = toUTC(data.apply_at_to, DATE_FORMAT.YMD_HMS);
        }

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    useEffect(() => {
        getAccountingProductOptions({
            params: { ...GET_ALL_PARAMS },
        });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormSelect
                    control={form.control}
                    name="product_id"
                    label="Product"
                    options={accountingProductOptions}
                />

                <DateTimePicker
                    control={form.control}
                    name={"apply_at_from"}
                    label="Apply At (From)"
                />

                <DateTimePicker
                    control={form.control}
                    name={"apply_at_to"}
                    label="Apply At (To)"
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[NEW, APPLYING, COMPLETED]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterFeeForm;
