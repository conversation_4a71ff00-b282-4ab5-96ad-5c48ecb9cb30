import { useLocale } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import clsx from "clsx";
import { isEmpty, lowerCase } from "lodash";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormDivider from "@/components/ui/FormDivider";
import FreeMonthPicker from "@/components/ui/FreeMonthPicker";
import {
    ACTIVE,
    appCurrencySymbol,
    CommonFormProps,
    DATE_FORMAT,
    discountAPI,
    discountBulkUpdateAPI,
    FIXED,
    INACTIVE,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useUserProfile } from "@/lib/store";
import {
    combinedNames,
    convertDateToYearMonth,
    convertYearMonthToDate,
    replaceAll,
} from "@/lib/utils";
import { Label } from "@/components/base-ui/label";

const IconProfilePhotoPlaceholder = "/icons/icon-profile-photo-placeholder.svg";

const DiscountForm = (props: CommonFormProps) => {
    const locale = useLocale();
    const { data: currentDiscountData, axiosQuery: getDiscount } = useAxios({
        api: discountAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getDiscount({ id: props.id });
        }
    }, []);

    return currentDiscountData ? (
        <FormWrap currentDiscountData={currentDiscountData} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentDiscountData: any;
};

const FormWrap = ({ currentDiscountData, refresh, close }: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            effective_from: null,
            effective_to: null,
        },
    });

    const locale = useLocale();
    const hasPermit = useUserProfile((state) => state.hasPermit);
    const [isLoadingPhoto, setIsLoadingPhoto] = useState(true);

    const { axiosPut: updateDiscount, error: putError } = useAxios({
        api: discountBulkUpdateAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                const transformToUpdateDiscountPayload = (formData) => {
                    return {
                        discounts: [
                            {
                                discount_id: currentDiscountData.id,
                                effective_from: convertYearMonthToDate(
                                    formData.effective_from,
                                    DATE_FORMAT.YMD
                                ),
                                effective_to: convertYearMonthToDate(
                                    formData.effective_to,
                                    DATE_FORMAT.YMD,
                                    true
                                ),
                            },
                        ],
                    };
                };
                updateDiscount({
                    data: transformToUpdateDiscountPayload(data),
                    showErrorInToast: true,
                });
            })
        );
    }

    useEffect(() => {
        if (currentDiscountData?.effective_from) {
            const effectiveFrom = convertDateToYearMonth(
                currentDiscountData?.effective_from
            );
            form.setValue("effective_from", effectiveFrom);
        }
        if (currentDiscountData?.effective_to) {
            const effectiveTo = convertDateToYearMonth(
                currentDiscountData?.effective_to
            );

            form.setValue("effective_to", effectiveTo);
        }
    }, [
        currentDiscountData?.effective_from,
        currentDiscountData?.effective_to,
    ]);

    function basicInformation() {
        const userInfo = currentDiscountData?.userable;
        return {
            name: combinedNames(userInfo?.translations?.name),
            student_number: userInfo?.number,
            class: combinedNames(userInfo?.current_primary_class?.class),
            status: lowerCase(userInfo?.is_active ? ACTIVE : INACTIVE),
            email: userInfo?.email ?? "-",
            phone_number: userInfo?.phone_number ?? "-",
        };
    }

    function formattedData(data) {
        return {
            basis_type: data?.basis == FIXED ? "Fixed Amount" : "Percentage",
            basis_amount: `${appCurrencySymbol} ${data?.basis_amount}`,
            max_amount: `${appCurrencySymbol} ${data?.max_amount}`,
            gl_account_codes: data?.gl_account_codes.join(", "),
            source:
                data?.source?.scholarship?.translations?.name?.[locale] ?? "-",
            description: data?.description ?? "-",
        };
    }

    return (
        <div>
            <h2 className="mb-5">Discount Details</h2>

            <div className="flex flex-col pb-2 lg:flex-row">
                <div className="relative w-[100px]">
                    {isLoadingPhoto && (
                        <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                            <Loader2
                                className="animate-spin text-themeGray"
                                size={30}
                            />
                        </div>
                    )}
                    {currentDiscountData?.userable?.photo ? (
                        <Image
                            src={currentDiscountData?.userable?.photo}
                            alt="Profile Photo"
                            width={100}
                            height={100}
                            className={clsx(
                                "h-auto w-auto max-w-[100px] rounded-sm",
                                isLoadingPhoto && "opacity-0"
                            )}
                            onLoad={() => {
                                setIsLoadingPhoto(false);
                            }}
                            onError={() => setIsLoadingPhoto(false)}
                        />
                    ) : (
                        <Image
                            src={IconProfilePhotoPlaceholder}
                            alt="Profile Photo"
                            width={100}
                            height={100}
                            className="h-auto w-auto max-w-[100px] rounded-sm"
                            unoptimized
                        />
                    )}
                </div>

                <div className="mt-2 grid flex-grow gap-3 lg:mt-0 lg:grid-cols-2 lg:pl-8">
                    {Object.entries(basicInformation()).map(
                        ([key, value], index) => (
                            <Fragment key={key}>
                                <div className="flex flex-col">
                                    <Label className="mb-1 capitalize">
                                        {replaceAll(
                                            key === "nric" ? "NRIC" : key,
                                            "_",
                                            " "
                                        )}
                                    </Label>
                                    <p
                                        className={clsx(
                                            "text-themeText font-medium",
                                            key === "status" &&
                                                `cell-status ${value}`
                                        )}
                                    >
                                        {isEmpty(value?.toString())
                                            ? "-"
                                            : value}
                                    </p>
                                </div>
                            </Fragment>
                        )
                    )}
                </div>
            </div>

            <FormDivider />

            <Form {...form}>
                <form
                    className="pb-5 lg:min-w-[800px] lg:px-2"
                    onSubmit={onSubmit}
                >
                    <div className="mb-5 mt-4 gap-x-3 gap-y-4">
                        <div className="grid gap-x-3 gap-y-3.5 lg:grid-cols-2">
                            {Object.entries(
                                formattedData(currentDiscountData)
                            ).map(([key, value], index) => (
                                <Fragment key={key}>
                                    <div className="flex flex-col">
                                        <Label className="capitalize">
                                            {key.replaceAll("_", " ")}
                                        </Label>
                                        <p
                                            className={clsx(
                                                "text-themeText mt-1 w-full max-w-sm font-medium"
                                            )}
                                        >
                                            {isEmpty(value?.toString())
                                                ? "-"
                                                : value}
                                        </p>
                                    </div>
                                </Fragment>
                            ))}
                            <div className="lg:col-span-2"></div>
                            <FreeMonthPicker
                                control={form.control}
                                name="effective_from"
                                label="Effective Date (From)"
                            />
                            <FreeMonthPicker
                                control={form.control}
                                name="effective_to"
                                label="Effective Date (To)"
                            />
                        </div>
                    </div>

                    {hasPermit("discount-update") && (
                        <div className="mt-6 flex justify-end gap-x-3">
                            <Button type="submit">Save</Button>
                        </div>
                    )}
                </form>
            </Form>
        </div>
    );
};

export default DiscountForm;
