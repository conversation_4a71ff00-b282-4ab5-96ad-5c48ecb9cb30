import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { indexOf, isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import FormSelect from "@/components/ui/FormSelect";
import { GET_ALL_PARAMS, SCHOLARSHIP, scholarshipAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { combinedNames, getTableSelection } from "@/lib/utils";

type FormWrapProps = {
    setSelection: (value: any) => void;
    close: () => void;
};

const DiscountSourceForm = ({ setSelection, close }: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            source_type: SCHOLARSHIP,
            scholarship_id: "",
            students: [],
        },
    });

    const locale = useLocale();
    const [selectedScholarship, setSelectedScholarship] = useState<any>();
    const [selectedStudents, setSelectedStudents] = useState<any[]>([]);
    const [hasSearched, setHasSearched] = useState(false);

    const { data: scholarshipOptions, axiosQuery: getScholarshipOptions } =
        useAxios({
            api: scholarshipAPI,
            locale,
        });

    useEffect(() => {
        getScholarshipOptions({
            params: { ...GET_ALL_PARAMS, is_active: 1 },
        });
    }, []);

    const studentColumns = [
        {
            key: "student_number",
        },
        {
            key: "name",
            displayAs: "student name",
            modify: (value, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="grid min-w-[200px] gap-y-1">
                        <span>{value}</span>
                        {form.formState.errors?.students?.[index]
                            ?.student_id && (
                            <span className="text-xs text-destructive">
                                Invalid student
                            </span>
                        )}
                    </div>
                );
            },
        },
    ];

    const { axiosQuery: getScholarshipStudents } = useAxios({
        api: scholarshipAPI,
        locale,
        onSuccess: (result) => {
            const students = result?.data?.awards?.map((item) => ({
                id: item?.student?.id,
                source_id: item?.id,
                student_number: item?.student?.student_number,
                name: combinedNames(item?.student?.translations?.name),
            }));
            form.setValue("students", students);
            setHasSearched(true);
        },
    });

    function onFilter(e) {
        e.preventDefault();
        getScholarshipStudents({ id: form.getValues("scholarship_id") });
    }

    function onMultiSelect(selectedIds: number[]) {
        const _selection = getTableSelection(
            selectedIds,
            form.getValues().students
        );
        setSelectedStudents(_selection);
    }

    function onConfirmStudents() {
        setSelection(selectedStudents);
        close();
    }

    useEffect(() => {
        if (!form.watch("scholarship_id") || !form.watch("source_type")) {
            setHasSearched(false);
            form.setValue("students", []);
            setSelectedStudents([]);
        }
    }, [form.watch("scholarship_id"), form.watch("source_type")]);

    return (
        <>
            <h2 className="mb-2">Select Discount Source</h2>
            <Form {...form}>
                <form className="min-h-[220px] lg:min-w-[650px]">
                    <div className="mb-5 grid items-end gap-x-3 gap-y-4 lg:flex">
                        <div className="flex-grow">
                            <FormSelect
                                control={form.control}
                                name={"source_type"}
                                label={"Source Type*"}
                                isStringOptions={true}
                                options={[SCHOLARSHIP]}
                            />
                        </div>
                        <div className="flex-grow">
                            <FormSelect
                                control={form.control}
                                name={"scholarship_id"}
                                label={"Scholarship"}
                                options={scholarshipOptions}
                            />
                        </div>
                        <Button
                            variant={"outline"}
                            type="submit"
                            onClick={onFilter}
                            disabled={
                                !form.watch("source_type") ||
                                !form.watch("scholarship_id")
                            }
                        >
                            Filter
                        </Button>
                    </div>

                    {hasSearched ? (
                        isEmpty(form.getValues("students")) ? (
                            <div className="h-20 text-center text-themeLabel">
                                No Record Found
                            </div>
                        ) : (
                            <>
                                <DataTable
                                    isSmaller
                                    onMultiSelect={onMultiSelect}
                                    columns={studentColumns}
                                    data={form.watch("students")}
                                    styleClass="overflow-visible mt-5"
                                />
                                <Button
                                    className="ml-auto mt-5"
                                    disabled={isEmpty(
                                        form.getValues("students")
                                    )}
                                    onClick={onConfirmStudents}
                                >
                                    Apply
                                </Button>
                            </>
                        )
                    ) : null}
                </form>
            </Form>
        </>
    );
};

export default DiscountSourceForm;
