import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormSelect from "@/components/ui/FormSelect";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import {
    accountingProductAPI,
    appCurrencySymbol,
    CommonFormProps,
    GET_ALL_PARAMS,
    glAccountAPI,
    uomAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages, useUserProfile } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const AccountingProductForm = (props: CommonFormProps) => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const hasPermit = useUserProfile((state) => state.hasPermit);

    const [glAccountOptions, setGlAccountOptions] = useState<any[]>([]);
    const [uomOptions, setUomOptions] = useState<any[]>([]);

    const { data: currentProduct, axiosQuery: getCurrentProduct } = useAxios({
        api: accountingProductAPI,
        locale,
        onError: props.close,
    });

    const { axiosQuery: getGlAccountOptions } = useAxios({
        api: glAccountAPI,
        locale,
        onSuccess: (response) => {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.label,
            }));
            setGlAccountOptions(result);
        },
        onError: props.close,
    });

    const { axiosQuery: getUomOptions } = useAxios({
        api: uomAPI,
        locale,
        onSuccess: (response) => {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.translations.name?.[locale],
            }));
            setUomOptions(result);
        },
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCurrentProduct({ id: props.id });
        }
        if (hasPermit("master-gl-account-view")) {
            getGlAccountOptions({
                params: { ...GET_ALL_PARAMS, is_active: 1 },
            });
        }
        if (hasPermit("master-uom-view")) {
            getUomOptions({ params: { ...GET_ALL_PARAMS, is_active: 1 } });
        }
    }, []);

    return activeLanguages &&
        glAccountOptions &&
        uomOptions &&
        (props.isCreate || currentProduct) ? (
        <FormWrap
            activeLanguages={activeLanguages}
            glAccountOptions={glAccountOptions}
            uomOptions={uomOptions}
            product={currentProduct}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    activeLanguages: Array<Record<string, any>>;
    glAccountOptions: any;
    uomOptions: any;
    product: any;
};

const FormWrap = ({
    isCreate = false,
    activeLanguages,
    glAccountOptions,
    uomOptions,
    product,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: product?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            code: product?.code ?? "",
            category: product?.category ?? "",
            sub_category_1: product?.sub_category_1 ?? "",
            sub_category_2: product?.sub_category_2 ?? "",
            uom_code: product?.uom_code ?? "",
            gl_account_code: product?.gl_account_code ?? "",
            unit_price: product?.unit_price ?? "",
            description: product?.description ?? "",
            is_active: isCreate ? true : product?.is_active ?? false,
        },
    });

    const { axiosPost: createProduct, error: postError } = useAxios({
        api: accountingProductAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateProduct, error: putError } = useAxios({
        api: accountingProductAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();

        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (isCreate) {
                    createProduct(data);
                } else {
                    updateProduct({ id: product.id, data });
                }
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <>
            <h2 className="mb-2">{isCreate ? "Create" : "Update"} Product</h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}

                    <FormInput
                        control={form.control}
                        name="code"
                        label="Code*"
                    />

                    <FormSelect
                        control={form.control}
                        name="category"
                        label="Category*"
                        isStringOptions={true}
                        options={[
                            "SCHOOL_FEES",
                            "HOSTEL_FEES",
                            "ENROLLMENT",
                            "OTHERS",
                        ]}
                    />

                    <FormInput
                        control={form.control}
                        name="sub_category_1"
                        label="Sub Category 1"
                    />

                    <FormInput
                        control={form.control}
                        name="sub_category_2"
                        label="Sub Category 2"
                    />

                    <FormSelect
                        control={form.control}
                        name="uom_code"
                        label="UOM*"
                        options={uomOptions}
                    />

                    <FormSelect
                        control={form.control}
                        name="gl_account_code"
                        label="GL Account*"
                        options={glAccountOptions}
                    />

                    <FormInputDecimal
                        control={form.control}
                        name="unit_price"
                        label={`Unit Price (${appCurrencySymbol})*`}
                    />

                    <FormInput
                        control={form.control}
                        name="description"
                        label="Description"
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="Status"
                    />

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default AccountingProductForm;
