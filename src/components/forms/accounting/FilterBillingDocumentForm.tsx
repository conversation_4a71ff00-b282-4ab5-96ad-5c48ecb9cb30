import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormSearchInput from "@/components/ui/FormSearchInput";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import GuardianSearchEngine from "@/components/ui/search-engines/GuardianSearchEngine";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    ADVANCE_INVOICE,
    CommonFilterProps,
    CONFIRMED,
    CREDIT_NOTE,
    DATE_FORMAT,
    DEBIT_NOTE,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    ECOMMERCE,
    EMPLOYEE,
    ENROLLMENT_EXAM_FEES,
    ENROLLMENT_FEES,
    FEES,
    GUARDIAN,
    HOSTEL_SAVINGS_ACCOUNT,
    INVOICE,
    OTHERS,
    PAID,
    PARTIAL,
    POSTED,
    STUDENT,
    UNPAID,
    VOIDED,
    WALLET,
} from "@/lib/constant";
import { optionUserLabel, toMyZonedTime, toUTC, toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterBillingDocumentForm = ({
    filter,
    setFilter,
    close,
    hideBillTo,
}: CommonFilterProps & { hideBillTo?: boolean }) => {
    const form = useForm({
        defaultValues: {
            bill_to_type: filter?.bill_to_type ?? undefined,
            bill_to_id: filter?.bill_to_id ?? undefined,
            bill_to_name: filter?.bill_to_name ?? undefined,
            type: filter?.type ?? undefined,
            sub_type: filter?.sub_type ?? undefined,
            paid_at_from: toMyZonedTime(filter?.paid_at_from) ?? undefined,
            paid_at_to: toMyZonedTime(filter?.paid_at_to) ?? undefined,
            reference_no: filter?.reference_no ?? undefined,
            document_date_from: filter?.document_date_from ?? undefined,
            document_date_to: filter?.document_date_to ?? undefined,
            payment_status: filter?.payment_status ?? undefined,
            status: filter?.status ?? undefined,
            payment_reference_no: filter?.payment_reference_no ?? undefined,
        },
    });
    const t = useTranslations("common");

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [openEmployeeSearch, setOpenEmployeeSearch] = useState(false);
    const [openGuardianSearch, setOpenGuardianSearch] = useState(false);

    const [documentDateRange, setDocumentDateRange] = useState<any>(null);

    function onSubmit(data: Record<string, any>) {
        if (data.paid_at_from) {
            if (!data.paid_at_to) {
                form.setError("paid_at_to", {
                    type: "custom",
                    message:
                        "Paid at (To) is necessary to filter payment date range.",
                });
                return;
            }
            data.paid_at_from = toUTC(data.paid_at_from, DATE_FORMAT.YMD_HMS);
        }

        if (data.paid_at_to) {
            if (!data.paid_at_from) {
                form.setError("paid_at_from", {
                    type: "custom",
                    message:
                        "Paid at (From) is necessary to filter payment date range.",
                });
                return;
            }
            data.paid_at_to = toUTC(data.paid_at_to, DATE_FORMAT.YMD_HMS);
        }

        if (data.document_date_from) {
            data.document_date_from = toYMD(data.document_date_from);
        }

        if (data.document_date_to) {
            data.document_date_to = toYMD(data.document_date_to);
        }

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    useEffect(() => {
        if (filter?.document_date_from || filter?.document_date_to) {
            setDocumentDateRange({
                key: "selection",
                startDate: filter?.document_date_from ?? "",
                endDate: filter?.document_date_to ?? "",
            });
        }
    }, [filter?.document_date_from, filter?.document_date_to]);

    useEffect(() => {
        if (documentDateRange?.startDate) {
            form.setValue("document_date_from", documentDateRange.startDate);
        } else {
            form.setValue("document_date_from", "");
        }

        if (documentDateRange?.endDate) {
            form.setValue("document_date_to", documentDateRange.endDate);
        } else {
            form.setValue("document_date_to", "");
        }
    }, [documentDateRange, form]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-1 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                {!hideBillTo && (
                    <>
                        <FormSelect
                            control={form.control}
                            name={"bill_to_type"}
                            isStringOptions={true}
                            options={[STUDENT, EMPLOYEE, GUARDIAN]}
                            onChange={() => {
                                form.setValue("bill_to_id", "");
                                form.setValue("bill_to_name", "");
                            }}
                        />

                        <FormSearchInput
                            control={form.control}
                            name="bill_to_id"
                            label="Bill to"
                            displayValue={form.watch("bill_to_name") ?? null}
                            onClick={() => {
                                if (form.watch("bill_to_type") === STUDENT)
                                    setOpenStudentSearch(true);
                                if (form.watch("bill_to_type") === EMPLOYEE)
                                    setOpenEmployeeSearch(true);
                                if (form.watch("bill_to_type") === GUARDIAN)
                                    setOpenGuardianSearch(true);
                            }}
                        />
                    </>
                )}

                <FormSelect
                    control={form.control}
                    name={"type"}
                    isStringOptions={true}
                    options={[
                        INVOICE,
                        ADVANCE_INVOICE,
                        CREDIT_NOTE,
                        DEBIT_NOTE,
                    ]}
                />

                <FormSelect
                    control={form.control}
                    name={"sub_type"}
                    isStringOptions={true}
                    options={[
                        FEES,
                        WALLET,
                        ECOMMERCE,
                        HOSTEL_SAVINGS_ACCOUNT,
                        ENROLLMENT_FEES,
                        OTHERS,
                    ]}
                />

                <FormSelect
                    control={form.control}
                    name={"payment_status"}
                    isStringOptions={true}
                    options={[UNPAID, PAID, PARTIAL]}
                />

                <FormSelect
                    control={form.control}
                    name={"status"}
                    isStringOptions={true}
                    options={[DRAFT, CONFIRMED, VOIDED, POSTED]}
                />

                <DateTimePicker
                    control={form.control}
                    name={"paid_at_from"}
                    label="Paid At (From)"
                />

                <DateTimePicker
                    control={form.control}
                    name={"paid_at_to"}
                    label="Paid At (To)"
                />

                <FormInput control={form.control} name={"reference_no"} />
                <FormInput
                    control={form.control}
                    name={"payment_reference_no"}
                />

                <div className="flex-grow">
                    <DateRangePicker
                        label={"document date range"}
                        range={documentDateRange}
                        setRange={setDocumentDateRange}
                    />
                </div>

                <Modal
                    open={openStudentSearch}
                    onOpenChange={setOpenStudentSearch}
                    size="large"
                >
                    <StudentSearchEngine
                        setSelection={(student) => {
                            if (student) {
                                form.setValue("bill_to_id", student.id);
                                form.setValue("bill_to_type", STUDENT);
                                form.setValue(
                                    "bill_to_name",
                                    optionUserLabel(
                                        student?.student_number,
                                        student?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenStudentSearch(false)}
                    />
                </Modal>

                <Modal
                    open={openEmployeeSearch}
                    onOpenChange={setOpenEmployeeSearch}
                    size="large"
                >
                    <StaffSearchEngine
                        setSelection={(employee) => {
                            if (employee) {
                                form.setValue("bill_to_id", employee.id);
                                form.setValue("bill_to_type", EMPLOYEE);
                                form.setValue(
                                    "bill_to_name",
                                    optionUserLabel(
                                        employee?.employee_number,
                                        employee?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenEmployeeSearch(false)}
                    />
                </Modal>

                <Modal
                    open={openGuardianSearch}
                    onOpenChange={setOpenGuardianSearch}
                    size="large"
                >
                    <GuardianSearchEngine
                        setSelection={(guardian) => {
                            if (guardian) {
                                form.setValue("bill_to_id", guardian.id);
                                form.setValue("bill_to_type", GUARDIAN);
                                form.setValue(
                                    "bill_to_name",
                                    optionUserLabel(
                                        guardian?.nric,
                                        guardian?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenGuardianSearch(false)}
                    />
                </Modal>

                <div className="filter-duo-buttons lg:col-span-2">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterBillingDocumentForm;
