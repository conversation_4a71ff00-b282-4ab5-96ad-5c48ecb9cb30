import { useLocale } from "next-intl";
import { useEffect } from "react";
import clsx from "clsx";
import { format } from "date-fns";
import { capitalize, isArray, isEmpty, lowerCase } from "lodash";
import DataTable from "@/components/ui/DataTable";
import InfoCard from "@/components/ui/InfoCard";
import {
    accountingFeeAPI,
    ACTIVE,
    appCurrencySymbol,
    DATE_FORMAT,
    INACTIVE,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    convertDateTime,
    displayDateTime,
    formatNumberForRead,
    showInteger,
} from "@/lib/utils";

const ViewFee = ({ id, close }) => {
    const locale = useLocale();

    const { data: fee, axiosQuery: getUnpaidItemAssignment } = useAxios({
        api: accountingFeeAPI,
        locale,
        onError: close,
    });

    useEffect(() => {
        getUnpaidItemAssignment({ id });
    }, []);

    function formattedData(data) {
        return {
            status: (
                <span
                    className={clsx(
                        data?.status === "COMPLETED" && "text-green-600",
                        data?.status === "APPLYING" && "text-yellow-500"
                    )}
                >
                    {capitalize(data?.status)}
                </span>
            ),
            name: data?.name,
            product:
                data?.product?.translations?.name?.[locale] ??
                data?.product?.name,
            unit_price: data?.unit_price
                ? `${appCurrencySymbol} ${formatNumberForRead(data?.unit_price)}`
                : "-",
            quantity: showInteger(data?.quantity),
            amount_before_tax: data?.amount_before_tax
                ? `${appCurrencySymbol} ${formatNumberForRead(data?.amount_before_tax)}`
                : "-",
            apply_at: convertDateTime(data?.apply_at) ?? "-",
            applied_at:
                displayDateTime(data?.applied_at, DATE_FORMAT.forDisplay) ??
                "-",
        };
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  student_number: item?.student?.student_number,
                  student_name: combinedNames(
                      item?.student?.translations?.name
                  ),
                  student_leave_status: item?.student?.leave_status ?? "-",
                  is_active: item
                      ? lowerCase(item.student?.is_active ? ACTIVE : INACTIVE)
                      : "",
                  status: item?.status,
                  applied_at: displayDateTime(
                      item?.applied_at,
                      DATE_FORMAT.forDisplay
                  ),
                  remarks: item?.remarks ?? "-",
              }))
            : [];
    }

    function definedRecurringSettingData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  ...item,
                  billing_date: format(item?.billing_date, DATE_FORMAT.MY),
              }))
            : [];
    }

    return fee ? (
        <div className="pb-10 lg:min-w-[900px]">
            <InfoCard
                cardStyleClass="w-full max-w-[500px]"
                title="Fee Details"
                noBorder
                data={formattedData(fee)}
            />

            <div className="c-text-size mb-2 mt-3 pt-2 font-medium text-themeLabel">
                Recurring Settings
            </div>
            <div className="w-fit min-w-[360px]">
                <DataTable
                    columns={[{ key: "billing_date" }, { key: "description" }]}
                    data={definedRecurringSettingData(fee.recurring_settings)}
                    isSmaller={true}
                />
            </div>
            <div className="c-text-size mb-2 mt-3 pt-2 font-medium text-themeLabel">
                Students
            </div>
            <DataTable
                columns={[
                    { key: "student_number" },
                    { key: "student_name" },
                    { key: "student_leave_status" },
                    {
                        key: "is_active",
                        displayAs: "Student Current Status",
                        modify: (value) => (
                            <div className={`cell-status ${value}`}>
                                {value}
                            </div>
                        ),
                    },
                    {
                        key: "status",
                        displayAs: "Fee Status",
                        modify: (value) => (
                            <span
                                className={clsx(
                                    value === "COMPLETED" && "text-green-600",
                                    value === "APPLYING" && "text-yellow-500"
                                )}
                            >
                                {capitalize(value)}
                            </span>
                        ),
                    },
                    {
                        key: "applied_at",
                        modify: (value) => (
                            <div className="text-[14px]">{value}</div>
                        ),
                    },
                    {
                        key: "remarks",
                        modify: (value) => (
                            <div className="text-[14px]">{value}</div>
                        ),
                    },
                ]}
                data={definedData(fee.students)}
                isSmaller={true}
            />
        </div>
    ) : (
        <div className="h-10"></div>
    );
};

export default ViewFee;
