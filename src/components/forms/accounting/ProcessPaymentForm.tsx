import { useLocale } from "next-intl";
import { useEffect, useRef, useState } from "react";
import React from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import { Plus, X } from "lucide-react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import FormTextarea from "@/components/ui/FormTextarea";
import FreeFileInput from "@/components/ui/FreeFileInput";
import FreeInput from "@/components/ui/FreeInput";
import FreeInputDecimal from "@/components/ui/FreeInputDecimal";
import FreeSelect from "@/components/ui/FreeSelect";
import InfoCard from "@/components/ui/InfoCard";
import {
    CONFIRMED,
    CommonFormProps,
    DRAFT,
    GET_ALL_PARAMS,
    PAID,
    PARTIAL,
    TableColumnType,
    UNPAID,
    VOIDED,
    appCurrencySymbol,
    bankAPI,
    billingDocumentAPI,
    enrollmentAPI,
    paymentMethodsAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { formatNumberForRead, showBackendFormError } from "@/lib/utils";
import toast from "react-hot-toast";

const ProcessPaymentForm = (
    props: CommonFormProps & {
        billingDocument: any;
        isEnrollment?: boolean;
    }
) => {
    const locale = useLocale();
    const [paymentMethodOptions, setPaymentMethodOptions] = useState([]);

    const { data: bankOptions, axiosQuery: getBankOptions } = useAxios({
        api: bankAPI,
        onError: close,
    });

    const { axiosQuery: getPaymentMethodOptions } = useAxios({
        api: paymentMethodsAPI,
        locale,
        onError: close,
        onSuccess(response) {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.name,
            }));
            setPaymentMethodOptions(result);
        },
    });

    useEffect(() => {
        getBankOptions({ params: GET_ALL_PARAMS });
        getPaymentMethodOptions({ params: GET_ALL_PARAMS });
    }, []);

    return props.billingDocument ? (
        <FormWrap
            {...props}
            billingDocument={props.billingDocument}
            bankOptions={bankOptions}
            paymentMethodOptions={paymentMethodOptions}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    isEnrollment?: boolean;
    billingDocument: any;
    bankOptions: any;
    paymentMethodOptions: any;
};

type FormValues = {
    payments: {
        payment_method_code: string;
        bank_id: string | null;
        payment_reference_no: string;
        amount_received: number;
        file: File | null;
    }[];
    remarks: string;
    total_amount_received: number;
};

const FormWrap = ({
    isEnrollment = false,
    billingDocument,
    bankOptions,
    paymentMethodOptions,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm<FormValues>({
        defaultValues: {
            payments: [
                {
                    payment_method_code: "",
                    bank_id: null,
                    payment_reference_no: "",
                    amount_received: 0,
                    file: null,
                },
            ],
            remarks: "",
            total_amount_received: 0,
        },
        shouldUnregister: false,
    });

    function formattedData(data) {
        return {
            reference_number: data?.reference_number ?? "-",
            status: data?.status ? (
                <span
                    className={clsx(
                        data?.status === CONFIRMED && "text-green-600",
                        data?.status === DRAFT && "text-yellow-500",
                        data?.status === VOIDED && "text-red-500"
                    )}
                >
                    {capitalize(data?.status)}
                </span>
            ) : (
                "-"
            ),
            payment_status: data?.payment_status ? (
                <span
                    className={clsx(
                        data?.payment_status === PARTIAL && "text-yellow-500",
                        data?.payment_status === PAID && "text-green-600",
                        data?.payment_status === UNPAID && "text-red-500"
                    )}
                >
                    {capitalize(data?.payment_status)}
                </span>
            ) : (
                "-"
            ),
            purchase_date: data?.document_date ?? "-",
            payment_due_date: data?.payment_due_date ?? "-",
            amount: data?.amount_before_tax_after_less_advance
                ? `${appCurrencySymbol} ${formatNumberForRead(data?.amount_before_tax_after_less_advance)}`
                : "-",
        };
    }

    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "payments",
    });

    const [showTable, setShowTable] = useState<boolean>(false);

    useEffect(() => {
        if (fields.length > 0) setShowTable(true);
    }, [fields]);

    const columns: TableColumnType[] = [
        {
            key: "_",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell.row.index;
                return (
                    <div className="flex h-full items-center">
                        <div
                            className="w-fit cursor-pointer rounded-full border border-themeGreenDark p-0.5 opacity-80"
                            onClick={() => {
                                remove(index);
                                if (fields.length === 1) {
                                    // Hide the table if it's the last item being removed
                                    setShowTable(false);
                                }
                            }}
                        >
                            <X size={14} className="text-themeGreenDark" />
                        </div>
                    </div>
                );
            },
        },
        {
            key: "payment_method_code",
            displayAs: "Payment Method",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="min-w-[200px] items-center">
                        <FreeSelect
                            hasLabel={false}
                            control={form.control}
                            name={`payments[${index}].payment_method_code`}
                            options={paymentMethodOptions}
                            error={
                                form.formState.errors?.payments?.[index]
                                    ?.payment_method_code
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "bank_id",
            displayAs: "Bank",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <div className="min-w-[250px] items-center">
                        <FreeSelect
                            control={form.control}
                            name={`payments[${index}].bank_id`}
                            options={bankOptions}
                            hasLabel={false}
                            error={
                                form.formState.errors?.payments?.[index]
                                    ?.bank_id
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "payment_reference_no",
            displayAs: "Reference Number",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <FreeInput
                        hasLabel={false}
                        control={form.control}
                        name={`payments[${index}].payment_reference_no`}
                        error={
                            form.formState.errors?.payments?.[index]
                                ?.payment_reference_no
                        }
                        onChange={() =>
                            setTimeout(() => {
                                form.setFocus(
                                    `payments.${index}.payment_reference_no`
                                );
                            }, 1)
                        }
                    />
                );
            },
        },
        {
            key: "file",
            displayAs: "Attachment",
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                const file = watchedPayments[index]?.file;
                return (
                    <div className="h-full min-w-[150px]">
                        <FreeFileInput
                            key={`file-input-${index}-${file?.name || ""}`}
                            control={form.control}
                            name={`payments[${index}].file`}
                            error={
                                form.formState.errors?.payments?.[index]?.file
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "amount_received",
            displayAs: `Amount Received (${appCurrencySymbol})`,
            hasSort: false,
            modify: (_, cell) => {
                const index = cell?.row?.index;
                return (
                    <FreeInputDecimal
                        key={`payments.${index}.amount_received`}
                        hasLabel={false}
                        control={form.control}
                        name={`payments[${index}].amount_received`}
                        error={
                            form.formState.errors?.payments?.[index]
                                ?.amount_received
                        }
                        inputRef={(el) =>
                            (inputRefs.current[
                                `payments[${index}].amount_received`
                            ] = el)
                        }
                        onFocus={() => handleFocus(index)}
                    />
                );
            },
        },
    ];

    const { axiosMultipartPost: createManualPayment, error: postError } =
        useAxios({
            api: `${isEnrollment ? enrollmentAPI : billingDocumentAPI}/${billingDocument?.id}/manual-payment`,
            onSuccess: () => {
                refresh();
                close();
            },
        });

    const handleAddPayment = () => {
        append({
            payment_method_code: "",
            bank_id: null,
            payment_reference_no: "",
            amount_received: 0,
            file: null,
        });
    };

    const inputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});
    const lastFocusedInput = useRef<string>("");
    const watchedPayments = useWatch({
        control: form.control,
        name: "payments",
    });

    const handleFocus = (index: number) => {
        lastFocusedInput.current = `payments[${index}].amount_received`;
    };

    useEffect(() => {
        if (!watchedPayments) return;

        const total = watchedPayments.reduce(
            (sum, payment) => sum + (Number(payment.amount_received) || 0),
            0
        );

        form.setValue("total_amount_received", total);

        // Focus on the last focused input
        if (lastFocusedInput.current) {
            setTimeout(() => {
                const inputElement =
                    inputRefs.current[lastFocusedInput.current];
                if (inputElement) {
                    inputElement.focus();
                    inputElement.setSelectionRange(
                        inputElement.value.length,
                        inputElement.value.length
                    );
                }
            }, 0);
        }
    }, [watchedPayments]);

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (
                    !isEnrollment &&
                    data.total_amount_received !=
                        billingDocument?.amount_before_tax_after_less_advance
                ) {
                    toast.error(
                        `Mismatch: Received ${appCurrencySymbol}${formatNumberForRead(data.total_amount_received)}, expected amount ${appCurrencySymbol}${formatNumberForRead(billingDocument?.amount_before_tax_after_less_advance)}.`
                    );
                    return;
                }

                createManualPayment(data);
            })
        );
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <>
            <h2>Process Payment</h2>

            <Form {...form}>
                <form onSubmit={onSubmit} className="lg:col-span-2">
                    <InfoCard
                        cardStyleClass="w-full max-w-[500px]"
                        title="Invoice Details"
                        noBorder
                        data={formattedData(billingDocument)}
                    />

                    <div className="mt-3 flex items-end justify-between gap-x-5 lg:col-span-2">
                        <h3 className="font-semibold text-themeGreenDark">
                            Payment
                        </h3>
                        <Button
                            variant={"outline"}
                            size={"smaller"}
                            className="flex gap-x-1"
                            onClick={handleAddPayment}
                        >
                            <Plus size={18} className="text-themeGreen" />
                            <span>Add Payment</span>
                        </Button>
                    </div>

                    {showTable && (
                        <div className="mb-5 mt-2.5 gap-y-2 lg:col-span-2">
                            <DataTable
                                canOverflow={true}
                                columns={columns}
                                data={fields.map((field: any, index) => ({
                                    ...field,
                                    index,
                                }))}
                                extraRow={[
                                    {
                                        value: (
                                            <span className="font-medium text-themeLabel">
                                                Total Amount (
                                                {appCurrencySymbol})
                                            </span>
                                        ),
                                        colSpan: 5,
                                    },
                                    {
                                        value: (
                                            <span className="text-[16px] font-medium">
                                                {formatNumberForRead(
                                                    form.watch(
                                                        "total_amount_received"
                                                    )
                                                )}
                                            </span>
                                        ),
                                    },
                                ]}
                            />
                        </div>
                    )}

                    <div className="mb-4 mt-4 max-w-[500px]">
                        <FormTextarea control={form.control} name="remarks" />
                    </div>

                    <div className="mt-3 lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default ProcessPaymentForm;
