import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    billingDocumentChangeStatusAPI,
    CONFIRMED,
    DRAFT,
    POSTED,
    VOIDED,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import { Button } from "../../base-ui/button";

const BillingDocumentChangeStatusForm = (props) => {
    const form = useForm({
        defaultValues: {
            new_status: props?.currentStatus ?? "",
        },
    });

    const { axiosPut: updateStatus, error: putError } = useAxios({
        api: `${billingDocumentChangeStatusAPI}?billing_document_id=${props.id}&new_status=${form.getValues("new_status")}`,
        onSuccess: () => {
            props.close();
            props.refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (props.id && form.getValues("new_status")) {
                updateStatus({ data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    const statusOptions =
        props.currentStatus === DRAFT
            ? [DRAFT, CONFIRMED, VOIDED]
            : [CONFIRMED, VOIDED];

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">Change Status</h2>
            <form onSubmit={onSubmit} className="grid gap-y-10">
                <FormSelect
                    control={form.control}
                    name="new_status"
                    label="status*"
                    isStringOptions={true}
                    options={statusOptions}
                />

                <Button type="submit" className="ml-auto mt-2">
                    Submit
                </Button>
            </form>
        </Form>
    );
};

export default BillingDocumentChangeStatusForm;
