import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { chunk, indexOf, isEmpty } from "lodash";
import { X } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DataTable from "@/components/ui/DataTable";
import FormDivider from "@/components/ui/FormDivider";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormSelect from "@/components/ui/FormSelect";
import FreeMonthPicker from "@/components/ui/FreeMonthPicker";
import Modal from "@/components/ui/Modal";
import RaisedButton from "@/components/ui/RaisedButton";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    CHUNKED_FILTER_PARAMS,
    CommonFormProps,
    DATE_FORMAT,
    discountAPI,
    discountBulkCreateAPI,
    FIXED,
    FULL,
    GET_ALL_PARAMS,
    glAccountAPI,
    PERCENT,
    STUDENT,
} from "@/lib/constant";
import { useAxios, usePaginatedTable, useSubmit } from "@/lib/hook";
import {
    combinedNames,
    convertYearMonthToDate,
    showBackendFormError,
} from "@/lib/utils";
import DiscountSourceForm from "./DiscountSourceForm";
import Tabs from "@/components/ui/Tabs";
import FreeInput from "@/components/ui/FreeInput";

const DiscountBulkCreateForm = (props: CommonFormProps) => {
    const [glAccountOptions, setGlAccountOptions] = useState<any[]>([]);
    const locale = useLocale();

    const { axiosQuery: getGlAccountOptions } = useAxios({
        api: glAccountAPI,
        locale,
        onSuccess: (response) => {
            const result = response.data.map((option) => ({
                id: option.code,
                name: option.label,
            }));
            setGlAccountOptions(result);
        },
    });

    const { data: currentDiscountData, axiosQuery: getDiscount } = useAxios({
        api: discountAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        getGlAccountOptions({
            params: { ...GET_ALL_PARAMS, is_active: 1 },
        });

        if (!props.isCreate && props.id) {
            getDiscount({ id: props.id });
        }
    }, []);

    return glAccountOptions && (props.isCreate || currentDiscountData) ? (
        <FormWrap
            currentDiscountData={currentDiscountData}
            glAccountOptions={glAccountOptions}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentDiscountData: any;
    glAccountOptions: Array<Record<string, any>>;
};

const FormWrap = ({
    currentDiscountData,
    glAccountOptions,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const _byStudent = "By Student";
    const _bySource = "By Source";

    const form = useForm<any>({
        defaultValues: {
            basis: currentDiscountData?.basis ?? "",
            basis_amount: currentDiscountData?.basis_amount ?? "",
            max_amount: currentDiscountData?.max_amount ?? "",
            effective_from: currentDiscountData?.effective_from ?? "",
            effective_to: currentDiscountData?.effective_to ?? "",
            gl_account_codes: currentDiscountData?.gl_account_codes ?? [],
            source_type: _bySource,
            students: currentDiscountData?.source ?? [],
        },
    });

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [openSourceSearch, setOpenSourceSearch] = useState(false);
    const [targetsChunk, setTargetsChunk] = useState<any[]>([]);

    const {
        filter,
        setFilter,
        pagination,
        setPagination,
        onRemove,
        onAdd,
        onSort,
        onSetPagination,
        clonedFilteredChunk,
    } = usePaginatedTable({
        targetType: STUDENT,
        targetsChunk,
        setTargetsChunk,
    });

    const { append: appendStudent, remove: removeStudent } = useFieldArray({
        control: form.control,
        name: "students",
    });

    const studentColumns = [
        {
            key: "student_number",
            hasSort: true,
        },
        {
            key: "name",
            hasSort: true,
            modify: (value, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="grid min-w-[200px] gap-y-1">
                        <span>{value}</span>
                        {form.formState.errors?.students?.[index]
                            ?.student_id && (
                            <span className="text-xs text-destructive">
                                Invalid student
                            </span>
                        )}
                    </div>
                );
            },
        },
        {
            key: "effective_from",
            displayAs: "Effective From*",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );

                return (
                    <div className="h-full">
                        <FreeMonthPicker
                            hasLabel={false}
                            control={form.control}
                            name={`students[${index}].effective_from`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.effective_from ||
                                form.formState.errors?.discounts?.[index]
                                    ?.effective_from
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "effective_to",
            displayAs: "Effective To*",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <div className="h-full">
                        <FreeMonthPicker
                            hasLabel={false}
                            control={form.control}
                            name={`students[${index}].effective_to`}
                            error={
                                form.formState.errors?.students?.[index]
                                    ?.effective_to ||
                                form.formState.errors?.discounts?.[index]
                                    ?.effective_to
                            }
                        />
                    </div>
                );
            },
        },
        {
            key: "_",
            modify: (_, cell) => {
                const index = indexOf(
                    form.watch("students").map((student) => student.id),
                    cell.row.id
                );
                return (
                    <X
                        size={20}
                        className="mx-auto cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                        onClick={() => {
                            removeStudent(index);
                            onRemove(cell.row.original?.id);
                            form.clearErrors();
                        }}
                    />
                );
            },
        },
    ];

    function onAddStudent(selection) {
        const currentIds = form
            .getValues()
            .students.map((student) => student.id.toString());

        const newStudents = selection
            .filter((student) => !currentIds.includes(student.id?.toString()))
            .map((student) => ({
                id: student?.id,
                student_number: student?.student_number,
                name: combinedNames(student?.translations?.name),
                effective_from:
                    student?.effective_from || form.watch("effective_from"),
                effective_to:
                    student?.effective_to || form.watch("effective_to"),
            }));

        const updatedRecords = form.getValues("students");
        setTargetsChunk(chunk(updatedRecords, CHUNKED_FILTER_PARAMS.per_page));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        onAdd(selection);
    }

    function onAddSourceStudent(selection) {
        // remove student list
        const currentStudents = form.getValues("students");
        currentStudents.forEach((student) => {
            removeStudent(student.id);
        });
        form.resetField("students");

        // add new student list
        const newStudents = selection.map((student) => ({
            ...student,
            effective_from:
                student?.effective_from || form.watch("effective_from"),
            effective_to: student?.effective_to || form.watch("effective_to"),
        }));

        onSetPagination(newStudents.length, 1);
        setTargetsChunk(chunk(newStudents, CHUNKED_FILTER_PARAMS.per_page));

        newStudents.forEach((student) => {
            appendStudent(student);
        });

        form.setValue("students", newStudents);
    }

    function clearSelectedTargets() {
        const currentStudents = form.getValues("students");
        currentStudents.forEach((_, index) => {
            removeStudent(index);
        });
        form.resetField("students");

        setTargetsChunk([]);
        onSetPagination(0, 1);
    }

    function setSourceType(type: typeof _byStudent | typeof _bySource) {
        clearSelectedTargets();
        form.setValue("source_type", type);
    }

    const { axiosPost: createDiscount, error: postError } = useAxios({
        api: discountBulkCreateAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                console.log("submit data", data);
                let hasError = false;
                const requiredFields = ["basis", "basis_amount", "max_amount"];
                const studentRequiredFields = [
                    "effective_from",
                    "effective_to",
                ];

                requiredFields.forEach((key) => {
                    if (!data[key]) {
                        hasError = true;
                        form.setError(key, {
                            type: "manual",
                            message: "This field is required",
                        });
                    }
                });

                data.students.forEach((student, index) => {
                    studentRequiredFields.forEach((field) => {
                        if (!student[field]) {
                            hasError = true;
                            form.setError(`students.${index}.${field}`, {
                                type: "manual",
                                message: "This field is required",
                            });
                        }
                    });
                });

                if (hasError) return;

                const transformToCreateDiscountPayload = (formData) => {
                    return {
                        discounts: formData.students.map((student) => ({
                            basis: formData.basis,
                            basis_amount: formData.basis_amount,
                            max_amount: formData.max_amount,
                            effective_from: convertYearMonthToDate(
                                student.effective_from,
                                DATE_FORMAT.YMD
                            ),
                            effective_to: convertYearMonthToDate(
                                student.effective_to,
                                DATE_FORMAT.YMD,
                                true
                            ),
                            gl_account_codes: formData.gl_account_codes,
                            description: formData.description,
                            source_type:
                                form.watch("source_type") == _bySource
                                    ? "App\\Models\\ScholarshipAward"
                                    : null,
                            source_id:
                                form.watch("source_type") == _bySource
                                    ? student.source_id
                                    : null,
                            student_id: student.id,
                        })),
                    };
                };

                const createDiscountPayload =
                    transformToCreateDiscountPayload(data);

                createDiscount(createDiscountPayload);
            })
        );
    }

    useEffect(() => {
        const students = form.getValues().students;
        setTargetsChunk(chunk(students, CHUNKED_FILTER_PARAMS.per_page));
    }, [form.watch("students")]);

    useEffect(() => {
        if (form.watch("effective_from")) {
            form.setValue(
                "students",
                form.getValues().students.map((student) => ({
                    ...student,
                    effective_from: form.getValues("effective_from"),
                }))
            );
        }
    }, [form.watch("effective_from")]);

    useEffect(() => {
        if (form.watch("effective_to")) {
            form.setValue(
                "students",
                form.getValues().students.map((student) => ({
                    ...student,
                    effective_to: form.getValues("effective_to"),
                }))
            );
        }
    }, [form.watch("effective_to")]);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <>
            <h2 className="mb-2">Create Discount</h2>
            <Form {...form}>
                <form
                    className="lg:px-.5 pb-3 lg:min-w-[800px]"
                    onSubmit={onSubmit}
                >
                    <div className="mb-5 grid gap-x-3 gap-y-4 lg:grid-cols-3">
                        <FormSelect
                            control={form.control}
                            name="basis"
                            label="basis type*"
                            options={[
                                {
                                    id: FIXED,
                                    name: "Fixed Amount",
                                },
                                {
                                    id: PERCENT,
                                    name: "Percentage",
                                },
                            ]}
                        />
                        <FormInputDecimal
                            control={form.control}
                            name="basis_amount"
                            label="Basis Amount*"
                        />
                        <FormInputDecimal
                            control={form.control}
                            name="max_amount"
                            label="Max Amount*"
                        />
                        <FormSelect
                            control={form.control}
                            name="gl_account_codes"
                            label="GL Account Codes"
                            isMulti={true}
                            options={glAccountOptions}
                        />
                        <FreeMonthPicker
                            control={form.control}
                            name="effective_from"
                        />
                        <FreeMonthPicker
                            control={form.control}
                            name="effective_to"
                        />
                        <FreeInput control={form.control} name="description" />
                    </div>
                    <FormDivider />
                    <div className="mb-5 mt-5">
                        <>
                            <Tabs
                                list={[_bySource, _byStudent]}
                                selected={form.watch("source_type")}
                                setSelected={setSourceType}
                                onChangeTab={() => form.clearErrors()}
                            />
                            {form.watch("source_type") === _byStudent && (
                                <div className="pb-5">
                                    <RaisedButton
                                        name="Add Students"
                                        onClick={() =>
                                            setOpenStudentSearch(true)
                                        }
                                    />
                                </div>
                            )}

                            {form.watch("source_type") === _bySource && (
                                <div className="pb-5">
                                    <RaisedButton
                                        name="Select Source"
                                        onClick={() =>
                                            setOpenSourceSearch(true)
                                        }
                                    />
                                </div>
                            )}

                            {targetsChunk?.length > 0 && pagination && (
                                <DataTable
                                    isSmaller
                                    columns={studentColumns}
                                    data={
                                        (clonedFilteredChunk ?? targetsChunk)[
                                            pagination?.current_page - 1
                                        ]
                                    }
                                    pagination={pagination}
                                    setPagination={setPagination}
                                    changePage={(arg) =>
                                        setFilter({ ...filter, ...arg })
                                    }
                                    hasPerPage={false}
                                    sorted={filter?.order_by}
                                    sort={onSort}
                                    styleClass="overflow-visible"
                                />
                            )}
                        </>

                        {/* add students */}
                        <Modal
                            open={openStudentSearch}
                            onOpenChange={setOpenStudentSearch}
                            size="large"
                        >
                            <StudentSearchEngine
                                isMultiSelect={true}
                                setSelection={(selection) =>
                                    onAddStudent(selection)
                                }
                                close={() => setOpenStudentSearch(false)}
                                otherFilterParams={{
                                    response: FULL,
                                    includes: [
                                        "currentSemesterPrimaryClass.semesterSetting",
                                        "currentSemesterPrimaryClass.semesterClass.classModel",
                                    ],
                                }}
                            />
                        </Modal>

                        {/* search source */}
                        <Modal
                            open={openSourceSearch}
                            onOpenChange={setOpenSourceSearch}
                            size="medium"
                        >
                            <DiscountSourceForm
                                setSelection={(selection) =>
                                    onAddSourceStudent(selection)
                                }
                                close={() => setOpenSourceSearch(false)}
                            />
                        </Modal>
                    </div>

                    <div className="mt-6 flex justify-end gap-x-3">
                        <Button
                            type="submit"
                            disabled={isEmpty(form.getValues("students"))}
                        >
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default DiscountBulkCreateForm;
