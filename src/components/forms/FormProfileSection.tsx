import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import { genderOptions, stateAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { DatePicker } from "../ui/DatePicker";
import FormDivider from "../ui/FormDivider";
import FormInput from "../ui/FormInput";
import FormInputInterger from "../ui/FormInputInterger";
import FormPhoneInput from "../ui/FormPhoneInput";
import FormSelect from "../ui/FormSelect";
import "react-phone-number-input/style.css";
import { capitalize } from "lodash";

type FormProfileSectionProps = {
    form: any;
    options: Record<string, any>;
    countryId: number;
    isDisabled?: boolean;
};

const FormProfileSection = ({
    form,
    options,
    countryId,
    isDisabled,
}: FormProfileSectionProps) => {
    const locale = useLocale();

    const {
        data: stateList,
        axiosQuery: getStateList,
        isLoading: isStatesLoading,
    } = useAxios({
        api: stateAPI,
        locale,
    });

    useEffect(() => {
        if (countryId) {
            getStateList({ params: { country_id: countryId } });
        }
    }, []);

    const t = useTranslations("common");

    return (
        <>
            <FormInput
                control={form.control}
                name="birth_cert_number"
                label="birth cert number*"
                disabled={isDisabled}
            />

            <FormInputInterger
                control={form.control}
                name="nric"
                label={"NRIC"}
                disabled={isDisabled}
            />

            <FormInput
                control={form.control}
                name="passport_number"
                disabled={isDisabled}
            />

            <FormSelect
                control={form.control}
                name="gender"
                label={t("gender") + "*"}
                isSortByName={false}
                options={genderOptions.map((option) => ({
                    id: option.id,
                    name: capitalize(t(option.name)),
                }))}
                isDisabled={isDisabled}
            />

            <FormSelect
                control={form.control}
                name="race_id"
                label="race*"
                options={options.races}
                isDisabled={isDisabled}
            />

            <FormSelect
                control={form.control}
                name="religion_id"
                label="religion*"
                options={options.religions}
                isDisabled={isDisabled}
            />

            <FormDivider />

            <FormSelect
                control={form.control}
                name="birthplace_id"
                label="birthplace*"
                options={options.countries}
                isDisabled={isDisabled}
            />

            <FormSelect
                control={form.control}
                name="nationality_id"
                label="nationality*"
                options={options.countries}
                isDisabled={isDisabled}
            />

            <DatePicker
                control={form.control}
                name={"date_of_birth"}
                label={"date of birth*"}
                isDisabled={isDisabled}
            />

            <FormDivider />

            <FormInput
                control={form.control}
                name="address"
                label="Address*"
                disabled={isDisabled}
            />

            <FormInput
                control={form.control}
                name="city"
                label="City*"
                disabled={isDisabled}
            />

            <FormSelect
                control={form.control}
                name="country_id"
                label="Country*"
                options={options.countries}
                onChange={(val) => {
                    getStateList({ params: { country_id: val } });
                    form.setValue("state_id", "");
                }}
                isDisabled={isDisabled}
            />

            <FormSelect
                control={form.control}
                name="state_id"
                label="State*"
                options={stateList}
                isLoading={isStatesLoading}
                isDisabled={isDisabled}
            />

            <FormInput
                control={form.control}
                name="postal_code"
                label="postal code*"
                disabled={isDisabled}
            />

            <FormDivider />

            <FormInput
                control={form.control}
                name="email"
                type="email"
                label="email*"
                disabled={isDisabled}
            />

            <FormPhoneInput
                form={form}
                name="phone_number"
                label="phone number*"
                disabled={isDisabled}
            />
        </>
    );
};

export default FormProfileSection;
