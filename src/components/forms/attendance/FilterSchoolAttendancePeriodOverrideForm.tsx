import { useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormInput from "@/components/ui/FormInput";
import { CommonFilterProps, DEFAULT_FILTER_PARAMS } from "@/lib/constant";
import { toYMD } from "@/lib/utils";
import { Button } from "../../base-ui/button";

const FilterSchoolAttendancePeriodOverrideForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            from: filter?.from ?? undefined,
            to: filter?.to ?? undefined,
            date: filter?.date ?? undefined,
            remarks: filter?.remarks ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        data.from = toYMD(data.from);
        data.to = toYMD(data.to);
        data.date = toYMD(data.date);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const t = useTranslations("common");

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <DatePicker
                    control={form.control}
                    name={"from"}
                    label={"From"}
                />
                <DatePicker control={form.control} name={"to"} label={"To"} />

                <DatePicker
                    control={form.control}
                    name={"date"}
                    label={"date"}
                />

                <FormInput control={form.control} name={"remarks"} />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSchoolAttendancePeriodOverrideForm;
