import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import {
    CommonFilterProps,
    STUDENT,
    EMPLOYEE,
    CONTRACTOR,
} from "@/lib/constant";
import { toYMD, getUserableType, optionUserLabel } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import TrainerSearchEngine from "@/components/ui/search-engines/TrainerSearchEngine";
import FormSearchInput from "@/components/ui/FormSearchInput";
import { useTranslations } from "next-intl";

const FilterAttendanceInputForm = ({
    filter,
    setFilter,
    close,
    type,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            attendance_recordable_id:
                filter?.attendance_recordable_id ?? undefined,
            attendance_recordable_type:
                filter?.attendance_recordable_type ?? undefined,
            date: filter?.date ?? undefined,
            name: filter?.name ?? undefined,
        },
    });
    const t = useTranslations("common");

    const [openStudentSearch, setOpenStudentSearch] = useState(false);
    const [openEmployeeSearch, setOpenEmployeeSearch] = useState(false);
    const [openContractorSearch, setOpenContractorSearch] = useState(false);

    function onSubmit(data: Record<string, any>) {
        data.date = toYMD(data.date);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ page: 1, per_page: filter?.per_page });
        close();
    }
    return (
        <Form {...form}>
            {/* <h2 className="mb-1 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                {type === STUDENT ? (
                    <FormSearchInput
                        control={form.control}
                        name="attendance_recordable_id"
                        label="Student"
                        displayValue={form.watch("name") ?? null}
                        onClick={() => setOpenStudentSearch(true)}
                    />
                ) : null}
                {type === EMPLOYEE ? (
                    <FormSearchInput
                        control={form.control}
                        name="attendance_recordable_id"
                        label="Employee"
                        displayValue={form.watch("name") ?? null}
                        onClick={() => setOpenEmployeeSearch(true)}
                    />
                ) : null}
                {type === CONTRACTOR ? (
                    <FormSearchInput
                        control={form.control}
                        name="attendance_recordable_id"
                        label="Coach"
                        displayValue={form.watch("name") ?? null}
                        onClick={() => setOpenContractorSearch(true)}
                    />
                ) : null}

                <DatePicker
                    control={form.control}
                    name={"date"}
                    label={"date"}
                />

                <Modal
                    open={openStudentSearch}
                    onOpenChange={setOpenStudentSearch}
                    size="large"
                >
                    <StudentSearchEngine
                        setSelection={(student) => {
                            if (student) {
                                form.setValue(
                                    "attendance_recordable_id",
                                    student.id
                                );
                                form.setValue(
                                    "attendance_recordable_type",
                                    getUserableType(STUDENT)
                                );
                                form.setValue(
                                    "name",
                                    optionUserLabel(
                                        student?.student_number,
                                        student?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenStudentSearch(false)}
                    />
                </Modal>

                <Modal
                    open={openEmployeeSearch}
                    onOpenChange={setOpenEmployeeSearch}
                    size="large"
                >
                    <StaffSearchEngine
                        setSelection={(employee) => {
                            if (employee) {
                                form.setValue(
                                    "attendance_recordable_id",
                                    employee.id
                                );
                                form.setValue(
                                    "attendance_recordable_type",
                                    getUserableType(EMPLOYEE)
                                );
                                form.setValue(
                                    "name",
                                    optionUserLabel(
                                        employee?.employee_number,
                                        employee?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenEmployeeSearch(false)}
                    />
                </Modal>

                <Modal
                    open={openContractorSearch}
                    onOpenChange={setOpenContractorSearch}
                    size="large"
                >
                    <TrainerSearchEngine
                        setSelection={(trainer) => {
                            if (trainer) {
                                form.setValue(
                                    "attendance_recordable_id",
                                    trainer.id
                                );
                                form.setValue(
                                    "attendance_recordable_type",
                                    getUserableType(CONTRACTOR)
                                );
                                form.setValue(
                                    "name",
                                    optionUserLabel(
                                        trainer?.contractor_number,
                                        trainer?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenContractorSearch(false)}
                    />
                </Modal>

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterAttendanceInputForm;
