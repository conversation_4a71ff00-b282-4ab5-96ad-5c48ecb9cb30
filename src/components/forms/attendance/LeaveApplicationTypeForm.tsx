import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import { CommonFormProps, leaveApplicationTypeAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isValueTrue, showBackendFormError } from "@/lib/utils";

const LeaveApplicationTypeForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: leaveApplicationType, axiosQuery: getLeaveApplicationType } =
        useAxios({
            api: leaveApplicationTypeAPI,
            locale,
            onError: props.close,
        });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getLeaveApplicationType({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || leaveApplicationType) ? (
        <FormWrap
            leaveApplicationType={leaveApplicationType}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    leaveApplicationType: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    leaveApplicationType,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]:
                            leaveApplicationType?.translations?.name?.[key] ??
                            "",
                    }),
                    {}
                ),
            is_present: isCreate
                ? false
                : isValueTrue(leaveApplicationType?.is_present),
            conduct_point_deduction:
                leaveApplicationType?.conduct_point_deduction ?? 0,
            average_point_deduction:
                leaveApplicationType?.average_point_deduction ?? 0,
            display_in_report_card: isCreate
                ? true
                : isValueTrue(leaveApplicationType?.display_in_report_card),
        },
    });

    const { axiosPost: createLeaveApplicationType, error: postError } =
        useAxios({
            api: leaveApplicationTypeAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosPut: updateLeaveApplicationType, error: putError } = useAxios({
        api: leaveApplicationTypeAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (isCreate) {
                createLeaveApplicationType(data);
            } else {
                updateLeaveApplicationType({
                    id: leaveApplicationType.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Leave Application Type
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}
                    <FormInputDecimal
                        control={form.control}
                        type="number"
                        step="0.01"
                        name="conduct_point_deduction"
                        label="Conduct Point Deduction*"
                    />
                    <FormInputDecimal
                        control={form.control}
                        type="number"
                        step="0.01"
                        name="average_point_deduction"
                        label="Average Point Deduction*"
                    />
                    <div className="flex justify-between p-1">
                        <FormCheckbox
                            control={form.control}
                            name="is_present"
                            label="Present"
                        />
                        <FormCheckbox
                            control={form.control}
                            name="display_in_report_card"
                            label="Display in Report Card"
                        />
                    </div>

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default LeaveApplicationTypeForm;
