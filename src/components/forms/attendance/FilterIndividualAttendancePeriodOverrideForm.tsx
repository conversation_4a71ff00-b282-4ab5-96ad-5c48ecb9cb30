import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    studentAPI,
    STUDENT,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { toYMD, getUserableType, optionUserLabel } from "@/lib/utils";
import { Button } from "../../base-ui/button";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import FormSearchInput from "@/components/ui/FormSearchInput";

const FilterIndividualAttendancePeriodOverrideForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            period: filter?.period ?? undefined,
            attendance_recordable_id:
                filter?.attendance_recordable_id ?? undefined,
            attendance_recordable_type:
                filter?.attendance_recordable_type ?? undefined,
            student_name: filter?.student_name ?? undefined,
        },
    });
    const t = useTranslations("common");

    const [openStudentSearch, setOpenStudentSearch] = useState(false);

    function onSubmit(data: Record<string, any>) {
        data.period = toYMD(data.period);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const locale = useLocale();

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSearchInput
                    control={form.control}
                    name="attendance_recordable_id"
                    label="Student"
                    displayValue={form.watch("student_name") ?? null}
                    onClick={() => setOpenStudentSearch(true)}
                />

                <DatePicker
                    control={form.control}
                    name={"period"}
                    label={"date"}
                />

                <Modal
                    open={openStudentSearch}
                    onOpenChange={setOpenStudentSearch}
                    size="large"
                >
                    <StudentSearchEngine
                        setSelection={(student) => {
                            if (student) {
                                form.setValue(
                                    "attendance_recordable_id",
                                    student.id
                                );
                                form.setValue(
                                    "attendance_recordable_type",
                                    getUserableType(STUDENT)
                                );
                                form.setValue(
                                    "student_name",
                                    optionUserLabel(
                                        student?.student_number,
                                        student?.translations?.name
                                    )
                                );
                            }
                        }}
                        close={() => setOpenStudentSearch(false)}
                    />
                </Modal>

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterIndividualAttendancePeriodOverrideForm;
