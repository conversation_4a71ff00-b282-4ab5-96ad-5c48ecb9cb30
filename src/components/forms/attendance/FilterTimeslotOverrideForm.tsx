import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { CommonFilterProps, DEFAULT_FILTER_PARAMS } from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { DatePicker } from "@/components/ui/DatePicker";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormSelect from "@/components/ui/FormSelect";
import { optionUserLabel, toYMD } from "@/lib/utils";
import { XCircle } from "lucide-react";
import Modal from "@/components/ui/Modal";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import FormSearchInput from "@/components/ui/FormSearchInput";

const FilterTimeslotOverrideForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const [openStudentSearch, setOpenStudentSearch] = useState(false);

    const form = useForm({
        defaultValues: {
            student_id: filter?.student_id ?? null,
            student_name: filter?.student_name ?? "",
            date: filter?.date ?? null,
            period: filter?.period ?? "",
            class_attendance_required:
                filter?.class_attendance_required ?? undefined,
            inherit_from_school_attendance:
                filter?.inherit_from_school_attendance ?? undefined,
            is_empty: filter?.is_empty ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        data.date = toYMD(data.date);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    return (
        <>
            <Form {...form}>
                {/* <h2 className="ml-0.5 text-themeBlack">Filter</h2> */}
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="filter-form"
                >
                    <FormSearchInput
                        control={form.control}
                        name="student_id"
                        label="Student"
                        displayValue={form.watch("student_name") ?? null}
                        onClick={() => setOpenStudentSearch(true)}
                    />
                    <div className="flex items-end gap-x-2">
                        <div className="flex-grow">
                            <DatePicker control={form.control} name="date" />
                        </div>
                        <XCircle
                            className="mb-2.5 text-gray-400"
                            onClick={() => form.setValue("date", null)}
                        />
                    </div>

                    <FormInputInterger control={form.control} name="period" />

                    <FormSelect
                        control={form.control}
                        name="class_attendance_required"
                        options={[
                            {
                                name: "True",
                                id: "1",
                            },
                            {
                                name: "False",
                                id: "0",
                            },
                        ]}
                    />

                    <FormSelect
                        control={form.control}
                        name="inherit_from_school_attendance"
                        options={[
                            {
                                name: "True",
                                id: "1",
                            },
                            {
                                name: "False",
                                id: "0",
                            },
                        ]}
                    />

                    <FormSelect
                        control={form.control}
                        name="is_empty"
                        options={[
                            {
                                name: "True",
                                id: "1",
                            },
                            {
                                name: "False",
                                id: "0",
                            },
                        ]}
                    />
                    <div className="filter-duo-buttons">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClear}
                        >
                            Clear
                        </Button>
                        <Button type="submit">Search</Button>
                    </div>
                </form>
            </Form>
            <Modal
                open={openStudentSearch}
                onOpenChange={setOpenStudentSearch}
                size="large"
            >
                <StudentSearchEngine
                    setSelection={(student) => {
                        if (student) {
                            form.setValue("student_id", student.id);
                            form.setValue(
                                "student_name",
                                optionUserLabel(
                                    student?.student_number,
                                    student?.translations?.name
                                )
                            );
                        }
                    }}
                    close={() => setOpenStudentSearch(false)}
                />
            </Modal>
        </>
    );
};

export default FilterTimeslotOverrideForm;
