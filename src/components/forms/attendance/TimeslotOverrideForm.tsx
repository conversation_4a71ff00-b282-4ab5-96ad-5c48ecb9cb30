import { <PERSON><PERSON> } from "@/components/base-ui/button";
import { DialogFooter } from "@/components/base-ui/dialog";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import { DatePicker } from "@/components/ui/DatePicker";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormInput from "@/components/ui/FormInput";
import FormSearchInput from "@/components/ui/FormSearchInput";
import Modal from "@/components/ui/Modal";
import StudentsPicker from "@/components/ui/PickersWithPaginatedTable/StudentsPicker";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import { TimePicker } from "@/components/ui/TimePicker";
import {
    getAllPeriodsAPI,
    teacherDropdownFilter,
    timeslotOverrideAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import {
    displayTime,
    formatTimeToDate,
    optionUserLabel,
    showBackendFormError,
    toYMD,
} from "@/lib/utils";
import clsx from "clsx";
import { format } from "date-fns";
import { flatten } from "lodash";
import { PenBoxIcon, XCircle } from "lucide-react";
import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";

const TimeslotOverrideForm = ({ refresh, close }) => {
    const [disabledPeriods, setDisabledPeriods] = useState<number[]>([]);
    const [selectedEmployee, setSelectedEmployee] = useState<any>();
    const [openSearch, setOpenSearch] = useState(false);
    const [targetsChunk, setTargetsChunk] = useState<any[]>([]);
    const [targetPeriodNumber, setTargetPeriodNumber] = useState<number | null>(
        null
    );
    const [targetDeletePeriod, setTargetDeletePeriod] = useState<number | null>(
        null
    );

    const form = useForm<any>({
        defaultValues: {
            date: null,
            employee_id: null,
            use_homeroom_teacher: false,
            student_ids: [],
            periods: [],
        },
    });

    const { append } = useFieldArray({
        control: form.control,
        name: "periods",
    });

    const locale = useLocale();

    const { data: periodData, axiosQuery: getAllPeriods } = useAxios({
        api: getAllPeriodsAPI,
        locale,
        onSuccess: (result) => {
            let _disabledPeriods: any[] = [];
            const _reenabledPeriods: any[] = [];

            result.data.period_groups.forEach((group) => {
                Object.values(group.period_labels).forEach((item: any) => {
                    if (!item?.is_attendance_required) {
                        _disabledPeriods.push(item.period);
                    } else if (
                        item?.is_attendance_required &&
                        _disabledPeriods.includes(item.period)
                    ) {
                        _reenabledPeriods.push(item.period);
                    }
                });
            });
            _disabledPeriods = _disabledPeriods.filter(
                (period) => !_reenabledPeriods.includes(period)
            );
            setDisabledPeriods([...new Set(_disabledPeriods)]);
        },
    });

    const { axiosPost: bulkUpdateTimeslotOverride, error: postError } =
        useAxios({
            api: timeslotOverrideAPI,
            locale,
            onSuccess: () => {
                refresh();
                close();
            },
        });

    function clearSlot(periodNumber) {
        const periods = form.getValues("periods");
        const updatedPeriods = periods.filter(
            (item) => item?.period != periodNumber
        );
        form.setValue("periods", updatedPeriods);
        setTargetDeletePeriod(null);
    }
    const { initLoader } = useSubmit();

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        initLoader(
            form.handleSubmit((data) => {
                data.student_ids = flatten(targetsChunk).map(
                    (student) => student.id
                );
                data.date = toYMD(data.date);
                bulkUpdateTimeslotOverride(data);
            })
        );
    }

    useEffect(() => {
        getAllPeriods();
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return periodData ? (
        <div>
            <h2 className="mb-5">Student Timeslot Override</h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form w-[90vw] max-w-fit"
                >
                    <DatePicker
                        control={form.control}
                        name="date"
                        label="Date*"
                    />
                    <div className="grid gap-y-3 lg:gap-y-2">
                        {!form.watch("use_homeroom_teacher") && (
                            <FormSearchInput
                                control={form.control}
                                name="employee_id"
                                label="Teacher*"
                                displayValue={optionUserLabel(
                                    selectedEmployee?.employee_number,
                                    selectedEmployee?.translations?.name
                                )}
                                onClick={() => setOpenSearch(true)}
                            />
                        )}
                        <div
                            className={clsx(
                                "ml-.5",
                                form.watch("use_homeroom_teacher") && "lg:mt-7"
                            )}
                        >
                            <FormCheckbox
                                control={form.control}
                                name="use_homeroom_teacher"
                                onChange={(value) => {
                                    if (!value) {
                                        form.setValue("employee_id", null);
                                        setSelectedEmployee(null);
                                        form.clearErrors("employee_id");
                                    }
                                }}
                            />
                        </div>
                    </div>
                    <div className="border-t border-dashed pt-3 lg:col-span-2">
                        <div className="max-w-[600px] pb-3">
                            <StudentsPicker
                                label="Students*"
                                targetsChunk={targetsChunk}
                                setTargetsChunk={setTargetsChunk}
                                errorMessage={
                                    form.formState.errors?.student_ids?.message
                                }
                                otherFilterParams={{
                                    only_active_class: 1,
                                    response: "FULL",
                                    includes: [
                                        "currentSemesterPrimaryClass.semesterClass.classModel",
                                    ],
                                }}
                                showPrimaryClass={true}
                            />
                        </div>
                        <div className="border-t border-dashed pt-4">
                            <h3 className="ml-.5">Timeslot Override</h3>
                            {form.formState.errors?.periods?.message && (
                                <div className="warning-text mt-2">
                                    {String(
                                        form.formState.errors?.periods?.message
                                    )}
                                </div>
                            )}
                        </div>
                        <div className="mt-3 w-[calc(90vw-64px)] overflow-auto">
                            <div className="timeslot-override w-fit rounded-sm border text-sm leading-tight">
                                {periodData?.period_groups.map(
                                    (group, index) => (
                                        <div
                                            className="flex border-b"
                                            key={index}
                                        >
                                            <div className="first-col">
                                                {group.name?.[locale]}
                                            </div>
                                            {Object.values(
                                                group?.period_labels
                                            ).map((period: any, index) => {
                                                const from = displayTime(
                                                    period?.from_time
                                                );
                                                const to = displayTime(
                                                    period?.to_time
                                                );
                                                const timeRange = `${from} - ${to}`;
                                                return (
                                                    <div
                                                        key={index}
                                                        className="col"
                                                    >
                                                        <div className="">
                                                            {
                                                                period?.name?.[
                                                                    locale
                                                                ]
                                                            }
                                                        </div>
                                                        <div className="leading-none">
                                                            {timeRange}
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )
                                )}
                                <div className="flex border-t border-gray-300">
                                    <div className="first-col">Override</div>
                                    {Array.from({
                                        length: periodData?.max_number_of_periods,
                                    }).map((_, index) => {
                                        const periodNumber = index + 1;

                                        return (
                                            <Slot
                                                key={index}
                                                form={form}
                                                periodNumber={periodNumber}
                                                isDisabled={disabledPeriods.includes(
                                                    periodNumber
                                                )}
                                                onSelect={(periodNumber) =>
                                                    setTargetPeriodNumber(
                                                        periodNumber
                                                    )
                                                }
                                                onRemove={() =>
                                                    setTargetDeletePeriod(
                                                        periodNumber
                                                    )
                                                }
                                            />
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                        <Button type="submit" className="mb-5 mt-8">
                            Save
                        </Button>
                    </div>
                </form>
            </Form>

            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StaffSearchEngine
                    otherFilterParams={teacherDropdownFilter}
                    setSelection={(person) => {
                        if (person) {
                            form.setValue("employee_id", person.id);
                            form.clearErrors("employee_id");
                            setSelectedEmployee(person);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>

            <Modal
                open={targetPeriodNumber != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTargetPeriodNumber(null);
                }}
            >
                <SlotForm
                    periodNumber={targetPeriodNumber}
                    form={form}
                    append={append}
                    close={() => setTargetPeriodNumber(null)}
                />
            </Modal>

            <Modal
                open={targetDeletePeriod}
                onOpenChange={setTargetDeletePeriod}
            >
                <>
                    <p className="mt-3 font-medium">
                        Are you sure you want to remove slot
                        {targetDeletePeriod}?
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setTargetDeletePeriod(null)}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                clearSlot(targetDeletePeriod);
                            }}
                        >
                            Confirm
                        </Button>
                    </DialogFooter>
                </>
            </Modal>
        </div>
    ) : (
        <div className="h-12"></div>
    );
};

const Slot = ({ form, periodNumber, isDisabled, onSelect, onRemove }) => {
    const periodData = form
        .watch("periods")
        ?.find((period) => period.period == periodNumber);

    const from = displayTime(periodData?.attendance_from);
    const to = displayTime(periodData?.attendance_to);
    const timeRange = `${from} - ${to}`;

    return (
        <div
            className={clsx(
                "col grid min-h-[80px] gap-y-1 font-medium leading-none",
                isDisabled && "bg-gray-100"
            )}
        >
            <div className="pt-1 font-semibold text-gray-700">
                {periodData?.placeholder ?? ""}
            </div>

            {periodData?.attendance_from && (
                <div className="text-[12px] text-gray-600">{timeRange}</div>
            )}

            {periodData?.is_empty && (
                <div className="rounded-sm border border-pink-400 p-1 text-[12px] text-pink-500">
                    Empty
                </div>
            )}
            {periodData?.inherit_from_school_attendance && (
                <div className="rounded-sm border border-blue-400 p-1 text-[12px] text-blue-500">
                    Inherit
                </div>
            )}
            {periodData?.class_attendance_required && (
                <div className="rounded-sm border border-orange-400 p-1 text-[12px] text-orange-500">
                    CA
                </div>
            )}

            {!isDisabled && (
                <div className="mt-auto flex w-full items-center justify-between px-1 pt-1">
                    {periodData && (
                        <XCircle
                            onClick={onRemove}
                            size={20}
                            className="cursor-pointer text-themeGray hover:text-gray-500"
                        />
                    )}
                    <PenBoxIcon
                        className="ml-auto cursor-pointer text-gray-400 hover:text-themeGreen"
                        size={20}
                        onClick={() => onSelect(periodNumber)}
                    />
                </div>
            )}
        </div>
    );
};

const SlotForm = ({ periodNumber, form, append, close }) => {
    const slotPeriodData = form
        .getValues("periods")
        .find((item) => item.period == periodNumber);

    type FormType = {
        period: number;
        placeholder: string;
        attendance_from: any;
        attendance_to: any;
        inherit_from_school_attendance: boolean;
        class_attendance_required: boolean;
        is_empty: boolean;
    };

    const slotForm = useForm<FormType>({
        defaultValues: {
            period: periodNumber,
            placeholder: slotPeriodData?.placeholder,
            attendance_from: formatTimeToDate(slotPeriodData?.attendance_from),
            attendance_to: formatTimeToDate(slotPeriodData?.attendance_to),
            inherit_from_school_attendance:
                slotPeriodData?.inherit_from_school_attendance ?? false,
            class_attendance_required:
                slotPeriodData?.class_attendance_required ?? false,
            is_empty: slotPeriodData?.is_empty ?? false,
        },
    });

    const [isHiddenTop, setIsHiddenTop] = useState(slotPeriodData?.is_empty);

    function onSubmit(e) {
        slotForm.clearErrors();
        e.preventDefault();
        slotForm.handleSubmit((data) => {
            let hasError = false;
            if (!data.is_empty) {
                const requiredFields: (keyof FormType)[] = [
                    "placeholder",
                    "attendance_from",
                    "attendance_to",
                ];
                requiredFields.forEach((key) => {
                    if (!data[key]) {
                        hasError = true;
                        slotForm.setError(key, {
                            type: "manual",
                            message: "This field is required",
                        });
                    }
                    if (
                        typeof data[key] == "string" &&
                        data[key].trim() === ""
                    ) {
                        hasError = true;
                        slotForm.setError(key, {
                            type: "manual",
                            message: "This field is required",
                        });
                    }
                });

                if (
                    data.attendance_from &&
                    data.attendance_to &&
                    data.attendance_from > data.attendance_to
                ) {
                    hasError = true;
                    slotForm.setError("attendance_from", {
                        type: "manual",
                        message: "Attendance from must be before Attendance to",
                    });
                }
                if (hasError) return;

                data.attendance_from = format(data.attendance_from, "HH:mm:ss");
                data.attendance_to = format(data.attendance_to, "HH:mm:ss");
            }

            console.log(data);

            if (slotPeriodData) {
                const updatedPeriods = form
                    .getValues("periods")
                    .map((item) =>
                        item.period == data.period ? { ...data } : item
                    );
                form.setValue("periods", updatedPeriods);
            } else {
                append(data);
            }
            close();
        })();
    }
    return (
        <Form {...slotForm}>
            <form onSubmit={onSubmit} className="">
                <h2 className="pb-4">Edit Slot {periodNumber}</h2>

                {!isHiddenTop && (
                    <>
                        <div className="mb-3 border-b border-dashed pb-4">
                            <div className="flex flex-wrap gap-5">
                                <div>
                                    <Label className="label">
                                        Attendance from*
                                    </Label>
                                    <TimePicker
                                        date={slotForm.watch(`attendance_from`)}
                                        setDate={(value) =>
                                            slotForm.setValue(
                                                `attendance_from`,
                                                value
                                            )
                                        }
                                    />
                                </div>
                                <div>
                                    <Label className="label">
                                        Attendance to*
                                    </Label>
                                    <TimePicker
                                        date={slotForm.watch(`attendance_to`)}
                                        setDate={(value) =>
                                            slotForm.setValue(
                                                `attendance_to`,
                                                value
                                            )
                                        }
                                    />
                                </div>
                            </div>
                            {slotForm.formState.errors?.attendance_from && (
                                <div className="warning-text mt-2">
                                    {String(
                                        slotForm.formState.errors
                                            ?.attendance_from?.message || ""
                                    )}
                                </div>
                            )}
                        </div>

                        <FormInput
                            control={slotForm.control}
                            name="placeholder"
                            label="Placeholder*"
                        />
                        <div className="mt-5 grid gap-y-1.5">
                            <FormCheckbox
                                control={slotForm.control}
                                name="inherit_from_school_attendance"
                            />
                            <FormCheckbox
                                control={slotForm.control}
                                name="class_attendance_required"
                            />
                        </div>
                    </>
                )}
                <div className="mt-2 border-t border-dashed pt-3">
                    <FormCheckbox
                        control={slotForm.control}
                        name="is_empty"
                        onChange={(value) => {
                            setIsHiddenTop(value);
                            if (value) {
                                slotForm.clearErrors();
                                slotForm.setValue("attendance_from", undefined);
                                slotForm.setValue("attendance_to", undefined);
                                slotForm.setValue("placeholder", "");
                                slotForm.setValue(
                                    "inherit_from_school_attendance",
                                    false
                                );
                                slotForm.setValue(
                                    "class_attendance_required",
                                    false
                                );
                            }
                        }}
                    />
                </div>
                <Button type="submit" className="ml-auto mt-8">
                    Save
                </Button>
            </form>
        </Form>
    );
};

export default TimeslotOverrideForm;
