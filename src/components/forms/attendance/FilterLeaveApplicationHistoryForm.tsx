import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormSelect from "@/components/ui/FormSelect";
import {
    APPROVED,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    PENDING,
    REJECTED,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { toYMD } from "@/lib/utils";
import { useTranslations } from "next-intl";

const FilterLeaveApplicationHistoryForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            date_from: filter?.date_from ?? undefined,
            date_to: filter?.date_to ?? undefined,
            status: filter?.status ?? undefined,
        },
    });
    const t = useTranslations("common");

    const [leaveApplicationDateRange, setLeaveApplicationDateRange] =
        useState<any>(null);

    function onSubmit(data: Record<string, any>) {
        if (leaveApplicationDateRange) {
            data.date_from = toYMD(leaveApplicationDateRange.startDate);
            data.date_to = toYMD(leaveApplicationDateRange.endDate);
        }

        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    useEffect(() => {
        if (filter?.date_from || filter?.date_to) {
            setLeaveApplicationDateRange(() => ({
                key: "selection",
                startDate: filter?.date_from ?? "",
                endDate: filter?.date_to ?? "",
            }));
        }
    }, [filter?.date_from, filter?.date_to]);

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="grid gap-y-5"
            >
                <div className="flex-grow">
                    <DateRangePicker
                        label={"leave application date"}
                        range={leaveApplicationDateRange}
                        setRange={setLeaveApplicationDateRange}
                    />
                </div>

                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[PENDING, APPROVED, REJECTED]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterLeaveApplicationHistoryForm;
