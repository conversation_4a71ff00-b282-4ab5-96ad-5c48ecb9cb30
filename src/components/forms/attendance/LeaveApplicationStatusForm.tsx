import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    PENDING,
    APPROVED,
    REJECTED,
    leaveApplicationsAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const LeaveApplicationStatusForm = ({ id, status, refresh }) => {
    const form = useForm({
        defaultValues: {
            status: status ?? null,
        },
    });

    const { axiosPut: updateStatus, error } = useAxios({
        api: `${leaveApplicationsAPI}/${id}/status?status=${status}`,
        noToast: true,
        onSuccess: (result) => {
            refresh();
        },
    });

    useEffect(() => {
        showBackendFormError(form, error);
    }, [error]);

    return (
        <Form {...form}>
            <form
                className="grid gap-4"
                onSubmit={(e) => {
                    e.preventDefault();
                    form.clearErrors();
                    form.handleSubmit((data) => {
                        updateStatus({
                            id: `${id}/status`,
                            data: { status: data.status },
                        });
                    })();
                }}
            >
                <h2 className="pb-1">Change Status</h2>
                <FormSelect
                    control={form.control}
                    name={"status"}
                    label="Status*"
                    options={[PENDING, APPROVED, REJECTED]}
                />
                <Button type="submit" className="ml-auto mt-2">
                    Save
                </Button>
            </form>
        </Form>
    );
};

export default LeaveApplicationStatusForm;
