import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import {
    APPROVED,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    leaveApplicationTypeAPI,
    PENDING,
    REJECTED,
    studentAPI,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { getTypeFromUserModel, getUserableType, toYMD } from "@/lib/utils";
import FormSelect from "@/components/ui/FormSelect";
import FormSelectAsync from "@/components/ui/FormSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";
import DateRangePicker from "@/components/ui/DateRangePicker";
import { useAxios } from "@/lib/hook";

const FilterLeaveApplicationForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const [leaveApplicationDateRange, setLeaveApplicationDateRange] =
        useState<any>(null);
    const [leaveApplicationTypeOptions, setLeaveApplicationTypeOptions] =
        useState<any[]>([]);

    const form = useForm({
        defaultValues: {
            leave_applicable_type: "Student",
            leave_applicable_id: filter?.leave_applicable_id ?? null,
            status: filter?.status ?? null,
            leave_application_date_from:
                filter?.leave_application_date_from ?? null,
            leave_application_date_to:
                filter?.leave_application_date_to ?? null,
            leave_application_date: filter?.leave_application_date ?? null,
            student_id: filter?.student_id ?? null,
            leave_application_type_id:
                filter?.leave_application_type_id ?? null,
        },
    });

    function onSubmit(data: Record<string, any>) {
        data.leave_applicable_type = getUserableType("Student");
        if (data.student_id && studentName) {
            data.leave_applicable_id = data.student_id;
            data.studentName = studentName;
        }

        if (leaveApplicationDateRange) {
            data.leave_application_date_from = toYMD(
                leaveApplicationDateRange.startDate
            );
            data.leave_application_date_to = toYMD(
                leaveApplicationDateRange.endDate
            );
        }
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const [studentName, setStudentName] = useState();

    const {
        asyncOptions: asyncStudentOptions,
        setAsyncOptions: setStudentAsyncOptions,
        loadAsyncOptions: loadStudentAsyncOptions,
    } = useAsyncSelect({
        api: studentAPI,
        useCommonSearch: true,
    });

    const { axiosQuery: getLeaveApplicationTypes } = useAxios({
        api: leaveApplicationTypeAPI,
        locale,
        onSuccess: (result) => {
            const options = result.data.map((res) => ({
                ...res,
                name: res.translations.name[locale],
            }));
            setLeaveApplicationTypeOptions(options);
        },
    });

    useEffect(() => {
        if (
            filter?.leave_application_date_from ||
            filter?.leave_application_date_to
        ) {
            setLeaveApplicationDateRange({
                key: "selection",
                startDate: filter?.leave_application_date_from ?? "",
                endDate: filter?.leave_application_date_to ?? "",
            });
        }
    }, [
        filter?.leave_application_date_from,
        filter?.leave_application_date_to,
    ]);

    useEffect(() => {
        if (
            getTypeFromUserModel(filter?.leave_applicable_type) === "Student" &&
            filter?.studentName
        ) {
            loadStudentAsyncOptions(
                filter?.studentName,
                setStudentAsyncOptions
            );
        }

        getLeaveApplicationTypes({ params: GET_ALL_PARAMS });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                {/* <FormSelect
                    control={form.control}
                    name="leave_applicable_type"
                    isStringOptions={true}
                    options={["Student", "Employee"]}
                /> */}

                {form.watch("leave_applicable_type") === "Student" && (
                    <FormSelectAsync
                        control={form.control}
                        name="student_id"
                        label="Leave Applicable (Student)"
                        loadOptions={loadStudentAsyncOptions}
                        value={asyncStudentOptions.find(
                            (option) =>
                                option.value === form.watch("student_id")
                        )}
                        onChange={(val) => {
                            if (val) {
                                form.setValue("student_id", val.value);
                                setStudentName(val?.name);
                            } else {
                                form.setValue("student_id", null);
                            }
                        }}
                    />
                )}

                <div className="flex-grow">
                    <DateRangePicker
                        label={"leave application date"}
                        range={leaveApplicationDateRange}
                        setRange={setLeaveApplicationDateRange}
                    />
                </div>

                <FormSelect
                    control={form.control}
                    name={"leave_application_type_id"}
                    label="Leave Type"
                    options={leaveApplicationTypeOptions}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[PENDING, APPROVED, REJECTED]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterLeaveApplicationForm;
