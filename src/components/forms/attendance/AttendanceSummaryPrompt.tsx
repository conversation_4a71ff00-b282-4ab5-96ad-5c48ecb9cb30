import React from "react";
import { Label } from "@/components/base-ui/label";

type AttendanceSummaryPromptProps = {
    attendanceSummary: Record<
        string,
        {
            header: string;
            body: { [status: string]: number }[];
            footer: { TOTAL: number };
        }
    >;
    close: () => void;
};

const AttendanceSummaryPrompt = ({
    close,
    attendanceSummary,
}: AttendanceSummaryPromptProps) => {
    return (
        <div className="p-2">
            <h2 className="mb-3 text-lg font-semibold">Attendance Summary</h2>
            <div className="space-y-8">
                {Object.entries(attendanceSummary).map(([period, summary]) => {
                    const late =
                        summary.body?.find((item) => item.LATE !== undefined)
                            ?.LATE || 0;
                    const absent =
                        summary.body?.find((item) => item.ABSENT !== undefined)
                            ?.ABSENT || 0;
                    const present =
                        summary.body?.find((item) => item.PRESENT !== undefined)
                            ?.PRESENT || 0;
                    const total = summary.footer.TOTAL;

                    return (
                        <div key={period}>
                            <div className="mb-3">
                                <Label>{summary.header}</Label>
                            </div>

                            <div className="mb-4 grid grid-cols-2 gap-4 sm:grid-cols-3">
                                <div className="rounded-xl border border-yellow-200 bg-yellow-50 p-4 text-center shadow-sm">
                                    <p className="text-sm font-medium text-yellow-600">
                                        LATE
                                    </p>
                                    <p className="text-2xl font-bold text-yellow-700">
                                        {late}
                                    </p>
                                </div>
                                <div className="rounded-xl border border-red-200 bg-red-50 p-4 text-center shadow-sm">
                                    <p className="text-sm font-medium text-red-600">
                                        ABSENT
                                    </p>
                                    <p className="text-2xl font-bold text-red-700">
                                        {absent}
                                    </p>
                                </div>
                                <div className="rounded-xl border border-green-200 bg-green-50 p-4 text-center shadow-sm">
                                    <p className="text-sm font-medium text-green-600">
                                        PRESENT
                                    </p>
                                    <p className="text-2xl font-bold text-green-700">
                                        {present}
                                    </p>
                                </div>
                            </div>

                            <div className="mb-6 w-full rounded-xl bg-gray-100 px-6 py-4 text-center shadow-inner">
                                <p className="text-sm tracking-wide text-gray-600">
                                    TOTAL STUDENTS
                                </p>
                                <p className="text-3xl font-bold text-gray-800">
                                    {total}
                                </p>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default AttendanceSummaryPrompt;
