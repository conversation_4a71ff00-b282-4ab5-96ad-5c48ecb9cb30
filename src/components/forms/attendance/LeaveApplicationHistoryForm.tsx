import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import DateRangePicker from "@/components/ui/DateRangePicker";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    APPROVED,
    CommonFormProps,
    leaveApplicationsAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { isValueTrue, showBackendFormError, toYMD } from "@/lib/utils";
import FormInputDecimal from "@/components/ui/FormInputDecimal";
import FormFileInput from "@/components/ui/FormFileInput";
import FormCheckbox from "@/components/ui/FormCheckbox";
import FormTextarea from "@/components/ui/FormTextarea";
import InfoCard from "@/components/ui/InfoCard";

const LeaveApplicationHistoryForm = (
    props: CommonFormProps & {
        allPeriods: any;
        selectedStudent: any;
        leaveApplicationTypes: any;
        createData?: any;
    }
) => {
    const locale = useLocale();
    const {
        data: currentLeaveApplicationData,
        axiosQuery: getLeaveApplicationData,
    } = useAxios({
        api: leaveApplicationsAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getLeaveApplicationData({ id: props.id });
        }
    }, []);

    return props.allPeriods &&
        ((props.isCreate && props.createData) ||
            currentLeaveApplicationData) ? (
        <FormWrap
            currentLeaveApplicationData={currentLeaveApplicationData}
            leaveApplicationTypeOptions={props.leaveApplicationTypes}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentLeaveApplicationData: any;
    allPeriods: any;
    leaveApplicationTypeOptions: any;
    selectedStudent: any;
    createData?: any;
};

const FormWrap = ({
    isCreate = false,
    currentLeaveApplicationData,
    leaveApplicationTypeOptions,
    allPeriods,
    selectedStudent,
    createData,
    refresh,
    close,
}: FormWrapProps) => {
    const locale = useLocale();
    const form = useForm<any>({
        defaultValues: {
            leave_application_type_id:
                currentLeaveApplicationData?.leave_application_type?.id ?? "",
            from_date: "",
            to_date: "",
            period_group_id: allPeriods?.period_group_id ?? "",
            period_label_ids: [],
            reason: isCreate
                ? `${selectedStudent?.grade} Absent`
                : currentLeaveApplicationData?.reason ?? "",
            remarks: currentLeaveApplicationData?.remarks ?? "",
            average_point_deduction:
                currentLeaveApplicationData?.average_point_deduction,
            conduct_point_deduction:
                currentLeaveApplicationData?.conduct_point_deduction,
            is_present: isValueTrue(currentLeaveApplicationData?.is_present),
        },
    });

    const [dateRange, setDateRange] = useState<any>(null);

    const handleApplicationTypeChange = (selected: any) => {
        if (!selected) {
            form.setValue("average_point_deduction", "");
            form.setValue("conduct_point_deduction", "");
            form.setValue("is_present", false);
        } else {
            const applicationTypeData = leaveApplicationTypeOptions.find(
                (type) => selected == type.id
            );

            form.setValue(
                "average_point_deduction",
                applicationTypeData.average_point_deduction
            );
            form.setValue(
                "conduct_point_deduction",
                applicationTypeData.conduct_point_deduction
            );
            form.setValue("is_present", applicationTypeData.is_present);
        }
    };

    const { axiosMultipartPost: createLeaveApplication, error: postError } =
        useAxios({
            api: leaveApplicationsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { axiosMultipartPut: updateLeaveApplicationRecord, error: putError } =
        useAxios({
            api: leaveApplicationsAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    const { initLoader } = useSubmit();

    function onSubmit(e) {
        form.clearErrors();
        e.preventDefault();

        initLoader(
            form.handleSubmit((data: any) => {
                if (dateRange) {
                    data.from_date = toYMD(dateRange.startDate);
                    data.to_date = toYMD(dateRange.endDate);
                }

                if (data.proof) {
                    data.proof = data.proof?.[0];
                } else {
                    if (isCreate) {
                        data.proof = null;
                    } else {
                        delete data.proof;
                    }
                }

                data.is_present = data.is_present ? 1 : 0;

                if (isCreate) {
                    const periodGroupStudents: any[] = [];
                    periodGroupStudents.push({
                        period_group_id: data.period_group_id,
                        period_label_ids: data.period_label_ids,
                        student_ids: [selectedStudent?.value],
                    });
                    data.period_group_students = periodGroupStudents;

                    delete data.period_label_ids;
                    delete data.period_group_id;

                    console.log("create 2:", data);
                    createLeaveApplication(data);
                } else {
                    updateLeaveApplicationRecord({
                        id: currentLeaveApplicationData.id,
                        data,
                    });
                }
            })
        );
    }

    useEffect(() => {
        if (createData) {
            if (createData?.date) {
                setDateRange({
                    startDate: createData?.date,
                    endDate: createData?.date,
                    key: "selection",
                });
            }

            if (createData?.absent_period_ids) {
                const periodLabelIds = allPeriods.period_labels
                    .filter((label) =>
                        createData.absent_period_ids.includes(label.period)
                    )
                    .map((label) => label.id);
                form.setValue("period_label_ids", periodLabelIds);
            }
        }
    }, [createData]);

    useEffect(() => {
        if (!currentLeaveApplicationData?.leave_application_periods) return;

        const { leaveDates, periodLabelIds } =
            currentLeaveApplicationData.leave_application_periods.reduce(
                (acc, lp) => {
                    const date = new Date(lp.date);
                    acc.leaveDates.push(date);
                    const matchedLabel = allPeriods.period_labels.find(
                        (periodLabel) => periodLabel.period == lp.period
                    );
                    if (matchedLabel) {
                        acc.periodLabelIds.push(matchedLabel.id);
                    }

                    return acc;
                },
                { leaveDates: [], periodLabelIds: [] }
            );

        leaveDates.sort((a, b) => a - b);

        if (leaveDates.length > 0) {
            const firstDate = toYMD(leaveDates[0]) ?? "-";
            const lastDate =
                toYMD(leaveDates[leaveDates.length - 1]) ?? firstDate;

            setDateRange({
                startDate: firstDate,
                endDate: lastDate,
                key: "selection",
            });
        }

        form.setValue("period_label_ids", periodLabelIds);
    }, [currentLeaveApplicationData?.leave_application_periods]);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <>
            <h2>{isCreate ? "Create" : "Update"} Attendance</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="lg:min-w-[800px]">
                    <InfoCard
                        title="Student information"
                        data={{
                            name: selectedStudent?.name,
                            student_number: selectedStudent?.student_number,
                            class: selectedStudent?.class,
                        }}
                        cardStyleClass="lg:min-w-[400px]"
                    />

                    <div className="grid-form pb-2 pt-5">
                        <div className="lg:col-span-1">
                            <DateRangePicker
                                label="Date Range*"
                                range={dateRange}
                                setRange={setDateRange}
                                error={
                                    form.formState.errors?.from_date?.message ||
                                    form.formState.errors?.to_date?.message
                                }
                                isDisabled={
                                    currentLeaveApplicationData?.status ===
                                    APPROVED
                                }
                            />
                        </div>

                        <FormSelect
                            control={form.control}
                            name={"leave_application_type_id"}
                            label="Leave Type*"
                            options={leaveApplicationTypeOptions}
                            onChange={(selected) =>
                                handleApplicationTypeChange(selected)
                            }
                            isDisabled={
                                currentLeaveApplicationData?.status === APPROVED
                            }
                        />

                        <FormFileInput
                            name="proof"
                            currentFileUrl={
                                currentLeaveApplicationData?.proof?.url
                            }
                            register={form.register}
                            errors={form.formState.errors}
                            disabled={
                                currentLeaveApplicationData?.status === APPROVED
                            }
                        />

                        <FormSelect
                            isMulti={true}
                            hasSelectAll={true}
                            control={form.control}
                            isSortByName={false}
                            name="period_label_ids"
                            label="absent periods"
                            options={
                                allPeriods?.period_labels
                                    ?.filter((p) => p.can_apply_leave)
                                    ?.map((p) => ({
                                        id: p?.id,
                                        name: `${p?.translations?.name?.[locale]} (${p.time})`,
                                    })) ?? []
                            }
                            hideSelectedOptions={false}
                            displayItemSelected={true}
                            maxMenuHeight={425}
                            isClearable={
                                currentLeaveApplicationData?.status === APPROVED
                                    ? false
                                    : true
                            }
                        />

                        <FormInput
                            control={form.control}
                            name="reason"
                            label="reason*"
                            disabled={
                                currentLeaveApplicationData?.status === APPROVED
                            }
                        />

                        <FormInputDecimal
                            control={form.control}
                            type="number"
                            step="0.01"
                            name="average_point_deduction"
                            label="Average Point Deduction (Per Period)"
                            disabled={
                                currentLeaveApplicationData?.status === APPROVED
                            }
                        />

                        <FormInputDecimal
                            control={form.control}
                            type="number"
                            step="0.01"
                            name="conduct_point_deduction"
                            label="Conduct Point Deduction (Per Period)"
                            disabled={
                                currentLeaveApplicationData?.status === APPROVED
                            }
                        />

                        <div className="mt-3 lg:mt-7">
                            <FormCheckbox
                                control={form.control}
                                name="is_present"
                                styleClass="mt-3 ml-1"
                                isDisabled={
                                    currentLeaveApplicationData?.status ===
                                    APPROVED
                                }
                            />
                        </div>

                        <div className="lg:col-span-2">
                            <FormTextarea
                                control={form.control}
                                name="remarks"
                                disabled={
                                    currentLeaveApplicationData?.status ===
                                    APPROVED
                                }
                            />
                        </div>
                    </div>

                    <div className="mt-6 flex justify-end gap-x-3">
                        <Button
                            type="submit"
                            disabled={
                                currentLeaveApplicationData?.status === APPROVED
                            }
                        >
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
};

export default LeaveApplicationHistoryForm;
