import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { format, parse } from "date-fns";
import { useForm, Controller } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form, FormLabel } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import { TimePicker } from "@/components/ui/TimePicker";
import {
    schoolAttendancePeriodOverrideAPI,
    CommonFormProps,
    DATE_FORMAT,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError, toYMD } from "@/lib/utils";
import FormInputInterger from "../../ui/FormInputInterger";
import FormTextarea from "../../ui/FormTextarea";

const SchoolAttendancePeriodOverrideForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const {
        data: schoolAttendancePeriodOverride,
        axiosQuery: getSchoolAttendancePeriodOverride,
    } = useAxios({
        api: schoolAttendancePeriodOverrideAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getSchoolAttendancePeriodOverride({ id: props.id });
        }
    }, []);

    return props.isCreate || schoolAttendancePeriodOverride ? (
        <FormWrap
            schoolAttendancePeriodOverride={schoolAttendancePeriodOverride}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    schoolAttendancePeriodOverride: any;
};

const FormWrap = ({
    isCreate = false,
    schoolAttendancePeriodOverride,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            from: schoolAttendancePeriodOverride?.from ?? undefined,
            to: schoolAttendancePeriodOverride?.to ?? undefined,
            attendance_from: schoolAttendancePeriodOverride?.attendance_from
                ? parse(
                      schoolAttendancePeriodOverride.attendance_from,
                      DATE_FORMAT.timeHms,
                      new Date()
                  )
                : undefined,
            attendance_to: schoolAttendancePeriodOverride?.attendance_to
                ? parse(
                      schoolAttendancePeriodOverride.attendance_to,
                      DATE_FORMAT.timeHms,
                      new Date()
                  )
                : undefined,
            remarks: schoolAttendancePeriodOverride?.remarks ?? "",
            priority: schoolAttendancePeriodOverride?.priority ?? 1,
        },
    });

    const {
        axiosPost: createSchoolAttendancePeriodOverride,
        error: postError,
    } = useAxios({
        api: schoolAttendancePeriodOverrideAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSchoolAttendancePeriodOverride, error: putError } =
        useAxios({
            api: schoolAttendancePeriodOverrideAPI,
            onSuccess: () => {
                close();
                refresh();
            },
        });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.from = toYMD(data.from);
            data.to = toYMD(data.to);
            data.attendance_from = data.attendance_from
                ? format(data.attendance_from, DATE_FORMAT.timeHms)
                : null;
            data.attendance_to = data.attendance_to
                ? format(data.attendance_to, DATE_FORMAT.timeHms)
                : null;
            if (isCreate) {
                createSchoolAttendancePeriodOverride(data);
            } else {
                updateSchoolAttendancePeriodOverride({
                    id: schoolAttendancePeriodOverride.id,
                    data: data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} School Attendance Period
                Override
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <DatePicker
                        control={form.control}
                        name={"from"}
                        label={"From*"}
                    />
                    <DatePicker
                        control={form.control}
                        name={"to"}
                        label={"To*"}
                    />
                    <div className="">
                        <FormLabel className="mb-1 capitalize">
                            Attendance Period*
                        </FormLabel>
                        <div className="flex items-center gap-5">
                            <Controller
                                name={`attendance_from`}
                                control={form.control}
                                render={({ field }) => (
                                    <div className="flex h-12 min-w-[120px] items-center justify-center">
                                        <TimePicker
                                            date={field.value}
                                            setDate={field.onChange}
                                        />
                                    </div>
                                )}
                            />
                            -
                            <Controller
                                name={`attendance_to`}
                                control={form.control}
                                render={({ field }) => (
                                    <div className="flex h-12 min-w-[120px] items-center justify-center">
                                        <TimePicker
                                            date={field.value}
                                            setDate={field.onChange}
                                        />
                                    </div>
                                )}
                            />
                        </div>
                        {(form.formState.errors?.attendance_from ||
                            form.formState.errors?.attendance_to) && (
                            <div className="mt-1 text-[13px] font-medium leading-tight text-destructive">
                                At least one attendance period must be set
                            </div>
                        )}
                    </div>
                    <FormTextarea
                        control={form.control}
                        name="remarks"
                        label={"remarks"}
                    />
                    <FormInputInterger
                        control={form.control}
                        name="priority"
                        label={"Priority"}
                    />

                    <Button type="submit" className="ml-auto mt-3">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SchoolAttendancePeriodOverrideForm;
