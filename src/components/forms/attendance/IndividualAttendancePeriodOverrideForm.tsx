import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { format, parse } from "date-fns";
import { useForm, Controller } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form, FormLabel } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSearchInput from "@/components/ui/FormSearchInput";
import Modal from "@/components/ui/Modal";
import { TimePicker } from "@/components/ui/TimePicker";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    individualAttendancePeriodOverrideAPI,
    CommonFormProps,
    DATE_FORMAT,
    STUDENT,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    showBackendFormError,
    toYMD,
    optionUserLabel,
    getInputErrorMessage,
} from "@/lib/utils";

const IndividualAttendancePeriodOverrideForm = (props: CommonFormProps) => {
    const locale = useLocale();

    const {
        data: individualAttendancePeriodOverride,
        axiosQuery: getIndividualAttendancePeriodOverride,
    } = useAxios({
        api: individualAttendancePeriodOverrideAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getIndividualAttendancePeriodOverride({ id: props.id });
        }
    }, []);

    return props.isCreate || individualAttendancePeriodOverride ? (
        <FormWrap
            individualAttendancePeriodOverride={
                individualAttendancePeriodOverride
            }
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    individualAttendancePeriodOverride: any;
};

const FormWrap = ({
    isCreate = false,
    individualAttendancePeriodOverride,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            student_ids: [
                individualAttendancePeriodOverride?.attendance_recordable
                    ?.userable_id ?? undefined,
            ],
            period: individualAttendancePeriodOverride?.period ?? undefined,
            attendance_from: individualAttendancePeriodOverride?.attendance_from
                ? parse(
                      individualAttendancePeriodOverride.attendance_from,
                      DATE_FORMAT.timeHms,
                      new Date()
                  )
                : parse("00:00:00", DATE_FORMAT.timeHms, new Date()),
            attendance_to: individualAttendancePeriodOverride?.attendance_to
                ? parse(
                      individualAttendancePeriodOverride.attendance_to,
                      DATE_FORMAT.timeHms,
                      new Date()
                  )
                : parse("00:00:00", DATE_FORMAT.timeHms, new Date()),
        },
    });

    const [openSearch, setOpenSearch] = useState<typeof STUDENT | null>(null);
    const [selectedStudent, setSelectedStudent] = useState<any>();

    const {
        axiosPost: createIndividualAttendancePeriodOverride,
        error: postError,
    } = useAxios({
        api: individualAttendancePeriodOverrideAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const {
        axiosPut: updateIndividualAttendancePeriodOverride,
        error: putError,
    } = useAxios({
        api: individualAttendancePeriodOverrideAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const locale = useLocale();

    const { axiosQuery: getStudentById } = useAxios({
        api: studentAPI,
        locale,
        onSuccess(result) {
            setSelectedStudent(result?.data);
        },
    });

    useEffect(() => {
        if (individualAttendancePeriodOverride) {
            getStudentById({
                id: individualAttendancePeriodOverride?.attendance_recordable
                    ?.userable_id,
            });
        }
    }, []);

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.period = toYMD(data.period);
            data.attendance_from = data.attendance_from
                ? format(data.attendance_from, DATE_FORMAT.timeHms)
                : undefined;
            data.attendance_to = data.attendance_to
                ? format(data.attendance_to, DATE_FORMAT.timeHms)
                : undefined;
            if (isCreate) {
                data.student_ids = data.student_ids.filter((id) => id);
                createIndividualAttendancePeriodOverride(data);
            } else {
                data.userable_id = data.student_ids?.[0];
                data.userable_type = "App\\Models\\Student";
                updateIndividualAttendancePeriodOverride({
                    id: individualAttendancePeriodOverride.id,
                    data: data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    return (
        <div>
            <h2 className="mb-5 mt-2">
                {isCreate ? "Create" : "Update"} Individual Attendance Period
                Override
            </h2>
            <Form {...form}>
                {/* is disabled with pointer-events-none*/}
                <form
                    onSubmit={onSubmit}
                    className="pointer-events-none grid gap-y-5"
                >
                    <div className="">
                        <FormSearchInput
                            name="student_id"
                            control={form.control}
                            label="Student*"
                            displayValue={optionUserLabel(
                                selectedStudent?.student_number,
                                selectedStudent?.translations?.name
                            )}
                            onClick={() => setOpenSearch(STUDENT)}
                        />
                        {selectedStudent?.photo && (
                            <img
                                src={selectedStudent.photo}
                                className="mt-2 h-auto w-40 rounded-sm"
                            />
                        )}
                        {form.formState.errors?.student_ids && (
                            <div className="warning-text mt-1">
                                {`${getInputErrorMessage(form.formState.errors.student_ids)}`}
                            </div>
                        )}
                    </div>
                    <DatePicker
                        control={form.control}
                        name={"period"}
                        label={"date*"}
                    />

                    <div className="">
                        <FormLabel className="mb-1 capitalize">
                            Attendance Period*
                        </FormLabel>
                        <div className="relative mb-1 flex items-center gap-5">
                            <Controller
                                name={`attendance_from`}
                                control={form.control}
                                render={({ field }) => (
                                    <div className="flex h-12 min-w-[120px] items-center justify-center">
                                        <TimePicker
                                            date={field.value}
                                            setDate={field.onChange}
                                        />
                                    </div>
                                )}
                            />
                            -
                            <Controller
                                name={`attendance_to`}
                                control={form.control}
                                render={({ field }) => (
                                    <div className="flex h-12 min-w-[120px] items-center justify-center">
                                        <TimePicker
                                            date={field.value}
                                            setDate={field.onChange}
                                        />
                                    </div>
                                )}
                            />
                        </div>
                    </div>

                    {/* <Button type="submit" className="ml-auto mt-5">
                        Submit
                    </Button> */}
                </form>
            </Form>

            <Modal
                open={openSearch === STUDENT}
                onOpenChange={setOpenSearch}
                size="large"
            >
                <StudentSearchEngine
                    setSelection={(student) => {
                        if (student) {
                            form.setValue("student_ids.0", student.id);
                            form.clearErrors("student_ids");
                            setSelectedStudent(student);
                        }
                    }}
                    close={() => setOpenSearch(null)}
                />
            </Modal>
        </div>
    );
};

export default IndividualAttendancePeriodOverrideForm;
