import { useLocale, useTranslations } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { capitalize } from "lodash";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import {
    attendanceInputAPI,
    CommonFormProps,
    DATE_FORMAT,
    STUDENT,
    EMPLOYEE,
    CONTRACTOR,
    studentAPI,
    trainerAPI,
    employeeAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    showBackendFormError,
    toUTC,
    toYMD,
    getUserableType,
    strStartCase,
    isValueTrue,
} from "@/lib/utils";
import FormTextarea from "../../ui/FormTextarea";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";
import FormCheckbox from "@/components/ui/FormCheckbox";

const AttendanceInputForm = (
    props: CommonFormProps & {
        type: typeof STUDENT | typeof EMPLOYEE | typeof CONTRACTOR;
    }
) => {
    const locale = useLocale();

    const { data: attendanceInput, axiosQuery: getAttendanceInput } = useAxios({
        api: attendanceInputAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getAttendanceInput({
                id: props.id,
            });
        }
    }, []);

    return props.isCreate || attendanceInput ? (
        <FormWrap attendanceInput={attendanceInput} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    type: typeof STUDENT | typeof EMPLOYEE | typeof CONTRACTOR;
    attendanceInput: any;
};

const FormWrap = ({
    type,
    isCreate = false,
    attendanceInput,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm<any>({
        defaultValues: {
            userable_id: attendanceInput?.attendance_recordable?.userable_id,
            userable_type:
                attendanceInput?.attendance_recordable?.userable_type,
            date: attendanceInput?.date ?? new Date(),
            record_datetime: attendanceInput?.record_datetime ?? new Date(),
            remarks: attendanceInput?.remarks ?? "",
            repost_school_attendance:
                isValueTrue(attendanceInput?.repost_school_attendance) ?? true,
        },
    });

    function getAPI() {
        switch (type) {
            case STUDENT:
                return studentAPI;
            case EMPLOYEE:
                return employeeAPI;
            case CONTRACTOR:
                return trainerAPI;
            default:
                return "";
        }
    }

    const { asyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: getAPI(),
        useCommonSearch: type === STUDENT,
    });

    const { axiosPost: createAttendanceInput, error: postError } = useAxios({
        api: attendanceInputAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateAttendanceInput, error: putError } = useAxios({
        api: attendanceInputAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            data.record_datetime = toUTC(
                data.record_datetime,
                DATE_FORMAT.YMD_HMS
            );
            data.date = toYMD(data.date);
            data.userable_type = data?.userable_type ?? getUserableType(type);
            if (isCreate) {
                createAttendanceInput(data);
            } else {
                updateAttendanceInput({
                    id: attendanceInput.id,
                    data: data,
                });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError || putError]);

    const t = useTranslations("common");

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} School Attendance Input (
                {type === CONTRACTOR ? "Coach" : capitalize(type)})
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FreeSelectAsync
                        control={form.control}
                        name="userable_id"
                        label={`${strStartCase(type)}*`}
                        placeholder={`Type to search ${type.toLowerCase()}`}
                        minWidth={300}
                        loadOptions={loadAsyncOptions}
                        value={asyncOptions.find(
                            (option) =>
                                option.value === form.getValues("student_id")
                        )}
                        error={form.formState.errors?.userable_id}
                    />

                    <DatePicker
                        control={form.control}
                        name={"date"}
                        label={"date*"}
                    />

                    <DateTimePicker
                        control={form.control}
                        name={"record_datetime"}
                        label="Tapped Card at*"
                    />

                    <FormTextarea control={form.control} name="remarks" />

                    <FormCheckbox
                        control={form.control}
                        name="repost_school_attendance"
                    />

                    <Button type="submit" className="ml-auto mt-5">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default AttendanceInputForm;
