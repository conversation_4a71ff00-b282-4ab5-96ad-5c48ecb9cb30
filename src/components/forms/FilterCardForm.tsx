import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    ACTIVE,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    INACTIVE,
} from "@/lib/constant";
import { Button } from "../base-ui/button";
import { useTranslations } from "next-intl";

const FilterCardForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            card_number: filter?.card_number ?? "",
            status: filter?.status ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        const _filter: any = { ...filter, ...data, page: 1 };
        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    return (
        <Form {...form}>
            <h2 className="mb-2 ml-0.5 text-themeBlack">{t("Filter")}</h2>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="grid gap-y-5"
            >
                <FormInput
                    control={form.control}
                    name={"card_number"}
                    label={`${t("Card number")}`}
                />

                <FormSelect
                    control={form.control}
                    name="status"
                    options={[
                        {
                            name: t("Active"),
                            id: ACTIVE,
                        },
                        {
                            name: t("Inactive"),
                            id: INACTIVE,
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterCardForm;
