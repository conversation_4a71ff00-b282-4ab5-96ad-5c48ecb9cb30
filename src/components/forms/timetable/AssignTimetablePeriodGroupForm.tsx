import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import { GET_ALL_PARAMS, periodGroupAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const AssignTimetablePeriodGroupForm = (props) => {
    const locale = useLocale();

    const { data: periodGroupOptions, axiosQuery: getPeriodGroups } = useAxios({
        api: periodGroupAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.id) return;
        getPeriodGroups({
            params: {
                ...GET_ALL_PARAMS,
                order_by: { name: "asc" },
            },
        });
    }, []);

    return periodGroupOptions ? (
        <FormWrap periodGroupOptions={periodGroupOptions} {...props} />
    ) : (
        <div className="h-10"></div>
    );
};

const FormWrap = ({ id, name, periodGroupOptions, refresh, close }) => {
    const form = useForm({
        defaultValues: {
            period_group_id: "",
        },
    });

    const { axiosPut: assignPeriodGroup, error: putError } = useAxios({
        api: `timetables/${id}/assign-period-group`,
        toastMsg: "Assigned successfully",
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            console.log("data", data);
            assignPeriodGroup({ data });
        })();
    }

    useEffect(() => {
        showBackendFormError(form, putError);
    }, [putError]);

    return (
        <Form {...form}>
            <form onSubmit={onSubmit}>
                <h2>Assign Period Group for {name}</h2>
                <div className="mt-5 grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name="period_group_id"
                        label="Period Group"
                        options={periodGroupOptions}
                    />

                    <Button type="submit" className="mb-8 ml-auto mt-12">
                        Submit
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default AssignTimetablePeriodGroupForm;
