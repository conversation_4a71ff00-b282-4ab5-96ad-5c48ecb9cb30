import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import { CommonFilterProps, DEFAULT_FILTER_PARAMS } from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterTimetableForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            class_name: filter?.class_name ?? "",
            semester_name: filter?.semester_name ?? "",
            is_active: filter?.is_active ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        console.log(data);
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormInput control={form.control} name={"semester_name"} />
                <FormInput control={form.control} name={"class_name"} />

                {/* <FormSelect
                    control={form.control}
                    name={"class_name"}
                    label="class"
                    isStringOptions={true}
                    options={semesterClassOptions?.map(
                        (option) => option?.class_model?.name
                    )}
                />

                <FormSelect
                    control={form.control}
                    name={"semester_name"}
                    label="semester"
                    isStringOptions={true}
                    options={semesterClassOptions?.map(
                        (option) => option?.semester_setting?.name
                    )}
                /> */}

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: "Active",
                            id: "1",
                        },
                        {
                            name: "Inactive",
                            id: "0",
                        },
                    ]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterTimetableForm;
