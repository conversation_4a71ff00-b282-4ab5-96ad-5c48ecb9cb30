import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    periodGroupAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";

const FilterPeriodForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const form = useForm({
        defaultValues: {
            period_group_id: filter?.period_group_id ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    const { data: periodGroupOptions, axiosQuery: getPeriodGroup } = useAxios({
        api: periodGroupAPI,
        locale,
        onError: close,
    });

    useEffect(() => {
        getPeriodGroup({ params: { ...GET_ALL_PARAMS } });
    }, [locale]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    name={"period_group_id"}
                    label="period group*"
                    options={periodGroupOptions}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterPeriodForm;
