import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    periodAPI,
    periodGroupAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const PeriodForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentPeriod, axiosQuery: getPeriod } = useAxios({
        api: periodAPI,
        locale,
        onError: props.close,
    });

    const { data: periodGroupOptions, axiosQuery: getPeriodGroup } = useAxios({
        api: periodGroupAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getPeriod({ id: props.id });
        }
        getPeriodGroup({ params: { ...GET_ALL_PARAMS } });
    }, []);

    return activeLanguages && (props.isCreate || currentPeriod) ? (
        <FormWrap
            currentPeriod={currentPeriod}
            periodGroupOptions={periodGroupOptions}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentPeriod: any;
    periodGroupOptions: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentPeriod,
    periodGroupOptions,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            period_group_id: currentPeriod?.period_group?.id ?? "",
        },
    });

    console.log("form values", form.getValues());

    const { axiosPost: createPeriod, error: postError } = useAxios({
        api: `${periodAPI}/bulk-save`,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updatePeriod, error: putError } = useAxios({
        api: periodAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createPeriod(data);
            } else {
                updatePeriod({ id: currentPeriod.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Period</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        name={"period_group_id"}
                        label="period group*"
                        options={periodGroupOptions}
                    />

                    {/* TODO: Period input table (low priority) */}

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default PeriodForm;
