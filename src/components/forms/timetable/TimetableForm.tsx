import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import {
    GET_ALL_PARAMS,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    timetableAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { showBackendFormError } from "@/lib/utils";

const TimetableForm = ({ refresh, close }) => {
    const form = useForm({
        defaultValues: {
            name: "",
            semester_setting_id: "",
            semester_class_id: "",
            is_active: false,
        },
    });

    const locale = useLocale();

    const { data: semesterOptions, axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );
            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClasses } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    const { axiosPost: createTimetable, error: postError } = useAxios({
        api: timetableAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            console.log("data", data);

            createTimetable(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    useEffect(() => {
        if (form.watch("semester_setting_id")) {
            getSemesterClasses({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: form.watch("semester_setting_id"),
                },
            });
        } else {
            form.setValue("semester_class_id", "");
        }
    }, [form.watch("semester_setting_id")]);

    return (
        <div>
            <h2 className="mb-5">Create Timetable</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormInput control={form.control} name="name" />

                    <FormSelect
                        control={form.control}
                        name={"semester_setting_id"}
                        label="Semester*"
                        options={semesterOptions}
                        isSortByName={false}
                    />

                    <FormSelect
                        control={form.control}
                        name="semester_class_id"
                        label="Semester Class"
                        isDisabled={!form.watch("semester_setting_id")}
                        options={
                            semesterClassOptions?.map((option) => ({
                                id: option?.id,
                                name: option?.class_model?.name,
                            })) ?? []
                        }
                    />

                    <StatusFormSwitch
                        control={form.control}
                        name="is_active"
                        label="Status"
                    />

                    <Button type="submit" className="ml-auto mt-10">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default TimetableForm;
