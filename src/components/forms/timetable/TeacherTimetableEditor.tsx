import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import clsx from "clsx";
import { format, parse } from "date-fns";
import { LucidePrinter, PenBoxIcon, X, XCircle } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Checkbox } from "@/components/base-ui/checkbox";
import { Label } from "@/components/base-ui/label";
import FreeCheckbox from "@/components/ui/FreeCheckbox";
import FreeInput from "@/components/ui/FreeInput";
import LoaderOverlay from "@/components/ui/LoaderOverlay";
import {
    classSubjectAPI,
    employeeAPI,
    employeeTimetableAPI,
    GET_ALL_PARAMS,
    PRIMARY,
    SECONDARY,
    timetableAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    displayTime,
    getSemesterClassName,
    isValueTrue,
    optionUserLabel,
    shortenDay,
    truncateText,
} from "@/lib/utils";
import { But<PERSON> } from "../../base-ui/button";
import { Form } from "../../base-ui/form";
import FormInput from "../../ui/FormInput";
import FormSelect from "../../ui/FormSelect";
import Modal from "../../ui/Modal";
import { TimePicker } from "../../ui/TimePicker";
import { DialogFooter } from "@/components/base-ui/dialog";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";

const slotHeight = "min-h-[100px]";

const TeacherTimetableEditor = ({ teacherId, semesterClassId, close }) => {
    const locale = useLocale();

    const { data: current, axiosQuery: getCurrentData } = useAxios({
        api: `${employeeTimetableAPI}/${teacherId}`,
        locale,
        onError: close,
    });

    const { data: latest, axiosQuery: getLatestData } = useAxios({
        api: `${employeeTimetableAPI}/${teacherId}`,
        locale,
    });

    const { data: classSubjectData, axiosQuery: getClassSubjectData } =
        useAxios({
            api: classSubjectAPI,
            noLoading: true,
            locale,
        });

    useEffect(() => {
        if (!teacherId) return;
        getClassSubjectData({
            params: {
                ...GET_ALL_PARAMS,
                semester_class_id: semesterClassId,
            },
        });
        getCurrentData();
    }, [teacherId]);

    return current && classSubjectData ? (
        <Editor
            data={{
                // name: props.name,
                // is_active: props.is_active,
                ...(latest ?? current),
            }}
            subjectOptions={classSubjectData?.map((item) => ({
                id: item?.id,
                name: item?.subject?.name,
                teachers: item?.teachers,
            }))}
            refresh={() => {
                getLatestData();
            }}
            close={close}
        />
    ) : (
        <div className="h-10">
            <LoaderOverlay />
        </div>
    );
};

const Editor = ({ data, subjectOptions, refresh, close }) => {
    const locale = useLocale();
    const form = useForm({
        defaultValues: {
            timeslots: getSlots(),
        },
    });

    const [target, setTarget] = useState<any>(null);

    useEffect(() => {
        // form.setValue("timeslots", getSlots());
        getSlots();
    }, [data]);

    function getSlots() {
        const slotDataList = Object.values(data.employee_timetable).flatMap(
            (periodsSlots: Record<string, any>) =>
                Object.values(periodsSlots).flatMap((slots: any[]) => slots)
        );

        const newSlots: any[] = [];

        // Place each slotData by timeslot_id as index
        slotDataList.forEach((slotData: any) => {
            if (slotData.timeslot_id == null) return;
            newSlots[slotData.timeslot_id] = slotData;
        });

        console.log("teacher slots", newSlots);
        return newSlots;
    }

    const { axiosPut: updateTimetable } = useAxios({
        api: timetableAPI,
        onSuccess: () => {
            setTimeout(() => {
                refresh();
            }, 10);
        },
    });

    function onSubmit() {
        // const formData = form.getValues();
        // formData.timeslots = formData.timeslots
        //     .filter((slot) => slot)
        //     .map((slot) => ({
        //         id: slot.timeslot_id,
        //         class_subject_id: slot.timeslot_class_subject_id,
        //         placeholder: slot.timeslot_placeholder,
        //         attendance_from: slot.timeslot_attendance_from,
        //         attendance_to: slot.timeslot_attendance_to,
        //         teachers: slot.timeslot_teachers,
        //     }));
        // console.log("formData", formData);
        // updateTimetable({
        //     id: data.id,
        //     data: formData,
        //     showErrorInToast: true,
        // });
    }

    function onSubmitSingle(slot) {
        updateTimetable({
            id: data.id,
            data: {
                name: data.name,
                is_active: data.is_active,
                timeslots: [
                    {
                        id: slot.timeslot_id,
                        class_subject_id: slot.timeslot_class_subject_id,
                        placeholder: slot.timeslot_placeholder,
                        attendance_from: slot.timeslot_attendance_from,
                        attendance_to: slot.timeslot_attendance_to,
                        teachers: slot.timeslot_teachers,
                    },
                ],
            },
            showErrorInToast: true,
        });
    }

    function onSubmitRemoveSingle(id) {
        // const slot = form
        //     .getValues()
        //     .timeslots.find((slot) => slot?.timeslot_id == id);
        // if (!slot) return;
        // updateTimetable({
        //     id: data.id,
        //     data: {
        //         name: data.name,
        //         is_active: data.is_active,
        //         timeslots: [
        //             {
        //                 id: id,
        //                 class_subject_id: null,
        //                 placeholder: null,
        //                 attendance_from: slot.timeslot_attendance_from,
        //                 attendance_to: slot.timeslot_attendance_to,
        //                 teachers: [],
        //             },
        //         ],
        //     },
        //     showErrorInToast: true,
        // });
    }

    function print() {
        const printArea = document.querySelector(".printable");
        if (!printArea) return;

        const originalTitle = document.title;
        document.title = `Timetable-${data.employee?.employee_number}-${data.employee?.translations?.name?.[locale]}`;
        printArea.classList.add("print-show");

        function reset() {
            document.title = originalTitle;
            if (printArea) {
                printArea.classList.remove("print-show");
            }
        }
        setTimeout(() => {
            window.print();
            window.onafterprint = () => {
                reset();
            };
            reset();
        }, 10);
    }

    return (
        <>
            <div className="fixed left-0 top-0 z-50 h-screen w-screen overflow-scroll bg-white">
                {data && (
                    <div className="top fixed z-10 flex w-full items-center gap-x-5 border-y bg-white px-6">
                        <LucidePrinter
                            size={20}
                            className="-mr-2 cursor-pointer text-themeGreen transition hover:text-themeGreen"
                            onClick={print}
                        />
                        <h2 className="py-3">
                            {data.employee?.employee_number} -{" "}
                            {data.employee?.translations?.name?.[locale]}
                        </h2>
                        <div className="ml-auto flex items-center gap-x-5 py-2">
                            {/* <Button onClick={onSubmit}>Save</Button> */}
                            <X
                                onClick={close}
                                size={20}
                                className="ml-5 cursor-pointer text-gray-500"
                            />
                        </div>
                    </div>
                )}

                <Form {...form}>
                    <form className="m-5 mt-20 w-fit border">
                        <div className="c-text-size flex items-stretch bg-gray-50 text-gray-500">
                            <div className="timetable-first-col border border-b-0 px-1 py-2 text-center text-[12px] font-semibold leading-[1.1]"></div>
                            <div className="col-center flex flex-grow">
                                {Array.from({
                                    length: data.number_of_periods,
                                }).map((_, index) => (
                                    <div
                                        className="timetable-col flex items-center justify-center border border-b-0 p-1 text-center text-[13px] font-medium"
                                        key={index}
                                    >
                                        {index + 1}
                                    </div>
                                ))}
                            </div>
                        </div>
                        {data.period_groups.map((group, index) => (
                            <div
                                key={group?.id + index.toString()}
                                className="c-text-size flex items-stretch bg-gray-50 text-gray-500"
                            >
                                <div className="timetable-first-col border border-b-0 px-1 py-2 text-center text-[12px] font-semibold leading-[1.1]">
                                    {group.name?.[locale]}
                                </div>
                                <div className="col-center flex flex-grow">
                                    {group.period_labels.map(
                                        (period, index) => (
                                            <div
                                                className="timetable-col flex flex-col items-center justify-center border border-b-0 p-1 text-center text-[13px] font-medium leading-none"
                                                key={index}
                                            >
                                                <div>
                                                    {period.name?.[locale]}
                                                </div>
                                                <div>
                                                    {displayTime(
                                                        period.from_time
                                                    )}{" "}
                                                    -
                                                    {displayTime(
                                                        period.to_time
                                                    )}
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        ))}
                        {/* days & slots row */}
                        {Object.entries(data.employee_timetable).map(
                            ([day, slots]: any) => (
                                <div className="flex" key={day}>
                                    <div
                                        className={clsx(
                                            slotHeight,
                                            "timetable-first-col flex items-center justify-center border text-[14px] text-gray-500"
                                        )}
                                    >
                                        {shortenDay(day)}
                                    </div>
                                    <div className="flex">
                                        {Object.values(slots).map(
                                            (slotDataList: any[], index) => (
                                                <Slot
                                                    key={index}
                                                    slotDataList={slotDataList}
                                                    onEdit={(data) =>
                                                        setTarget(data)
                                                    }
                                                />
                                            )
                                        )}
                                    </div>
                                </div>
                            )
                        )}
                    </form>
                </Form>

                <div className="fixed bottom-[14px] left-0 h-px w-full bg-gray-200"></div>
            </div>
            <Modal
                open={target != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTarget(null);
                }}
            >
                <SlotEditor
                    slotDataList={target}
                    form={form}
                    close={() => setTarget(null)}
                    subjectOptions={subjectOptions}
                    onUpdate={onSubmitRemoveSingle}
                />
            </Modal>

            <div className="printable">
                <h2 className="py-3">
                    {data.employee?.employee_number} -{" "}
                    {data.employee?.translations?.name?.[locale]}
                </h2>
                <table className="print-timetable-table text-[10px]">
                    <thead>
                        <tr>
                            <th className="text-center font-semibold"></th>
                            {Array.from({
                                length: data.number_of_periods,
                            }).map((_, index) => (
                                <th
                                    className="text-center font-medium"
                                    key={index}
                                >
                                    {index + 1}
                                </th>
                            ))}
                        </tr>
                        {data.period_groups.map((group, index) => (
                            <tr
                                key={index}
                                className="bg-gray-50 text-gray-500"
                            >
                                <th className="text-center font-semibold">
                                    {group.name?.[locale]}
                                </th>
                                {group.period_labels.map((period, index) => (
                                    <th key={index}>
                                        <div
                                            className="flex flex-col items-center justify-center text-center font-medium leading-none"
                                            key={index}
                                        >
                                            <div>{period.name?.[locale]}</div>
                                            <div>
                                                {displayTime(period.from_time)}{" "}
                                                -{displayTime(period.to_time)}
                                            </div>
                                        </div>
                                    </th>
                                ))}
                            </tr>
                        ))}
                    </thead>
                    <tbody>
                        {Object.entries(data.employee_timetable).map(
                            ([day, slots]: any) => (
                                <tr key={day}>
                                    <td className="text-center text-gray-500">
                                        {shortenDay(day)}
                                    </td>
                                    {Object.values(slots).map(
                                        (slotDataList: any[], index) => (
                                            <td key={index}>
                                                <SlotInfo
                                                    key={index}
                                                    slotDataList={slotDataList}
                                                />
                                            </td>
                                        )
                                    )}
                                </tr>
                            )
                        )}
                    </tbody>
                </table>
            </div>
        </>
    );
};

const SlotInfo = ({ slotDataList }) => {
    const locale = useLocale();

    return (
        <div className="flex flex-col gap-y-1">
            {slotDataList.map((data, index) => (
                <div
                    key={index}
                    className={
                        "flex flex-wrap items-start justify-center gap-1"
                    }
                >
                    {data?.subject_name ? (
                        <div className="text-center font-medium leading-none text-themeGreen">
                            {data.subject_name[locale]}
                        </div>
                    ) : (
                        <div className="text-center font-medium leading-none text-[#1d91d8]">
                            {data?.placeholder}
                        </div>
                    )}
                    <div className="text-center font-medium leading-none text-gray-600">
                        {data?.class_name?.[locale]}
                    </div>
                </div>
            ))}
        </div>
    );
};

const Slot = ({ slotDataList, onEdit }) => {
    const locale = useLocale();

    return (
        <div
            className={clsx(
                "flex flex-col gap-y-1 border px-1 pb-2 pt-2",
                "timetable-col",
                slotHeight
            )}
        >
            {slotDataList.map((data, index) => (
                <div
                    key={index}
                    className={clsx(
                        "flex flex-wrap items-start justify-center gap-1",
                        data?.class_name &&
                            "rounded-sm border border-gray-200 px-0.5 py-1"
                    )}
                >
                    {data?.subject_name ? (
                        <div className="text-center text-[14px] font-medium leading-none text-themeGreen">
                            {data.subject_name[locale]}
                        </div>
                    ) : (
                        <div className="text-center text-[14px] font-medium leading-none text-[#1d91d8]">
                            {data?.placeholder}
                        </div>
                    )}
                    <div className="text-center text-[14px] font-medium leading-none text-gray-600">
                        {data?.class_name?.[locale]}
                    </div>
                </div>
            ))}
            <div className="mt-auto flex items-center justify-between pl-1.5 pr-1 pt-2.5">
                {/* <PenBoxIcon
                    className="ml-auto cursor-pointer text-gray-400 hover:text-themeGreen"
                    size={18}
                    onClick={() => onEdit(slotDataList)}
                /> */}
            </div>
        </div>
    );
};

const SlotEditor = ({
    slotDataList,
    subjectOptions,
    form,
    onUpdate,
    close,
}) => {
    const locale = useLocale();
    const timeslots = form.watch("timeslots");

    const [targetSlot, setTargetSlot] = useState(null);

    function clearSlot(idIndex) {
        form.setValue(`timeslots.${idIndex}.timeslot_class_subject_id`, null);
        form.setValue(`timeslots.${idIndex}.timeslot_placeholder`, null);
        // form.setValue(
        //     `timeslots.${idIndex}.timeslot_attendance_from`,
        //     data.default_attendance_from
        // );
        // form.setValue(
        //     `timeslots.${idIndex}.timeslot_attendance_to`,
        //     data.default_attendance_to
        // );
        onUpdate(idIndex);
        close();
    }

    const hasData =
        slotDataList?.filter((data) => data.timeslot_id != null)?.length > 0;

    return (
        <>
            <div className="">
                <h2 className="mb-4">Edit Slot</h2>
                {hasData && (
                    <div className="ml-0.5 grid gap-y-1.5 pb-2">
                        {slotDataList?.map(
                            (data) =>
                                data?.timeslot_id && (
                                    <div
                                        key={data?.timeslot_id}
                                        className="c-text-size flex items-center gap-x-2 text-center font-medium"
                                    >
                                        {data?.subject_name ? (
                                            <div
                                                className="cursor-pointer text-themeGreen hover:underline"
                                                onClick={() =>
                                                    setTargetSlot(data)
                                                }
                                            >
                                                {data?.subject_name?.[locale]}
                                            </div>
                                        ) : (
                                            <div
                                                className="cursor-pointer text-[#1d91d8] hover:underline"
                                                onClick={() =>
                                                    setTargetSlot(data)
                                                }
                                            >
                                                {data?.placeholder}
                                            </div>
                                        )}
                                        <div className="text-gray-600">
                                            {data?.class_name?.[locale]}
                                        </div>
                                        <XCircle
                                            className="hover:text-themeRed cursor-pointer text-gray-400"
                                            size={20}
                                            onClick={() => {
                                                clearSlot(data?.timeslot_id);
                                            }}
                                        />
                                    </div>
                                )
                        )}
                    </div>
                )}

                <Button variant={"outline"} size={"smaller"} className="mt-2">
                    Add
                </Button>

                <Modal
                    open={targetSlot != null}
                    onOpenChange={(isOpen) => {
                        if (!isOpen) setTargetSlot(null);
                    }}
                >
                    <SlotForm
                        slotData={targetSlot}
                        subjectOptions={subjectOptions}
                        onUpdateSlot={(data) => {
                            setTargetSlot(null);
                        }}
                    />
                </Modal>
            </div>
        </>
    );
};

const SlotForm = (props: {
    slotData: any;
    subjectOptions: any;
    onUpdateSlot: (data: Record<string, any>) => void;
}) => {
    return props.slotData ? (
        <SlotFormWrap {...props} />
    ) : (
        <div className="h-[200px]"></div>
    );
};

const SlotFormWrap = ({ slotData, subjectOptions, onUpdateSlot }) => {
    const _typeSubject = "subject";
    const _typePlaceholder = "placeholder";

    const [type, setType] = useState<"subject" | "placeholder">(
        slotData?.class_subject_id ? "subject" : "placeholder"
    );

    const slotForm = useForm<any>({
        defaultValues: {
            // default_attendance_from: slotData.default_attendance_from,
            // default_attendance_to: slotData.default_attendance_to,

            timeslot_id: slotData.timeslot_id,
            timeslot_class_subject_id: slotData.class_subject_id ?? "",
            timeslot_placeholder: slotData.placeholder ?? "",
            timeslot_attendance_from: formatToDate(slotData.attendance_from),
            timeslot_attendance_to: formatToDate(slotData.attendance_to),
        },
    });

    function formatToDate(timeString?: string) {
        return timeString
            ? parse(timeString, "HH:mm:ss", new Date())
            : undefined;
    }

    function onSubmit(e) {
        e.preventDefault();
        slotForm.clearErrors();
        if (
            type === _typeSubject &&
            !slotForm.watch("timeslot_class_subject_id")
        ) {
            slotForm.setError("timeslot_class_subject_id", {
                type: "required",
                message: "Subject is required",
            });
            return;
        }
        if (
            type === _typePlaceholder &&
            !slotForm.watch("timeslot_placeholder")
        ) {
            slotForm.setError("timeslot_placeholder", {
                type: "required",
                message: "Placeholder is required",
            });
            return;
        }
        slotForm.handleSubmit((data) => {
            if (type === _typeSubject) data.timeslot_placeholder = null;
            if (type === _typePlaceholder)
                data.timeslot_class_subject_id = null;

            data.timeslot_attendance_from = format(
                data.timeslot_attendance_from,
                "HH:mm:ss"
            );
            data.timeslot_attendance_to = format(
                data.timeslot_attendance_to,
                "HH:mm:ss"
            );

            delete data._select_teacher;
            console.log(data);

            onUpdateSlot(data);
        })();
    }

    return (
        <Form {...slotForm}>
            <form className="grid gap-y-3" onSubmit={onSubmit}>
                <h2 className="pb-1">Edit Slot</h2>

                <div className="c-text-size mb-2 mt-1 flex w-fit cursor-pointer overflow-hidden rounded-md border font-semibold">
                    <div
                        className={clsx(
                            "px-4 py-2.5",
                            type === _typeSubject
                                ? "bg-themeGreen3 text-themeGreen2"
                                : "text-gray-500"
                        )}
                        onClick={() => setType(_typeSubject)}
                    >
                        Subject
                    </div>
                    <div className="h-full w-px bg-gray-300"></div>
                    <div
                        className={clsx(
                            "px-4 py-2.5",
                            type === _typePlaceholder
                                ? "bg-themeGreen3 text-themeGreen2"
                                : "text-gray-500"
                        )}
                        onClick={() => setType(_typePlaceholder)}
                    >
                        Placeholder
                    </div>
                </div>

                <div className="flex flex-wrap gap-5 border-b border-dashed pb-4">
                    <div>
                        <Label className="label">Attendance from</Label>
                        <TimePicker
                            date={slotForm.watch(`timeslot_attendance_from`)}
                            setDate={(value) =>
                                slotForm.setValue(
                                    `timeslot_attendance_from`,
                                    value
                                )
                            }
                        />
                    </div>
                    <div>
                        <Label className="label">Attendance to</Label>
                        <TimePicker
                            date={slotForm.watch(`timeslot_attendance_to`)}
                            setDate={(value) =>
                                slotForm.setValue(
                                    `timeslot_attendance_to`,
                                    value
                                )
                            }
                        />
                    </div>
                </div>

                {type === _typePlaceholder && (
                    <FormInput
                        control={slotForm.control}
                        name="timeslot_placeholder"
                        label="Placeholder"
                    />
                )}
                {type === _typeSubject && (
                    <FormSelect
                        control={slotForm.control}
                        name="timeslot_class_subject_id"
                        label="Subject"
                        options={subjectOptions}
                    />
                )}

                <Button type="submit" className="ml-auto mt-8">
                    Save
                </Button>
            </form>
        </Form>
    );
};

export default TeacherTimetableEditor;
