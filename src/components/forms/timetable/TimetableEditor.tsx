import { useLocale, useTranslations } from "next-intl";
import { Fragment, useEffect, useRef, useState } from "react";
import clsx from "clsx";
import { format } from "date-fns";
import { LucidePrinter, PenBoxIcon, X, XCircle } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { Checkbox } from "@/components/base-ui/checkbox";
import { Label } from "@/components/base-ui/label";
import FreeCheckbox from "@/components/ui/FreeCheckbox";
import FreeInput from "@/components/ui/FreeInput";
import LoaderOverlay from "@/components/ui/LoaderOverlay";
import {
    ABSENT,
    classSubjectAPI,
    employeeAPI,
    employeeTimetableAPI,
    GET_ALL_PARAMS,
    LATE,
    PRESENT,
    PRIMARY,
    SECONDARY,
    teacherDropdownFilter,
    timetableAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import {
    combinedNames,
    displayTime,
    formatTimeToDate,
    formatWithBrackets,
    getSemesterClassName,
    isValueTrue,
    optionUserLabel,
} from "@/lib/utils";
import { Button } from "../../base-ui/button";
import { Form } from "../../base-ui/form";
import FormInput from "../../ui/FormInput";
import FormSelect from "../../ui/FormSelect";
import Modal from "../../ui/Modal";
import { TimePicker } from "../../ui/TimePicker";
import { DialogFooter } from "@/components/base-ui/dialog";
import FreeSelectAsync from "@/components/ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";
import TeacherTimetableEditor from "./TeacherTimetableEditor";
import DataTable from "@/components/ui/DataTable";
import { orderBy } from "lodash";
import FormCheckbox from "@/components/ui/FormCheckbox";

const slotHeight = "min-h-[100px]";

function shortenDay(day) {
    return day?.slice(0, 3);
}

function orderTeachersByType(teachers) {
    return orderBy(teachers, [(item) => item?.type === PRIMARY], ["desc"]);
}

const TimetableEditor = (props) => {
    const locale = useLocale();

    const { data: current, axiosQuery: getCurrentData } = useAxios({
        api: `${timetableAPI}/${props.id}`,
        locale,
        onError: close,
    });

    const { data: latest, axiosQuery: getLatestData } = useAxios({
        api: `${timetableAPI}/${props.id}`,
        locale,
        onSuccess: () => {
            props.refresh();
        },
    });

    const { data: classSubjectData, axiosQuery: getClassSubjectData } =
        useAxios({
            api: classSubjectAPI,
            noLoading: true,
            locale,
        });

    useEffect(() => {
        if (!props.id) return;
        getClassSubjectData({
            params: {
                ...GET_ALL_PARAMS,
                semester_class_id: props.semesterClass?.id,
            },
        });
        getCurrentData();
    }, [props.id]);

    return current && classSubjectData ? (
        <Editor
            {...props}
            data={latest ?? current}
            subjectOptions={classSubjectData?.map((item) => ({
                id: item?.id,
                name: item?.subject?.name,
                teachers: item?.teachers,
            }))}
            classSubjectData={classSubjectData}
            refresh={() => {
                getLatestData();
            }}
        />
    ) : (
        <div className="h-10">
            <LoaderOverlay />
        </div>
    );
};

const Editor = ({
    data,
    semesterClass,
    periodGroup,
    subjectOptions,
    classSubjectData,
    refresh,
    close,
}) => {
    const form = useForm({
        defaultValues: {
            name: data.name,
            is_active: isValueTrue(data.is_active),
            timeslots: getSlots(),
        },
    });

    const [target, setTarget] = useState<any>(null);
    const [targetTeacher, setTargetTeacher] = useState<any>(null);
    const [highlightedSubjectTeacher, setHighlightedSubjectTeacher] =
        useState<any>(null);
    const [
        highlightedTeacherUnassignedSlots,
        setHighlightedTeacherUnassignedSlots,
    ] = useState<any[]>([]);

    useEffect(() => {
        form.setValue("timeslots", getSlots());
    }, [data]);

    function getSlots() {
        const periods = data.display_groups?.[0]?.periods;
        // backend return all slots
        // use index to represent timeslot_id
        const slots = data?.display_groups?.reduce((acc, group) => {
            // Flatten all slotData into a single array
            const slotDataList = Object.entries(group.days).flatMap(
                ([day, data]: [day: string, data: any]) => {
                    console.log("day", day);
                    return Object.entries(data).map(
                        ([periodKey, slotData]) => ({
                            ...(slotData ?? {}),
                            periodIndex: Number(periodKey) - 1,
                            day,
                        })
                    );
                }
            );

            // Place each slotData by timeslot_id as index
            slotDataList.forEach((slotData: any) => {
                acc[slotData.timeslot_id] = {
                    ...slotData,
                    default_attendance_from:
                        periods[slotData.periodIndex]?.from_time,
                    default_attendance_to:
                        periods[slotData.periodIndex]?.to_time,
                };
            });

            return acc;
        }, [] as any[]);
        console.log("slots", slots);
        return slots;
    }

    function getTeacherPeriodCount(teacherIds, subjectId) {
        const timeslots = form.watch("timeslots");

        const assignedCount = timeslots.filter((slot) => {
            const timeslotTeachersIds = slot.timeslot_teachers?.map(
                (teacher) => teacher?.employee_id
            );
            return (
                slot.timeslot_class_subject_id === subjectId &&
                timeslotTeachersIds?.some((teacherId) =>
                    teacherIds?.includes(teacherId)
                )
            );
        }).length;

        return assignedCount;
    }

    const { axiosQuery: getTeacherTimetable } = useAxios({
        api: `${employeeTimetableAPI}/${highlightedSubjectTeacher?.id}`,
        onSuccess: (result) => {
            const unassignedSlots: Record<string, any>[] = [];

            Object.entries(result?.data?.employee_timetable).forEach(
                ([day, slots]: [day: string, slots: Record<number, any[]>]) => {
                    Object.entries(slots).forEach(([period, list]) => {
                        const item = list?.[0];
                        if (!item) return;
                        if (!item.timeslot_id) {
                            unassignedSlots.push({
                                day,
                                period,
                            });
                        }
                    });
                }
            );
            setHighlightedTeacherUnassignedSlots(unassignedSlots);
        },
    });

    const { axiosPut: updateTimetable } = useAxios({
        api: timetableAPI,
        onSuccess: () => {
            setTimeout(() => {
                refresh();
            }, 10);
        },
    });

    function getSubjectName(id) {
        return (
            subjectOptions?.find((subject) => subject.id === id)?.name ?? "-"
        );
    }

    function onSubmit() {
        const formData = form.getValues();
        formData.timeslots = formData.timeslots
            .filter((slot) => slot)
            .map((slot) => ({
                id: slot.timeslot_id,
                class_subject_id: slot.timeslot_class_subject_id,
                placeholder: slot.timeslot_placeholder,
                attendance_from: slot.timeslot_attendance_from,
                attendance_to: slot.timeslot_attendance_to,
                teachers: slot.timeslot_teachers,
            }));
        console.log("formData", formData);
        updateTimetable({
            id: data.id,
            data: formData,
            showErrorInToast: true,
        });
    }

    function onSubmitSingle(slot) {
        updateTimetable({
            id: data.id,
            data: {
                name: form.getValues("name"),
                is_active: form.getValues("is_active"),
                timeslots: [
                    {
                        id: slot.timeslot_id,
                        class_subject_id: slot.timeslot_class_subject_id,
                        placeholder: slot.timeslot_placeholder,
                        attendance_from: slot.timeslot_attendance_from,
                        attendance_to: slot.timeslot_attendance_to,
                        teachers: slot.timeslot_teachers,
                        default_init_status: slot.default_init_status,
                        has_mark_deduction: slot.has_mark_deduction,
                    },
                ],
            },
            showErrorInToast: true,
        });
    }

    function onSubmitRemoveSingle(id) {
        const slot = form
            .getValues()
            .timeslots.find((slot) => slot?.timeslot_id == id);
        if (!slot) return;

        updateTimetable({
            id: data.id,
            data: {
                name: form.getValues("name"),
                is_active: form.getValues("is_active"),
                timeslots: [
                    {
                        id: id,
                        class_subject_id: null,
                        placeholder: null,
                        attendance_from: slot.timeslot_attendance_from,
                        attendance_to: slot.timeslot_attendance_to,
                        teachers: [],
                    },
                ],
            },
            showErrorInToast: true,
        });
    }

    useEffect(() => {
        if (highlightedSubjectTeacher) {
            getTeacherTimetable();
        }
    }, [highlightedSubjectTeacher]);

    const _assignedTeachersColumns = [
        {
            key: "no",
            displayAs: "No.",
            modify: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: "subject",
            modify: (list) => (
                <>
                    <span>{list?.[0]}</span>
                    <span className="inline-block pl-1 text-[14px] text-gray-500">
                        {formatWithBrackets(list?.[1])}
                    </span>
                </>
            ),
        },
        {
            key: "teacher",
            modify: (teachers) =>
                teachers.map((teacher, index) => (
                    <Fragment key={index}>
                        <div
                            onClick={() =>
                                setHighlightedSubjectTeacher({
                                    id: teacher.id,
                                    subjectId: teacher.subjectId,
                                })
                            }
                            className={clsx(
                                "cursor-pointer p-1 font-medium",
                                highlightedSubjectTeacher?.subjectId ==
                                    teacher.subjectId &&
                                    highlightedSubjectTeacher?.id == teacher?.id
                                    ? "bg-themeGreen3 text-themeGreen2"
                                    : "text-themeGreenDark hover:underline"
                            )}
                        >
                            {teacher?.name}
                        </div>
                        {index < teachers.length - 1 && (
                            <div className="my-1 border-b border-gray-100"></div>
                        )}
                    </Fragment>
                )),
        },
        {
            key: "period",
            modify: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: "remaining_period",
            modify: (value) => <div className="text-center">{value}</div>,
        },
    ];

    const _assignedTeachersData = classSubjectData
        ?.filter((item) => item?.number_of_period_per_week != 0)
        .map((item, index) => ({
            no: index + 1,
            subject: [item?.subject?.name, item?.subject?.code],
            teacher: item?.teachers?.map((i) => ({
                name: i?.teacher?.name,
                id: i?.teacher?.id,
                subjectId: item?.id,
            })),
            period: item?.number_of_period_per_week,
            remaining_period:
                item?.number_of_period_per_week -
                getTeacherPeriodCount(
                    item?.teachers?.map((i) => i?.teacher?.id),
                    item?.id
                ),
        }));

    function print() {
        const printArea = document.querySelector(".printable");
        if (!printArea) return;

        const originalTitle = document.title;
        document.title = `Timetable-${combinedNames(periodGroup)}-${form.watch("name")}`;
        printArea.classList.add("print-show");

        function reset() {
            document.title = originalTitle;
            if (printArea) {
                printArea.classList.remove("print-show");
            }
        }
        setTimeout(() => {
            window.print();
            window.onafterprint = () => {
                reset();
            };
            reset();
        }, 10);
    }

    return (
        <>
            <div className="fixed left-0 top-0 z-50 h-screen w-screen overflow-scroll bg-white">
                {data && (
                    <div className="top fixed z-10 flex w-full flex-col justify-between gap-x-5 overflow-auto border-y bg-white md:flex-row md:items-center md:px-6 print:static print:border-0">
                        <LucidePrinter
                            size={20}
                            className="-mr-2 cursor-pointer text-themeGreen transition hover:text-themeGreen"
                            onClick={print}
                        />
                        <div className="max-h-[140px] flex-grow overflow-auto px-5 py-2 md:max-h-[72px] md:px-0">
                            <div className="grid min-w-[600px] items-center gap-1 gap-x-3 md:flex">
                                <h3 className="whitespace-nowrap">
                                    {getSemesterClassName(semesterClass)}
                                </h3>
                                <h3 className="whitespace-nowrap">
                                    {combinedNames(periodGroup)}
                                </h3>
                                <div className="min-w-[240px] md:flex-grow lg:min-w-0">
                                    <FreeInput
                                        hasLabel={false}
                                        control={form.control}
                                        name={"name"}
                                        placeholder="Name"
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center justify-between gap-x-5 px-5 py-2 md:px-0">
                            <FreeCheckbox
                                control={form.control}
                                name={"is_active"}
                            />
                            <Button onClick={onSubmit}>Save</Button>
                            <X
                                onClick={close}
                                size={20}
                                className="ml-auto cursor-pointer text-gray-500 md:ml-5"
                            />
                        </div>
                    </div>
                )}

                {form.watch("timeslots")?.length > 0 && (
                    <Form {...form}>
                        <form className="m-5 mt-[190px] w-fit border md:mt-20 print:mt-0">
                            {data &&
                                data.display_groups.map((group, index) => {
                                    return (
                                        <div key={group.id + index.toString()}>
                                            {/* periods row */}
                                            <div className="c-text-size flex items-stretch bg-gray-50 text-gray-500">
                                                <div className="timetable-first-col border border-b-0 px-1 py-2 text-center text-[12px] font-semibold leading-[1.1]">
                                                    Day/ Period
                                                </div>
                                                <div className="col-center flex flex-grow">
                                                    {group.periods.map(
                                                        (period, index) => (
                                                            <div
                                                                className="timetable-col flex h-full flex-col items-center justify-center border border-b-0 p-1 text-center text-[13px] font-medium leading-none"
                                                                key={
                                                                    "period" +
                                                                    index
                                                                }
                                                            >
                                                                <div>
                                                                    {
                                                                        period.period_label_name
                                                                    }
                                                                </div>
                                                                <div>
                                                                    {displayTime(
                                                                        period.from_time
                                                                    )}{" "}
                                                                    -
                                                                    {displayTime(
                                                                        period.to_time
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </div>

                                            {/* days & slots row */}
                                            {Object.entries(group.days).map(
                                                ([day, slots]: any) => (
                                                    <div
                                                        className="flex"
                                                        key={group.id + day}
                                                    >
                                                        <div
                                                            className={clsx(
                                                                slotHeight,
                                                                "timetable-first-col flex items-center justify-center border text-[14px] text-gray-500"
                                                            )}
                                                        >
                                                            {shortenDay(day)}
                                                        </div>
                                                        <div className="flex">
                                                            {Object.values(
                                                                slots
                                                            ).map(
                                                                (
                                                                    slotData: any
                                                                ) => (
                                                                    <Slot
                                                                        key={
                                                                            slotData.timeslot_id
                                                                        }
                                                                        highlightedSubjectTeacher={
                                                                            highlightedSubjectTeacher
                                                                        }
                                                                        highlightedTeacherUnassignedSlots={
                                                                            highlightedTeacherUnassignedSlots
                                                                        }
                                                                        id={
                                                                            slotData.timeslot_id
                                                                        }
                                                                        form={
                                                                            form
                                                                        }
                                                                        getSubjectName={
                                                                            getSubjectName
                                                                        }
                                                                        onEdit={(
                                                                            data
                                                                        ) =>
                                                                            setTarget(
                                                                                data
                                                                            )
                                                                        }
                                                                        onClickTeacher={(
                                                                            data
                                                                        ) =>
                                                                            setTargetTeacher(
                                                                                data
                                                                            )
                                                                        }
                                                                        onClear={(
                                                                            id
                                                                        ) =>
                                                                            onSubmitRemoveSingle(
                                                                                id
                                                                            )
                                                                        }
                                                                    />
                                                                )
                                                            )}
                                                        </div>
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    );
                                })}
                        </form>
                    </Form>
                )}

                <div className="mb-10 w-fit px-5">
                    <DataTable
                        columns={_assignedTeachersColumns}
                        data={_assignedTeachersData}
                    />
                </div>
                <div className="fixed bottom-[14px] left-0 h-px w-full bg-gray-200"></div>
            </div>
            <Modal
                open={target != null}
                onOpenChange={(isOpen) => {
                    if (!isOpen) setTarget(null);
                }}
            >
                <SlotForm
                    slotData={form.watch(`timeslots.${target?.id}`)}
                    currentType={target?.type}
                    subjectOptions={subjectOptions}
                    onUpdateSlot={(data) => {
                        form.setValue(`timeslots.${target?.id}`, data);
                        setTarget(null);
                        onSubmitSingle(data);
                    }}
                />
            </Modal>

            {targetTeacher && (
                <TeacherTimetableEditor
                    teacherId={targetTeacher?.employee_id}
                    semesterClassId={semesterClass?.id}
                    close={() => setTargetTeacher(null)}
                />
            )}

            <div className="printable">
                <div className="mb-3 mt-2 flex flex-wrap items-center gap-4">
                    <h3 className="whitespace-nowrap">
                        {getSemesterClassName(semesterClass)}
                    </h3>
                    <h3 className="whitespace-nowrap">
                        {combinedNames(periodGroup)}
                    </h3>
                    <div className="c-text-size font-semibold">
                        {form.watch("name")}
                    </div>
                </div>
                {data?.display_groups.map((group, index) => {
                    return (
                        <table
                            className="print-timetable-table"
                            key={group.id + index.toString()}
                        >
                            <thead>
                                <tr className="c-text-size bg-gray-50 text-gray-500">
                                    <th className="text-center text-[12px] font-semibold leading-[1.1]">
                                        Day/ Period
                                    </th>
                                    {group.periods.map((period, index) => (
                                        <th key={"period" + index}>
                                            <div className="flex h-full flex-col items-center justify-center text-center text-[12px] font-medium leading-none">
                                                <div>
                                                    {period.period_label_name}
                                                </div>
                                                <div>
                                                    {displayTime(
                                                        period.from_time
                                                    )}{" "}
                                                    -
                                                    {displayTime(
                                                        period.to_time
                                                    )}
                                                </div>
                                            </div>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {Object.entries(group.days).map(
                                    ([day, slots]: any) => (
                                        <tr key={group.id + day}>
                                            <td>
                                                <div className="flex items-center justify-center text-[12px] text-gray-500">
                                                    {shortenDay(day)}
                                                </div>
                                            </td>
                                            {Object.values(slots).map(
                                                (slotData: any, index) => (
                                                    <td key={index}>
                                                        <SlotInfo
                                                            key={
                                                                slotData.timeslot_id
                                                            }
                                                            id={
                                                                slotData.timeslot_id
                                                            }
                                                            form={form}
                                                            getSubjectName={
                                                                getSubjectName
                                                            }
                                                        />
                                                    </td>
                                                )
                                            )}
                                        </tr>
                                    )
                                )}
                            </tbody>
                        </table>
                    );
                })}
                <div className="mt-5 w-fit">
                    <DataTable
                        columns={_assignedTeachersColumns}
                        data={_assignedTeachersData}
                    />
                </div>
            </div>
        </>
    );
};

const SlotInfo = ({ id, form, getSubjectName }) => {
    const idIndex = id;
    const data = form.watch("timeslots")[idIndex];
    const hasData = data.timeslot_class_subject_id || data.timeslot_placeholder;

    function displayAttendance() {
        if (
            data.timeslot_attendance_from === data.default_attendance_from &&
            data.timeslot_attendance_to === data.default_attendance_to
        )
            return;
        return (
            <>
                <div className="mt-1 w-full bg-gray-100 text-center leading-tight text-gray-500">
                    Attendance
                </div>
                <div className="pt-1 text-center leading-none">
                    {`${displayTime(data.timeslot_attendance_from)} - ${displayTime(data.timeslot_attendance_to)}`}
                </div>
            </>
        );
    }

    return (
        <div className="flex flex-col justify-between text-[10px] leading-none">
            {hasData && (
                <div className={clsx("flex flex-col items-center")}>
                    {data.timeslot_class_subject_id ? (
                        <div className="text-center font-medium leading-none">
                            {getSubjectName(data.timeslot_class_subject_id)}
                        </div>
                    ) : (
                        <div className="text-center font-medium leading-tight text-[#1d91d8]">
                            {data.timeslot_placeholder}
                        </div>
                    )}
                    {displayAttendance()}

                    {data.timeslot_teachers?.length > 0 && (
                        <div className="mt-1 w-full text-center">
                            <div className="text-[8px] leading-tight text-gray-600">
                                Teachers ({data.timeslot_teachers?.length})
                            </div>
                            <div className="mt-0.5 grid gap-y-0.5 text-[9px] font-semibold leading-none text-themeGreen">
                                {orderTeachersByType(
                                    data.timeslot_teachers
                                ).map((teacher, index) => (
                                    <div key={index} className="text-center">
                                        {teacher?.name}
                                        {index <
                                        data.timeslot_teachers?.length - 1
                                            ? ", "
                                            : ""}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

const Slot = ({
    id,
    form,
    getSubjectName,
    onEdit,
    onClear,
    onClickTeacher,
    highlightedSubjectTeacher,
    highlightedTeacherUnassignedSlots,
}) => {
    const idIndex = id;
    const data = form.watch("timeslots")[idIndex];

    const hasData = data.timeslot_class_subject_id || data.timeslot_placeholder;

    const [openDelete, setOpenDelete] = useState(false);

    function clearSlot() {
        form.setValue(`timeslots.${idIndex}.timeslot_class_subject_id`, null);
        form.setValue(`timeslots.${idIndex}.timeslot_placeholder`, null);
        form.setValue(
            `timeslots.${idIndex}.timeslot_attendance_from`,
            data.default_attendance_from
        );
        form.setValue(
            `timeslots.${idIndex}.timeslot_attendance_to`,
            data.default_attendance_to
        );
        form.setValue(`timeslots.${idIndex}.timeslot_teachers`, []);
        onClear(id);
    }

    function displayAttendance() {
        if (
            data.timeslot_attendance_from === data.default_attendance_from &&
            data.timeslot_attendance_to === data.default_attendance_to
        )
            return;
        return (
            <>
                <div className="mt-1 w-full bg-gray-100 text-center text-[11px] leading-tight text-gray-500">
                    Attendance
                </div>
                <div className="pt-1 text-center text-[12px] leading-none">
                    {`${displayTime(data.timeslot_attendance_from)} - ${displayTime(data.timeslot_attendance_to)}`}
                </div>
            </>
        );
    }

    function isHighlighted() {
        return (
            data.timeslot_class_subject_id ===
                highlightedSubjectTeacher?.subjectId &&
            data.timeslot_teachers
                .map((teacher) => teacher?.employee_id)
                ?.includes(highlightedSubjectTeacher?.id)
        );
    }

    return (
        <>
            <div
                className={clsx(
                    "timetable-col flex flex-col justify-between border px-1 pb-2 pt-2",
                    slotHeight,
                    isHighlighted()
                        ? "bg-themeGreen4 bg-opacity-30"
                        : !hasData &&
                              highlightedTeacherUnassignedSlots?.some(
                                  (slot) =>
                                      slot.day == data.day &&
                                      slot.period == data.periodIndex + 1
                              ) &&
                              "bg-amber-50"
                )}
            >
                {hasData && (
                    <div className={clsx("flex flex-col items-center")}>
                        {data.timeslot_class_subject_id ? (
                            <div className="text-center text-[13px] font-medium leading-none">
                                {getSubjectName(data.timeslot_class_subject_id)}
                            </div>
                        ) : (
                            <div className="text-center text-[14px] font-medium leading-tight text-[#1d91d8]">
                                {data.timeslot_placeholder}
                            </div>
                        )}
                        {displayAttendance()}

                        {data.timeslot_teachers?.length > 0 && (
                            <div className="mt-1 w-full text-center">
                                <div className="bg-gray-100 px-1 text-[11px] leading-tight text-gray-600">
                                    Teachers ({data.timeslot_teachers?.length})
                                </div>
                                <div className="mt-1 grid gap-y-0.5 text-[12px] font-semibold leading-none text-themeGreen">
                                    {orderTeachersByType(
                                        data.timeslot_teachers
                                    ).map((teacher, index) => (
                                        <div
                                            key={index}
                                            className="cursor-pointer text-center hover:underline"
                                            onClick={() =>
                                                onClickTeacher(teacher)
                                            }
                                        >
                                            {teacher?.name}
                                            {index <
                                            data.timeslot_teachers?.length - 1
                                                ? ", "
                                                : ""}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                )}
                <div className="mt-auto flex items-center justify-between pl-1.5 pr-1 pt-2.5">
                    {hasData && (
                        <XCircle
                            onClick={() => setOpenDelete(true)}
                            size={20}
                            className="cursor-pointer text-themeGray hover:text-gray-500"
                        />
                    )}
                    <PenBoxIcon
                        className="ml-auto cursor-pointer text-gray-400 hover:text-themeGreen"
                        size={18}
                        onClick={() =>
                            onEdit({
                                id,
                                type: data.timeslot_placeholder
                                    ? "placeholder"
                                    : "subject",
                            })
                        }
                    />
                </div>
            </div>
            <Modal open={openDelete} onOpenChange={setOpenDelete}>
                <>
                    <p className="mt-3 font-medium">
                        Are you sure you want to delete this?
                    </p>
                    <DialogFooter className={"mt-2"}>
                        <Button
                            variant="outline"
                            onClick={() => setOpenDelete(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                setOpenDelete(false);
                                clearSlot();
                            }}
                        >
                            Confirm
                        </Button>
                    </DialogFooter>
                </>
            </Modal>
        </>
    );
};

const SlotForm = (props: {
    currentType: "subject" | "placeholder";
    slotData: any;
    subjectOptions: any;
    onUpdateSlot: (data: Record<string, any>) => void;
}) => {
    return props.slotData ? (
        <SlotFormWrap {...props} />
    ) : (
        <div className="h-[400px]"></div>
    );
};

const SlotFormWrap = ({
    currentType,
    slotData,
    subjectOptions,
    onUpdateSlot,
}) => {
    const _typeSubject = "subject";
    const _typePlaceholder = "placeholder";

    const isAssigned =
        slotData.timeslot_class_subject_id || slotData.timeslot_placeholder;

    const [type, setType] = useState<"subject" | "placeholder">(currentType);

    const { loadAsyncOptions } = useAsyncSelect({
        api: employeeAPI,
        params: teacherDropdownFilter,
    });

    const slotForm = useForm<any>({
        defaultValues: {
            default_attendance_from: slotData.default_attendance_from,
            default_attendance_to: slotData.default_attendance_to,

            timeslot_id: slotData.timeslot_id,
            timeslot_class_subject_id: slotData.timeslot_class_subject_id ?? "",
            timeslot_placeholder: slotData.timeslot_placeholder ?? "",
            timeslot_attendance_from: formatTimeToDate(
                slotData.timeslot_attendance_from
            ),
            timeslot_attendance_to: formatTimeToDate(
                slotData.timeslot_attendance_to
            ),
            timeslot_teachers:
                orderTeachersByType(slotData.timeslot_teachers) ?? [],
            _select_teacher: null,
            default_init_status: isAssigned
                ? slotData.timeslot_default_init_status
                : ABSENT,
            has_mark_deduction: isAssigned
                ? isValueTrue(slotData.timeslot_has_mark_deduction)
                : true,
        },
    });

    const { append: appendTeacher, remove: removeTeacher } = useFieldArray({
        control: slotForm.control,
        name: "timeslot_teachers",
    });

    function onAppendTeacher(target) {
        const existingTeachers = slotForm.getValues("timeslot_teachers");

        if (
            existingTeachers.find(
                (teacher) => teacher?.employee_id == target?.value
            )
        )
            return;

        const hasPrimaryTicked =
            existingTeachers.find((teacher) => teacher?.type === PRIMARY) !==
            undefined;

        appendTeacher({
            type:
                hasPrimaryTicked && target?.type === PRIMARY
                    ? SECONDARY
                    : target?.type ?? SECONDARY,
            employee_id: target?.value,
            name: target?.label ?? target?.name,
        });
    }

    function getSubjectTeacherOptions() {
        return (
            subjectOptions
                .find(
                    (subject) =>
                        subject.id ===
                        slotForm.watch("timeslot_class_subject_id")
                )
                ?.teachers?.map((option) => formatTeacherOption(option)) ?? []
        );
    }

    function formatTeacherOption(option) {
        return {
            type: option?.type,
            value: option?.teacher?.id,
            name: optionUserLabel(
                option?.teacher?.employee_number,
                option?.teacher?.translations?.name
            ),
        };
    }

    function onSetPrimaryTeacher(teacher, index) {
        const teachers = slotForm.getValues("timeslot_teachers");

        if (teachers.length > 1 && teacher?.type === SECONDARY) {
            // uncheck others
            const checkedIndex = teachers.findIndex(
                (teacher) => teacher?.type === PRIMARY
            );

            if (checkedIndex === index) return;

            if (checkedIndex > -1) {
                slotForm.setValue(
                    `timeslot_teachers[${checkedIndex}].type`,
                    SECONDARY
                );
            }
        }

        slotForm.setValue(
            `timeslot_teachers[${index}].type`,
            teacher?.type === PRIMARY ? SECONDARY : PRIMARY
        );
    }

    function onSubmit(e) {
        e.preventDefault();
        slotForm.clearErrors();
        if (!slotForm.watch("default_init_status")) {
            slotForm.setError("default_init_status", {
                type: "required",
                message: "Status is required",
            });
            return;
        }
        if (
            type === _typeSubject &&
            !slotForm.watch("timeslot_class_subject_id")
        ) {
            slotForm.setError("timeslot_class_subject_id", {
                type: "required",
                message: "Subject is required",
            });
            return;
        }
        if (
            type === _typePlaceholder &&
            !slotForm.watch("timeslot_placeholder")
        ) {
            slotForm.setError("timeslot_placeholder", {
                type: "required",
                message: "Placeholder is required",
            });
            return;
        }
        slotForm.handleSubmit((data) => {
            if (type === _typeSubject) data.timeslot_placeholder = null;
            if (type === _typePlaceholder)
                data.timeslot_class_subject_id = null;

            data.timeslot_attendance_from = format(
                data.timeslot_attendance_from,
                "HH:mm:ss"
            );
            data.timeslot_attendance_to = format(
                data.timeslot_attendance_to,
                "HH:mm:ss"
            );

            delete data._select_teacher;
            console.log(data);

            onUpdateSlot(data);
        })();
    }

    const t = useTranslations("common");

    return (
        <Form {...slotForm}>
            <form className="grid gap-y-3" onSubmit={onSubmit}>
                <h2 className="pb-1">Edit Slot</h2>

                <FormSelect
                    control={slotForm.control}
                    name="default_init_status"
                    label="Status*"
                    isStringOptions={true}
                    options={[PRESENT, ABSENT, LATE]}
                />

                <FormCheckbox
                    control={slotForm.control}
                    name="has_mark_deduction"
                    styleClass="ml-0.5 mt-1.5"
                />
                <div className="border-t border-dashed pt-1"></div>
                <div className="c-text-size mb-2 mt-1 flex w-fit cursor-pointer overflow-hidden rounded-md border font-semibold">
                    <div
                        className={clsx(
                            "px-4 py-2.5",
                            type === _typeSubject
                                ? "bg-themeGreen3 text-themeGreen2"
                                : "text-gray-500"
                        )}
                        onClick={() => setType(_typeSubject)}
                    >
                        Subject
                    </div>
                    <div className="h-full w-px bg-gray-300"></div>
                    <div
                        className={clsx(
                            "px-4 py-2.5",
                            type === _typePlaceholder
                                ? "bg-themeGreen3 text-themeGreen2"
                                : "text-gray-500"
                        )}
                        onClick={() => setType(_typePlaceholder)}
                    >
                        Placeholder
                    </div>
                </div>

                <div className="flex flex-wrap gap-5 border-b border-dashed pb-4">
                    <div>
                        <Label className="label">Attendance from</Label>
                        <TimePicker
                            date={slotForm.watch(`timeslot_attendance_from`)}
                            setDate={(value) =>
                                slotForm.setValue(
                                    `timeslot_attendance_from`,
                                    value
                                )
                            }
                        />
                    </div>
                    <div>
                        <Label className="label">Attendance to</Label>
                        <TimePicker
                            date={slotForm.watch(`timeslot_attendance_to`)}
                            setDate={(value) =>
                                slotForm.setValue(
                                    `timeslot_attendance_to`,
                                    value
                                )
                            }
                        />
                    </div>
                </div>

                {type === _typePlaceholder && (
                    <FormInput
                        control={slotForm.control}
                        name="timeslot_placeholder"
                        label="Placeholder*"
                    />
                )}
                {type === _typeSubject && (
                    <FormSelect
                        control={slotForm.control}
                        name="timeslot_class_subject_id"
                        label="Subject*"
                        options={subjectOptions}
                        onChange={(subjectId) => {
                            slotForm.setValue("timeslot_teachers", []);
                            const primaryTeacher = subjectOptions
                                ?.find((subject) => subject.id === subjectId)
                                ?.teachers?.find(
                                    (teacher) => teacher?.type === PRIMARY
                                );
                            if (primaryTeacher) {
                                onAppendTeacher(
                                    formatTeacherOption(primaryTeacher)
                                );
                            }
                        }}
                    />
                )}

                {type === _typeSubject && (
                    <FormSelect
                        control={slotForm.control}
                        name="_select_teacher"
                        label="Teachers"
                        placeholder="Select teacher"
                        isDisabled={
                            !slotForm.watch("timeslot_class_subject_id")
                        }
                        options={getSubjectTeacherOptions()}
                        onChange={(teacher) => {
                            slotForm.setValue("_select_teacher", null);
                            onAppendTeacher(teacher);
                        }}
                    />
                )}

                {type === _typePlaceholder && (
                    <FreeSelectAsync
                        control={slotForm.control}
                        name="_select_teacher"
                        placeholder={"Type to search teacher"}
                        minWidth={300}
                        loadOptions={loadAsyncOptions}
                        value={null}
                        onChange={(option) => {
                            console.log(option);
                            onAppendTeacher(option);
                        }}
                    />
                )}

                <div>
                    {slotForm.watch("timeslot_teachers")?.length > 0 && (
                        <>
                            <div className="mb-2.5 mt-3 text-[13px] text-gray-500">
                                Set a primary teacher by ticking the checkbox
                            </div>
                            <div className="ml-0.5 grid gap-y-2.5">
                                {slotForm
                                    .watch("timeslot_teachers")
                                    .map((teacher, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center gap-x-2"
                                        >
                                            <Checkbox
                                                checked={
                                                    slotForm.watch(
                                                        `timeslot_teachers[${index}].type`
                                                    ) === PRIMARY
                                                }
                                                onCheckedChange={() =>
                                                    onSetPrimaryTeacher(
                                                        teacher,
                                                        index
                                                    )
                                                }
                                            />

                                            <div className="c-text-size">
                                                {teacher?.label ??
                                                    teacher?.name}
                                            </div>
                                            <X
                                                size={20}
                                                className="cursor-pointer rounded-full border border-gray-400 bg-white p-0.5 text-gray-400"
                                                onClick={() =>
                                                    removeTeacher(index)
                                                }
                                            />
                                        </div>
                                    ))}
                            </div>
                        </>
                    )}
                </div>

                <Button type="submit" className="ml-auto mt-8">
                    Save
                </Button>
            </form>
        </Form>
    );
};

export default TimetableEditor;
