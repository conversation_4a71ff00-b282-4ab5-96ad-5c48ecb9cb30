import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import { CommonFormProps, periodGroupAPI } from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const PeriodGroupForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentData, axiosQuery: getPeriodGroup } = useAxios({
        api: periodGroupAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getPeriodGroup({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentData,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            number_of_periods: currentData?.number_of_periods ?? "",
        },
    });

    const { axiosPost: createPeriodGroup, error: postError } = useAxios({
        api: periodGroupAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updatePeriodGroup, error: putError } = useAxios({
        api: periodGroupAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createPeriodGroup(data);
            } else {
                updatePeriodGroup({ id: currentData.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Period Group
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}*`}
                        />
                    ))}

                    <FormInputInterger
                        control={form.control}
                        name="number_of_periods"
                        label="Number of Periods*"
                        min={1}
                    />

                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default PeriodGroupForm;
