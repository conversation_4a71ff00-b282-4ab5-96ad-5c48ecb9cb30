import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { capitalize, isArray, isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import Card from "@/components/ui/Card";
import DataTable from "@/components/ui/DataTable";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormRadioGroup from "@/components/ui/FormRadioGroup";
import InfoCard from "@/components/ui/InfoCard";
import {
    counsellingCaseRecordAPI,
    DATE_FORMAT,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    FULL,
    POSTED,
    studentAPI,
    TableColumnType,
} from "@/lib/constant";
import { useAxios, useCheckViewPermit } from "@/lib/hook";
import {
    combinedNames,
    convertDateTime,
    optionUserLabel,
    refreshForUpdate,
    showBackendFormError,
    toUTC,
} from "@/lib/utils";
import FormTextarea from "../../ui/FormTextarea";
import ActionDropdown from "../../ui/ActionDropdown";
import {
    DropdownMenuItem,
    DropdownMenuSeparator,
} from "../../base-ui/dropdown-menu";
import { useUserProfile } from "../../../lib/store";
import Modal from "../../ui/Modal";
import CaseRecordForm from "./CaseRecordForm";
import DeletePrompt from "../../ui/DeletePrompt";

const IconBarcodeScanner = "/icons/icon-barcode-scanner.svg";

const CreateCaseRecordForm = () => {
    useCheckViewPermit("counselling-case-record-create");

    const hasPermit = useUserProfile((state) => state.hasPermit);

    const locale = useLocale();

    const [selectedStudent, setSelectedStudent] = useState<any>();

    const form = useForm({
        defaultValues: {
            student_id: "",
            student_number: "",
            visit_datetime: new Date(),
            note: "",
            status: "",
        },
    });

    const { axiosPost: createCaseRecord, error: postError } = useAxios({
        api: counsellingCaseRecordAPI,
        onSuccess: () => {
            fetchCaseRecords();
            form.reset();
            form.clearErrors();
        },
    });

    const { axiosQuery: getStudent } = useAxios({
        api: studentAPI,
        locale,
        onSuccess(result) {
            form.setValue("student_number", "");

            if (result.data?.length === 0) {
                setSelectedStudent(null);
                toast("Student not found.");
                return;
            }
            setSelectedStudent(result.data[0]);
        },
    });

    function onScanStudent() {
        const studentNumber = form.watch("student_number");
        if (isEmpty(studentNumber.trim())) return;
        getStudent({
            params: {
                student_number: studentNumber,
                includes: [
                    "user",
                    "currentSemesterPrimaryClass.semesterSetting",
                    "currentSemesterPrimaryClass.semesterClass.classModel",
                ],
                response: FULL,
            },
        });
    }

    function formattedStudent(student) {
        console.log("student", student);
        return {
            photo: student?.photo,
            student_number: student?.student_number,
            name: combinedNames(student?.translations?.name),
            gender: student?.gender,
            class: `${combinedNames(
                student?.current_primary_class?.semester_class?.class_model
                    ?.translations?.name
            )} (${student?.current_primary_class?.semester_setting?.name})`,
        };
    }

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.student_id = selectedStudent?.id;
            data.visit_datetime = toUTC(
                data.visit_datetime,
                DATE_FORMAT.YMD_HMS
            );

            createCaseRecord(data);
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError);
    }, [postError]);

    const [columns, setColumns] = useState<TableColumnType[]>([]);

    const [filter, setFilter] = useState<Record<string, any>>({
        ...DEFAULT_FILTER_PARAMS,
        order_by: {
            visit_datetime: "desc",
        },
    });
    const [pagination, setPagination] = useState();

    const [targetId, setTargetId] = useState(null);
    const [targetDeleteId, setTargetDeleteId] = useState(null);

    const { data, axiosQuery: getStudentCaseRecord } = useAxios({
        api: counsellingCaseRecordAPI,
        locale,
        onSuccess: (result) => setPagination(result?.pagination),
    });

    function closeForm() {
        setTargetId(null);
    }

    function defineColumn() {
        const _columns: TableColumnType[] = [
            {
                key: "visit_datetime",
                modify: (value) => {
                    return <span className="text-sm">{value}</span>;
                },
                hasSort: true,
            },
            {
                key: "note",
                modify: (value) => (
                    <div className="min-w-[200px] text-sm leading-tight">
                        {value}
                    </div>
                ),
            },
            {
                key: "created_by",
                modify: (value) => (
                    <div className="min-w-[120px] text-sm leading-tight">
                        {value}
                    </div>
                ),
            },
            {
                key: "status",
                hasSort: true,
            },
        ];
        setColumns(_columns);
    }

    function definedData(data) {
        return isArray(data)
            ? data.map((item) => ({
                  id: item?.id,
                  visit_datetime: item.visit_datetime
                      ? convertDateTime(item.visit_datetime)
                      : "-",
                  created_by: optionUserLabel(
                      item?.created_by?.employee_number,
                      item?.created_by?.translations?.name
                  ),
                  note: item?.note,
                  status: capitalize(item?.status),
              }))
            : [];
    }

    function onSort(name: string, direction: string) {
        setFilter({
            ...filter,
            order_by: { [name]: direction },
        });
    }

    function fetchCaseRecords() {
        getStudentCaseRecord({
            params: {
                ...filter,
                student_id: selectedStudent?.id,
                includes: ["createdBy"],
            },
        });
    }

    useEffect(() => {
        if (selectedStudent) {
            fetchCaseRecords();
        }
    }, [filter, locale, selectedStudent]);

    useEffect(() => {
        if (data) {
            defineColumn();
        }
    }, [data]);

    return (
        <Card styleClass="max-w-screen-2xl mx-auto">
            <div className="pb-7 lg:px-2">
                <h2 className="mb-5 pt-2">Create Case Record</h2>
                <Form {...form}>
                    <form onSubmit={onSubmit} className="grid gap-y-5 pb-5">
                        <div className="flex items-center gap-x-3">
                            <div className="max-w-[300px] flex-grow">
                                <p className="mb-2.5 ml-0.5 font-medium text-gray-500">
                                    Scan/Input Student Number*
                                </p>
                                <FormInput
                                    control={form.control}
                                    name={"student_number"}
                                    hasLabel={false}
                                    suffixIcon={IconBarcodeScanner}
                                />
                            </div>
                            <Button
                                type="button"
                                variant={"outline"}
                                onClick={onScanStudent}
                                className="max-h-0 self-end"
                            >
                                Enter
                            </Button>
                        </div>

                        {selectedStudent && (
                            <div className="grid gap-4 gap-x-8 border-t border-dashed pt-4 lg:flex lg:items-start">
                                <InfoCard
                                    title="Student"
                                    data={formattedStudent(selectedStudent)}
                                    cardStyleClass="w-full mb-2 lg:max-w-[500px]"
                                />
                                <div className="grid flex-grow gap-4 lg:mt-8">
                                    <DateTimePicker
                                        control={form.control}
                                        name="visit_datetime"
                                        label="visit datetime*"
                                    />

                                    <FormRadioGroup
                                        control={form.control}
                                        name="status"
                                        label="status*"
                                        isStringOptions={true}
                                        isHorizontal={true}
                                        options={[DRAFT, POSTED]}
                                    />

                                    <FormTextarea
                                        control={form.control}
                                        name="note"
                                        label="note*"
                                        rows={8}
                                    />

                                    <div className="mt-1 flex justify-end gap-x-4">
                                        <Button type="submit" className="mt-1">
                                            Submit
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </form>
                </Form>

                {selectedStudent && (
                    <div className="border-t border-dashed pt-4">
                        <h2 className={"mb-5 capitalize"}>Case Records</h2>
                        <DataTable
                            columns={columns}
                            data={definedData(data)}
                            pagination={pagination}
                            setPagination={setPagination}
                            changePage={(arg) =>
                                setFilter({ ...filter, ...arg })
                            }
                            sorted={filter?.order_by}
                            sort={onSort}
                            actionMenu={({ cell }) => (
                                <ActionDropdown>
                                    {hasPermit(
                                        "counselling-case-record-update"
                                    ) && (
                                        <DropdownMenuItem
                                            className="c-text-size"
                                            onClick={() =>
                                                setTargetId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Edit / View
                                        </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    {hasPermit(
                                        "counselling-case-record-delete"
                                    ) && (
                                        <DropdownMenuItem
                                            className="c-text-size text-red-600"
                                            onClick={() =>
                                                setTargetDeleteId(
                                                    cell.row.original.id
                                                )
                                            }
                                        >
                                            Delete
                                        </DropdownMenuItem>
                                    )}
                                </ActionDropdown>
                            )}
                        />
                    </div>
                )}

                {/* update */}
                <Modal open={targetId} onOpenChange={setTargetId} size="medium">
                    <CaseRecordForm
                        id={targetId}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                        close={closeForm}
                    />
                </Modal>

                {/* delete */}
                <Modal open={targetDeleteId} onOpenChange={setTargetDeleteId}>
                    <DeletePrompt
                        id={targetDeleteId}
                        api={counsellingCaseRecordAPI}
                        close={() => setTargetDeleteId(null)}
                        refresh={() => refreshForUpdate(filter, setFilter)}
                    />
                </Modal>
            </div>
        </Card>
    );
};

export default CreateCaseRecordForm;
