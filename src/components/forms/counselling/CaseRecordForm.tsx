import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import React from "react";
import { format } from "date-fns";
import { isEmpty } from "lodash";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { But<PERSON> } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { DateTimePicker } from "@/components/ui/DateTimePicker";
import FormInput from "@/components/ui/FormInput";
import FormRadioGroup from "@/components/ui/FormRadioGroup";
import InfoCard from "@/components/ui/InfoCard";
import { axiosInstance } from "@/lib/api";
import {
    CommonFormProps,
    counsellingCaseRecordAPI,
    DATE_FORMAT,
    DRAFT,
    POSTED,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import {
    combinedNames,
    combineSemesterClass,
    showBackendFormError,
    toMyZonedTime,
    toUTC,
} from "@/lib/utils";
import FormSelectAsync from "../../ui/FormSelectAsync";
import FormTextarea from "../../ui/FormTextarea";

const IconBarcodeScanner = "/icons/icon-barcode-scanner.svg";

const CaseRecordForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentData, axiosQuery: getCaseRecord } = useAxios({
        api: counsellingCaseRecordAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getCaseRecord({ id: props.id });
        }
    }, []);

    console.log("currentData", currentData);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentData,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            visit_datetime: toMyZonedTime(currentData?.visit_datetime ?? null),
            student_id: currentData?.student?.id ?? "",
            student_number: currentData?.student?.student_number ?? "",
            status: currentData?.status ?? "",
            note: currentData?.note ?? "",
        },
    });

    const [student, setStudent] = useState<Record<string, any> | null>(null);

    const { axiosPost: createCaseRecord, error: postError } = useAxios({
        api: counsellingCaseRecordAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateCaseRecord, error: putError } = useAxios({
        api: counsellingCaseRecordAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const locale = useLocale();

    const { axiosQuery: getStudent } = useAxios({
        api: studentAPI,
        locale,
        onSuccess(result) {
            form.setValue("student_number", "");

            if (result.data?.length === 0) {
                toast("Student not found.");
                return;
            }
            setStudent(result.data[0]);
        },
    });

    function onScanStudent() {
        const studentNumber = form.watch("student_number");
        if (isEmpty(studentNumber.trim())) return;
        getStudent({
            params: { student_number: studentNumber },
        });
    }

    function formattedStudent(student) {
        return {
            photo: student?.photo,
            student_number: student?.student_number,
            gender: student?.gender,
            name: combinedNames(student?.translations?.name),
            // class: combineSemesterClass(student?.current_primary_class),
            //TODO: add student's class? (currently class did not return from case record student)
        };
    }

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            data.student_id = student?.id;
            data.visit_datetime = toUTC(
                data.visit_datetime,
                DATE_FORMAT.YMD_HMS
            );
            delete data.student_number;

            if (isCreate) {
                createCaseRecord(data);
            } else {
                updateCaseRecord({
                    id: currentData.id,
                    data,
                });
            }
        })();
    }

    useEffect(() => {
        if (currentData?.student?.student_number) {
            setStudent(currentData.student);
            form.setValue("student_number", currentData.student.student_number);
            form.setValue("student_id", currentData.student.id);
        }
    }, [currentData?.student]);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">
                {isCreate ? "Create" : "Update"} Case Record
            </h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    {/* TODO: create case record move to another page, not sure this is still needed in update form */}
                    {/* <FormSelectAsync
                        control={form.control}
                        name="student_id"
                        label="Student"
                        loadOptions={loadStudentOptions}
                        value={students.find(
                            (option) =>
                                option.value === form.watch("student_id")
                        )}
                    /> */}

                    {/* <div className="flex items-center gap-x-3">
                        <div className="w-full min-w-[300px]">
                            <p className="mb-2.5 ml-0.5 font-medium text-themeLabel">
                                Scan/Input Student Number*
                            </p>
                            <FormInput
                                control={form.control}
                                name={"student_number"}
                                hasLabel={false}
                                suffixIcon={IconBarcodeScanner}
                            />
                        </div>

                        <Button
                            type="button"
                            variant={"outline"}
                            onClick={onScanStudent}
                            className="max-h-0 self-end"
                        >
                            Enter
                        </Button>
                    </div> */}

                    <div>
                        {student && (
                            <div className="xl:col-span-4">
                                <InfoCard
                                    title="Student"
                                    data={formattedStudent(student)}
                                />
                            </div>
                        )}
                    </div>

                    <DateTimePicker
                        control={form.control}
                        name="visit_datetime"
                        label="visit datetime*"
                    />

                    <FormTextarea
                        control={form.control}
                        name="note"
                        label="note*"
                        rows={8}
                    />

                    <FormRadioGroup
                        control={form.control}
                        name="status"
                        label="status*"
                        isStringOptions={true}
                        isHorizontal={true}
                        options={[DRAFT, POSTED]}
                    />

                    <Button type="submit" className="ml-auto mt-10">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default CaseRecordForm;
