import { useLocale } from "next-intl";
import React, { use, useEffect, useState } from "react";
import clsx from "clsx";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import { DatePicker } from "@/components/ui/DatePicker";
import FormSearchInput from "@/components/ui/FormSearchInput";
import FormSelect from "@/components/ui/FormSelect";
import Modal from "@/components/ui/Modal";
import StaffSearchEngine from "@/components/ui/search-engines/StaffSearchEngine";
import StudentSearchEngine from "@/components/ui/search-engines/StudentSearchEngine";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    DRAFT,
    employeeAPI,
    POSTED,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { optionUserLabel, toYMD } from "@/lib/utils";
import { But<PERSON> } from "../../base-ui/button";
import FormSelectAsync from "../../ui/FormSelectAsync";
import { useAsyncSelect } from "../../../lib/async-select-hook";

const FilterCaseRecordForm = ({
    filter,
    setFilter,
    close,
}: CommonFilterProps) => {
    const locale = useLocale();
    const [openSearch, setOpenSearch] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<any>();
    const [openSearchCreatedBy, setOpenSearchCreatedBy] = useState(false);
    const [selectedCreatedBy, setSelectedCreatedBy] = useState<string | null>(
        filter.created_by_name ?? null
    );

    const [createdByEmployeeName, setcreatedByEmployeeName] = useState();

    const { asyncOptions, setAsyncOptions, loadAsyncOptions } = useAsyncSelect({
        api: employeeAPI,
    });

    console.log("cr filter", filter);

    const form = useForm({
        defaultValues: {
            student_id: filter?.student_id ?? "",
            visit_date: filter?.visit_date ?? "",
            created_by_employee_id: filter?.created_by_employee_id ?? "",
            status: filter?.status ?? undefined,
        },
    });

    function onSubmit(data: Record<string, any>) {
        data.created_by_name = selectedCreatedBy;

        const _filter: any = { ...filter, ...data, page: 1 };

        if (data.created_by_employee_id && createdByEmployeeName) {
            _filter.createdByEmployeeName = createdByEmployeeName;
        }
        if (data.visit_date) {
            _filter.visit_date = toYMD(data.visit_date);
        }

        setFilter(_filter);
        close();
    }

    function onClear() {
        form.reset();

        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });

        close();
    }

    useEffect(() => {
        if (filter?.created_by_employee_id && filter?.createdByEmployeeName) {
            loadAsyncOptions(filter.createdByEmployeeName, setAsyncOptions);
        }
    }, []);

    const { axiosQuery: getStudentById } = useAxios({
        api: studentAPI,
        locale,
        onSuccess(result) {
            setSelectedStudent(result?.data);
        },
    });

    useEffect(() => {
        if (filter?.student_id) {
            getStudentById({ id: filter?.student_id });
        }
    }, [filter?.student_id]);

    return (
        <>
            <Form {...form}>
                {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="filter-form"
                >
                    <div>
                        <FormSearchInput
                            name="student_id"
                            control={form.control}
                            label="Student*"
                            displayValue={optionUserLabel(
                                selectedStudent?.student_number,
                                selectedStudent?.translations?.name
                            )}
                            onClick={() => setOpenSearch(true)}
                        />
                    </div>
                    <DatePicker control={form.control} name="visit_date" />

                    <FormSelectAsync
                        control={form.control}
                        name="created_by_employee_id"
                        label="Created by"
                        loadOptions={loadAsyncOptions}
                        value={asyncOptions.find(
                            (option) =>
                                option.value ===
                                form.watch("created_by_employee_id")
                        )}
                        onChange={(val) => {
                            if (val) {
                                form.setValue(
                                    "created_by_employee_id",
                                    val.value
                                ); // Set the correct field
                                setcreatedByEmployeeName(val?.name);
                            } else {
                                form.setValue("created_by_employee_id", null);
                            }
                        }}
                    />

                    <FormSelect
                        control={form.control}
                        name="status"
                        isStringOptions
                        options={[DRAFT, POSTED]}
                    />
                    <div className="filter-duo-buttons">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClear}
                        >
                            Clear
                        </Button>
                        <Button type="submit">Search</Button>
                    </div>
                </form>
            </Form>
            <Modal open={openSearch} onOpenChange={setOpenSearch} size="large">
                <StudentSearchEngine
                    setSelection={(student) => {
                        if (student) {
                            form.setValue("student_id", student.id);
                            setSelectedStudent(student);
                        }
                    }}
                    close={() => setOpenSearch(false)}
                />
            </Modal>
        </>
    );
};

export default FilterCaseRecordForm;
