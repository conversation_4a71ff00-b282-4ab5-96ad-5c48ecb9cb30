import { useLocale, useTranslations } from "next-intl";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    GET_ALL_PARAMS,
    societyCategoryAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../../base-ui/button";

const FilterSocietyForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            code: filter?.code ?? "",
            club_category_id: filter?.club_category_id ?? "",
        },
    });

    const locale = useLocale();
    const t = useTranslations("common");

    const {
        data: societyCategories,
        axiosQuery: getSocietyCategories,
        isLoading: isLoadingOptions,
    } = useAxios({
        api: societyCategoryAPI,
        locale,
        onError: close,
    });

    useEffect(() => {
        getSocietyCategories({
            params: GET_ALL_PARAMS,
        });
    }, [locale]);

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({ ...DEFAULT_FILTER_PARAMS, per_page: filter?.per_page });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormSelect
                    control={form.control}
                    options={societyCategories}
                    name={"club_category_id"}
                    label={t("Society/Club Category")}
                    isLoading={isLoadingOptions}
                />
                <FormInput control={form.control} name={"code"} />
                <FormInput control={form.control} name={"name"} />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterSocietyForm;
