import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormSelect from "@/components/ui/FormSelect";
import {
    cocurriculumTrainerDetailReportAPI,
    EXCEL,
    GET_ALL_PARAMS,
    PDF,
    semesterSettingAPI,
} from "@/lib/constant";
import { useAxios, useSubmit } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { downloadFile } from "@/lib/utils";

const TrainerDownloadReportForm = () => {
    const locale = useLocale();
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const [semesterOptions, setSemesterOptions] = useState<any[]>([]);

    const form = useForm<any>({
        defaultValues: {
            report_language: locale,
            export_type: PDF,
        },
    });

    const { axiosQuery: getSemesterOptions } = useAxios({
        api: semesterSettingAPI,
        locale,
        onSuccess: (response) => {
            setSemesterOptions(response.data);

            const currentSemester = response.data.find(
                (option) => option.is_current_semester === true
            );

            if (currentSemester) {
                form.setValue("semester_setting_id", currentSemester.id);
            }
        },
    });

    const { initLoader } = useSubmit();

    const { axiosQuery: getReportsUrl } = useAxios({
        api: cocurriculumTrainerDetailReportAPI,
        locale,
        onSuccess(result) {
            downloadFile(result.data.url);
        },
    });

    const { axiosQuery: getReportsUrlForPreview } = useAxios({
        api: cocurriculumTrainerDetailReportAPI,
        locale,
        onSuccess(result) {
            window.open(result.data.url, "_blank");
        },
    });

    function submit(query) {
        initLoader(
            form.handleSubmit((data) => {
                console.log(data);

                let hasError = false;
                const requiredFields = [
                    "report_language",
                    "export_type",
                    "semester_setting_id",
                ];
                requiredFields.forEach((key) => {
                    if (!data[key]) {
                        hasError = true;
                        form.setError(key, {
                            type: "manual",
                            message: "This field is required",
                        });
                    }
                });
                if (hasError) return;
                query(data);
            })
        );
    }

    function preview() {
        submit((data) =>
            getReportsUrlForPreview({
                params: { ...data, export_type: PDF },
            })
        );
    }

    function download() {
        submit((data) => getReportsUrl({ params: { ...data } }));
    }

    useEffect(() => {
        getSemesterOptions({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
    }, []);

    return (
        <Form {...form}>
            <h3 className="mb-2 ml-0.5 text-themeGreenDark">
                Download Coach Details Report
            </h3>
            <form className="grid gap-y-3">
                <FormSelect
                    control={form.control}
                    name={"semester_setting_id"}
                    label="Semester*"
                    options={semesterOptions}
                    isSortByName={false}
                />
                <FormSelect
                    control={form.control}
                    name="report_language"
                    label="report language*"
                    options={activeLanguages?.map((language) => ({
                        id: language?.code,
                        name: language?.name,
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="export_type"
                    label="Export Type*"
                    options={[
                        { id: EXCEL, name: "Excel" },
                        { id: PDF, name: "PDF" },
                    ]}
                />

                <div className="mt-5 flex justify-end gap-x-3">
                    <Button onClick={preview} variant={"outline"}>
                        Preview PDF
                    </Button>
                    <Button onClick={download}>Download Report</Button>
                </div>
            </form>
        </Form>
    );
};

export default TrainerDownloadReportForm;
