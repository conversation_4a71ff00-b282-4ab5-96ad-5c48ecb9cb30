import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    CommonFormProps,
    GET_ALL_PARAMS,
    societyAPI,
    societyCategoryAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { showBackendFormError } from "@/lib/utils";

const SocietyForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentData, axiosQuery: getSociety } = useAxios({
        api: societyAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getSociety({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentData) ? (
        <FormWrap
            currentData={currentData}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentData: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    isCreate = false,
    currentData,
    activeLanguages,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            club_category_id: currentData?.category?.id ?? "",
            code: currentData?.code ?? "",
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentData?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
        },
    });

    const locale = useLocale();

    const {
        data: societyCategories,
        axiosQuery: getSocietyCategories,
        isLoading: isLoadingOptions,
    } = useAxios({
        api: societyCategoryAPI,
        locale,
        onError: close,
    });

    const { axiosPost: createSociety, error: postError } = useAxios({
        api: societyAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateSociety, error: putError } = useAxios({
        api: societyAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();
        form.handleSubmit((data: any) => {
            if (isCreate) {
                createSociety(data);
            } else {
                updateSociety({ id: currentData.id, data });
            }
        })();
    }

    useEffect(() => {
        getSocietyCategories({
            params: GET_ALL_PARAMS,
        });
    }, []);

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Society</h2>
            <Form {...form}>
                <form onSubmit={onSubmit} className="grid gap-y-5">
                    <FormSelect
                        control={form.control}
                        options={societyCategories}
                        name={"club_category_id"}
                        label="Club/Society Category*"
                        isLoading={isLoadingOptions}
                    />

                    <FormInput
                        control={form.control}
                        name="code"
                        label={"code*"}
                    />

                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}`}
                        />
                    ))}
                    <Button type="submit" className="ml-auto mt-1">
                        Submit
                    </Button>
                </form>
            </Form>
        </div>
    );
};

export default SocietyForm;
