import { useLocale } from "next-intl";
import { useEffect } from "react";
import React from "react";
import clsx from "clsx";
import { useForm } from "react-hook-form";
import { Button } from "@/components/base-ui/button";
import { Form } from "@/components/base-ui/form";
import { Label } from "@/components/base-ui/label";
import { DatePicker } from "@/components/ui/DatePicker";
import FormDivider from "@/components/ui/FormDivider";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import StatusFormSwitch from "@/components/ui/StatusFormSwitch";
import {
    ACTIVE,
    COCURRICULUM,
    CommonFormProps,
    INACTIVE,
    trainerAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useLanguages } from "@/lib/store";
import { isProperPhoneNumber, showBackendFormError, toYMD } from "@/lib/utils";

const TrainerForm = (props: CommonFormProps) => {
    const activeLanguages = useLanguages((state) => state.activeLanguages);
    const locale = useLocale();

    const { data: currentTrainer, axiosQuery: getTrainer } = useAxios({
        api: trainerAPI,
        locale,
        onError: props.close,
    });

    useEffect(() => {
        if (!props.isCreate && props.id) {
            getTrainer({ id: props.id });
        }
    }, []);

    return activeLanguages && (props.isCreate || currentTrainer) ? (
        <FormWrap
            currentTrainer={currentTrainer}
            activeLanguages={activeLanguages}
            {...props}
        />
    ) : (
        <div className="h-10"></div>
    );
};

type FormWrapProps = CommonFormProps & {
    currentTrainer: any;
    activeLanguages: Array<Record<string, any>>;
};

const FormWrap = ({
    currentTrainer,
    activeLanguages,
    isCreate = false,
    refresh,
    close,
}: FormWrapProps) => {
    const form = useForm({
        defaultValues: {
            trainer_number: currentTrainer?.contractor_number ?? undefined,
            name: activeLanguages
                .map((lang) => lang?.code)
                .reduce(
                    (o, key) => ({
                        ...o,
                        [key]: currentTrainer?.translations?.name?.[key] ?? "",
                    }),
                    {}
                ),
            email: currentTrainer?.email ?? "",
            phone_number: currentTrainer?.phone_number ?? "",
            department: currentTrainer?.department ?? COCURRICULUM,
            nric: currentTrainer?.nric ?? "",
            passport_number: currentTrainer?.passport_number ?? "",
            employed_date: isCreate
                ? new Date()
                : currentTrainer?.employed_date ?? "",
            resignation_date: currentTrainer?.resignation_date ?? "",
            status: isCreate ? true : currentTrainer?.status === ACTIVE,
        },
    });

    const { axiosPost: createTrainer, error: postError } = useAxios({
        api: trainerAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    const { axiosPut: updateTrainer, error: putError } = useAxios({
        api: trainerAPI,
        onSuccess: () => {
            close();
            refresh();
        },
    });

    function onSubmit(e) {
        e.preventDefault();
        form.clearErrors();

        form.handleSubmit((data: any) => {
            if (!isProperPhoneNumber(form, "phone_number", data.phone_number)) {
                return;
            }
            data.status = data.status ? ACTIVE : INACTIVE;
            data.department = COCURRICULUM;
            data.employed_date = toYMD(data.employed_date);
            data.resignation_date = toYMD(data.resignation_date);

            if (isCreate) {
                createTrainer(data);
            } else {
                updateTrainer({ id: currentTrainer.id, data });
            }
        })();
    }

    useEffect(() => {
        showBackendFormError(form, postError || putError);
    }, [postError, putError]);

    return (
        <div>
            <h2 className="mb-5">{isCreate ? "Create" : "Update"} Coach</h2>
            <Form {...form}>
                <form
                    onSubmit={onSubmit}
                    className="grid-form lg:min-w-[800px]"
                >
                    <div>
                        <Label className="label">Coach Number</Label>
                        <div
                            className={clsx(
                                "c-text-input flex h-[42px] w-full items-center rounded-sm border border-gray-200 px-4",
                                isCreate ? "text-themeLabel" : "text-gray-600"
                            )}
                        >
                            {form.watch("trainer_number") ?? "Auto-generated"}
                        </div>
                        {isCreate && (
                            <div className="ml-1 mt-1.5 text-[12px] leading-tight text-themeLabel lg:-mb-2">
                                The coach number will be automatically generated
                                using a sequential number
                            </div>
                        )}
                    </div>
                    <FormDivider />
                    {activeLanguages.map((lang, index) => (
                        <FormInput
                            key={lang?.code || index}
                            control={form.control}
                            name={`name[${lang?.code}]`}
                            label={`Name in ${lang?.name}`}
                        />
                    ))}

                    <FormInput
                        control={form.control}
                        name="email"
                        type="email"
                        label="email*"
                    />

                    <FormPhoneInput
                        form={form}
                        name="phone_number"
                        label="phone number*"
                    />

                    <FormInputInterger
                        control={form.control}
                        name="nric"
                        label="NRIC"
                    />

                    <FormInput control={form.control} name="passport_number" />

                    <DatePicker
                        control={form.control}
                        name="employed_date"
                        label="employed_date*"
                    />

                    <DatePicker
                        control={form.control}
                        name="resignation_date"
                    />

                    <StatusFormSwitch control={form.control} name="status" />

                    <div className="lg:col-span-2">
                        <Button type="submit" className="ml-auto mt-1">
                            Submit
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default TrainerForm;
