import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import FormInputInterger from "@/components/ui/FormInputInterger";
import FormPhoneInput from "@/components/ui/FormPhoneInput";
import FormSelect from "@/components/ui/FormSelect";
import {
    ACTIVE,
    CommonFilterProps,
    DEFAULT_FILTER_PARAMS,
    INACTIVE,
} from "@/lib/constant";
import { Button } from "../../base-ui/button";
import { useTranslations } from "next-intl";

const FilterTrainerForm = ({ filter, setFilter, close }: CommonFilterProps) => {
    const form = useForm({
        defaultValues: {
            name: filter?.name ?? "",
            email: filter?.email ?? "",
            phone_number: filter?.phone_number ?? "",
            nric: filter?.nric ?? "",
            passport_number: filter?.passport_number ?? "",
            status: filter?.status ?? undefined,
        },
    });
    const t = useTranslations("common");

    function onSubmit(data: Record<string, any>) {
        setFilter({ ...filter, ...data, page: 1 });
        close();
    }

    function onClear() {
        form.reset();
        setFilter({
            ...DEFAULT_FILTER_PARAMS,
            per_page: filter?.per_page,
        });
        close();
    }

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form"
            >
                <FormInput control={form.control} name={"name"} />

                <FormInput control={form.control} name="email" type="email" />

                <FormPhoneInput form={form} name="phone_number" />

                <FormInputInterger
                    control={form.control}
                    name="nric"
                    label="NRIC number"
                />

                <FormInput control={form.control} name={"passport_number"} />

                <FormSelect
                    control={form.control}
                    name="status"
                    isStringOptions={true}
                    options={[ACTIVE, INACTIVE]}
                />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterTrainerForm;
