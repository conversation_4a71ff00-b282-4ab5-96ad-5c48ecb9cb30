import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/base-ui/form";
import FormInput from "@/components/ui/FormInput";
import {
    classStreamOptions,
    CommonFilterProps,
    genderOptions,
    GET_ALL_PARAMS,
    gradeAPI,
    hostelStudentAPIFilter,
    semesterClassDropdownFilter,
    semesterClassesAPI,
    semesterSettingAPI,
    studentAPI,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { Button } from "../base-ui/button";
import FormPhoneInput from "../ui/FormPhoneInput";
import FormSelect from "../ui/FormSelect";
import { groupedSemesterClassOptions } from "@/lib/utils";
import FormGroupedSelect from "../ui/FormGroupedSelect";
import FreeSelectAsync from "../ui/FreeSelectAsync";
import { useAsyncSelect } from "@/lib/async-select-hook";

const FilterStudentForm = ({
    filter,
    setFilter,
    close,
    isHostel,
}: CommonFilterProps & { isHostel?: boolean }) => {
    const locale = useLocale();
    const t = useTranslations("common");

    const [currentSemester, setCurrentSemester] = useState<any>();
    const [asyncValue, setAsyncValue] = useState<Record<string, any> | null>(
        null
    );

    const { loadAsyncOptions } = useAsyncSelect({
        api: studentAPI,
        isStudentNumber: true,
        useCommonSearch: true,
    });

    const form = useForm({
        defaultValues: {
            student_number: filter?.student_number ?? "",
            name: filter?.name ?? "",
            semester_setting_id: filter?.semester_setting_id ?? "",
            grade_ids: filter?.grade_ids ?? [],
            semester_class_ids: filter?.semester_class_ids ?? [],
            admission_type: filter?.admission_type ?? "",
            class_stream: filter?.class_stream ?? "",
            is_active: filter?.is_active ?? undefined,

            guardian_name: filter?.guardian_name ?? "",
            guardian_email: filter?.guardian_email ?? "",
            guardian_phone_number: filter?.guardian_phone_number ?? "",
        },
    });

    function onSubmit(data: Record<string, any>) {
        setFilter({
            ...filter,
            ...data,
            page: 1,
            student_number:
                data.student_number.trim() === ""
                    ? filter.student_number
                    : data.student_number,
        });
        close();
    }

    function onClear() {
        form.reset();
        setAsyncValue(null);
        form.setValue("semester_setting_id", currentSemester?.id);
        setFilter({
            ...(isHostel ? hostelStudentAPIFilter : {}),
            per_page: filter?.per_page,
            page: 1,
            semester_setting_id: currentSemester?.id ?? null,
            order_by: {
                student_number: "desc",
            },
        });
        close();
    }

    const { data: semesterSettings, axiosQuery: getSemesterSettings } =
        useAxios({
            api: semesterSettingAPI,
            locale,
            onSuccess: (res) => {
                const _currentSemester = res.data.find(
                    (semester) => semester.is_current_semester
                );
                if (_currentSemester) {
                    setCurrentSemester(_currentSemester);
                    form.setValue("semester_setting_id", _currentSemester?.id);
                    fetchSemesterClasses();
                }
            },
        });

    const { data: grades, axiosQuery: getGrades } = useAxios({
        api: gradeAPI,
        locale,
        onSuccess: (res) => {
            res.data.sort((a, b) => b.sequence - a.sequence);
        },
    });

    const { data: semesterClassOptions, axiosQuery: getSemesterClassOptions } =
        useAxios({
            api: semesterClassesAPI,
            locale,
        });

    useEffect(() => {
        getSemesterSettings({
            params: { ...GET_ALL_PARAMS, order_by: { name: "desc" } },
        });
        getGrades({ params: GET_ALL_PARAMS });
    }, [locale]);

    function fetchSemesterClasses() {
        const semesterSettingId = form.watch("semester_setting_id");
        if (semesterSettingId) {
            getSemesterClassOptions({
                params: {
                    ...GET_ALL_PARAMS,
                    ...semesterClassDropdownFilter(locale),
                    semester_setting_id: form.getValues("semester_setting_id"),
                },
            });
        }
    }

    useEffect(() => {
        fetchSemesterClasses();
    }, [locale]);

    useEffect(() => {
        form.setValue("student_number", filter?.student_number ?? "");
    }, [filter?.student_number]);

    return (
        <Form {...form}>
            {/* <h2 className="mb-2 ml-0.5 text-themeBlack">Filter</h2> */}
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="filter-form bigger"
            >
                <div className="lg:col-span-2">
                    <FreeSelectAsync
                        control={form.control}
                        name="student_number"
                        label="quick_search"
                        placeholder="Type to search student"
                        loadOptions={loadAsyncOptions}
                        value={asyncValue}
                        onChange={(option) => {
                            form.setValue(
                                "student_number",
                                option?.value ?? ""
                            );
                            setAsyncValue(option);
                            setFilter({
                                ...filter,
                                page: 1,
                                student_number: option?.value ?? undefined,
                            });
                        }}
                    />
                </div>

                {/* <FormInput control={form.control} name="student_number" /> */}

                <FormInput
                    control={form.control}
                    name="name"
                    label={t("mix_name")}
                />

                <FormSelect
                    control={form.control}
                    name="semester_setting_id"
                    label="semester"
                    options={semesterSettings}
                    isSortByName={false}
                    onChange={(semesterSettingId) => {
                        form.setValue("semester_class_ids", "");
                        form.setValue("grade_ids", "");
                        if (semesterSettingId) {
                            fetchSemesterClasses();
                        }
                    }}
                />

                <FormGroupedSelect
                    isMulti={true}
                    control={form.control}
                    name={"semester_class_ids"}
                    label={"classes"}
                    isDisabled={!form.watch("semester_setting_id")}
                    options={groupedSemesterClassOptions(semesterClassOptions)}
                />

                <FormSelect
                    isMulti={true}
                    control={form.control}
                    name="grade_ids"
                    label="grades"
                    options={grades}
                    isSortByName={false}
                    isDisabled={!form.watch("semester_setting_id")}
                />

                <FormSelect
                    control={form.control}
                    name="gender"
                    isSortByName={false}
                    options={genderOptions.map((gender) => ({
                        id: gender.id,
                        name: t(gender.name),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="admission_type"
                    isSortByName={false}
                    options={["NEW", "TRANSFERRED"].map((type) => ({
                        id: type,
                        name: t(type === "NEW" ? "new_student" : type),
                    }))}
                />

                <FormSelect
                    control={form.control}
                    name="class_stream"
                    options={classStreamOptions
                        .filter((option) => option.id != "NA")
                        .map((type) => ({
                            id: type.id,
                            name: t(type.name),
                        }))}
                />

                <FormSelect
                    control={form.control}
                    name="is_active"
                    label="status"
                    options={[
                        {
                            name: t("Active"),
                            id: "1",
                        },
                        {
                            name: t("Inactive"),
                            id: "0",
                        },
                    ]}
                />

                <FormInput control={form.control} name="guardian_name" />

                <FormInput control={form.control} name="guardian_email" />

                <FormPhoneInput form={form} name={`guardian_phone_number`} />

                <div className="filter-duo-buttons">
                    <Button type="button" variant="outline" onClick={onClear}>
                        {t("Clear")}
                    </Button>
                    <Button type="submit">{t("Search")}</Button>
                </div>
            </form>
        </Form>
    );
};

export default FilterStudentForm;
