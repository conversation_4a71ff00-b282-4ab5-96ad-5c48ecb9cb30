import React, { useEffect, useState } from "react";
import clsx from "clsx";
import { capitalize } from "lodash";
import {
    ArrowLeft,
    Bell,
    ChevronDown,
    ChevronsLeft,
    Menu,
    XCircle,
} from "lucide-react";
import Image from "next/image";
import Logo from "/public/icons/logo.png";
import Link from "next/link";
import { useRouter } from "next/router";
import { useTranslations } from "use-intl";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/base-ui/accordion";
import {
    EMPLOYEE,
    locales,
    nav,
    profileAPI,
    schoolAppName,
} from "@/lib/constant";
import { useAxios } from "@/lib/hook";
import { useNav, useUserProfile } from "@/lib/store";
import {
    arrayContainsAll,
    arrayContainsAny,
    logout,
    replaceAll,
} from "@/lib/utils";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "./base-ui/dropdown-menu";
import MyProfileForm from "./forms/MyProfileForm";
import Modal from "./ui/Modal";

type NavWrapProps = {
    path: string;
    children: React.ReactNode;
    locale?: string;
};

const NavWrap = ({ path, children, locale }: NavWrapProps) => {
    const userProfile = useUserProfile((state) => state.userProfile);
    const setUserProfile = useUserProfile((state) => state.setUserProfile);
    const t = useTranslations("Nav");

    const router = useRouter();
    const isHome = path === "";
    const [isHovered, setIsHovered] = useState(false);
    const [openMobileMenu, setOpenMobileMenu] = useState(false);
    const { isMinimized, setIsMinimized } = useNav();
    const [openMyProfile, setOpenMyProfile] = useState(false);
    const [userPermissions, setUserPermissions] = useState<any[] | null>(null);
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setUserPermissions(
            userProfile
                ? userProfile.permissions?.flatMap((p) => [
                      p?.sub_category,
                      p?.name,
                  ])
                : null
        );
    }, [userProfile]);

    useEffect(() => {
        setMounted(true);
    }, []);

    return mounted ? (
        <div className={clsx("flex h-screen flex-col antialiased")}>
            {/* top */}
            <div className="sticky top-0 z-10 flex items-center justify-between gap-x-4 bg-[#9de7a8] px-5 py-1 lg:static lg:gap-x-6 lg:px-8">
                <Link
                    href={"/"}
                    className="my-1 flex items-center gap-x-2 lg:my-0.5"
                >
                    <Image
                        priority
                        src={Logo}
                        alt={schoolAppName}
                        className="h-9 w-auto lg:h-14"
                    />
                </Link>

                <LanguageSelector locale={locale} />

                <Link href={"/notifications"} className="hidden lg:block">
                    <Bell size={24} className="text-themeGreenDark" />
                </Link>

                <div className="">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <div>
                                <Avatar locale={locale} />
                            </div>
                        </DropdownMenuTrigger>

                        <DropdownMenuContent
                            align="end"
                            className="rounded-md bg-white text-sm"
                        >
                            <DropdownMenuItem
                                onClick={() => setOpenMyProfile(true)}
                            >
                                {t("My Profile")}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() =>
                                    logout(router, () => setUserProfile(null))
                                }
                            >
                                {t("Log Out")}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <Menu
                    className="text-themeGreenDark lg:hidden"
                    onClick={() => setOpenMobileMenu(true)}
                />
            </div>

            {/* content */}
            <div className="flex h-full flex-col bg-themeGreenDark lg:flex-grow lg:flex-row lg:overflow-auto">
                {isHome ? (
                    <div className="px-5 pt-4 lg:hidden">
                        <div className="mb-4 flex items-center justify-between gap-x-3">
                            <div className="text-white">
                                <p>{t("Welcome Back,")}</p>
                                <p className="font-semibold">
                                    {userProfile?.userables[0]?.name ?? ""}
                                </p>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div>
                        <ArrowLeft
                            color="white"
                            className="my-2 ml-4 lg:hidden"
                            onClick={() => router.back()}
                        />
                    </div>
                )}
                <div
                    className={clsx(
                        "fixed right-0 top-0 z-10 w-full transition lg:relative lg:w-fit",
                        openMobileMenu
                            ? "translate-x-0"
                            : "translate-x-full lg:translate-x-0"
                    )}
                >
                    <XCircle
                        className="absolute right-2 top-2 z-10 text-white opacity-70 lg:hidden"
                        onClick={() => setOpenMobileMenu(false)}
                    />
                    <div
                        className={clsx(
                            "absolute right-0 top-0 grid h-screen overflow-auto bg-themeGreenDark pb-12 pt-5 lg:relative lg:h-full lg:min-w-fit lg:pb-3 lg:pt-3",
                            !isMinimized && "lg:w-[210px]"
                        )}
                    >
                        <div
                            className={clsx(
                                "flex h-fit min-w-fit flex-col gap-x-2 gap-y-1 px-2.5 pt-3 lg:pt-0"
                            )}
                            onMouseEnter={() => setIsHovered(true)}
                            onMouseLeave={() => setIsHovered(false)}
                        >
                            {Array.isArray(userPermissions) &&
                                Object.entries(nav).map(([key, data]) => (
                                    <NavBtn
                                        key={key}
                                        name={key}
                                        data={data}
                                        activePath={path}
                                        prevPath=""
                                        depth={0}
                                        activeParent={true}
                                        isOpen={isHovered || !isMinimized}
                                        userPermissions={userPermissions}
                                    />
                                ))}
                        </div>
                        <div className="mt-auto">
                            <ChevronsLeft
                                className={clsx(
                                    "ml-auto mr-5 mt-5 hidden cursor-pointer text-white opacity-50 transition hover:opacity-80 lg:block",
                                    isMinimized && "rotate-180"
                                )}
                                onClick={() => setIsMinimized(!isMinimized)}
                            />
                        </div>
                    </div>
                </div>

                <div className="h-full rounded-t-3xl bg-white px-5 py-6 lg:flex-grow lg:overflow-auto lg:rounded-none lg:bg-[#f1f1f1] lg:px-8">
                    {children}
                </div>
            </div>

            <Modal open={openMyProfile} onOpenChange={setOpenMyProfile}>
                <MyProfileForm
                    close={() => setOpenMyProfile(false)}
                    refresh={() => {}}
                />
            </Modal>
        </div>
    ) : null;
};

function getNestedPermissions(data) {
    let nestedPermissions: string[] = [];
    const items = data.items ? Object.values(data.items) : [];

    if (items?.length > 0) {
        items.forEach((nestedData: Record<any, string>) => {
            const nestedItems = nestedData.items
                ? Object.values(nestedData.items)
                : [];

            if (nestedItems?.length > 0) {
                nestedPermissions = [
                    ...nestedPermissions,
                    ...getNestedPermissions(nestedData),
                ];
            } else if (Array.isArray(nestedData.permissions)) {
                nestedPermissions = [
                    ...nestedPermissions,
                    ...nestedData.permissions,
                ];
            }
        });
    } else if (Array.isArray(data.permissions)) {
        nestedPermissions = [...nestedPermissions, ...data.permissions];
    }

    return nestedPermissions;
}

const NavBtn = ({
    name,
    data,
    activePath,
    prevPath,
    depth,
    activeParent,
    isOpen,
    userPermissions,
}: {
    name: string;
    data: Record<string, any>;
    activePath: string;
    prevPath: string;
    depth: number;
    activeParent: boolean;
    isOpen: boolean;
    userPermissions: any[];
}) => {
    const userProfile = useUserProfile((state) => state.userProfile);

    const t = useTranslations("Nav");

    const isActive: boolean =
        activeParent &&
        activePath?.split("/")[depth] === replaceAll(name, "_", "-");
    const currentPath = `${prevPath}/${replaceAll(name, "_", "-")}`;

    const hasMerchantType = userProfile?.userables?.some((userable) =>
        userable.userable_type?.toLowerCase().includes("merchant")
    );
    if (data.hideForMerchantRole && hasMerchantType) return null;

    if (data.items) {
        const isPermitted = data.containAllPermissions
            ? arrayContainsAll(
                  userPermissions,
                  data.permissions
                      ? data.permissions
                      : getNestedPermissions(data)
              )
            : arrayContainsAny(
                  userPermissions,
                  data.permissions
                      ? data.permissions
                      : getNestedPermissions(data)
              );

        if (!isPermitted) return null;
        return (
            <Accordion
                type="single"
                collapsible
                defaultValue={isActive ? name : ""}
            >
                <AccordionItem hasLine={false} value={name}>
                    <AccordionTrigger
                        hasChevron={isOpen}
                        className={clsx(
                            "nav-btn",
                            isActive &&
                                "bg-themeGreen5 bg-opacity-50 text-themeGreen4"
                        )}
                        isWhite={true}
                    >
                        <div
                            className={clsx(
                                "flex w-full items-center",
                                isOpen && "gap-x-3"
                            )}
                        >
                            {data.lucideIcon ? (
                                <data.lucideIcon className="block h-auto w-5 text-themeGreen" />
                            ) : data.icon ? (
                                data.icon && (
                                    <Image
                                        src={data.icon}
                                        alt={"icon"}
                                        className="block h-auto w-5"
                                        width={20}
                                        height={20}
                                        unoptimized
                                    />
                                )
                            ) : (
                                <div className="nav-dot opacity-30"></div>
                            )}

                            {isOpen && (
                                <div className="w-full max-w-[calc(100%-16px)]">
                                    {t(data.title)}
                                </div>
                            )}
                        </div>
                    </AccordionTrigger>
                    <AccordionContent
                        className={
                            "relative ml-3.5 mt-1 grid gap-y-0.5 pb-1 pl-1"
                        }
                    >
                        {depth > 0 && (
                            <div className="absolute -left-1.5 h-full w-px border-l border-dashed border-themeGreen5"></div>
                        )}
                        {isOpen &&
                            Object.entries(data.items).map(
                                (
                                    [key, value]: [
                                        key: string,
                                        value: Record<string, any>,
                                    ],
                                    index
                                ) => {
                                    return (
                                        <NavBtn
                                            key={currentPath + index}
                                            name={key}
                                            data={value}
                                            activeParent={isActive}
                                            activePath={activePath}
                                            prevPath={currentPath}
                                            depth={depth + 1}
                                            isOpen={isOpen}
                                            userPermissions={userPermissions}
                                        />
                                    );
                                }
                            )}
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        );
    } else {
        const userPermissions = userProfile?.permissions.flatMap((p) => [
            p?.sub_category,
            p?.name,
        ]);
        const btnPermittedCategories = data?.permissions ?? [];

        const isPermitted = data.containAllPermissions
            ? arrayContainsAll(userPermissions, btnPermittedCategories)
            : arrayContainsAny(userPermissions, btnPermittedCategories);

        if (!isPermitted) return null;

        return (
            <Link
                className={clsx(
                    "nav-btn",
                    isActive && "bg-themeGreen5 text-themeGreen4"
                )}
                href={`${currentPath}`}
            >
                <div className="flex w-full items-center gap-x-3">
                    {data.lucideIcon ? (
                        <data.lucideIcon className="block h-auto w-5 text-themeGreen" />
                    ) : data.icon ? (
                        <Image
                            src={data.icon}
                            alt={"icon"}
                            className="h-auto w-5"
                            width={20}
                            height={20}
                            unoptimized
                        />
                    ) : (
                        <div className="nav-dot"></div>
                    )}
                    {isOpen && (
                        <div className="w-full max-w-[calc(100%-16px)]">
                            {t(data.title)}
                        </div>
                    )}
                </div>
            </Link>
        );
    }
};

const Avatar = ({ locale }) => {
    const router = useRouter();
    const { userProfile, setUserProfile } = useUserProfile((state) => state);
    const employeeUserable = userProfile?.userables.find(
        (userable) => userable.user_type_description === capitalize(EMPLOYEE)
    );
    const userable = employeeUserable ?? userProfile?.userables[0];
    const userName = userable?.translations?.name?.[locale] ?? userable?.name;

    const { axiosQuery: getProfile } = useAxios({
        api: profileAPI,
        locale,
        noLoading: true,
        onSuccess(result) {
            setUserProfile(result.data);
            if (result.data?.is_password_reset_required) {
                router.push("/change-password");
            }
        },
    });

    useEffect(() => {
        if (!userProfile) {
            getProfile({
                params: {
                    includes: [
                        "userables.wallets",
                        "guardian.students.userable.wallets",
                        "permissions",
                        "roles",
                    ],
                },
            });
        }
    }, [userProfile]);

    return (
        <div className="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-white text-[16px] transition hover:opacity-80">
            {userName?.charAt(0)}
        </div>
    );
};

const LanguageSelector = ({ locale }: { locale?: string }) => {
    const router = useRouter();

    return (
        <div className="ml-auto cursor-pointer">
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <div className="flex items-center text-xs font-medium text-themeGreenDark lg:text-sm">
                        <span>{locale ? locales[locale] : ""}</span>
                        <ChevronDown className="ml-1" size={16} />
                    </div>
                </DropdownMenuTrigger>

                <DropdownMenuContent
                    align="end"
                    className="rounded-md bg-white text-sm"
                >
                    {Object.entries(locales).map(([key, value]) => {
                        return (
                            <DropdownMenuItem asChild key={key}>
                                <Link href={router.asPath} locale={key}>
                                    {value}
                                </Link>
                            </DropdownMenuItem>
                        );
                    })}
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};

export default NavWrap;
