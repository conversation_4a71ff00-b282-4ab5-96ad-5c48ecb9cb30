{"parser": "@typescript-eslint/parser", "extends": ["next", "next/core-web-vitals", "prettier", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "consistent-return": "off", "import/prefer-default-export": "off", "lines-between-class-members": ["error", "always", {"exceptAfterSingleLine": true}], "react/no-danger": "off", "react/require-default-props": "off", "react/jsx-first-prop-new-line": [2, "multiline"], "react/jsx-max-props-per-line": [2, {"maximum": 1, "when": "multiline"}]}}