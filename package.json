{"name": "skribble-learn-backoffice-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3006", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "lint:write": "next lint --fix", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@sentry/nextjs": "8", "@tanstack/react-table": "^8.16.0", "@tiptap/pm": "^2.9.0", "@tiptap/react": "^2.9.0", "@tiptap/starter-kit": "^2.9.0", "axios": "^1.6.8", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "isomorphic-dompurify": "^2.19.0", "lodash": "^4.17.21", "lucide-react": "^0.376.0", "next": "14.2.3", "next-intl": "^3.17.2", "nookies": "^2.5.2", "qrcode": "^1.5.4", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-date-range": "^2.0.1", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.51.3", "react-hot-toast": "^2.4.1", "react-phone-number-input": "^3.4.5", "react-quill": "^2.0.0", "react-select": "^5.8.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.2", "zod": "^3.23.5", "zustand": "^4.5.2"}, "devDependencies": {"@babel/types": "^7.24.9", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@types/lodash": "^4.17.7", "@types/node": "20.14.11", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^2.0.4", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "husky": ">=6", "jsdom": "^24.1.1", "lint-staged": ">=10", "postcss": "^8", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.1", "tailwindcss": "^3.4.1", "typescript": "5.5.3", "vitest": "^2.0.4"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,md}": "prettier --write"}}